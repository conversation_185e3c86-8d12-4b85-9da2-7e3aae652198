<?php

switch ($ctype) {
    case 'clinical_video':
        if (!empty($env)) {
            if ($env != 'GL') {
                $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
            } else {
                $envStatus = "AND cm.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        $sql = "SELECT
        cm.video_archive_id as type_id,
        cm.video_archive_question,
        cm.video_archive_answer,
        cm.video_archive_question_raw,
        cm.video_archive_answer_raw,
        cm.video_archive_file_img,
        cm.video_archive_file_img_thumbnail,
        cm.start_like,
        cm.added_on,
        cm.publication_date,
        cln.client_name,
        cln.client_logo,
        cm.deeplink,
        cm.type,
        cm.vendor,
        cm.src,
        cm.gl_deeplink,
        cm.video_archive_tags,
        ks.session_doctor_id,
        kvtd.play_time,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        cm.video_archive_speciality_id ,
        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = " . $type_id . " and  rt.post_type='video_archive' and rt.rating!=0)as averageRating,
        rtmy.rating  as myrating,
        (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = " . $type_id . " and kcm.type = 'video_archive')as count_comment,
        kv.status as vault
        FROM knwlg_video_archive  as cm
        JOIN video_archive_to_specialities  as cmTs ON cmTs.video_archive_id = cm.video_archive_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        LEFT JOIN knwlg_video_tracking_data as kvtd ON kvtd.content_id=cm.video_archive_id AND kvtd.content_type='video_archive' AND kvtd.user_master_id=" . $user_master_id . "
        LEFT JOIN video_archive_to_sponsor  as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
        LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
        LEFT JOIN client_master as cln ON cln.client_master_id = cm.client_id
        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id and  rtmy.post_type='video_archive' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id and  kv.type_text='video_archive' and  kv.user_id = " . $user_master_id . "
        LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id and  rt.post_type='video_archive'
        WHERE cm.status=3
        " . $envStatus . "
        AND date(cm.publication_date)<=CURDATE() and
        cm.video_archive_id = " . $type_id . "";
        //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
        //echo $sql; exit;
        //cm.publication_date
        $query = $CI->db->query($sql);
        $result = $query->row();
        //banner end
        //poll start
        $sqlCompl = "SELECT
        sv.*
        FROM
        survey_user_answer sv
        WHERE
        sv.user_master_id = '" . $user_master_id . "'";
        $queryCompl = $CI->db->query($sqlCompl);
        $resultCompl = $queryCompl->result();
        $complID = array();
        foreach ($resultCompl as $valCompl) {
            $complID[] = $valCompl->survey_id;
        }
        //print_r($complID); exit;
        $sqlInCompl = "SELECT
        sv.*
        FROM
        survey_user_incomplete_answer sv
        WHERE
        sv.status = 3
        and
        sv.user_master_id = '" . $user_master_id . "'";
        $queryInCompl = $CI->db->query($sqlInCompl);
        $resultInCompl = $queryInCompl->result();
        $incomplID = array();
        foreach ($resultInCompl as $valInCompl) {
            $incomplID[] = $valInCompl->survey_id;
        }
        $arrayFinal = array_unique(array_merge($complID, $incomplID));
        //print_r($arrayFinal); exit;
        $complIDStr = implode(",", array_filter($arrayFinal));
        //echo $complIDStr ; exit;
        if ($complIDStr) {
            $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
        } else {
            $qryStr = '';
        }
        $sqlPoll = "SELECT
        sv.* ,
        svd.data,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        cln.client_name,
        cln.client_logo,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
        FROM
        survey sv
        left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
        left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
        JOIN client_master as cln ON cln.client_master_id = sv.client_id
        LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
        JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
        JOIN survey_to_medwiki as stm ON stm.survey_id = sv.survey_id
        left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
        WHERE
        sv.status = 3
        and date(sv.publishing_date) <= CURDATE() and
        stm.medwiki_id = " . $type_id . "
        " . $qryStr . " ";
        $queryPoll = $CI->db->query($sqlPoll);
        $resultPoll = $queryPoll->result();
        //echo $sqlPoll;
        // print_r($resultPoll);
        foreach ($resultPoll as $valSurvey) {
            $dataArry = unserialize($valSurvey->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);
            $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($valSurvey->sponsor_logo) {
                    $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $vxPoll[] = array(
                "survey_id" => $valSurvey->survey_id,
                "category" => $valSurvey->category,
                "point" => $valSurvey->survey_points,
                "json_data" => $str,
                "survey_title" => $valSurvey->survey_title,
                "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0), //$valSurvey->deeplink,
                "survey_description" => substr($valSurvey->survey_description, 0, 150),
                "image" => $valSurvey->image,
                "specialities_name" => $valSurvey->specialities_name,
                "client_name" => $valSurvey->client_name,
                "client_logo" => '' . $valSurvey->client_logo,
                "sponsor_name" => $valSurvey->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "publishing_date" => $valSurvey->publishing_date,
            );
        }
        //print_r($vxPoll);
        //poll end
        /* if (@getimagesize(base_url() . "uploads/compendium/" . $result->comp_qa_file_img)) {
             $img = image_thumb_url('uploads/compendium/' . $result->comp_qa_file_img, $result->comp_qa_file_img, 203, 304, '');
         } else {
             $img = '';
         }*/
        if ($result->video_archive_file_img) {
            $img = $result->video_archive_file_img; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;
        } else {
            $img = '';
        }
        $sponsorLogoArry = explode(",", $result->sponsor_logo);
        if (count($sponsorLogoArry) > 0) {
            foreach ($sponsorLogoArry as $valueSponor) {
                if ($valueSponor) {
                    $sponsorLogomix[] = '' . $valueSponor;
                }
            }
        } else {
            if ($result->sponsor_logo) {
                $sponsorLogomix[] = '' . $result->sponsor_logo;
            }
        }
        $sponsorLogo = implode(",", (array)$sponsorLogomix);
        unset($sponsorLogomix);
        unset($sponsorLogoArry);
        //---------------------------------------------------------------------------
        $session_doc_array = explode(",", $result->session_doctor_id);
        $ses_doc_det_array = array();
        $inc_pp = 0;
        foreach ($session_doc_array as $single_doctor) {
            $var = session_doc_detail($single_doctor); //-----------------------remote fu
            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
            if ($image) {
                if (stripos($image, "https://storage.googleapis.com") > -1) {
                    $logic_image = $image;
                } else {
                    $logic_image = docimg;
                }
                //=======================================
            } else {
                $logic_image = docimg;
            }
            $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
            $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
            $inc_pp++;
        }
        //$string = htmlentities($result->comp_qa_question_raw, null, 'utf-8');
        $string = $result->video_archive_question_raw;
        // $string = str_replace("&#39;", "dddd", $string);
        // $string = str_replace("nbsp;", "", $string);
        // $string = str_replace("&amp;", "", $string);
        $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
        $main_description = "";
        $main_description = str_replace("\n\t", "\n", $result->video_archive_answer_raw);
        $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);
        $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
        $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
        $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
        $responses['content_id'] = $result->type_id;
        $vx = array(
            "type_id" => $result->type_id,
            "con_type" => $result->type,
            "type" => 'video_archive',
            "vendor" => $result->vendor,
            "src" => $result->src,
            "date" => date(' jS F y', strtotime($result->publication_date)),
            "question" => html_entity_decode($string),
            "answer" => html_entity_decode($main_description), //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),
            "video_archive_question" => $result->video_archive_question,
            "video_archive_answer" => $result->video_archive_answer, //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),
            "image" => $img,
            "specialities" => $result->specialities_name,
            "specialities_id" => $result->comp_qa_speciality_id,
            "specialities_ids_and_names" => explode_speciality_string($result->specialities_ids_and_names),
            "client_name" => $result->client_name,
            "client_logo" => '' . $result->client_logo,
            "channel" => getchannelid($ctype, $type_id),
            "sponsor_name" => $result->sponsor,
            "sponsor_logo" => $sponsorLogo,
            "play_time" => $result->play_time,
            "comment_count" => $result->count_comment,
            "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
            "myrating" => ($result->myrating != '') ? true : false,
            "vault" => ($result->vault != '') ? $result->vault : 0,
            "deeplink" => ($env == 'GL') ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
            "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,
            "disclaimer" => 'All scientific content on the platform is provided for general medical education purposes meant for registered medical practitioners only. The content is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. In no event will CLIRNET be liable for any decision made or action taken in reliance upon the information provided through this content.',
            "survey" => $vxPoll,
            "session_doctor_id" => $result->session_doctor_id,
            "session_doctor_entities" => $ses_doc_det_array,
        );
        $responses['content_detail'] = $vx;
        return $responses;
        break;
        //add child checking in this sql
        //echo $sql;
        //exit;
        return $vx;
        break;
    case 'comp':
        //MEDWIKI
        if (!empty($env)) {
            if ($env != 'GL') {
                $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
            } else {
                $envStatus = "AND cm.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        $sql = "SELECT
                cm.comp_qa_id as type_id,
                cm.comp_qa_question,
                cm.comp_qa_answer,
                cm.comp_qa_answer_raw,
                cm.comp_qa_question_raw,
                cm.comp_qa_file_img,
                cm.added_on,
                cm.deeplink,
                cm.gl_deeplink,
                cm.comp_qa_tags,
                cm.publication_date,
                cln.client_name,
                cln.client_logo,
                cm.type,
                cm.vendor,
                cm.src,
                cm.start_like,
                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                cm.comp_qa_speciality_id,
                (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = " . $type_id . " and  rt.post_type='comp' and rt.rating!=0)as averageRating,
                rtmy.rating  as myrating,
                (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm
                    where kcm.type_id = " . $type_id . " and kcm.type = 'comp' and kcm.comment_approve_status = '1' )as count_comment,
                kv.status as vault
                FROM knwlg_compendium_V1 as cm
                JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
                JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                LEFT JOIN compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                JOIN client_master as cln ON cln.client_master_id = cm.client_id
                LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.comp_qa_id and  rtmy.post_type='comp' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and  kv.type_text='comp' and  kv.user_id = " . $user_master_id . "
                LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.comp_qa_id and  rt.post_type='comp'
                WHERE cm.status=3
                " . $envStatus . "
                AND date(cm.publication_date)<=CURDATE() and
                cm.comp_qa_id = " . $type_id . "";
        //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
        // echo $sql;
        // exit;
        $query = $CI->db->query($sql);
        $result = $query->row();
        $entities = array();
        $i = 0;
        if ($result->comp_qa_file_img) {
            $img = $result->comp_qa_file_img; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;
        } else {
            $img = '';
        }
        $sponsorLogoArry = explode(",", $result->sponsor_logo);
        if (count($sponsorLogoArry) > 0) {
            foreach ($sponsorLogoArry as $valueSponor) {
                if ($valueSponor) {
                    $sponsorLogomix[] = '' . $valueSponor;
                }
            }
        } else {
            if ($result->sponsor_logo) {
                $sponsorLogomix[] = '' . $result->sponsor_logo;
            }
        }
        if (!empty($sponsorLogomix)) {
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
        }
        unset($sponsorLogomix);
        unset($sponsorLogoArry);
        $string = $result->comp_qa_question_raw;
        $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
        $main_description = "";
        $main_description = str_replace("\n\t", "\n", $result->comp_qa_answer_raw);
        $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);
        if ($result->vendor == "youtube") {
            $vid_src = "https://www.youtube.com/watch?v=" . $result->src;
            $vid_code = $result->src;
        } else {
            $vid_src = "" . $result->src;
            $vid_code = "";
        }
        $responses['content_id'] = $result->type_id;
        $vx = array(
            "type_id" => $result->type_id,
            "con_type" => $result->type,
            "vendor" => $result->vendor,
            "src" => $vid_src,
            "src_code" => $vid_code,
            "type" => 'comp',
            "date" => date(' jS F y', strtotime($result->publication_date)),
            "question" => html_entity_decode($string),
            "answer" => html_entity_decode($main_description), //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),
            "question_htm" => $result->comp_qa_question,
            "answer_htm" => $result->comp_qa_answer, //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),
            "image" => $img,
            "banner_image" => '',
            "banner_url" => '',
            "specialities" => $result->specialities_name,
            "specialities_id" => $result->comp_qa_speciality_id,
            "client_name" => $result->client_name,
            "client_logo" => '' . $result->client_logo,
            "specialities_ids_and_names" => explode_speciality_string($result->specialities_ids_and_names),
            "sponsor_name" => $result->sponsor,
            "sponsor_logo" => $sponsorLogo,
            "comment_count" => $result->count_comment,
            "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
            "myrating" => ($result->myrating != '') ? true : false,
            "vault" => ($result->vault != '') ? $result->vault : 0,
            "deeplink" => ($env == 'GL') ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
            "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,
            "disclaimer" => disclaimer('knowledge'),
            "survey" => array(),
        );
        // if ($from_type) {}
        $responses['content_detail'] = $vx;
        return $responses;
        break;
    case 'gr':
        $sqlInt = "select
            specialities_id
            from
            user_to_interest
            where
            user_master_id = " . $user_master_id . "";
        $queryInt = $CI->db->query($sqlInt);
        $resultInt = $queryInt->result_array();
        $specialities = array();
        foreach ($resultInt as $val) {
            $specialities[] = $val['specialities_id'];
            //$specialities = array_merge($specialities, $val);
        }
        if (count($specialities) > 0) {
            $specialityIds = implode(",", (array)$specialities);
        }
        if ($specialityIds != '') {
            $specialities_query = ' and (' . implode(' OR ', array_map(function ($x) {
                return "FIND_IN_SET('$x', fd.speciality_id)";
            }, explode(',', $specialityIds))) . ')';
        } else {
            $specialities = "";
        }
        //echo $specialities; exit;
        //get user speciality
        if ($client_ids) {
            //$client_list = ' and '. implode(' OR ', array_map(function($x) { return "FIND_IN_SET('$x', kcp.client_id)"; }, explode(',', $CI->session->userdata('client_ids'))));
            $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                return "FIND_IN_SET('$x', fd.client_id)";
            }, explode(',', $client_ids))) . ')';
        }
        // print_r($client_ids);
        // die;
        if (!empty($env)) {
            if ($env != 'GL') {
                $envStatus = "AND (gr.env ='GL' or gr.env ='" . $env . "')";
            } else {
                $envStatus = "AND gr.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        $sql = "SELECT
            gr.gr_id as type_id,
            gr.gr_title as title,
            gr.title_video as title_video,
            gr.gr_description as description,
            gr.gr_chief_scientific_editor ,
            gr.gr_preview_image,
            gr.gr_type,
            gr.gr_video_source,
            gr.vendor,
            gr.start_like,
            gr.live_video,
            gr.hotline_status,
            gr.qn_status,
            gr.start_datetime,
            gr.end_datetime,
            gr.added_on,
            gr.gr_date_of_publication as publish_date,
            gr.deeplink,
            gr.gl_deeplink,
            gr.association_status,
            gr.association_setting,
            cln.client_name,
            cln.client_logo,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
            max( ms.rank) as maxrank,
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id  ORDER BY  grTsdoc.session_doctor_id  ASC ) as session_doctor_id,
            GROUP_CONCAT(DISTINCT grTsdoc.description  ORDER BY  grTsdoc.session_doctor_id  ASC SEPARATOR '----') as gr_doc_description,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
            (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = gr.gr_id and  rt.post_type='gr')as averageRating,
            rtmy.rating  as myrating,
            (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = gr.gr_id and kcm.type = 'gr' and kcm.comment_approve_status = 1)as count_comment,
            kv.status as vault
            FROM knwlg_gr_register as gr
            JOIN gr_to_specialities  as grTs ON grTs.gr_id = gr.gr_id
            JOIN master_specialities_V1 as ms ON ms.master_specialities_id = grTs.specialities_id
            JOIN client_master as cln ON cln.client_master_id = gr.client_id
            LEFT JOIN gr_to_sponsor as grTspon ON grTspon.gr_id = gr.gr_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = grTspon.sponsor_id
            LEFT JOIN gr_to_session_doctor as grTsdoc ON grTsdoc.gr_id = gr.gr_id
            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = gr.gr_id and  rtmy.post_type='gr' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
            LEFT JOIN knwlg_vault as kv ON kv.post_id = gr.gr_id and  kv.type_text='gr' and  kv.user_id = " . $user_master_id . "
            LEFT JOIN knwlg_rating as rt ON rt.post_id = gr.gr_id and  rt.post_type='gr'
            WHERE
            gr.status=3
            " . $envStatus . "
            and date(gr.gr_date_of_publication)<=CURDATE() and
            gr.gr_id = " . $type_id . "";
        // echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        // echo  $sql; exit;
        $query = $CI->db->query($sql);
        //$CI->db->cache_off();
        $val = $query->row();
        //print_r($val); exit;
        //echo $val->gr_preview_image;
        // echo"<pre>";print_r($val); die();
        $vx = array();
        if ($val->gr_preview_image) {
            $gr_logic_image = $val->gr_preview_image;
        } else {
            $gr_logic_image = '';
        }
        $sponsorLogoArry = explode(",", $val->sponsor_logo);
        if (count($sponsorLogoArry) > 0) {
            foreach ($sponsorLogoArry as $valueSponor) {
                if ($valueSponor) {
                    $sponsorLogomix[] = $valueSponor;
                }
            }
        } else {
            if ($val->sponsor_logo) {
                $sponsorLogomix[] = $val->sponsor_logo;
            }
        }
        $sponsorLogo = implode(",", (array)$sponsorLogomix);
        unset($sponsorLogomix);
        unset($sponsorLogoArry);
        $ses_doc_det_array = array();
        if ($val->session_doctor_id) {
            $session_doc_array = explode(",", $val->session_doctor_id);
            $session_gr_doc_description_array = explode("----", $val->gr_doc_description);
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                    $logic_image = $var[0]['profile_image'];
                } else {
                    $logic_image = docimg;
                    //$logic_image = $var[0]['profile_image'];
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $session_gr_doc_description_array[$inc_pp];
                $inc_pp++;
            }
        }
        //print_r($ses_doc_det_array);
        //gr.association_status,
        //gr.association_setting,
        $fileArray = array();
        if ($val->type_id) {
            $fileArray = grFile($val->type_id);
        }
        if ($val->vendor == "youtube") {
            $vid_src = "https://www.youtube.com/watch?v=" . $val->gr_video_source;
            $vid_code = $val->gr_video_source;
        } else {
            $vid_src = "" . $val->gr_video_source;
        }
        $responses['content_id'] = $val->type_id;
        $vx[] = array(
            "type_id" => $val->type_id,
            "type" => 'gr',
            "date" => date(' jS F y', strtotime($val->publish_date)),
            "title" => html_entity_decode(strip_tags($val->title)),
            "title_video" => html_entity_decode(strip_tags($val->title_video)),
            "media_type" => $val->gr_type,
            "image" => $gr_logic_image,
            "video" => $vid_src,
            "src_code" => $vid_code,
            "vendor" => $val->vendor,
            "live_video" => $val->live_video,
            "hotline_status" => $val->hotline_status,
            "qn_status" => $val->qn_status,
            "association_status" => $val->association_status,
            "association_setting" => $val->association_setting,
            "start_datetime" => $val->start_datetime,
            "end_datetime" => $val->end_datetime,
            "description" => html_entity_decode(strip_tags($val->description)),
            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
            "specialities_ids_and_names" => explode_speciality_string($val->specialities_ids_and_names),
            "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
            "client_name" => $val->client_name,
            "client_logo" => $val->client_logo,
            "sponsor_name" => $val->sponsor,
            "sponsor_logo" => $sponsorLogo,
            "session_doctor_entities" => $ses_doc_det_array,
            "gr_files" => $fileArray,
            "comment_count" => $val->count_comment,
            "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : $val->start_like,
            "myrating" => ($val->myrating != '') ? true : false,
            "vault" => ($val->vault != '') ? $val->vault : 0,
            "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
            "disclaimer" => disclaimer('knowledge'),
        );
        //    print_r($vx);
        //    die;
        $responses['content_detail'] = $vx;
        return $responses;
        break;
    case 'session':
        if (!empty($env)) {
            if ($env != 'GL') {
                $envStatus = "AND (ks.env ='GL' or ks.env ='" . $env . "')";
            } else {
                $envStatus = "AND ks.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
            return "FIND_IN_SET('$x', ks.client_id)";
        }, explode(',', $client_ids))) . ')';
        $sql = "SELECT
                    ks.session_id,
                    ks.session_doctor_id,
                    ks.session_topic,
                    ks.session_description,
                    ks.sessions_question,
                    ks.master_tag_ids,
                    ks.client_id,
                    ks.sponsor_id,
                    ks.user_group_id,
                    ks.category_id,
                    ks.start_datetime,
                    ks.end_datetime,
                    ks.speciality_id,
                    ks.total_seats,
                    ks.total_buffer,
                    ks.add_question_buffer_days,
                    ks.session_link,
                    ks.master_conf_provider_id,
                    ks.session_access_code,
                    ks.deeplink,
                    ks.in_deeplink,
                    ks.gl_deeplink,
                    ks.template_id,
                    ks.cert_template_id,
                    ks.display_in_dashboard,
                    ks.conf_phone_no,
                    ks.privacy_status,
                    ks.color,
                    ks.added_on,
                    ks.added_by,
                    ks.session_status,
                    ks.cover_image,
                    ks.modified_on,
                    ks.modified_by,
                    ks.is_recommended,
                    ks.is_multiday_session,
                    ks.break_json,
                    ks.status,
                    ks.is_featured,
                    ks.rating_flag,
                    ks.remarks,
                    ks.crm_id,
                    ks.img_credits,
                    ks.session_json,
                    ks.certified,
                    ks.env,
                    ks.notification_template,
                    ks.shortlink,
                    ks.invitefile,
                    ks.exitroute,
                    ks.is_share,
                    ks.is_like,
                    ks.is_comment,
                    ksp.participant_id,
                    ksp.room_link,
                    cln.client_name,
                    cln.client_logo,
                    msct.category_name,
                    msct.category_logo,
                    sd.knwlg_sessions_docs_id,
                    sd.document_path,
                    sd.comment,
                    sd.added_on,
                    sd.added_by,
                    sd.modified_on,
                    sd.modified_by,
                    sd.updated_at,
                    sd.updated_by,
                    sd.status,
                    mst.status_name,
                    kv.meta_data,
                    ksv.vendor_id,
                    ksv.video_embed_src,
                    ksv.room_id,
                    ksv.vouchpro_url,
                    ksv.go_to_meeting_url,
                    ksv.landing_page_url,
                    ksv.session_cast_type,
                    (ks.total_buffer + ks.total_seats) AS tot_seat,
                    -- Aggregated fields
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                    GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
                    GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
                    GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') AS speciality,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
                    GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
                    GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images,
                    GROUP_CONCAT(ksp.participant_id) AS PartName,
                    GROUP_CONCAT(DISTINCT ksp.participant_id) AS users,
                    GROUP_CONCAT(ksp.is_attended) AS IS_ATTENDED
                FROM knwlg_sessions_V1 AS ks
                LEFT JOIN master_specialities_V1 AS ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
                LEFT JOIN client_master AS cln ON cln.client_master_id = ks.client_id
                LEFT JOIN session_to_sponsor AS sTspon ON sTspon.session_id = ks.session_id
                LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                LEFT JOIN knwlg_sessions_vendor AS ksv ON ksv.session_id = ks.session_id
                LEFT JOIN master_vendor AS kv ON kv.vendor_id = ksv.vendor_id
                LEFT JOIN master_session_category AS msct ON msct.mastersession_category_id = ks.category_id
                LEFT JOIN knwlg_sessions_doctors AS sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                LEFT JOIN knwlg_sessions_documents AS sd ON sd.knwlg_sessions_id = ks.session_id
                LEFT JOIN knwlg_sessions_participant AS ksp ON ksp.knwlg_sessions_id = ks.session_id
                LEFT JOIN master_session_status AS mst ON mst.master_session_status_id = ks.session_status
                WHERE
                    ks.status = 3
                    AND ks.session_status IN (1, 2, 3, 4)
                    " . $envStatus . "
                    AND ks.session_id = " . $type_id . " " . $client_list . "
                GROUP BY
                    ks.session_id,
                    ks.session_doctor_id,
                    ks.session_topic,
                    ks.session_description,
                    ks.sessions_question,
                    ks.master_tag_ids,
                    ks.client_id,
                    ks.sponsor_id,
                    ks.user_group_id,
                    ks.category_id,
                    ks.start_datetime,
                    ks.end_datetime,
                    ks.speciality_id,
                    ks.total_seats,
                    ks.total_buffer,
                    ks.add_question_buffer_days,
                    ks.session_link,
                    ks.master_conf_provider_id,
                    ks.session_access_code,
                    ks.deeplink,
                    ks.in_deeplink,
                    ks.gl_deeplink,
                    ks.template_id,
                    ks.cert_template_id,
                    ks.display_in_dashboard,
                    ks.conf_phone_no,
                    ks.privacy_status,
                    ks.color,
                    ks.added_on,
                    ks.added_by,
                    ks.session_status,
                    ks.cover_image,
                    ks.modified_on,
                    ks.modified_by,
                    ks.is_recommended,
                    ks.is_multiday_session,
                    ks.break_json,
                    ks.status,
                    ks.is_featured,
                    ks.rating_flag,
                    ks.remarks,
                    ks.crm_id,
                    ks.img_credits,
                    ks.session_json,
                    ks.certified,
                    ks.env,
                    ks.notification_template,
                    ks.shortlink,
                    ks.invitefile,
                    ks.exitroute,
                    ks.is_share,
                    ks.is_like,
                    ks.is_comment,
                    ksp.participant_id,
                    ksp.room_link,
                    cln.client_name,
                    cln.client_logo,
                    msct.category_name,
                    msct.category_logo,
                    sd.knwlg_sessions_docs_id,
                    sd.document_path,
                    sd.comment,
                    sd.added_on,
                    sd.added_by,
                    sd.modified_on,
                    sd.modified_by,
                    sd.updated_at,
                    sd.updated_by,
                    sd.status,
                    mst.status_name,
                    kv.meta_data,
                    ksv.vendor_id,
                    ksv.video_embed_src,
                    ksv.room_id,
                    ksv.vouchpro_url,
                    ksv.go_to_meeting_url,
                    ksv.landing_page_url,
                    ksv.session_cast_type
                ORDER BY ks.start_datetime DESC";
        $query = $CI->db->query($sql);
        // echo $sql;exit;//" . $group_list . "
        //AND (ks.session_status=1 OR ks.session_status=4)
        //echo $CI->db->last_query(); exit();
        $result = $query->result_array();
        //echo count($result); exit();
        // print_r($result); exit();
        // die;
        //banner old start
        //banner old end
        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            $sql_check = "SELECT
            knwlg_sessions_participant_details.question,
            knwlg_sessions_participant_details.upload_documents,
            knwlg_sessions_participant.knwlg_sessions_participant_id,
            knwlg_sessions_participant.room_link
            FROM knwlg_sessions_participant
            LEFT JOIN knwlg_sessions_participant_details ON knwlg_sessions_participant_details.sessions_participant_id=knwlg_sessions_participant.knwlg_sessions_participant_id
            WHERE
            knwlg_sessions_participant.status=3
            AND knwlg_sessions_participant.participant_type='member'
            AND knwlg_sessions_participant.knwlg_sessions_id=" . $row['session_id'] . "
            AND participant_id=" . $user_master_id . " ";
            $query_check = $CI->db->query($sql_check);
            //echo $CI->db->last_query(); exit();
            $result_check = $query_check->row_array();
            // print_r($result_check); exit();
            if (!empty($result_check)) {
                // echo $result_check['knwlg_sessions_participant_id']; exit();
                //$result_check['knwlg_sessions_participant_id']
                $entities[$i]['is_booked'] = true;
                $entities[$i]['room_link'] = $result_check['room_link'];
                $entities[$i]['asked_query'] = $result_check['question'];
                $entities[$i]['upload_documents'] = $result_check['upload_documents'];
                $entities[$i]['my_participant_id'] = $result_check['knwlg_sessions_participant_id'];
            } else {
                $entities[$i]['is_booked'] = false;
                $entities[$i]['room_link'] = '';
                $entities[$i]['asked_query'] = "";
                $entities[$i]['my_participant_id'] = "";
            }
            $entities[$i]['last_join_date'] = '';
            $entities[$i]['recorded_video_id'] = '';
            $entities[$i]['channel_details'] = [];
            $sql_check_rating = "SELECT
            *
            FROM knwlg_session_rating_reviews
            WHERE session_id=" . $row['session_id'] . "
            AND user_master_id=" . $user_master_id . "";
            $query_check_rating = $CI->db->query($sql_check_rating);
            //echo $CI->db->last_query(); exit();
            $result_check_rating_array = $query_check_rating->row_array();
            // print_r($result_check); exit();
            if (!empty($result_check_rating_array)) {
                // echo $result_check['knwlg_sessions_participant_id']; exit();
                //$result_check['knwlg_sessions_participant_id']
                $entities[$i]['review'] = $result_check_rating_array['review'];
                $entities[$i]['rating'] = $result_check_rating_array['rating'];
                $entities[$i]['is_rating_review'] = true;
            } else {
                $entities[$i]['is_rating_review'] = false;
            }
            $sql_check_recording = "SELECT
            *
            FROM knwlg_session_recording_request
            WHERE session_id=" . $row['session_id'] . "
            AND user_master_id=" . $user_master_id . "";
            $query_check_recording = $CI->db->query($sql_check_recording);
            //echo $CI->db->last_query(); exit();
            $result_check_recording_array = $query_check_recording->row_array();
            // print_r($result_check); exit();
            if (!empty($result_check_recording_array)) {
                // echo $result_check['knwlg_sessions_participant_id']; exit();
                //$result_check['knwlg_sessions_participant_id']
                $entities[$i]['recording_type'] = $result_check_recording_array['recording_type'];
                $entities[$i]['is_sent_recording'] = true;
            } else {
                $entities[$i]['is_sent_recording'] = false;
            }
            $sqlCompl = "SELECT
            sv.*
            FROM
            survey_user_answer sv
            WHERE
            sv.user_master_id = '" . $user_master_id . "'";
            $queryCompl = $CI->db->query($sqlCompl);
            $resultCompl = $queryCompl->result();
            $complID = array();
            foreach ($resultCompl as $valCompl) {
                $complID[] = $valCompl->survey_id;
            }
            //print_r($complID); exit;
            $sqlInCompl = "SELECT
            sv.*
            FROM
            survey_user_incomplete_answer sv
            WHERE
            sv.status = 3
            and
            sv.user_master_id = '" . $user_master_id . "'";
            $queryInCompl = $CI->db->query($sqlInCompl);
            $resultInCompl = $queryInCompl->result();
            $incomplID = array();
            foreach ($resultInCompl as $valInCompl) {
                $incomplID[] = $valInCompl->survey_id;
            }
            $arrayFinal = array_unique(array_merge($complID, $incomplID));
            //print_r($arrayFinal); exit;
            $complIDStr = implode(",", array_filter($arrayFinal));
            //echo $complIDStr ; exit;
            if ($complIDStr) {
                $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
            } else {
                $qryStr = '';
            }
            $sqlPoll = "SELECT
            sv.* ,
            svd.data,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
            cln.client_name,
            cln.client_logo,
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
            FROM
            survey sv
            left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
            left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
            JOIN client_master as cln ON cln.client_master_id = sv.client_id
            LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
            JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
            JOIN survey_to_session as stm ON stm.survey_id = sv.survey_id
            left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
            WHERE
            sv.status = 3
            and date(sv.publishing_date)<=CURDATE() and
            stm.session_id = " . $row['session_id'] . "
            " . $qryStr . " ";
            $queryPoll = $CI->db->query($sqlPoll);
            $resultPoll = $queryPoll->result();
            //echo $sqlPoll;
            // print_r($resultPoll);
            $vxPoll = array();
            foreach ($resultPoll as $valSurvey) {
                $dataArry = unserialize($valSurvey->data);
                $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                $str = preg_replace('/\\\"/', "\"", $json);
                $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);
                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {
                    if ($valSurvey->sponsor_logo) {
                        $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                if ($valSurvey->survey_id) {
                    $vxPoll[] = array(
                        "survey_id" => $valSurvey->survey_id,
                        "category" => $valSurvey->category,
                        "point" => $valSurvey->survey_points,
                        "json_data" => $str,
                        "survey_title" => $valSurvey->survey_title,
                        "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0), //$valSurvey->deeplink,
                        "survey_description" => substr($valSurvey->survey_description, 0, 150),
                        "image" => $valSurvey->image,
                        "specialities_name" => $valSurvey->specialities_name,
                        "specialities_ids_and_names" => explode_speciality_string($valSurvey->specialities_ids_and_names),
                        "client_name" => $valSurvey->client_name,
                        "client_logo" => '' . $valSurvey->client_logo,
                        "sponsor_name" => $valSurvey->sponsor,
                        "sponsor_logo" => $sponsorLogo,
                        "publishing_date" => $valSurvey->publishing_date,
                    );
                }
            }
            // $entities[$i]['my_participant_id'] = $row['participant_id'];
            $entities[$i]['is_available'] = (strtotime($row['start_datetime']) < time()) ? false : true;
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $coverImg = base_url() . "uploads/sessionBgDefault.jpeg";
            $cov_img = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
            $tempcover = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
            $coverimageArry = explode(",", $tempcover);
            $responses['content_id'] = $row['session_topic'];
            $entities[$i]['cover_image'] = $coverimageArry;
            //$entities[$i]['cover_image'] = $row['cover_image'];
            $entities[$i]['session_topic'] = $row['session_topic'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['speciality_id'] = $row['speciality_id'];
            $entities[$i]['session_description'] = strip_tags($row['session_description']);
            $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            $entities[$i]['client_id'] = $row['client_id'];
            $entities[$i]['vendor_meta_data'] = $row['meta_data'];
            $entities[$i]['client_name'] = $row['client_name'];
            $entities[$i]['status_name'] = $row['status_name'];
            $entities[$i]['vendor_id'] = $row['vendor_id'];
            $entities[$i]['room_id'] = $row['room_id'];
            $entities[$i]['vouchpro_url'] = $row['vouchpro_url'];
            $entities[$i]['go_to_meeting_url'] = $row['go_to_meeting_url'];
            $entities[$i]['landing_page_url'] = $row['landing_page_url'];
            $entities[$i]['video_embed_src'] = $row['video_embed_src'];
            $entities[$i]['session_cast_type'] = $row['session_cast_type'];
            $sponserentity = array();
            $sponsorLogoArry = explode(",", $row['sponsor']);
            $sponsorNameArry = explode(",", $row['sponsor_logo']);
            $ii = 0;
            foreach ($sponsorLogoArry as $singlelogo) {
                if ($singlelogo != "") {
                    $sponserentity[$ii]['sponsor_name'] = $singlelogo;
                    $sponserentity[$ii]['sponsor_logo'] = $sponsorNameArry[$ii];
                }
                $ii++;
            }
            $entities[$i]['sponsor_entity'] = $sponserentity;
            /**
             * new sponsor logic
             */
            $sponsorLogoArry = explode(",", $row['sponsor_logo']);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = $valueSponor;
                    }
                }
            } else {
                if ($row['sponsor_logo']) {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = $sponsorLogo;
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($row['document_path'] != "" || $row['document_path'] != null) {
                $entities[$i]['file_size'] = round((filesize('./uploads/mastersession_docs/' . $row['document_path'] . '') / 1024)) . "Kb";
                $entities[$i]['document_path_exact_file_name'] = $row['document_path'];
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
                $entities[$i]['extension_logo_path'] = base_url() . "themes/front/images/" . get_logo_by_file_extension($row['document_path']);
            } else {
                $entities[$i]['document_path_exact_file_name'] = "";
                $entities[$i]['document_path'] = "";
                $entities[$i]['file_size'] = "";
                $entities[$i]['extension_logo_path'] = "";
            }
            if ($row['comment'] != "" || $row['comment'] != null) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['category_name'] = $row['category_name'];
            $entities[$i]['category_image'] = base_url() . "/themes/front/images/session/" . $row['category_logo'];
            $entities[$i]['start_datetime'] = $row['start_datetime'];
            $entities[$i]['start_datetimex'] = strtotime($row['start_datetime']);
            $entities[$i]['now_datetimex'] = time();
            $start_time = $row['start_datetime'];
            $date = new DateTime($start_time);
            //$start_time = date("g:i A", strtotime($start_time));
            $now = new DateTime();
            $diff = date_diff($date, $now);
            $entities[$i]['days_remaining'] = abs($diff->format("%R%a")) + 1;
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_time_format'] = $start_time . "-" . $end_time;
            $post_time = $row['start_datetime'];
            $phpdate = strtotime($post_time);
            $mysqldate = date('D, j M `y  ', $phpdate);
            $entities[$i]['display_date_format'] = $mysqldate;
            $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            $post_date = $row['added_on'];
            $start_date = $row['start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            $entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $CI->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            $entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }
            $entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }
            $cpt_flag = 0;
            $on_of_booking_button = 0;
            $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
            if ($row['users'] == null) {
                $total_original_booking = 0;
            } else {
                $users_array = array();
                $users_array = explode(",", $row['users']);
                $total_original_booking = count($users_array);
            }
            if ($total_original_booking < $row['total_seats']) {
                $total_booking = $total_original_booking;
            }
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $minus_flag = $total_after_buffer - $total_original_booking;
                $total_booking = ($row['total_seats']) - 1;
            }
            if ($total_original_booking >= ($total_after_buffer)) {
                $total_booking = $row['total_seats'];
                $on_of_booking_button = 1;
                $cpt_flag = 1;
            }
            if ($total_booking > 0) {
                $available_percent = ($total_booking / $row['total_seats']) * 100;
            } else {
                $available_percent = 0;
            }
            $available_percent = round($available_percent);
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $total_booking;
            } else {
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
            }
            $perc = $available_percent;
            if ($cpt_flag == 0) {
                $entities[$i]['percentage'] = ceil($perc);
            } else {
                $entities[$i]['percentage'] = ceil($perc);
            }
            $color = get_progress_color($perc);
            $entities[$i]['color_profress_bar'] = $color;
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['start_datetime_old'] = $row['start_datetime']; //date('Y-m-d h:i:s', strtotime($row['start_datetime']));
            $entities[$i]['end_datetime_old'] = $row['end_datetime'];
            $entities[$i]['start_datetime'] = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $entities[$i]['end_datetime'] = (new DateTime($row['end_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            $entities[$i]['deeplink'] = ($env == 'GL') ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0); //$row['deeplink'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['category_image'] = base_url() . "/themes/front/images/session/" . $row['category_logo'];
            $entities[$i]['client_logo'] = base_url() . "uploads/logo/" . $row['client_logo'];
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = $row['category_logo'];
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            $session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                //print_r($var);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if ($image) {
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image = docimg; //$imgPr;
                    }
                } else {
                    $logic_image = docimg;
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $ses_doc_det_array[$inc_pp]['description'] = $var[0]['description'];
                $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                $inc_pp++;
            }
            $entities[$i]['sponsor_id'] = $row['sponsor_id'];
            $sponsor_array = explode(",", $row['sponsor_id']);
            $sponsor_det_array = array();
            if (count($sponsor_array) > 1) {
                $inc_spp = 0;
                foreach ($sponsor_array as $single_sponsor) {
                    $var = sponsor_detail($single_sponsor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);
                    if (@getimagesize(base_url() . "uploads/logo/" . $image)) {
                        $logic_image = base_url() . "uploads/logo/" . $image;
                    } else {
                        $logic_image = base_url() . "uploads/docimg/MConsult.png";
                    }
                    $sponsor_det_array[$inc_spp]['sponsor_name'] = $var[0]['client_name'];
                    $sponsor_det_array[$inc_spp]['sponsor_logo'] = $logic_image;
                    $sponsor_det_array[$inc_spp]['sponsor_id'] = $var[0]['client_master_id'];
                    $inc_spp++;
                }
            } else {
                if ($row['sponsor_id']) {
                    $var = sponsor_detail($row['sponsor_id']);
                    $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);
                    if (@getimagesize(base_url() . "uploads/logo/" . $image)) {
                        $logic_image = base_url() . "uploads/logo/" . $image;
                    } else {
                        $logic_image = base_url() . "uploads/docimg/MConsult.png";
                    }
                    $sponsor_det_array['sponsor_name'] = $var[0]['client_name'];
                    $sponsor_det_array['sponsor_logo'] = $logic_image;
                    $sponsor_det_array['sponsor_id'] = $var[0]['client_master_id'];
                }
            }
            //$row['sessions_question'];
            if ($row['sessions_question'] != '') {
                $qu_val = explode("#", $row['sessions_question']);
                $queries = $qu_val;
            } else {
                $queries = array();
            }
            //$queries = array('I want to know how this system will work', 'I want to know the main topic', 'I want to know when we can start', 'I want to know when we can start', 'I want to know when we can start');
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            //$entities[$i]['sponsor_entities'] = $sponsor_det_array;
            $entities[$i]['sponsor_entities'] = $sponserentity;
            $entities[$i]['session_queries'] = $queries;
            $entities[$i]['cpddetail'] = getcpddetails($row['session_id']);
            $entities[$i]['survey'] = $vxPoll;
            $entities[$i]['disclaimer'] = 'The information in this educational activity is provided for general medical education purposes meant for registered medical practitioners only. The activity is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. The viewpoints expressed in this CME activity are those of the authors/faculty. They do not represent an endorsement by CLIRNET or the Sponsor. In no event will CLIRNET, the Sponsor or, the authors/faculty be liable for any decision made or action taken in reliance upon the information provided through this CME activity. All CMEs are recorded to be used for research & information purposes only. Clirnet at the request of Sponsor, may share your details such as name, location, recording of the session and session feedback for information purposes only.';
            $i++;
        }
        $responses['content_detail'] = $entities;
        return $responses;
        break;
    case 'epub':
        if (!empty($env)) {
            if ($env != 'GL') {
                $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
            } else {
                $envStatus = "AND cm.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        $sql = "SELECT
            cm.epub_id as type_id,
            cm.epub_description as description,
            cm.epub_title as title,
            cm.epub_img,
            cm.epub_img_thumbnail,
            cm.epub_file,
            cm.start_like,
            cm.added_on,
            cm.publication_date as publish_date,
            cln.client_name,
            cln.client_logo,
            cm.deeplink,
            cm.gl_deeplink,
            cm.color,
            cm.author,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
            (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.epub_id and  rt.post_type='epub')as averageRating,
            rtmy.rating  as myrating,
            (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.epub_id and kcm.type = 'epub')as count_comment,
            kv.status as vault
            FROM epub_master as cm
            JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
            JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
            JOIN client_master as cln ON cln.client_master_id = cm.client_id
            LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
            LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type_text='epub' and  kv.user_id = " . $user_master_id . "
            LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'
            WHERE
            cm.status=3
            " . $envStatus . "
            AND
            cm.epub_id = " . $type_id . "";
        //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
        //echo $sql; exit;
        $query = $CI->db->query($sql);
        $result = $query->row();
        //poll start
        $sqlCompl = "SELECT
            sv.*
            FROM
            survey_user_answer sv
            WHERE
            sv.user_master_id = '" . $user_master_id . "'";
        $queryCompl = $CI->db->query($sqlCompl);
        $resultCompl = $queryCompl->result();
        $complID = array();
        foreach ($resultCompl as $valCompl) {
            $complID[] = $valCompl->survey_id;
        }
        //print_r($complID); exit;
        //----------------------------------------------------------------
        $sqlAuther = "SELECT * FROM epub_to_author WHERE epub_id = $type_id ";
        $queryAuther = $CI->db->query($sqlAuther);
        $resultAuther = $queryAuther->result();
        $autherArray = array();
        $autherIndex = 0;
        foreach ($resultAuther as $rw) {
            $autherArray[$autherIndex] = array();
            $autherArray[$autherIndex]['author_name'] = $rw->author_name;
            $autherArray[$autherIndex]['author_image'] = $rw->author_image;
            $autherArray[$autherIndex]['author_description'] = $rw->author_description;
            $autherIndex++;
        }
        //print_r($complID); exit;
        //----------------------------------------------------------------
        $sqlInCompl = "SELECT
            sv.*
            FROM
            survey_user_incomplete_answer sv
            WHERE
            sv.status = 3
            and
            sv.user_master_id = '" . $user_master_id . "'";
        $queryInCompl = $CI->db->query($sqlInCompl);
        $resultInCompl = $queryInCompl->result();
        $incomplID = array();
        foreach ($resultInCompl as $valInCompl) {
            $incomplID[] = $valInCompl->survey_id;
        }
        $arrayFinal = array_unique(array_merge($complID, $incomplID));
        //print_r($arrayFinal); exit;
        $complIDStr = implode(",", array_filter($arrayFinal));
        //echo $complIDStr ; exit;
        if ($complIDStr) {
            $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
        } else {
            $qryStr = '';
        }
        $sqlPoll = "SELECT
            sv.* ,
            svd.data,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            cln.client_name,
            cln.client_logo,
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
            FROM
            survey sv
            left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
            left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
            JOIN client_master as cln ON cln.client_master_id = sv.client_id
            LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
            JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
            JOIN survey_to_medwiki as stm ON stm.survey_id = sv.survey_id
            left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
            WHERE
            sv.status = 3
            and date(sv.publishing_date) <= CURDATE() and
            stm.medwiki_id = " . $type_id . "
            " . $qryStr . " ";
        $queryPoll = $CI->db->query($sqlPoll);
        $resultPoll = $queryPoll->result();
        //echo $sqlPoll;
        // print_r($resultPoll);
        $vxPoll = array();
        foreach ($resultPoll as $valSurvey) {
            $dataArry = unserialize($valSurvey->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);
            $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($valSurvey->sponsor_logo) {
                    $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($valSurvey->survey_id) {
                $vxPoll[] = array(
                    "survey_id" => $valSurvey->survey_id,
                    "category" => $valSurvey->category,
                    "point" => $valSurvey->survey_points,
                    "json_data" => $str,
                    "survey_title" => $valSurvey->survey_title,
                    "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0), //$valSurvey->deeplink,
                    "survey_description" => substr($valSurvey->survey_description, 0, 150),
                    "image" => $valSurvey->image,
                    "specialities_name" => $valSurvey->specialities_name,
                    "client_name" => $valSurvey->client_name,
                    "client_logo" => '' . $valSurvey->client_logo,
                    "sponsor_name" => $valSurvey->sponsor,
                    "sponsor_logo" => $sponsorLogo,
                    "publishing_date" => $valSurvey->publishing_date,
                );
            }
        }
        //print_r($vxPoll);
        //poll end
        if ($result->epub_img_thumbnail) {
            $img = $result->epub_img_thumbnail; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;
        } else {
            $img = '';
        }
        $sponsorLogoArry = explode(",", $result->sponsor_logo);
        if (count($sponsorLogoArry) > 0) {
            foreach ($sponsorLogoArry as $valueSponor) {
                if ($valueSponor) {
                    $sponsorLogomix[] = '' . $valueSponor;
                }
            }
        } else {
            if ($result->sponsor_logo) {
                $sponsorLogomix[] = '' . $result->sponsor_logo;
            }
        }
        $sponsorLogo = implode(",", (array)$sponsorLogomix);
        unset($sponsorLogomix);
        unset($sponsorLogoArry);
        $string = $result->comp_qa_question_raw;
        $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
        $main_description = "";
        $main_description = str_replace("\n\t", "\n", $result->comp_qa_answer_raw);
        $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);
        $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
        $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
        $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
        $responses['content_id'] = $result->type_id;
        $vx = array(
            "type_id" => $result->type_id,
            "type" => 'epub',
            "date" => date(' jS F y', strtotime($result->publication_date)),
            "title" => html_entity_decode($result->title),
            "description" => html_entity_decode($result->description), //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),
            "epub_file" => $result->epub_file,
            "author" => $result->author,
            "image" => $img,
            "author_entities" => $autherArray,
            "specialities" => $result->specialities_name,
            "specialities_id" => $result->comp_qa_speciality_id,
            "client_name" => $result->client_name,
            "client_logo" => '' . $result->client_logo,
            "sponsor_name" => $result->sponsor,
            "sponsor_logo" => $sponsorLogo,
            "start_like" => $result->start_like,
            "comment_count" => $result->count_comment,
            "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
            "myrating" => ($result->myrating != '') ? true : false,
            "vault" => ($result->vault != '') ? $result->vault : 0,
            "deeplink" => ($env == 'GL') ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
            "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,
            //  "disclaimer" => 'All scientific content on the platform is provided for general medical education purposes meant for registered medical practitioners only. The content is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. In no event will CLIRNET be liable for any decision made or action taken in reliance upon the information provided through this content.',
            "disclaimer" => disclaimer('knowledge'),
            "survey" => $vxPoll,
        );
        // if ($from_type) {}
        //add child checking in this sql
        //echo $sql;
        //exit;
        $responses['content_detail'] = $vx;
        return $responses;
        break;
    case 'survey':
        $sqlCompl = "SELECT
        sv.*
        FROM
        survey_user_answer sv
        WHERE
        sv.user_master_id = '" . $user_master_id . "'";
        $queryCompl = $CI->db->query($sqlCompl);
        $resultCompl = $queryCompl->result();
        $complID = array();
        foreach ($resultCompl as $valCompl) {
            $complID[] = $valCompl->survey_id;
        }
        //print_r($complID); exit;
        $sqlInCompl = "SELECT
        sv.*
        FROM
        survey_user_incomplete_answer sv
        WHERE
        sv.status = 3
        and
        sv.user_master_id = '" . $user_master_id . "'";
        // echo $sqlInCompl; exit;
        $queryInCompl = $CI->db->query($sqlInCompl);
        $resultInCompl = $queryInCompl->result();
        $incomplID = array();
        foreach ($resultInCompl as $valInCompl) {
            $incomplID[] = $valInCompl->survey_id;
        }
        // print_r($incomplID); exit;
        $arrayFinal = array_filter(array_unique(array_merge($complID, $incomplID)));
        //print_r($arrayFinal); exit;
        if (!in_array($type_id, $arrayFinal)) {
            if (!empty($env)) {
                if ($env != 'GL') {
                    $envStatus = "AND (sv.env ='GL' or sv.env ='" . $env . "')";
                } else {
                    $envStatus = "AND sv.env ='" . $env . "'";
                }
            } else {
                $envStatus = "";
            }
            $sql = "SELECT
                        sv.* ,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                        cln.client_name,
                        cln.client_logo,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                        sv.deeplink,
                        sv.gl_deeplink,
                        sd.data
                        FROM
                        survey sv
                        left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
                        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                        JOIN client_master as cln ON cln.client_master_id = sv.client_id
                        LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
                        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                        JOIN survey_detail as sd ON sd.survey_id = sv.survey_id
                        WHERE
                        sv.status = 3
                        " . $envStatus . "
                        and date(sv.publishing_date) <= CURDATE() and
                        sv.survey_id = " . $type_id . " ";
            //echo $sql; exit;
            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;
            $query = $CI->db->query($sql);
            //$CI->db->cache_off();
            $val = $query->row();
            $dataArry = unserialize($val->data);
            //remove correct answer from the json
            foreach ($dataArry as $key => $value) {
                foreach ($value as $key2 => $value2) {
                    unset($dataArry[$key][$key2]['correctoption']);
                    //print_r($value2);
                }
            }
            //remove correct answer from the json
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);
            //echo stripslashes($json);
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $responses['content_id'] = $val->survey_id;
            $vx[] = array(
                "survey_id" => $val->survey_id,
                "category" => $val->category,
                "survey_title" => $val->survey_title,
                "survey_description" => $val->survey_description,
                "survey_points" => $val->survey_points,
                "point" => $val->survey_points,
                "survey_time" => $val->survey_time,
                "question_count" => $val->question_count,
                "image" => $val->image,
                "specialities_name" => $val->specialities_name,
                "specialities_ids_and_names" => explode_speciality_string($val->specialities_ids_and_names),
                "client_name" => $val->client_name,
                "client_logo" => base_url('uploads/logo/') . $val->client_logo,
                "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "publishing_date" => $val->publishing_date,
                "data_json" => $str,
                'disclaimer' => "The Sponsor of this Survey is solely responsible for its content. The purpose of this survey is for research & information only and is meant for participation by registered medical practitioners only. Your participation in this survey is voluntary. The information given and results expressed in this activity are those of the participants and not that of CLIRNET or the Sponsor. The information given and results do not represent and cannot be construed as an  endorsement by CLIRNET or the Sponsor. CLIRNET at the request of the Survey Sponsor, may share your personal details such as name, location and survey results with the Sponsor for information purposes only. If you wish to not share your personal information with the Sponsor, then please do not respond to the survey. You may refer to our Privacy Policy for further details. CLIRNET reserves the right to terminate or withdraw a survey, or your opportunity to participate in the survey, at any time for any reason.", //'The information in this educational activity is provided for general medical education purposes meant for registered medical practitioners only. The activity is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. The viewpoints expressed in this CME activity are those of the authors/faculty. They do not represent an endorsement by CLIRNET or the Sponsor. In no event will CLIRNET, the Sponsor or, the authors/faculty be liable for any decision made or action taken in reliance upon the information provided through this CME activity. All CMEs are recorded to be used for research & information purposes only. Clirnet at the request of Sponsor, may share your details such as name, location, recording of the session and session feedback for information purposes only.',
                'channel' => getchannelid($ctype, $type_id)
            );
            $responses['content_detail'] = $vx;
            // $compArry = getcomp(155,0,2,'survey',$type_id);
            // $spqArry = getspq(155,0,2,'survey',$type_id);
            // $grArry = getgr(155,0,2,'survey',$type_id);
            // $sessionpArry = getsession(155,0,2,'survey',$type_id);
            // $vidArry = getvid(155,0,3,'survey',$type_id);
            // $finalArry = array_merge_recursive($sessionpArry,$compArry,$spqArry,$grArry,$vidArry);
            //print_r($finalArry); exit;
            $final['survey'] = $vx;
            // $final['related'] = $finalArry;
            // return $final;
        } else {
            $final['survey'] = array();
        }
        //print_r($final); exit;
        return $responses;
        break;
    case 'channel':
        $sqlInt = "select
            count(channel_master_id) as total
            from
            channel_to_user
            where
            status = 3
            and
            channel_master_id = " . $type_id . "";
        $queryInt = $CI->db->query($sqlInt);
        $resultInt = $queryInt->row();
        //echo $specialities; exit;
        //get user speciality
        if ($client_ids) {
            //$client_list = ' and '. implode(' OR ', array_map(function($x) { return "FIND_IN_SET('$x', kcp.client_id)"; }, explode(',', $CI->session->userdata('client_ids'))));
            $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                return "FIND_IN_SET('$x', fd.client_id)";
            }, explode(',', $client_ids))) . ')';
        }
        if (!empty($env)) {
            if ($env != 'GL') {
                $envStatus = "AND (ch.env ='GL' or ch.env ='" . $env . "')";
            } else {
                $envStatus = "AND ch.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        $sql = "SELECT
            ch.channel_master_id as type_id,
            ch.title as title,
            ch.description as description,
            ch.short_description as short_description,
            ch.membershipformurl,
            cTus.status as followed_status,
            ch.cover_image,
            ch.privacy_status,
            ch.logo,
            ch.address,
            ch.added_on,
            ch.deeplink,
            ch.gl_deeplink,
            ch.featured_video,
            bnd.logo,
            cln.client_name,
            cln.client_logo,
            GROUP_CONCAT(DISTINCT chTsdoc.session_doctor_id  ORDER BY  chTsdoc.session_doctor_id  ASC ) as session_doctor_id,
            GROUP_CONCAT(DISTINCT chTsdoc.description  ORDER BY  chTsdoc.session_doctor_id  ASC SEPARATOR '----') as ch_doc_description
            FROM channel_master as ch
            LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = ch.channel_master_id
            LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
            LEFT JOIN client_master as cln ON cln.client_master_id = ch.client_id
            LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = ch.brand_id
            LEFT JOIN channel_to_session_doctor as chTsdoc ON chTsdoc.channel_master_id = ch.channel_master_id
            LEFT JOIN channel_to_user  as cTus ON (cTus.channel_master_id = ch.channel_master_id and user_master_id = " . $user_master_id . "   )
            WHERE
            ch.status=3
            " . $envStatus . "
            and
            ch.channel_master_id = " . $type_id . "";
        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $CI->db->query($sql);
        //$CI->db->cache_off();
        $val = $query->row();
        //print_r($result); exit;
        //echo $val->gr_preview_image;
        $ses_doc_det_array = array();
        if ($val->session_doctor_id) {
            $session_doc_array = explode(",", $val->session_doctor_id);
            $session_gr_doc_description_array = explode("----", $val->ch_doc_description);
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                if ($session_gr_doc_description_array[$inc_pp]) {
                    $gr_doc_descriptionLa = $session_gr_doc_description_array[$inc_pp];
                } else {
                    $gr_doc_descriptionLa = '';
                }
                //print_r($var);
                if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                    $logic_image = $var[0]['profile_image'];
                } else {
                    $logic_image = docimg;
                    //$logic_image = $var[0]['profile_image'];
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                // $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $var[0]['profile'];//$var[0]['description']; //$session_gr_doc_description_array[$inc_pp];
                $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $gr_doc_descriptionLa; //$session_gr_doc_description_array[$inc_pp];
                $inc_pp++;
            }
        }
        $vx = array();
        $responses['content_id'] = $val->type_id;
        $vx[] = array(
            "type_id" => $val->type_id,
            "type" => 'channel',
            "added_on" => date(' jS F y', strtotime($val->added_on)),
            "title" => html_entity_decode(strip_tags($val->title)),
            "cover_image" => $val->cover_image,
            "logo" => $val->client_logo,
            "followed_status" => ($val->followed_status != '') ? $val->followed_status : 0,
            "follower_count" => $resultInt->total,
            "address" => $val->address,
            "privacy_status" => $val->privacy_status,
            //"user_subs_status" => $val->address,
            "featured_video" => archiveVideoDetial($val->featured_video, $user_master_id),
            "featured_video_id" => $val->featured_video,
            "description" => $val->description,
            "short_description" => html_entity_decode(strip_tags(substr($val->short_description, 0, 300))),
            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
            "channel_doctor_entities" => $ses_doc_det_array,
            "client_name" => $val->client_name,
            "client_logo" => base_url('uploads/logo/') . $val->client_logo,
            "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
            "disclaimer" => disclaimer('knowledge'),
            "membershipformurl" => $val->membershipformurl,
        );
        $responses['content_detail'] = $vx;
        return $responses;
        break;
    default:
        $responses['content_detail'] = array();
        return $responses;
        break;
}
