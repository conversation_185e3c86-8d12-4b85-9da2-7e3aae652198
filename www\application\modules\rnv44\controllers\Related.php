<?php

defined('BASEPATH') or exit('No direct script access allowed');
require dirname(__FILE__) . '/../libraries/NEW_REST_Controller.php';
///require  'vendor/autoload.php';
//use \Firebase\JWT\JWT;
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);
class Related extends MY_REST_Controller
{
    private $token_data;
    private $logdata = array();

    public function __construct()
    {
        parent::__construct();
        $token = $this->input->get_request_header('Authorization');
        //this->set_response($all_data, REST_Controller::HTTP_OK); //This is the respon if success
        //Validate user
        $this->validate_token($token);
        $this->token_data = $this->get_token_data($token);
        //print_r($token);
        $this->load->library('form_validation');
        $this->load->model('Related_model');
        $this->load->helper('image');

        /**
         * For api detail table
         */
        $this->logdata['called_on'] = date('Y-m-d H:i:s', time());
        $this->logdata['process_start_time'] = date('Y-m-d H:i:s');
        $this->logdata['version'] = $this->input->get_request_header('version');
        /**
         * For api detail table
         */
    }

    public function trending_get()
    {



        try {

            $user_master_id = $this->token_data->userdetail->user_master_id;
            $env = get_user_env($user_master_id);

            $client_ids = $this->token_data->userdetail->client_ids;
            $limitTo = $this->input->get('to');
            $limitFrom = $this->input->get('from');
            $type = $this->input->get('type'); // for search value.
            $spIds = $this->input->get('speciality'); // for search value.
            $type_id = $this->input->get('type_id'); // for search value.


            $key = 'related_topic_' . $spIds . $env. $limitFrom. $limitTo.$type_id.$type;
            // echo $key;die;
            if ($all_data = $this->myredis->get($key)) {

                $message = 'Data Has Been Succesfully Surved From Cache!!';
                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success


            } else {


                $all_data = $this->Related_model->trending($type, $user_master_id, $client_ids, $limitTo, $limitFrom, $spIds, $type_id, $env);
                $this->myredis->set($key, $all_data, 60 * 60 * 4);

                $message = 'Data Has Been Succesfully Surved From DB!!';
                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
                //This is the respon if success
            }
        } catch (\Exception $e) {
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $e->getMessage();
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            $this->set_response(null, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }
    }

    public function discoverlist_get()
    {

        $user_master_id = $this->token_data->userdetail->user_master_id;
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $client_ids = $this->token_data->userdetail->client_ids;
        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $type = $this->input->get('type'); // for search value.
        $spIds = $this->input->get('speciality'); // for search value.
        $type_id = $this->input->get('type_id'); // for search value.

        $all_data = $this->Related_model->discoverlist($type, $user_master_id, $client_ids, $limitTo, $limitFrom, $spIds, $type_id);

        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }


    /**
     * Code written by rakesh
     */
    public function linked_content_get()
    {
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);
        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $type = $this->input->get('type'); // for search value.

        $type_id = $this->input->get('type_id'); // for search value.

        $all_data = $this->Related_model->linked_content($type_id, $limitTo, $limitFrom, $this->token_data->userdetail->user_master_id);

        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

        //echo 1;


    }
    public function topics_get()
    {

        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);
        $service_id = $this->input->get('service_id'); // for search value.

        $type_id = $this->input->get('type_id'); // for search value.

        // echo $type_id, $service_id;
        // exit;
        $all_data = $this->Related_model->topics($type_id, $service_id);

        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

        // print_r($all_data);
        // exit;
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

        //echo 1;


    }

    public function similar_content_get()
    {

        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $service_id = $this->input->get('service_id'); // for search value.
        $type_id = $this->input->get('type_id'); // for search value.
        $spIds = $this->input->get('spIds');
        $user_master_id = $this->token_data->userdetail->user_master_id;

        $all_data = $this->Related_model->similar_content($service_id, $type_id, $limitFrom, $limitTo, $user_master_id, $spIds);

        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

        // print_r($all_data);
        // exit;
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
}
