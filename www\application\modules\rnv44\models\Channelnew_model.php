<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Channelnew_model extends CI_Model
{
    public function __construct()
    {
        $ci = get_instance();
        $ci->load->helper('image');
        $ci->load->helper('utility');
        $this->load->library('Myredis');
    }

    /**
     * @param string $user_master_id
     * @return mixed
     */

    public function channelstatus($type_id)
    {
        $this->db->select('status');
        $this->db->from('channel_master');
        $this->db->where(array('channel_master_id' => $type_id, 'status' => 3));
        $query = $this->db->get();

        if (($query) && ($query->num_rows() > 0)) {
            return $query->num_rows();
        } else {
            return 0;
        }
    }

    public function fetchChannelDoctorwise($type_id, $type)
    {
        if ($type == 5) {
            $this->db->select('user_master_id');
            $this->db->from('knwlg_sessions_doctors');
            $this->db->where('sessions_doctors_id', $type_id);
            $user_ids_query = $this->db->get();
            if ($user_ids_query && $user_ids_query->num_rows() > 0) {
                $user_ids = array_column($user_ids_query->result_array(), 'user_master_id');
                $this->db->select('channel_master_id');
                $this->db->from('channel_master');
                $this->db->where('type', 5);
                $this->db->where_in('type_id', $user_ids);
                $query = $this->db->get();
                if ($query && $query->num_rows() > 0) {
                    return $query->result_array();
                } else {
                    return [];
                }
            } else {
                return [];
            }

            if ($query && $query->num_rows() > 0) {
                return $query->result_array();
            } else {
                return 0;
            }
        } else {
            return null;
        }
    }

    public function fetchAnnouncements($data_id, $expiry_date, $ref_type, $publish_date)
    {

        $sql = "SELECT `name`, `ref_type`, `type_id`, `type`, `title`, `description`, `link`, `img_url`, `publish_date`, `expiry_date`, `added_on`, `added_by` FROM `gb_announcements` INNER JOIN `gb_announcement_type_master` ON `gb_announcement_type_master`.`id` = `gb_announcements`.`type` WHERE `gb_announcements`.`status` = 3 and `gb_announcements`.`type_id` = $data_id";
        if (!empty($expiry_date)) {
            //$sql .= " AND expiry_date <= " . $this->db->escape($expiry_date);
            $sql .= " AND expiry_date BETWEEN ".$this->db->escape($publish_date)." AND ".$this->db->escape($expiry_date)."";
        }
        if (!empty($publish_date)) {
            $sql .= " AND publish_date >= " . $this->db->escape($publish_date);
        }
        if (!empty($ref_type)) {
            $sql .= " AND ref_type <= " . $this->db->escape($ref_type);
        }
        //echo $sql; exit;
        $query = $this->db->query($sql);

        $result = $query->result();
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            $vx[] = array(
                "slno" => $i,
                "type" => $val->name,
                "title" => $val->title,
                "description" => $val->description,
                "img_url" => $val->img_url,
                "publish_date" => $val->publish_date,
                "expiry_date" => $val->expiry_date,
                "link" => $val->link,
            );
            $i++;
        }
        return $vx;
    }

    public function userChannelCount($user_master_id, $client_ids)
    {

        if (!empty($user_master_id)) {

            //get user speciality
            $sqlInt = "select 
            specialities_id
            from
            user_to_interest
            where 
            user_master_id = " . $user_master_id . "";
            $queryInt = $this->db->query($sqlInt);
            $resultInt = $queryInt->result_array();

            //print_r($resultInt); exit;
            $specialities = array();
            foreach ($resultInt as $val) {

                $specialities[] = $val['specialities_id'];
                //$specialities = array_merge($specialities, $val);

            }
            if (count($specialities) > 0) {

                $specialityIds = implode(",", (array)$specialities);
            }
            if ($specialityIds != '') {

                $specialities_query = ' and (' . implode(' OR ', array_map(function ($x) {
                    return "FIND_IN_SET('$x', fd.speciality_id)";
                }, explode(',', $specialityIds))) . ')';
            } else {
                $specialities = "";
            }
            //echo $specialities; exit;
            //get user speciality
            if ($client_ids) {

                $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                    return "FIND_IN_SET('$x', fd.client_id)";
                }, explode(',', $client_ids))) . ')';
            }
            $sql = "SELECT
            ch.channel_master_id as type_id,
            ch.title as title,
            ch.description as description,
            ch.privacy_status,
            ch.cover_image,
            ch.added_on,
            ch.deeplink,
            ch.gl_deeplink,
            bnd.logo,
            cln.client_name,
            cln.client_logo,
            cTus.status as followed_status
            
            
            FROM channel_master as ch
            LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = ch.channel_master_id
            LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
            LEFT JOIN client_master as cln ON cln.client_master_id = ch.client_id   
            LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = ch.brand_id  
             
            LEFT JOIN channel_to_user  as cTus ON (cTus.channel_master_id = ch.channel_master_id  and (cTus.status = 1 or cTus.status = 3) )
            
            WHERE 
            ch.status=3 and ch.privacy_status = 0 
            and 
            cTus.user_master_id = " . $user_master_id . "
            group by ch.channel_master_id
            order by  ch.added_on desc";

            //echo  $sql; exit;
            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();
            $count = $query->num_rows();
            //print_r($result); exit;
            $i = 1;
            $vx = array();
            $vx[] = array("count" => $count);
            return $vx;
        }
    }

    public function topratedmedwiki($limitFrom, $limitTo, $user_master_id, $client_ids)
    {
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'channel');
        // if($key_locked == '') {
        //     return NULL;
        // }

        // ====================== fetching env_id  ======================//

        $channels = array();
        if ($limitFrom != '' and $limitTo != '') {

            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {

            $limit = "";
        }
        if ($client_ids) {

            $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                return "FIND_IN_SET('$x', cm.client_id)";
            }, explode(',', $client_ids))) . ')';
        }

        if ($val != '') {
            $valX = preg_replace('/[-?]/', '', $val);
            $stopWords = [
                '/is /',
                '/the /',
                '/what /',
                '/where /',
                '/when /',
                '/we /',
                '/how /',
                '/to /',
                '/who /',
                '/are /',
            ];

            $valFilter = preg_replace($stopWords, '', $valX);

            $laString = explode(" ", $valFilter);

            if (count($laString) > 0) {

                foreach ($laString as $value) {

                    if ($value != '') {

                        $sqlTagLikeArray[] = " tag_name like '%" . $value . "%'";
                        $sqlSepLikeArray[] = " specialities_name like '" . $value . "'";
                    }
                }

                $sqlTagLike = implode(" or ", $sqlTagLikeArray);
                $sqlSepLike = implode(" or ", $sqlSepLikeArray);
            } else {
                $sqlTagLike = " tag_name like '%" . $val . "%'";
                $sqlSepLike = " specialities_name like '%" . $val . "%'";
            }

            /*$sqlTag ="select
            *
            from master_tags
            WHERE " . $sqlTagLike . "";
            $query = $this->db->query($sqlTag);
            $result = $query->result();*/

            $sqlSpec = "select 
                        master_specialities_id
                        from master_specialities_V1 
                        WHERE " . $sqlSepLike . "";

            //echo $sqlSpec; exit;
            $query = $this->db->query($sqlSpec);
            $resultSpeTag = $query->result_array();
            foreach ($resultSpeTag as $valSpec) {

                $specIdArray[] = $valSpec['master_specialities_id'];
            }
            if (count($specIdArray) > 0) {

                $specIdStr = implode(",", (array)$specIdArray);
            } else {

                $specIdStr = $valSpec['master_specialities_id'];
            }
            //echo $val; exit;
            if ($specIdStr) {
                $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $specIdStr . ")  and (cm.publication_date <= CURDATE() " . $client_list . ") ) )";
            }

            if ($valFilter) {
                $searchQuery[] = " ( (MATCH(cm.comp_qa_question_raw, cm.comp_qa_answer_raw) AGAINST ('" . $valFilter . "' IN NATURAL LANGUAGE MODE) ) and  (cm.publication_date <= CURDATE()  " . $client_list . ") )";
            }
        } else {
            //$searchQuery[] = "";
        }

        if ($spIds != '') {

            $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $spIds . ")  and (cm.publication_date <= CURDATE() " . $client_list . ") ) )";
        }
        if ($val == '' and $spIds == '') {

            $searchQuery[] = " (cm.publication_date <= CURDATE() " . $client_list . ")";
        }

        if ($from_date != '' and $to_date != '') {

            $searchBYdate = " AND (cm.publication_date BETWEEN '" . $from_date . "' AND '" . $to_date . "' )";
        } else {

            $searchBYdate = " ";
        }

        $searchQueryStr = implode("or", $searchQuery);
        if (!empty($user_master_id)) {

            //$this->db->cache_on();

            if ($type == 1) {

                $typeSql = " AND cm.type = 'video'";
                $typedashboardstatus = "";
            } else {

                $typeSql = "";

                $typedashboardstatus = "AND cm.display_in_dashboard =1 ";
            }
            // excho "<pre>";print_r($type);die();

            $sql = "SELECT
            cm.comp_qa_id as type_id,
            cm.comp_qa_question ,
            cm.comp_qa_answer ,
            cm.comp_qa_answer_raw as description,
            cm.comp_qa_question_raw as title,
            cm.comp_qa_file_img,
            cm.comp_qa_file_img_thumbnail,
            cm.added_on,
            cm.publication_date as publish_date,
            cln.client_name,
            cln.client_logo,
            
            cTenv.price,
            uTpyCont.status as user_contnet_payment_status,
            cm.type,
            cm.vendor,
            cm.src,
            cm.deeplink,
            cm.gl_deeplink,
            cm.color,
            
            GROUP_CONCAT(DISTINCT cmTs.specialities_id) as specialities,
            GROUP_CONCAT(DISTINCT ctc.channel_master_id) as channels,
            GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
            max( ms.rank) as maxrank,
            
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
            
            cm.comp_qa_speciality_id,
            (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.comp_qa_id and  rt.post_type='comp')as averageRating,
            rtmy.rating  as myrating,
            
            (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.comp_qa_id and kcm.type = 'comp')as count_comment,
            kv.status as vault
            
            FROM knwlg_compendium_V1 as cm

            -- JOIN channel_to_compendium cts ON cTs.comp_qa_id = cm.comp_qa_id         
            JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
            JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
            
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id and  cTenv.type = 1
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.comp_qa_id and  uTpyCont.type = 1 and 	uTpyCont.user_master_id = " . $user_master_id . "
            JOIN channel_to_compendium as ctc ON ctc.comp_qa_id = cm.comp_qa_id
            JOIN client_master as cln ON cln.client_master_id = cm.client_id 
            join channel_master as chm on chm.channel_master_id = ctc.channel_master_id
            LEFT JOIN compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            
            
            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.comp_qa_id and  rtmy.post_type='comp' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
            LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and  kv.type_text='comp' and  kv.user_id = " . $user_master_id . "
            LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.comp_qa_id and  rt.post_type='comp'
            
            WHERE 
            cm.status=3 and cm.is_draft = 0 and cm.privacy_status =0 and chm.status =3
            " . $envStatus . "
            group by cm.comp_qa_id
            order by averageRating DESC " . $limit . "";
            #echo $sql; exit;

            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();
            // print_r($result); exit;
            $i = 1;
            $vx = array();
            foreach ($result as $val) {
                //its taking too much time, we have to figure it out
                if ($val->comp_qa_file_img) {
                    $logic_image_path = "uploads/compendium/" . $val->comp_qa_file_img;
                    $imgPr = image_thumb_url($logic_image_path, $val->comp_qa_file_img, 450, 250, '');
                    $logic_image = $imgPr;
                } else {

                    $logic_image = '';
                }
                if ($val->comp_qa_file_img_thumbnail) {
                    // $logic_image_path = "uploads/compendium/" . $val->comp_qa_file_img;
                    // $imgPr = image_thumb_url($logic_image_path, $val->comp_qa_file_img, 450, 250, '');
                    $logic_image = $val->comp_qa_file_img_thumbnail;
                } else {

                    $logic_image = '';
                }
                $specarr = [];
                $charr = [];

                $specialities = explode(',', $val->specialities);
                if (!empty($specialities)) {

                    $s = 0;

                    foreach ($specialities as $spec) {

                        $this->db->select('msv.master_specialities_id, msv.specialities_name');
                        $this->db->from('master_specialities_V1 as msv');
                        $this->db->where(['msv.master_specialities_id' => $spec]);

                        $specresult = $this->db->get();

                        if ($specresult) {
                            $specresponse = $specresult->result_array();
                        }

                        foreach ($specresponse as $specval) {
                            $specarr[$s]['id'] = $specval['master_specialities_id'];
                            $specarr[$s]['name'] = $specval['specialities_name'];
                        }

                        $s++;
                    }
                }

                $channels = explode(',', $val->channels);
                if (!empty($channels)) {

                    $ch = 0;

                    foreach ($channels as $channel) {

                        $this->db->select('cm.channel_master_id, cm.title, cm.logo');
                        $this->db->from('channel_master as cm');
                        // $this->db->join('client_master as clientm', 'clientm.client_master_id = cm.channel_master_id', 'left');
                        $this->db->where(['cm.channel_master_id' => $channel, 'cm.status' => 3]);

                        $chresult = $this->db->get();

                        if ($chresult) {
                            $chresponse = $chresult->result_array();
                        }

                        // print_r($chresponse); exit;

                        foreach ($chresponse as $chval) {
                            $charr[$ch]['channel_id'] = $chval['channel_master_id'];
                            $charr[$ch]['channel_name'] = $chval['title'];
                            $charr[$ch]['channel_logo'] = change_img_src($chval['logo']);

                            $ch++;
                        }
                    }
                }

                $vx[] = array(

                    "slno" => $i,
                    "con_type" => $val->type,
                    "vendor" => $val->vendor,
                    "src" => $val->src,
                    "type_id" => $val->type_id,
                    "type" => 'comp',
                    "date" => date(' jS F y', strtotime($val->publish_date)),
                    "question" => html_entity_decode(strip_tags($val->title)),
                    "image" => change_img_src($logic_image),
                    "color" => ($val->color != '') ? $val->color : '#918c91',

                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "price" => $val->price,
                    "user_content_payment" => $val->user_contnet_payment_status,
                    //============ integrated for subscription ============//

                    "answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    // "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "specialities_ids_and_names" => $specarr,
                    "channels" => $charr,
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src('' . $val->client_logo),
                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($val->sponsor_logo),
                    "comment_count" => $val->count_comment,
                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                    "myrating" => ($val->myrating != '') ? true : false,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                    "deeplink" => ($env == 2) ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0),
                );

                $i++;
            }
            // print_r($vx);
            // exit;
            return $vx;
        }
    }
    public function getAssociatChannels($user_master_id, $client_ids, $limitTo, $limitFrom, $service)
    {
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $result = null;
        switch ($service) {
            case "medwiki":

                try {

                    $sql = "SELECT
                                DISTINCT(ctc.channel_master_id),
                                cm.title ,
                                cm.description ,
                                cm.follower_count ,
                                cm.cover_image,
                                cm.logo

                            FROM 
                                channel_to_compendium AS ctc 
                            JOIN 
                                channel_master AS cm ON cm.channel_master_id = ctc.channel_master_id
                            LEFT JOIN 
                                content_to_env as cTenv ON cTenv.type_id = ctc.comp_qa_id and cTenv.type = 1

                            WHERE
                                cm.privacy_status = 0
                                AND cm.status = 3 
                                {$envStatus}
                            LIMIT
                                {$limitFrom} , {$limitTo}
                    
                    ";

                    // echo $sql ;die;
                    $query = $this->db->query($sql);

                    if (!$query) {
                        $error = $this->db->error();
                        throw new Exception("Database error: " . $error['message'], $error['code']);
                    }

                    $result = $query->result();

                    return $result;
                } catch (Exception $e) {
                    throw new Exception(" Error Executing Medwiki Query! error: " . $e->getMessage());
                }

                break;

            case "video":

                try {

                    $sql = "SELECT
                                DISTINCT(ctv.channel_master_id),
                                cm.title ,
                                cm.description ,
                                cm.follower_count ,
                                cm.cover_image,
                                cm.logo

                            FROM 
                                channel_to_video_archive AS ctv
                            JOIN 
                                channel_master AS cm ON cm.channel_master_id = ctv.channel_master_id
                            LEFT JOIN 
                                content_to_env as cTenv ON cTenv.type_id = ctv.video_archive_id and cTenv.type = 3

                            WHERE
                                cm.privacy_status = 0
                                AND cm.status = 3 
                                {$envStatus}
                            LIMIT
                                {$limitFrom} , {$limitTo}
                    
                    ";

                    // echo $sql ;die;
                    $query = $this->db->query($sql);

                    if (!$query) {
                        $error = $this->db->error();
                        throw new Exception("Database error: " . $error['message'], $error['code']);
                    }

                    $result = $query->result();

                    return $result;
                } catch (Exception $e) {
                    throw new Exception(" Error Executing Video Query! error: " . $e->getMessage());
                }

                break;

            case "session":

                try {

                    $sql = "SELECT
                                DISTINCT(cts.channel_master_id),
                                cm.title ,
                                cm.description ,
                                cm.follower_count ,
                                cm.cover_image,
                                cm.logo

                            FROM 
                                channel_to_session AS cts
                            JOIN 
                                channel_master AS cm ON cm.channel_master_id = cts.channel_master_id
                            LEFT JOIN 
                                content_to_env as cTenv ON cTenv.type_id = cts.session_id and cTenv.type = 2

                            WHERE
                                cm.privacy_status = 0
                                AND cm.status = 3 
                                {$envStatus}
                            LIMIT
                                {$limitFrom} , {$limitTo}
                    
                    ";

                    // echo $sql ;die;
                    $query = $this->db->query($sql);

                    if (!$query) {
                        $error = $this->db->error();
                        throw new Exception("Database error: " . $error['message'], $error['code']);
                    }

                    $result = $query->result();

                    return $result;
                } catch (Exception $e) {
                    throw new Exception(" Error Executing Session Query! error: " . $e->getMessage());
                }

                break;

            case "course":

                try {

                    $sql = "SELECT
                                DISTINCT(ctc.channel_master_id),
                                cm.title ,
                                cm.description ,
                                cm.follower_count ,
                                cm.cover_image,
                                cm.logo

                            FROM 
                                channel_to_course AS ctc
                            JOIN 
                                channel_master AS cm ON cm.channel_master_id = ctc.channel_master_id
                            LEFT JOIN 
                                content_to_env as cTenv ON cTenv.type_id = ctc.course_id and cTenv.type = 4

                            WHERE
                                cm.privacy_status = 0
                                AND cm.status = 3 
                                {$envStatus}
                            LIMIT
                                {$limitFrom} , {$limitTo}
                    
                    ";

                    // echo $sql ;die;
                    $query = $this->db->query($sql);

                    if (!$query) {
                        $error = $this->db->error();
                        throw new Exception("Database error: " . $error['message'], $error['code']);
                    }

                    $result = $query->result();

                    return $result;
                } catch (Exception $e) {
                    throw new Exception(" Error Executing Course Query! error: " . $e->getMessage());
                }

                break;

            case "spq":

                try {

                    $sql = "SELECT
                                DISTINCT(cts.channel_master_id),
                                cm.title ,
                                cm.description ,
                                cm.follower_count ,
                                cm.cover_image,
                                cm.logo

                            FROM 
                                channel_to_survey AS cts
                            JOIN 
                                channel_master AS cm ON cm.channel_master_id = cts.channel_master_id
                            LEFT JOIN 
                                content_to_env as cTenv ON cTenv.type_id = cts.survey_id and cTenv.type = 6

                            WHERE
                                cm.privacy_status = 0
                                AND cm.status = 3 
                                {$envStatus}
                            LIMIT
                                {$limitFrom} , {$limitTo}
                    
                    ";

                    // echo $sql ;die;
                    $query = $this->db->query($sql);

                    if (!$query) {
                        $error = $this->db->error();
                        throw new Exception("Database error: " . $error['message'], $error['code']);
                    }

                    $result = $query->result();

                    return $result;
                } catch (Exception $e) {
                    throw new Exception(" Error Executing spq Query! error: " . $e->getMessage());
                }

            case "epub":

                try {

                    $sql = "SELECT
                                cm.channel_master_id,
                                cm.title ,
                                cm.description ,
                                cm.follower_count ,
                                cm.cover_image,
                                cm.logo

                            FROM 
                                epub_master AS em
                            JOIN 
                                channel_master AS cm ON cm.client_id = em.client_id
                            LEFT JOIN 
                                content_to_env as cTenv ON cTenv.type_id = em.epub_id and cTenv.type = 9

                            WHERE
                                cm.privacy_status = 0
                                AND cm.status = 3 
                                {$envStatus}
                            LIMIT
                                {$limitFrom} , {$limitTo}
                    
                    ";

                    // echo $sql ;die;
                    $query = $this->db->query($sql);

                    if (!$query) {
                        $error = $this->db->error();
                        throw new Exception("Database error: " . $error['message'], $error['code']);
                    }

                    $result = $query->result();

                    return $result;
                } catch (Exception $e) {
                    throw new Exception(" Error Executing spq Query! error: " . $e->getMessage());
                }

            default:
                return $result;
        }
    }

    public function getspecialityBasedChannels($user_master_id, $client_ids, $limitTo, $limitFrom, $specialityId)
    {

        $result = null;

        try {

            $sql = "SELECT
                        cts.channel_master_id,
                        cm.title ,
                        cm.description ,
                        cm.follower_count ,
                        cm.cover_image,
                        cm.logo

                    FROM 
                        channel_to_specialities AS cts
                    JOIN 
                        channel_master AS cm ON cm.channel_master_id = cts.channel_master_id

                    WHERE
                        cm.privacy_status = 0
                        AND cm.status = 3 
                        AND cts.specialities_id = {$specialityId}
                    LIMIT
                        {$limitFrom} , {$limitTo}
            
            ";

            // echo $sql ;die;
            $query = $this->db->query($sql);

            if (!$query) {
                $error = $this->db->error();
                throw new Exception("Database error: " . $error['message'], $error['code']);
            }

            $result = $query->result();

            return $result;
        } catch (Exception $e) {
            throw new Exception(" Error Executing Medwiki Query! error: " . $e->getMessage());
        }
    }

    public function addDiscussionData($user_master_id, $postData)
    {

        $checkUserSql = "SELECT
                            discussion_id
                        FROM 
                            discussion_master
                        WHERE
                           created_by = {$user_master_id} 
                           AND channel_id = {$postData['channel_id']}
        ";

        $checkQuery = $this->db->query($checkUserSql);

        if ($checkQuery->num_rows() > 0) {
            throw new Exception("A Discussion Already Exists For This User!", 203);
        }
        $insertData = array(
            "channel_id" => $postData['channel_id'],
            "parent_id" => 0,
            "created_by" => $user_master_id,
            "status" => 3,
            "created_on" => date('Y-m-d H:i:s')
        );

        if (!$this->db->insert("discussion_master", $insertData)) {
            $error = $this->db->error();
            throw new Exception("A Database Error Occured", 500);
        }

        $discussion_id = $this->db->insert_id();

        $insertData = array(
            "discussion_id" => $discussion_id,
            "discussion_title" => $postData['title'],
            "discussion_description" => $postData['description'],
            "discussion_image" => json_encode($postData['image_urls']),
        );

        if (!$this->db->insert("discussion_detail", $insertData)) {
            $error = $this->db->error();
            throw new Exception("A Database Error Occured", 500);
        }

        return $discussion_id;
    }
    public function addDiscussionCommentData($user_master_id, $postData)
    {
        $insertData = array(
            "discussion_id" => $postData['discussion_id'],
            "thread_data" => $postData['comment'],
            "parent_id" => $postData['parent_id'],
            "created_by" => $user_master_id,
            "status" => 3,
            "created_on" => date('Y-m-d H:i:s')
        );

        if (!$this->db->insert("discussion_thread", $insertData)) {
            $error = $this->db->error();
            throw new Exception("A Database Error Occured", 500);
        }

        $thread_id = $this->db->insert_id();

        return $thread_id;
    }
    public function editDiscussionCommentData($user_master_id, $postData)
    {

        $updateData = array(
            "thread_data" => $postData['comment'],
            "modified_on" => date('Y-m-d H:i:s')
        );

        if (!$this->db->where("thread_id", $postData['thread_id'])->where("created_by", $user_master_id)->update("discussion_thread", $updateData)) {
            $error = $this->db->error();
            throw new Exception("A Database Error Occured", 500);
        }

        if ($this->db->affected_rows() === 0) {
            throw new Exception("thread id does not exist or no changes were made.", 203);
        }

        return 1;
    }

    public function updateDiscussionData($user_master_id, $postData)
    {
        $updateData = array(
            "discussion_title" => $postData['title'],
            "discussion_description" => $postData['description'],
            "discussion_image" => json_encode($postData['image_urls']),
        );

        if (!$this->db->where("discussion_id", $postData['discussion_id'])->update("discussion_detail", $updateData)) {
            $error = $this->db->error();
            throw new Exception("A Database Error Occured ", 500);
        }
        if ($this->db->affected_rows() === 0) {
            throw new Exception("discussion id " . $postData['discussion_id'] . " does not exist or no changes were made.", 203);
        }

        $updateData = array(
            "modified_on" => date('Y-m-d H:i:s'),
        );

        if (!$this->db->where("discussion_id", $postData['discussion_id'])->update("discussion_master", $updateData)) {
            $error = $this->db->error();
            throw new Exception("A Database Error Occured ", 500);
        }

        return 1;
    }
    public function getDiscussionByChannelId($limitTo, $limitFrom, $channel_id)
    {

        try {
            $sql = "SELECT
                        dm.discussion_id,
                        dm.created_on ,
                        dd.discussion_title,
                        dd.discussion_description,
                        dd.discussion_image,
                        CONCAT(ud.first_name , ' ' , ud.middle_name , ' ' , last_name ) as created_by,
                        COUNT(kr.rating_id) as likes 

                    FROM 
                        discussion_master dm
                    JOIN
                        discussion_detail AS dd ON dd.discussion_id = dm.discussion_id
                    JOIN 
                        channel_master cm ON cm.channel_master_id = dm.channel_id
                    LEFT JOIN 
                        user_detail ud ON ud.user_master_id = dm.created_by
                    LEFT JOIN 
                        knwlg_rating as kr ON kr.post_id = dm.discussion_id and kr.post_type like 'discussion' AND kr.rating = 1
                    
                    WHERE   
                        dm.channel_id = {$channel_id}
                        AND dm.status = 3
                        AND cm.privacy_status = 0
                        AND cm.status = 3

                    GROUP BY
                        dm.discussion_id
                    LIMIT
                        {$limitFrom} , {$limitTo}

            ";

            // echo $sql ;die;
            $query = $this->db->query($sql);

            if (!$query) {
                $error = $this->db->error();
                throw new Exception("Database error: " . $error['message'], $error['code']);
            }

            $result = $query->result();

            foreach ($result as $key => $value) {
                if (!empty($result[$key]->discussion_image)) {
                    $result[$key]->discussion_image = json_decode($result[$key]->discussion_image);
                }
            }

            return $result;
        } catch (Exception $e) {

            throw new Exception(" Error Executing Query! error: " . $e->getMessage());
        }
    }
    public function getAChannelAllUsers($user_master_id, $channel_id, $limitFrom, $limitTo)
    {

        try {
            $sql = "SELECT
                        ctu.user_master_id,
                        ctu.badge ,
                        ctu.added_on,
                        ms.specialities_name,
                        CONCAT(ud.first_name , ' ' , ud.middle_name , ' ' , last_name ) as user_name,
                        ud.profile_image as user_profile_image

                    FROM 
                        channel_to_user ctu
                    JOIN 
                        channel_master cm ON cm.channel_master_id = ctu.channel_master_id
                    LEFT JOIN 
                        user_detail ud ON ud.user_master_id = ctu.user_master_id
                    LEFT JOIN 
                        user_to_specialities AS uts ON uts.user_master_id = ctu.user_master_id

                    LEFT JOIN 
                        master_specialities_V1 AS ms ON ms.master_specialities_id = uts.specialities_id
                    
                    WHERE   
                        ctu.channel_master_id = {$channel_id}
                        AND ms.status = 1
                        AND cm.privacy_status = 0
                        AND cm.status = 3

                   
                    LIMIT
                        {$limitFrom} , {$limitTo}

            ";

            // echo $sql ;die;
            $query = $this->db->query($sql);

            if (!$query) {
                $error = $this->db->error();
                throw new Exception("A database error encountered! ", 500);
            }

            $result = $query->result();
            return $result;
        } catch (Exception $e) {

            throw new Exception(" Error Executing Query! error: " . $e->getMessage(), 500);
        }
    }
    public function getDiscusiionComments($limitTo, $limitFrom, $discussion_id)
    {

        try {

            $discussionDetailsql = "SELECT
                                        dm.discussion_id,
                                        dm.created_on ,
                                        dd.discussion_title,
                                        dd.discussion_description,
                                        dd.discussion_image,
                                        CONCAT(ud.first_name , ' ' , ud.middle_name , ' ' , last_name ) as created_by,
                                        COUNT(kr.rating_id) as likes 

                                    FROM 
                                        discussion_master dm
                                    JOIN
                                        discussion_detail AS dd ON dd.discussion_id = dm.discussion_id
                                    JOIN 
                                        channel_master cm ON cm.channel_master_id = dm.channel_id
                                    LEFT JOIN 
                                        user_detail ud ON ud.user_master_id = dm.created_by
                                    LEFT JOIN 
                                        knwlg_rating as kr ON kr.post_id = dm.discussion_id and kr.post_type like 'discussion' AND kr.rating = 1
                                    
                                    WHERE   
                                        dm.discussion_id = {$discussion_id}
                                        AND dm.status = 3
                                        AND cm.privacy_status = 0
                                        AND cm.status = 3
            ";

            // echo $sql ;die;
            $discussionDetailQuery = $this->db->query($discussionDetailsql);

            if (!$discussionDetailQuery) {
                $error = $this->db->error();
                throw new Exception("A database error encountered! ", 500);
            }

            $discussionDetailResult = $discussionDetailQuery->row();

            if (empty($discussionDetailResult)) {
                throw new Exception("discussion id does not exist", 203);
            }
            if (!empty($discussionDetailResult->discussion_image)) {
                $discussionDetailResult->discussion_image = json_decode($discussionDetailResult->discussion_image);
            }
            $sql = "SELECT
                        dm.discussion_id,
                        CONCAT(ud.first_name , ' ' , ud.middle_name , ' ' , ud.last_name ) as created_by,
                        dt.thread_data,
                        dt.created_on,
                        dt.thread_id,
                        dt.parent_id,
                        COALESCE(COUNT(dtr.user_master_id), 0) AS 'likes'

                    FROM 
                        discussion_master dm
                    JOIN
                        discussion_detail AS dd ON dd.discussion_id = dm.discussion_id
                    JOIN 
                        channel_master cm ON cm.channel_master_id = dm.channel_id

                    JOIN 
                        discussion_thread AS dt ON dt.discussion_id = dm.discussion_id 
                    LEFT JOIN 
                        user_detail ud ON ud.user_master_id = dt.created_by
                    LEFT JOIN 
                        discussion_thread_ratings AS dtr ON dtr.thread_id = dt.thread_id

                    WHERE   
                        dm.discussion_id = {$discussion_id}
                        AND dm.status = 3
                        AND cm.privacy_status = 0
                        AND cm.status = 3
                        AND dt.discussion_id = {$discussion_id}
                        AND dt.status = 3

                    GROUP BY
                        dt.thread_id
                    LIMIT
                        {$limitFrom} , {$limitTo}

            ";

            // echo $sql ;die;
            $query = $this->db->query($sql);

            if (!$query) {
                $error = $this->db->error();
                throw new Exception("Database error: " . $error['message'], $error['code']);
            }

            $result = $query->result();
            $commnetTree = $this->buildCommentTree($result);

            $returnData = array(
                'discussionDetail' => $discussionDetailResult,
                'comments' => $commnetTree
            );

            return $returnData;
        } catch (Exception $e) {

            throw new Exception(" Error Executing Query! error: " . $e->getMessage());
        }
    }
    public function switchDiscussionLike($user_master_id, $type, $id)
    {

        try {
            switch ($type) {
                case 'discussion':

                    try {
                        $checkSql = "SELECT 
                                            kr.rating_id,
                                            kr.rating
                                        FROM
                                            knwlg_rating kr
                                        WHERE 
                                            kr.post_id = {$id}
                                            AND kr.post_type like 'discussion'
                                            AND kr.user_master_id = {$user_master_id}
                            
                            ";

                        $checkQuery = $this->db->query($checkSql);

                        if ($checkQuery->num_rows() > 0) {
                            $result = $checkQuery->row();
                            $rating_id = $result->rating_id;
                            $rating = $result->rating;
                            if ($rating == 0) {
                                $updateRating = 1;
                            } else {
                                $updateRating = 0;
                            }

                            $updateSql = "UPDATE 
                                                knwlg_rating 
                                            SET 
                                                rating = {$updateRating}
                                            WHERE 
                                                rating_id = {$rating_id}
                                ";
                            if (!$this->db->query($updateSql)) {
                                throw new Exception("A database error encountered! ", 500);
                            }

                            return 1;
                        } else {
                            $insertData = array(
                                "user_master_id" => $user_master_id,
                                "post_id" => $id,
                                "post_type" => "discussion",
                                "rating" => 1,
                                "timestamp" => date('Y-m-d H:i:s'),
                            );

                            if (!$this->db->insert('knwlg_rating', $insertData)) {
                                throw new Exception("A database error encountered! ", 500);
                            }

                            return 1;
                        }
                    } catch (Exception $e) {
                        throw new Exception(" A database error encountered!", 500);
                    }

                    break;

                case "comment":
                    try {

                        $checkSql = "SELECT 
                                            dtr.rating_id,
                                            dtr.rating
                                        FROM
                                            discussion_thread_ratings dtr
                                        WHERE 
                                            dtr.thread_id = {$id}
                                            AND dtr.post_type like 'comment'
                                            AND dtr.user_master_id = {$user_master_id}
                            
                            ";

                        $checkQuery = $this->db->query($checkSql);

                        if ($checkQuery->num_rows() > 0) {
                            $result = $checkQuery->row();
                            $rating_id = $result->rating_id;
                            $rating = $result->rating;
                            if ($rating == 0) {
                                $updateRating = 1;
                            } else {
                                $updateRating = 0;
                            }

                            $updateSql = "UPDATE 
                                                discussion_thread_ratings 
                                            SET 
                                                rating = {$updateRating}
                                            WHERE 
                                                rating_id = {$rating_id}
                                ";

                            if (!$this->db->query($updateSql)) {
                                throw new Exception("A database error encountered! ", 500);
                            }

                            return 1;
                        } else {
                            $insertData = array(
                                "user_master_id" => $user_master_id,
                                "thread_id" => $id,
                                "post_type" => "comment",
                                "rating" => 1,
                                "timestamp" => date('Y-m-d H:i:s'),
                            );

                            if (!$this->db->insert('discussion_thread_ratings', $insertData)) {
                                throw new Exception("A database error encountered! ", 500);
                            }

                            return 1;
                        }
                    } catch (Exception $e) {
                        throw new Exception("A database error encountered! ", 500);
                    }
                    break;

                default:
                    throw new Exception("Invalid type", 203);
            }
        } catch (Exception $e) {

            throw new Exception(" Error Executing Query! error: " . $e->getMessage());
        }
    }
    public function buildCommentTree($comments, $parentId = 0)
    {
        $branch = [];

        foreach ($comments as $comment) {
            if ($comment->parent_id == $parentId) {
                $children = $this->buildCommentTree($comments, $comment->thread_id);
                if ($children) {
                    $comment->children = $children;
                }
                $branch[] = $comment;
            }
        }
        return $branch;
    }

    public function getDiscussionByUserId($limitTo, $limitFrom, $user_master_id)
    {

        try {
            $sql = "SELECT
                        dm.discussion_id,
                        dm.created_on ,
                        dd.discussion_title,
                        dd.discussion_description,
                        dd.discussion_image,
                        COUNT(kr.rating_id) as likes 

                    FROM 
                        discussion_master dm
                    JOIN
                        discussion_detail AS dd ON dd.discussion_id = dm.discussion_id
                    JOIN 
                        channel_master cm ON cm.channel_master_id = dm.channel_id
                    
                    LEFT JOIN 
                        knwlg_rating as kr ON kr.post_id = dm.discussion_id and kr.post_type like 'discussion' AND kr.rating = 1
                    
                    WHERE   
                        dm.created_by = {$user_master_id}
                        AND dm.status = 3
                        AND cm.privacy_status = 0
                        AND cm.status = 3

                    GROUP BY
                        dm.discussion_id

                    LIMIT
                        {$limitFrom} , {$limitTo}

            ";

            // echo $sql ;die;
            $query = $this->db->query($sql);

            if (!$query) {
                $error = $this->db->error();
                throw new Exception("Database error: " . $error['message'], $error['code']);
            }

            $result = $query->result();

            foreach ($result as $key => $value) {
                if (!empty($result[$key]->discussion_image)) {
                    $result[$key]->discussion_image = json_decode($result[$key]->discussion_image);
                }
            }

            return $result;
        } catch (Exception $e) {

            throw new Exception(" Error Executing Query! error: " . $e->getMessage());
        }
    }
    public function mostfollowedchannel($user_master_id, $client_ids, $limitTo, $limitFrom)
    {
        // echo $user_master_id;
        // exit;
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $env_arr = array(2, $env);
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $env_arr = array($env);
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'channel');
        //    if($key_locked == '') {
        //       return NULL;
        //    }

        // ====================== fetching env_id  ======================//
        $response = [];

        $this->db->select('cm.channel_master_id, cTenv.price,  uTpyCont.status as user_contnet_payment_status, cm.privacy_status, cm.added_on, cm.title, cm.cover_image, cm.logo, cm.description, 
                                cm.follower_count,cm.deeplink,cm.gl_deeplink, ctu.status');
        $this->db->from('channel_master as cm');
        $this->db->join('channel_to_user as ctu', 'cm.channel_master_id = ctu.channel_master_id AND ctu.user_master_id = ' . $user_master_id, 'left');

        $this->db->join('content_to_env as cTenv', "cTenv.type_id = cm.channel_master_id and  cTenv.type = 11", "left");
        $this->db->join('payment_user_to_content as uTpyCont', "uTpyCont.type_id = cm.channel_master_id and  uTpyCont.type = 11", "left");
        $this->db->where(['cm.status' => 3, 'cm.privacy_status' => 0]);
        $this->db->where_in('cTenv.env', $env_arr);
        $this->db->order_by('cm.follower_count', 'desc');
        // $this->db->group_by('cts.channel_master_id');
        if ($limitTo != '' && $limitFrom != '') {
            $this->db->limit($limitTo, $limitFrom);
        }

        $result = $this->db->get();

        // echo $this->db->last_query(); exit;

        if ($result) {
            $response = $result->result_array();
        }
        $c = 0;
        $slno = 1;
        $data = [];

        foreach ($response as $val) {
            $this->db->select('count(DISTINCT(user_master_id)) as total');
            $this->db->from('channel_to_user');
            $this->db->where('channel_master_id', $val['channel_master_id']);
            $this->db->where('status', 3);
            $querytotal = $this->db->get();
            //print_r($this->db->last_query()); exit;
            //print_r($querytotal); exit;
            if (($querytotal) && ($querytotal->num_rows() > 0)) {
                $fstatus = $querytotal->row();

                $followedstatus = ($fstatus->total != '') ? $fstatus->total + $val['follower_count'] : $val['follower_count'];
            } else {
                $followedstatus = $val['follower_count'];
            }
            $c = $followedstatus;

            $data[$c]['slno'] = $slno;
            $data[$c]['type_id'] = $val['channel_master_id'];
            $data[$c]['type'] = 'channel';
            $data[$c]['privacy_status'] = $val['privacy_status'];
            $data[$c]['followed_status'] = (empty($val['status'])) ? 0 : $val['status'];
            $data[$c]['follower_count'] = $followedstatus; //$val['follower_count'];
            $data[$c]['added_on'] = $val['added_on'];
            $data[$c]['title'] = $val['title'];
            $data[$c]['cover_image'] = change_img_src($val['cover_image']);
            $data[$c]['logo'] = ($val['logo']) ? change_img_src($val['logo']) : "https://img-cdn.clirnet.com/medwiki/43_server/images/1696423240_1688713284_78rr-removebg-preview_(1).png";
            $data[$c]['slug'] = $val['title'];
            $data[$c]['description'] = $val['description'];
            $data[$c]['description_view'] =  preg_replace('/[\r\n]/', '', html_entity_decode(strip_tags($val['description']))); //html_entity_decode(strip_tags($val['description']));
            $data[$c]['specialities'] = $val['specialities'];

            $data[$c]['is_locked'] = $key_locked;
            $data[$c]['price'] = $val['price'];
            $data[$c]['user_content_payment'] = $val['user_content_payment'];

            $data[$c]['deeplink'] = ($env == 2) ? (($val['gl_deeplink'] != '') ? $val['gl_deeplink'] : 0) : (($val['deeplink'] != '') ? $val['deeplink'] : 0); //$val['deeplink'];

            //$c++;
            $slno++;
        }

        // echo "<pre>";
        krsort($data);
        $res = array();
        foreach ($data as $k => $v) {
            //print_r($v);
            $res[] = $v;
        }
        return $res;
    }

    public function trendingebook($limitFrom = '', $limitTo = '', $user_master_id)
    {

        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'channel');

        // if($key_locked == '') {
        //     return NULL;
        // }
        // ====================== fetching env_id  ======================//

        if ($limitFrom != '' and $limitTo != '') {

            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {

            $limit = "";
        }

        $sql = "SELECT
            cm.epub_id as type_id,

            cm.epub_description as description,
            cm.epub_title as title,

            cm.epub_img,
            cm.epub_img_thumbnail,
            cm.epub_file,
            cm.author,

            cm.added_on,
            cm.publication_date as publish_date,

            cTenv.price,
            uTpyCont.status as user_contnet_payment_status,
            cm.deeplink,
            cm.gl_deeplink,
            cm.color,
            cts.channel_master_id as channels,
            GROUP_CONCAT(DISTINCT cmTs.specialities_id) as specialities,
            GROUP_CONCAT(DISTINCT cmTspon.sponsor_id) as sponsors

            FROM epub_master as cm

            JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.epub_id and  cTenv.type = 9
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.epub_id and  uTpyCont.type = 9 and 	uTpyCont.user_master_id = " . $user_master_id . "
            LEFT JOIN channel_master as cts ON cts.client_id = cm.client_id and cts.status = 3
            LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            
            WHERE 
            cm.status=3 
            " . $envStatus . "
            and cm.privacy_status = 0  and cm.display_in_dashboard = 1 and cts.channel_master_id != ''
            group by cm.epub_id
            order by  cm.publication_date desc " . $limit . "";

        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            // print_r($val->channels);
            // die;
            if ($val->epub_img_thumbnail) {
                // $logic_image_path = "uploads/compendium/" . $val->comp_qa_file_img;
                // $imgPr = image_thumb_url($logic_image_path, $val->comp_qa_file_img, 450, 250, '');
                $logic_image = $val->epub_img_thumbnail;
            } else {

                $logic_image = '';
            }

            $charr = [];
            $channels = explode(',', $val->channels);
            if (!empty($channels)) {
                # print_r($channels."=> ");
                $ch = 0;

                foreach ($channels as $channel) {

                    $this->db->select('cm.channel_master_id, cm.title, cm.logo');
                    $this->db->from('channel_master as cm');
                    $this->db->join('client_master as clientm', 'clientm.client_master_id = cm.channel_master_id', 'left');
                    $this->db->where(['cm.channel_master_id' => $channel, 'cm.status' => 3]);

                    $chresult = $this->db->get();

                    if ($chresult) {
                        $chresponse = $chresult->result_array();
                    }

                    // print_r($chresponse); exit;

                    foreach ($chresponse as $chval) {

                        $charr[$ch]['channel_id'] = $chval['channel_master_id'];
                        $charr[$ch]['channel_name'] = $chval['title'];
                        $charr[$ch]['channel_logo'] = change_img_src($chval['logo']);

                        $ch++;
                    }
                }
            }
            #print_r($val->type_id." => ".$charr);

            $sponarr = [];
            $specarr = [];

            $specialities = explode(',', $val->specialities);
            if (!empty($specialities)) {

                $s = 0;

                foreach ($specialities as $spec) {

                    $this->db->select('msv.master_specialities_id, msv.specialities_name');
                    $this->db->from('master_specialities_V1 as msv');
                    $this->db->where(['msv.master_specialities_id' => $spec]);

                    $specresult = $this->db->get();

                    if ($specresult) {
                        $specresponse = $specresult->result_array();
                    }

                    foreach ($specresponse as $specval) {
                        $specarr[$s]['id'] = $specval['master_specialities_id'];
                        $specarr[$s]['name'] = $specval['specialities_name'];
                    }

                    $s++;
                }
            }

            $sponsor = explode(',', $val->sponsors);
            if (!empty($sponsor)) {

                $splus = 0;

                foreach ($sponsor as $spon) {

                    $this->db->select('clientm.client_name, clientm.client_logo');
                    $this->db->from('client_master as clientm');
                    $this->db->where(['clientm.client_master_id' => $spon]);

                    $sponresult = $this->db->get();

                    if ($sponresult) {
                        $sponresponse = $sponresult->result_array();
                    }

                    foreach ($sponresponse as $sponval) {
                        $sponarr[$splus]['client_name'] = $sponval['client_name'];
                        $sponarr[$splus]['client_logo'] = change_img_src($sponval['client_logo']);
                    }

                    $splus++;
                }
            }
            if (!empty($charr)) {
                $vx[] = array(

                    "slno" => $i,
                    "type_id" => $val->type_id,
                    "type" => 'epub',
                    "date" => date(' jS F y', strtotime($val->publish_date)),
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "description_short" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "price" => $val->price,
                    "user_content_payment" => $val->user_contnet_payment_status,
                    //============ integrated for subscription ============//

                    "epub_file" => $val->epub_file,
                    "image" => change_img_src($logic_image),
                    "color" => ($val->color != '') ? $val->color : '#918c91',

                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src('' . $val->client_logo),
                    "sponsor_names_logos" => $sponarr,
                    "specialities_ids_and_names" => $specarr,
                    "channel" => $charr, //$this->channeldetail($val->type_id),

                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), // ($val->deeplink != '') ? $val->deeplink : 0,

                );
                # print_r($channel);
            }
        }
        $i++;
        return $vx;
        // }
    }
    public function trendingspq($limitTo, $limitFrom, $user_master_id)
    {
        // $env = get_user_env($user_master_id);

        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'survey');
        // if($key_locked == '') {
        //    return NULL;
        // }

        // ====================== fetching env_id  ======================//

        if ($limitFrom != '' and $limitTo != '') {

            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {
            $limit = "";
        }

        $sqlCompl = "SELECT 
        sv.* 
        
        FROM 
        survey_user_answer sv
        WHERE 
        sv.user_master_id = '" . $user_master_id . "'";
        $queryCompl = $this->db->query($sqlCompl);
        $resultCompl = $queryCompl->result();

        $complID = array();
        foreach ($resultCompl as $valCompl) {

            $complID[] = $valCompl->survey_id;
        }
        //print_r($complID); exit;
        $sqlInCompl = "SELECT 
        sv.* 
        
        FROM 
        survey_user_incomplete_answer sv
        WHERE 
        sv.status = 3
        and 
        sv.user_master_id = '" . $user_master_id . "'";
        // echo $sqlInCompl; exit;
        $queryInCompl = $this->db->query($sqlInCompl);
        $resultInCompl = $queryInCompl->result();

        $incomplID = array();

        foreach ($resultInCompl as $valInCompl) {

            $incomplID[] = $valInCompl->survey_id;
        }
        // print_r($incomplID); exit;
        $arrayFinal = array_filter(array_unique(array_merge($complID, $incomplID)));
        //print_r($arrayFinal); exit;
        $complIDStr = implode(",", (array)$arrayFinal);

        if ($complIDStr) {
            $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
        } else {
            $qryStr = '';
        }

        $sql = "SELECT 
            sv.* ,
            svd.data,

            cTenv.price,
            uTpyCont.status as user_contnet_payment_status,

            GROUP_CONCAT(DISTINCT svts.speciality_id) as specialities,
            GROUP_CONCAT(DISTINCT cts.channel_master_id) as channels,
            GROUP_CONCAT(DISTINCT suvTspon.sponsor_id) as sponsors,
            cln.client_name,
            cln.client_logo,
            
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
            
            FROM 
            survey sv
            JOIN channel_to_survey as cv ON cv.survey_id = sv.survey_id
            left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id          
            left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
            JOIN client_master as cln ON cln.client_master_id = sv.client_id
            
            
            
            LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
            LEFT JOIN channel_to_survey as cts ON cts.survey_id = sv.survey_id
            
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = sv.survey_id and  cTenv.type = 6
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = sv.survey_id and  uTpyCont.type = 6 and 	uTpyCont.user_master_id = " . $user_master_id . "
  
            
            JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
            left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
            WHERE 
            sv.status = 3 and
            sv.privacy_status = 0
            " . $qryStr . "
            " . $envStatus . "
            group by sv.survey_id order by publishing_date DESC " . $limit . "";
        //echo $sql; exit;
        $query = $this->db->query($sql);
        //print_r($this->db->last_query()); exit;
        //$this->db->cache_off();
        $result = $query->result_array();

        foreach ($result as $val) {
            $dataArry = unserialize($val['data']);

            // print_r($dataArry); exit;

            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);
            $charr = [];
            $specarr = [];
            $sponarr = [];
            $specialities = explode(',', $val['specialities']);
            if (!empty($specialities)) {

                $s = 0;

                foreach ($specialities as $spec) {

                    $this->db->select('msv.master_specialities_id, msv.specialities_name');
                    $this->db->from('master_specialities_V1 as msv');
                    $this->db->where(['msv.master_specialities_id' => $spec]);

                    $specresult = $this->db->get();

                    if ($specresult) {
                        $specresponse = $specresult->result_array();
                    }

                    foreach ($specresponse as $specval) {
                        $specarr[$s]['id'] = $specval['master_specialities_id'];
                        $specarr[$s]['name'] = $specval['specialities_name'];
                    }

                    $s++;
                }
            }

            $channels = explode(',', $val['channels']);
            if (!empty($channels)) {

                $ch = 0;

                foreach ($channels as $channel) {

                    $this->db->select('cm.channel_master_id, cm.title, cm.logo');
                    $this->db->from('channel_master as cm');
                    // $this->db->join('client_master as clientm', 'clientm.client_master_id = cm.channel_master_id', 'left');
                    $this->db->where(['cm.channel_master_id' => $channel]);

                    $chresult = $this->db->get();

                    if ($chresult) {
                        $chresponse = $chresult->result_array();
                    }

                    // print_r($chresponse); exit;

                    foreach ($chresponse as $chval) {
                        $charr[$ch]['channel_id'] = $chval['channel_master_id'];
                        $charr[$ch]['channel_name'] = $chval['title'];
                        $charr[$ch]['channel_logo'] = change_img_src($chval['logo']);

                        $ch++;
                    }
                }
            }

            $sponsor = explode(',', $val['sponsors']);
            if (!empty($sponsor)) {

                $splus = 0;

                foreach ($sponsor as $spon) {

                    $this->db->select('clientm.client_name, clientm.client_logo');
                    $this->db->from('client_master as clientm');
                    $this->db->where(['clientm.client_master_id' => $spon]);

                    $sponresult = $this->db->get();

                    if ($sponresult) {
                        $sponresponse = $sponresult->result_array();
                    }

                    foreach ($sponresponse as $sponval) {
                        $sponarr[$splus]['client_name'] = $sponval['client_name'];
                        $sponarr[$splus]['client_logo'] = change_img_src($sponval['client_logo']);
                    }

                    $splus++;
                }
            }
            $vx[] = array(

                "survey_id" => $val['survey_id'],
                "category" => $val['category'],

                "question_count" => $val['question_count'],
                "survey_time" => $val['survey_time'],

                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val['price'],
                "user_content_payment" => $val['user_contnet_payment_status'],
                //============ integrated for subscription ============//

                "point" => $val['survey_points'],
                "points_on_approval" => $val['points_on_approval'],
                "json_data" => $str,
                "survey_title" => $val['survey_title'],
                "deeplink" => ($env == 2) ? (($val['gl_deeplink'] != '') ? $val['gl_deeplink'] : 0) : (($val['deeplink'] != '') ? $val['deeplink'] : 0), //$val['deeplink'],
                "survey_description" => (strlen($val['survey_description']) > 150) ? html_entity_decode(substr($val['survey_description'], 0, 150)) . "..." : html_entity_decode($val['survey_description']),
                "image" => change_img_src($val['image']),
                "channels" => $charr,
                "specialities_ids_and_names" => $specarr,

                "client_name" => $val['client_name'],
                "client_logo" => change_img_src($val['client_logo']),

                "sponsor_names_and_logos" => $sponarr,
                // "sponsor_logo" => $sponsorLogo,
                "publishing_date" => $val['publishing_date'],

            );
        }

        return $vx;
    }

    public function mostviewedvideo($user_master_id, $client_ids)
    {

        $env = get_user_env($user_master_id);
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $env_arr = array(2, $env);
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $env_arr = array($env);
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'video_archived');

        //    if($key_locked == '') {
        //       return NULL;
        //    }

        // ====================== fetching env_id  ======================//

        // $sql ='SELECT video_archive_id, viewed, src, video_archive_question, video_archive_file_img, video_archive_answer, deeplink FROM knwlg_video_archive ORDER BY viewed desc limit 5';
        // $query = $this->db->query($sql);
        //$this->db->save_queries = TRUE;
        $this->db->select('kva.video_archive_id, cTenv.price,uTpyCont.status as user_contnet_payment_status, kva.viewed, kva.src, kva.video_archive_question, kva.video_archive_file_img, kva.video_archive_answer, kva.deeplink,kva.gl_deeplink,
                            GROUP_CONCAT(Distinct vats.specialities_id SEPARATOR ",") as specialities,
                            GROUP_CONCAT(Distinct vatspon.sponsor_id SEPARATOR ",") as sponsor,
                            GROUP_CONCAT(Distinct sts.sessions_doctors_id SEPARATOR ",") as doctors,
                            GROUP_CONCAT(Distinct ctva.channel_master_id SEPARATOR ",") as channels');
        $this->db->from('knwlg_video_archive as kva');
        $this->db->join('video_archive_to_specialities as vats', 'kva.video_archive_id = vats.video_archive_id', 'LEFT');
        $this->db->join('video_archive_to_sponsor as vatspon', 'vatspon.video_archive_id = kva.video_archive_id', 'LEFT');
        $this->db->join('content_to_env as cTenv', "cTenv.type_id = kva.video_archive_id  and  cTenv.type = 3", "left");
        $this->db->join('payment_user_to_content as uTpyCont', "uTpyCont.type_id = kva.video_archive_id  and  uTpyCont.type = 3", "left");
        $this->db->join('session_to_sessiondoctor as sts', 'sts.session_id = kva.video_archive_session_id', 'LEFT');
        $this->db->join('channel_to_video_archive as ctva', 'kva.video_archive_id = ctva.video_archive_id', 'LEFT');
        $this->db->join('channel_master as cm', 'cm.channel_master_id = ctva.channel_master_id', 'LEFT');
        $this->db->where(['kva.privacy_status' => 0, 'kva.status' => 3, 'cm.status' => 3]);
        $this->db->where_in('cTenv.env', $env_arr);
        $this->db->order_by('kva.viewed', 'desc');
        $this->db->group_by('vats.video_archive_id');
        $this->db->limit(5);
        // if ($limitTo != '' && $limitFrom != '') {
        //     $this->db->limit($limitTo, $limitFrom);
        // }

        $query = $this->db->get();
        //$this->db->save_queries = TRUE;
        // print_r($this->db->last_query());
        // exit;

        $response = [];
        $responsearr = [];
        $specarr = [];
        $sponarr = [];
        $docarr = [];
        $charr = [];

        if ($query) {
            $response = $query->result_array();

            $v = 0;
            foreach ($response as $vidval) {

                // echo $val['video_archive_id'].'----';
                $specialities = explode(',', $vidval['specialities']);
                if (!empty($specialities)) {

                    $s = 0;

                    foreach ($specialities as $spec) {

                        $this->db->select('msv.master_specialities_id, msv.specialities_name');
                        $this->db->from('master_specialities_V1 as msv');
                        $this->db->where(['msv.master_specialities_id' => $spec]);

                        $specresult = $this->db->get();

                        if ($specresult) {
                            $specresponse = $specresult->result_array();
                        }

                        foreach ($specresponse as $specval) {
                            $specarr[$s]['id'] = $specval['master_specialities_id'];
                            $specarr[$s]['name'] = $specval['specialities_name'];
                        }

                        $s++;
                    }
                }
                $sponsorresp = [];

                $sponsor = explode(',', $vidval['sponsor']);
                if (!empty($sponsor)) {

                    $splus = 0;

                    foreach ($sponsor as $spon) {
                        $sponarr = [];
                        //print_r($spon); //exit;
                        $this->db->select('clientm.client_name, clientm.client_logo');
                        $this->db->from('client_master as clientm');
                        $this->db->where(['clientm.client_master_id' => $spon]);

                        $sponresult = $this->db->get();

                        if ($sponresult) {
                            $sponresponse = $sponresult->result_array();
                        }

                        foreach ($sponresponse as $sponval) {
                            $sponarr[$splus]['client_name'] = $sponval['client_name'];
                            $sponarr[$splus]['client_logo'] = change_img_src($sponval['client_logo']);
                        }

                        $splus++;
                    }
                }

                $doctors = explode(',', $vidval['doctors']);
                if (!empty($doctors)) {

                    $d = 0;

                    foreach ($doctors as $doc) {

                        $this->db->select('ksd.sessions_doctors_id, ksd.doctor_name, ksd.profile_image, ksd.subtitle, ksd.profile');
                        $this->db->from('knwlg_sessions_doctors as ksd');
                        $this->db->where(['ksd.sessions_doctors_id' => $doc]);

                        $docresult = $this->db->get();

                        if ($docresult) {
                            $docresponse = $docresult->result_array();
                        }

                        foreach ($docresponse as $docval) {
                            $docarr[$d]['session_doctor_id'] = $docval['sessions_doctors_id'];
                            $docarr[$d]['session_doctor_name'] = $docval['doctor_name'];
                            $docarr[$d]['session_doctor_image'] =  change_img_src($docval['profile_image']);
                            $docarr[$d]['DepartmentName'] = $docval['subtitle'];
                            $docarr[$d]['profile'] = $docval['profile'];
                        }

                        $d++;
                    }
                }

                $channels = explode(',', $vidval['channels']);
                if (!empty($channels)) {

                    $ch = 0;

                    foreach ($channels as $channel) {

                        $this->db->select('cm.channel_master_id, cm.title, cm.logo');
                        $this->db->from('channel_master as cm');
                        // $this->db->join('client_master as clientm', 'clientm.client_master_id = cm.channel_master_id', 'left');
                        $this->db->where(['cm.channel_master_id' => $channel]);

                        $chresult = $this->db->get();

                        if ($chresult) {
                            $chresponse = $chresult->result_array();
                        }

                        // print_r($chresponse); exit;

                        foreach ($chresponse as $chval) {
                            $charr[$ch]['channel_id'] = $chval['channel_master_id'];
                            $charr[$ch]['channel_name'] = $chval['title'];
                            $charr[$ch]['channel_logo'] = change_img_src($chval['logo']);

                            $ch++;
                        }
                    }
                }

                $responsearr[$v]['trending_type'] = 'video';
                $responsearr[$v]['type_id'] = $vidval['video_archive_id'];
                $responsearr[$v]['src'] = $vidval['src'];
                $responsearr[$v]['question'] = $vidval['video_archive_question'];
                $responsearr[$v]['image'] = change_img_src($vidval['video_archive_file_img']);

                //============ integrated for subscription ============//
                $responsearr[$v]['is_locked'] = $key_locked;
                $responsearr[$v]['price'] = $vidval['price'];
                $responsearr[$v]['user_content_payment'] = get_user_content_status($vidval['video_archive_id'], 3, $user_master_id);
                //============ integrated for subscription ============//

                $responsearr[$v]['answer'] = $vidval['video_archive_answer'];
                $responsearr[$v]['deeplink'] = ($env == '2') ? (($vidval['gl_deeplink'] != '') ? $vidval['gl_deeplink'] : 0) : (($vidval['deeplink'] != '') ? $vidval['deeplink'] : 0); //$vidval['deeplink'] ;
                $responsearr[$v]['specialities_ids_and_names'] = $specarr;
                $responsearr[$v]['sponsor_names_and_logos'] = $sponarr; //$sponarr;
                $responsearr[$v]['session_doctor_entities'] = $docarr;
                $responsearr[$v]['Channels'] = $charr;

                $v++;

                //     $this->db->select('video_archive_id, MAX(viewed)');
                //     $this->db->from('knwlg_video_archive');
                //     // $this->db->join('knwlg_video_archive as kva', 'kva.video_archive_id = chva.video_archive_id', 'left');
                //     $this->db->where(['video_archive_id' => $val['video_archive_id']]);

            }
        }

        //print_r($responsearr); exit;
        return $responsearr;
    }

    public function upcomingsessions($user_master_id, $client_ids, $limitTo, $limitFrom)
    {
        $response = [];
        $getchildsession = $this->get_all_childsession();
        // $env = get_user_env($user_master_id);
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $env_arr = array(2, $env);
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $env_arr = array($env);
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'session');
        //    if($key_locked == '') {
        //       return NULL;
        //    }

        // ====================== fetching env_id  ======================//
        //$this->db->save_queries = TRUE;
        $this->db->distinct();
        $this->db->select('cts.session_id,  cTenv.price,  uTpyCont.status as user_contnet_payment_status , ksv.session_topic, ksv.session_description, ksv.cover_image, ksv.deeplink, ksv.color, ksv.session_status, 
                            ksv.start_datetime, ksv.end_datetime, ksv.deeplink,ksv.gl_deeplink,
                            GROUP_CONCAT(Distinct sts.specialities_id SEPARATOR ",") as specialities,
                            GROUP_CONCAT(Distinct stspon.sponsor_id SEPARATOR ",") as sponsor,
                            GROUP_CONCAT(Distinct stsd.sessions_doctors_id SEPARATOR ",") as doctors,
                            msc.category_name,ksv.is_multiday_session,stci.cover_image1,stci.cover_image2,stci.cover_image3,stci.cover_image4,stci.cover_image5');
        $this->db->from('channel_to_session as cts');
        $this->db->join('channel_master as cm', 'cm.channel_master_id = cts.channel_master_id', 'LEFT');
        $this->db->join('knwlg_sessions_V1 as ksv', 'ksv.session_id = cts.session_id', 'left');
        $this->db->join('session_to_cover_image as stci', 'stci.session_id = cts.session_id', 'left');
        $this->db->join('session_to_specialities as sts', 'sts.session_id = cts.session_id', 'LEFT');
        $this->db->join('session_to_sponsor as stspon', 'stspon.session_id = cts.session_id', 'LEFT');
        $this->db->join('content_to_env as cTenv', "cTenv.type_id = cts.channel_master_id and  cTenv.type = 2", "left");
        $this->db->join('payment_user_to_content as uTpyCont', "uTpyCont.type_id = cts.channel_master_id and  uTpyCont.type = 2", "left");
        $this->db->join('session_to_sessiondoctor as stsd', 'stsd.session_id = cts.session_id', 'LEFT');
        $this->db->join('master_session_category as msc', 'msc.mastersession_category_id = ksv.category_id', 'LEFT');
        $this->db->where(['cts.is_featured' => 1, 'ksv.privacy_status' => 0, 'ksv.status' => 3, 'cm.status' => 3]);
        $this->db->where_in('ksv.session_status', array(1, 2, 7));

        $this->db->where_in('cTenv.env', $env_arr);
        $this->db->where("ksv.start_datetime > CURDATE()");
        if (!empty($getchildsession)) {
            $this->db->where_not_in('ksv.session_id', $getchildsession['sessions']);
        }
        $this->db->group_by('cts.channel_master_id');
        if ($limitTo != '' && $limitFrom != '') {
            $this->db->limit($limitTo, $limitFrom);
        }

        $result = $this->db->get();

        // $this->db->save_queries = TRUE;
        //echo $this->db->last_query(); exit;

        if ($result) {
            $response = $result->result_array();
        }

        $specarr = [];
        $sponarr = [];
        $docarr = [];
        $charr = [];
        $v = 0;

        foreach ($response as $val) {

            $this->db->select('chts.channel_master_id, cm.title, cm.logo');
            $this->db->from('channel_to_session as chts');
            $this->db->join('channel_master as cm', 'cm.channel_master_id = chts.channel_master_id', 'left');
            $this->db->where(['chts.session_id' => $val['session_id'], 'chts.is_featured' => 1]);

            $chresult = $this->db->get();

            if ($chresult) {
                $chresponse = $chresult->result_array();
            }

            // print_r($chresponse); exit;

            $ch = 0;

            foreach ($chresponse as $chval) {
                $charr[$ch]['channel_id'] = $chval['channel_master_id'];
                $charr[$ch]['channel_name'] = $chval['title'];
                $charr[$ch]['channel_logo'] = change_img_src($chval['logo']);

                $ch++;
            }
            $specialities = explode(',', $val['specialities']);
            if (!empty($specialities)) {

                $s = 0;

                foreach ($specialities as $spec) {

                    $this->db->select('msv.master_specialities_id, msv.specialities_name');
                    $this->db->from('master_specialities_V1 as msv');
                    $this->db->where(['msv.master_specialities_id' => $spec]);

                    $specresult = $this->db->get();

                    if ($specresult) {
                        $specresponse = $specresult->result_array();
                    }

                    foreach ($specresponse as $specval) {
                        $specarr[$s]['id'] = $specval['master_specialities_id'];
                        $specarr[$s]['name'] = $specval['specialities_name'];
                    }

                    $s++;
                }
            }

            $sponsor = explode(',', $val['sponsor']);
            if (!empty($sponsor)) {

                $splus = 0;

                foreach ($sponsor as $spon) {

                    $this->db->select('clientm.client_name, clientm.client_logo');
                    $this->db->from('client_master as clientm');
                    $this->db->where(['clientm.client_master_id' => $spon]);

                    $sponresult = $this->db->get();

                    if ($sponresult) {
                        $sponresponse = $sponresult->result_array();
                    }

                    foreach ($sponresponse as $sponval) {
                        $sponarr[$splus]['client_name'] = $sponval['client_name'];
                        $sponarr[$splus]['client_logo'] = change_img_src($sponval['client_logo']);
                    }

                    $splus++;
                }
            }
            if ($val['doctors'] != '') {
                $doctors = explode(",", $val['doctors']);
            } else {
                //print_r($getchildsession['session_doctors']);
                $doctors = explode(",", $getchildsession['session_doctors'][$row['session_id']]);
                //print_r($session_doc_array); exit;
            }
            //$doctors = explode(',',$val['doctors']);
            if (!empty($doctors)) {

                $d = 0;

                foreach ($doctors as $doc) {

                    $this->db->select('ksd.sessions_doctors_id, ksd.doctor_name, ksd.profile_image, ksd.subtitle, ksd.profile');
                    $this->db->from('knwlg_sessions_doctors as ksd');
                    $this->db->where(['ksd.sessions_doctors_id' => $doc]);

                    $docresult = $this->db->get();

                    if ($docresult) {
                        $docresponse = $docresult->result_array();
                    }

                    foreach ($docresponse as $docval) {
                        $docarr[$d]['session_doctor_id'] = $docval['sessions_doctors_id'];
                        $docarr[$d]['session_doctor_name'] = $docval['doctor_name'];
                        $docarr[$d]['session_doctor_image'] = change_img_src($docval['profile_image']);
                        $docarr[$d]['DepartmentName'] = $docval['subtitle'];
                        $docarr[$d]['profile'] = $docval['profile'];
                    }

                    $d++;
                    $store_total_doctors[$val->session_id] = $d++;
                }
            }
            $k = array_keys((array)$getchildsession['sessioncount']);
            //print_r(in_array($val->session_id,$k));

            if (in_array($val['session_id'], $k)) {

                $total_multiday_session = $getchildsession['sessioncount'][$val['session_id']];
                // print_R($total_multiday_session."/n");
            } else {
                $total_multiday_session = 0;
            }
            $keyvalue_1 = array_keys((array)$getchildsession['doctorcount']);
            if (in_array($val['session_id'], $keyvalue_1)) {
                $total_doctors = $getchildsession['doctorcount'][$val['session_id']];
            } else {
                $total_doctors = $store_total_doctors[$val['session_id']];
            }

            $datetime1 = new DateTime(date('Y-m-d H:i:s'));

            $datetime2 = new DateTime($val->start_datetime);

            $difference = $datetime1->diff($datetime2);

            $responsearr[$v]['trending_type'] = 'session';
            $responsearr[$v]['session_id'] = $val['session_id'];
            $responsearr[$v]['is_multiday_session'] = $val['is_multiday_session'];
            $responsearr[$v]['total_session'] = $total_multiday_session;
            $responsearr[$v]['total_doctors'] = $total_doctors;
            $responsearr[$v]['total_days'] = $difference->d;
            $responsearr[$v]['session_topic'] = $val['session_topic'];
            $responsearr[$v]['session_description'] = $val['session_description'];
            $responsearr[$v]['cover_image'] = change_img_src($val['cover_image']);
            $responsearr[$v]['cover_image1'] = change_img_src($val['cover_image1']);
            $responsearr[$v]['cover_image2'] = change_img_src($val['cover_image2']);
            $responsearr[$v]['cover_image3'] = change_img_src($val['cover_image3']);
            $responsearr[$v]['cover_image4'] = change_img_src($val['cover_image4']);
            $responsearr[$v]['cover_image5'] = change_img_src($val['cover_image5']);
            $responsearr[$v]['deeplink'] = ($env == 2) ? (($val['gl_deeplink'] != '') ? $val['gl_deeplink'] : 0) : (($val['deeplink'] != '') ? $val['deeplink'] : 0); //$val['deeplink'] ;
            $responsearr[$v]['client_name'] = $val['client_name'];
            ;
            $responsearr[$v]['ms_cat_name'] = $val['category_name'];
            $responsearr[$v]['color'] = $val['color'];
            $responsearr[$v]['session_status'] = $val['session_status'];
            $responsearr[$v]['channel'] = $charr;

            $responsearr[$v]['is_locked'] = $key_locked;
            $responsearr[$v]['price'] = $val['price'];
            $responsearr[$v]['user_content_payment'] = $val['user_contnet_payment_status'];

            $responsearr[$v]['specialities_ids_and_names'] = $specarr;
            $responsearr[$v]['sponsor_names_and_logos'] = $sponarr;
            $responsearr[$v]['session_doctor_entities'] = $docarr;
            $responsearr[$v]['start_datetime'] = $val['start_datetime'];
            $responsearr[$v]['end_datetime'] = $val['end_datetime'];
            $responsearr[$v]['cpddetail'] = $this->getcpddetails($val['session_id']);
        }
        return $responsearr;
        // print_r($responsearr); exit;
    }

    public function get_all_childsession()
    {
        $ids = array();
        $this->db->select("kstc.multidaysession_id,GROUP_CONCAT(DISTINCT kstc.childsession_id SEPARATOR ',') as childsession_id,GROUP_CONCAT(DISTINCT sts.sessions_doctors_id SEPARATOR ',') as session_soctor_id");
        $this->db->from('knwlg_session_to_child as kstc');
        $this->db->join('session_to_sessiondoctor as sts', 'sts.session_id = kstc.childsession_id', 'left');
        $this->db->group_by('kstc.multidaysession_id');

        $query = $this->db->get();
        //print_r($this->db->last_query()); exit;
        $ids = array();
        $getdoctors = array();
        if (($query) && ($query->num_rows() > 0)) {
            $session_ids = $query->result();
            foreach ($session_ids as $key => $value) {
                // print_R($value);
                $totalids = explode(',', $value->childsession_id);
                $getids[$value->multidaysession_id] = count(explode(',', $value->childsession_id)); //$value->childsession_id;
                $getdoctorcount[$value->multidaysession_id] = count(explode(',', $value->session_soctor_id));
                $getdoctors[$value->multidaysession_id] = $value->session_soctor_id;
                $ids = array_merge($ids, $totalids); //$value->childsession_id;
                // $countmultidayminisession[$value->multidaysession_id] = sizeof($getids[$value->multidaysession_id]);
            }
        }
        $response['doctorcount'] = $getdoctorcount;
        $response['sessioncount'] = $getids; //$countmultidayminisession;
        $response['sessions'] = $ids;
        $response['session_doctors'] = $getdoctors;
        //print_R($response); exit;
        return $response;
    }

    public function featured_channel(
        $user_master_id,
        $client_ids,
        $limitTo,
        $limitFrom
    ) {
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $env_arr = array(2, $env);
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $env_arr = array($env);
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'channel');
        //    if($key_locked == '') {
        //       return NULL;
        //    }

        // ====================== fetching env_id  ======================//
        $response = [];
        if (($limitTo != '') && ($limitFrom != '')) {
            $limit = "limit " . $limitFrom . "," . $limitTo;
        } else {
            $limit = "limit 0,5";
        }
        // echo $limit; exit;
        $cache_key = "channel_featured_" . $env;
        //echo $cache_key; exit;
        if ($this->myredis->exists($cache_key)) {
            $result = $this->myredis->get($cache_key);
            // echo "cache";
            // echo "<pre>";print_r($result);die;
        } else {
            $sql = 'SELECT 
            cm.channel_master_id, 
            cTenv.price, 
            uTpyCont.status AS user_content_payment_status,
            cm.privacy_status, 
            cm.added_on, 
            cm.address, 
            cm.follower_count, 
            cm.title, 
            cm.cover_image,
            cm.logo, 
            cm.description, 
            GROUP_CONCAT(DISTINCT cts.specialities_id SEPARATOR ", ") AS specialities,
            GROUP_CONCAT(DISTINCT ctva.video_archive_id SEPARATOR ", ") AS videos, 
            GROUP_CONCAT(DISTINCT ctc.comp_qa_id SEPARATOR ", ") AS comps, 
            GROUP_CONCAT(DISTINCT ctsess.session_id SEPARATOR ", ") AS sessions, 
            GROUP_CONCAT(DISTINCT ctsurv.survey_id SEPARATOR ", ") AS surveys, 
            cm.deeplink, 
            cm.gl_deeplink, 
            clm.client_logo, 
            (
                SELECT COUNT(DISTINCT ctu.user_master_id) 
                FROM channel_to_user ctu 
                WHERE ctu.status = 3 AND ctu.channel_master_id = cm.channel_master_id
            ) AS total
        FROM 
            channel_master AS cm
        LEFT JOIN 
            channel_to_specialities AS cts ON cm.channel_master_id = cts.channel_master_id
        LEFT JOIN 
            channel_to_video_archive AS ctva ON cm.channel_master_id = ctva.channel_master_id AND ctva.is_featured = 1
        LEFT JOIN 
            channel_to_compendium AS ctc ON cm.channel_master_id = ctc.channel_master_id AND ctc.is_featured = 1
        LEFT JOIN 
            channel_to_session AS ctsess ON cm.channel_master_id = ctsess.channel_master_id AND ctsess.is_featured = 1
        LEFT JOIN 
            channel_to_survey AS ctsurv ON cm.channel_master_id = ctsurv.channel_master_id AND ctsurv.is_featured = 1
        LEFT JOIN 
            client_master AS clm ON clm.client_master_id = cm.client_id
        LEFT JOIN 
            content_to_env AS cTenv ON cTenv.type_id = cm.channel_master_id AND cTenv.type = 11
        LEFT JOIN 
            payment_user_to_content AS uTpyCont ON uTpyCont.type_id = cm.channel_master_id AND uTpyCont.type = 11
        LEFT JOIN 
            channel_to_user AS ctu ON (ctu.channel_master_id = cm.channel_master_id AND ctu.status = 3)
        WHERE 
            cm.status = 3
            AND cm.is_featured = 1
            AND cm.privacy_status = 0
            ' . $envStatus . '
        GROUP BY 
            cm.channel_master_id, 
            cTenv.price, 
            uTpyCont.status, 
            cm.privacy_status, 
            cm.added_on, 
            cm.address, 
            cm.follower_count, 
            cm.title, 
            cm.cover_image, 
            cm.logo, 
            cm.description, 
            cm.deeplink, 
            cm.gl_deeplink, 
            clm.client_logo
        ORDER BY 
            cm.channel_master_id DESC ' . $limit;
            // echo $sql;
            // exit;
            $result =  $this->db->query($sql)->result_array();

            $this->myredis->set($cache_key, $result);
            // echo "<pre>";print_r($result);die;
        }
        if ($result) {
            $response = $result;
        }

        $c = 0;
        $slno = 1;
        $data = [];
        foreach ($response as $val) {
            if ($slno <= $limitTo) {
                // $data[$c]['videos']=$val['videos'];
                $responsearr = [];
                $v = 0;
                // echo $val['comps']; exit;

                $videos = explode(',', $val['videos']);
                if (!empty($videos)) {

                    // $v=0;
                    foreach ($videos as $vid) {
                        $cache_chan_vid = "channel_featured_vid_cache_" . $vid;

                        if ($this->myredis->exists($cache_chan_vid)) {
                            $vidresponse = $this->myredis->get($cache_chan_vid);
                        } else {

                            $this->db->select('kva.video_archive_id, kva.src, kva.video_archive_question, kva.video_archive_file_img, kva.video_archive_answer, 
                                            GROUP_CONCAT(Distinct vats.specialities_id SEPARATOR ",") as specialities, kva.deeplink,kva.gl_deeplink,
                                            GROUP_CONCAT(Distinct vatspon.sponsor_id SEPARATOR ",") as sponsor,
                                            GROUP_CONCAT(Distinct sts.sessions_doctors_id SEPARATOR ",") as doctors');
                            $this->db->from('knwlg_video_archive as kva');
                            $this->db->join('video_archive_to_specialities as vats', 'kva.video_archive_id = vats.video_archive_id', 'LEFT');
                            $this->db->join('video_archive_to_sponsor as vatspon', 'vatspon.video_archive_id = kva.video_archive_id', 'LEFT');
                            $this->db->join('session_to_sessiondoctor as sts', 'sts.session_id = kva.video_archive_session_id', 'LEFT');
                            $this->db->where(['kva.status' => 3, 'kva.video_archive_id' => $vid]);
                            $this->db->group_by('vats.video_archive_id');
                            // if ($limitTo != '' && $limitFrom != '') {
                            //     $this->db->limit($limitFrom, $limitTo);
                            // }

                            $vidresult = $this->db->get();

                            // echo $this->db->last_query(); exit;

                            if ($vidresult) {
                                $vidresponse = $vidresult->result_array();
                            }

                            $this->myredis->set($cache_chan_vid, $vidresponse);
                        }
                        $specarr = [];
                        $sponarr = [];
                        $docarr = [];

                        foreach ($vidresponse as $vidval) {

                            $specialities = explode(',', $vidval['specialities']);
                            if (!empty($specialities)) {

                                $s = 0;

                                foreach ($specialities as $spec) {

                                    $cache_chan_vid_spec = "channel_featured_vid_spec_cache_" . $spec;

                                    if ($this->myredis->exists($cache_chan_vid_spec)) {
                                        $specresponse  = $this->myredis->get($cache_chan_vid_spec);
                                    } else {
                                        $this->db->select('msv.master_specialities_id, msv.specialities_name');
                                        $this->db->from('master_specialities_V1 as msv');
                                        $this->db->where(['msv.master_specialities_id' => $spec]);

                                        $specresult = $this->db->get();

                                        if ($specresult) {
                                            $specresponse = $specresult->result_array();
                                        }

                                        $this->myredis->set($cache_chan_vid_spec, $specresponse);
                                    }
                                    foreach ($specresponse as $specval) {
                                        $specarr[$s]['id'] = $specval['master_specialities_id'];
                                        $specarr[$s]['name'] = $specval['specialities_name'];
                                    }

                                    $s++;
                                }
                            }

                            $sponsor = explode(',', $vidval['sponsor']);
                            if (!empty($sponsor)) {

                                $splus = 0;

                                foreach ($sponsor as $spon) {
                                    $cache_chan_vid_sponsor = "channel_featured_vid_sponsor_cache_" . $spon;

                                    if ($this->myredis->exists($cache_chan_vid_sponsor)) {
                                        $sponresponse  = $this->myredis->get($cache_chan_vid_sponsor);
                                    } else {
                                        $this->db->select('clientm.client_name, clientm.client_logo');
                                        $this->db->from('client_master as clientm');
                                        $this->db->where(['clientm.client_master_id' => $spon]);

                                        $sponresult = $this->db->get();

                                        if ($sponresult) {
                                            $sponresponse = $sponresult->result_array();
                                        }
                                        $this->myredis->set($cache_chan_vid_sponsor, $sponresponse);
                                    }

                                    foreach ($sponresponse as $sponval) {
                                        $sponarr[$splus]['client_name'] = $sponval['client_name'];
                                        $sponarr[$splus]['client_logo'] = change_img_src($sponval['client_logo']);
                                    }

                                    $splus++;
                                }
                            }

                            $doctors = explode(',', $vidval['doctors']);
                            if (!empty($doctors)) {

                                $d = 0;

                                foreach ($doctors as $doc) {
                                    $cache_chan_vid_sesson_doc = "channel_featured_vid_session_doctor_cache_" . $doc;

                                    if ($this->myredis->exists($cache_chan_vid_sesson_doc)) {
                                        $docresponse  = $this->myredis->get($cache_chan_vid_sesson_doc);
                                    } else {
                                        $this->db->select('ksd.sessions_doctors_id, ksd.doctor_name, ksd.profile_image, ksd.subtitle, ksd.profile');
                                        $this->db->from('knwlg_sessions_doctors as ksd');
                                        $this->db->where(['ksd.sessions_doctors_id' => $doc]);

                                        $docresult = $this->db->get();

                                        if ($docresult) {
                                            $docresponse = $docresult->result_array();
                                        }
                                        $this->myredis->set($cache_chan_vid_sesson_doc, $docresponse);
                                    }
                                    foreach ($docresponse as $docval) {
                                        $docarr[$d]['session_doctor_id'] = $docval['sessions_doctors_id'];
                                        $docarr[$d]['session_doctor_name'] = $docval['doctor_name'];
                                        $docarr[$d]['session_doctor_image'] = change_img_src($docval['profile_image']);
                                        $docarr[$d]['DepartmentName'] = $docval['subtitle'];
                                        $docarr[$d]['profile'] = $docval['profile'];
                                    }

                                    $d++;
                                }
                            }
                            $responsearr[$v]['trending_type'] = 'video';
                            $responsearr[$v]['type_id'] = $vidval['video_archive_id'];
                            $responsearr[$v]['src'] = $vidval['src'];
                            $responsearr[$v]['question'] = $vidval['video_archive_question'];
                            $responsearr[$v]['image'] = change_img_src($vidval['video_archive_file_img']);
                            $responsearr[$v]['deeplink'] = ($env == 'GL') ? (($vidval['gl_deeplink'] != '') ? $vidval['gl_deeplink'] : 0) : (($vidval['deeplink'] != '') ? $vidval['deeplink'] : 0); //$vidval['deeplink'] ;
                            $responsearr[$v]['answer'] = $vidval['video_archive_answer'];
                            $responsearr[$v]['specialities_ids_and_names'] = $specarr;
                            $responsearr[$v]['sponsor_names_and_logos'] = $sponarr;
                            $responsearr[$v]['session_doctor_entities'] = $docarr;

                            $v++;
                        }
                    }
                }

                $comps = explode(',', $val['comps']);
                if (!empty($comps)) {

                    // $v=0;
                    foreach ($comps as $comp) {

                        $cache_chan_comp = "channel_featured_comp_cache_" . $comp;

                        if ($this->myredis->exists($cache_chan_comp)) {
                            $compresponse = $this->myredis->get($cache_chan_comp);
                        } else {

                            $this->db->select('kcv.comp_qa_id, kcv.publication_date, kcv.comp_qa_question, kcv.comp_qa_file_img, kcv.color, kcv.comp_qa_answer, 
                                                GROUP_CONCAT(Distinct cts.specialities_id SEPARATOR ",") as specialities, kcv.deeplink,kcv.gl_deeplink, 
                                                GROUP_CONCAT(Distinct ctspon.sponsor_id SEPARATOR ",") as sponsor');
                            $this->db->from('knwlg_compendium_V1 as kcv');
                            $this->db->join('compendium_to_specialities as cts', 'kcv.comp_qa_id = cts.comp_qa_id', 'LEFT');
                            $this->db->join('compendium_to_sponsor as ctspon', 'ctspon.comp_qa_id = kcv.comp_qa_id', 'LEFT');
                            // $this->db->join('session_to_sessiondoctor as sts', 'sts.session_id = kva.video_archive_session_id', 'LEFT');
                            $this->db->where(['kcv.status' => 3, 'kcv.comp_qa_id' => $comp]);
                            $this->db->group_by('cts.comp_qa_id');
                            // if ($limitTo != '' && $limitFrom != '') {
                            //     $this->db->limit($limitFrom, $limitTo);
                            // }

                            $compresult = $this->db->get();

                            // echo $this->db->last_query(); exit;

                            if ($compresult) {
                                $compresponse = $compresult->result_array();
                            }
                            $this->myredis->set($cache_chan_comp, $compresponse);
                        }
                        $specarr = [];
                        $sponarr = [];
                        // $docarr = [];

                        foreach ($compresponse as $compval) {

                            $specialities = explode(',', $compval['specialities']);
                            if (!empty($specialities)) {

                                $s = 0;

                                foreach ($specialities as $spec) {
                                    $cache_chan_comp_spec = "channel_featured_comp_spec_cache_" . $spec;

                                    if ($this->myredis->exists($cache_chan_comp_spec)) {
                                        $specresponse = $this->myredis->get($cache_chan_comp_spec);
                                    } else {

                                        $this->db->select('msv.master_specialities_id, msv.specialities_name');
                                        $this->db->from('master_specialities_V1 as msv');
                                        $this->db->where(['msv.master_specialities_id' => $spec]);

                                        $specresult = $this->db->get();

                                        if ($specresult) {
                                            $specresponse = $specresult->result_array();
                                        }
                                        $this->myredis->set($cache_chan_comp_spec, $specresponse);
                                    }

                                    foreach ($specresponse as $specval) {
                                        $specarr[$s]['id'] = $specval['master_specialities_id'];
                                        $specarr[$s]['name'] = $specval['specialities_name'];
                                    }

                                    $s++;
                                }
                            }

                            $sponsor = explode(',', $compval['sponsor']);
                            if (!empty($sponsor)) {

                                $splus = 0;

                                foreach ($sponsor as $spon) {
                                    $cache_chan_comp_sponsor = "channel_featured_comp_sponsor_cache_" . $spon;

                                    if ($this->myredis->exists($cache_chan_comp_sponsor)) {
                                        $sponresponse = $this->myredis->get($cache_chan_comp_sponsor);
                                    } else {

                                        $this->db->select('clientm.client_name, clientm.client_logo');
                                        $this->db->from('client_master as clientm');
                                        $this->db->where(['clientm.client_master_id' => $spon]);

                                        $sponresult = $this->db->get();

                                        if ($sponresult) {
                                            $sponresponse = $sponresult->result_array();
                                        }

                                        $this->myredis->set($cache_chan_comp_sponsor, $sponresponse);
                                    }

                                    foreach ($sponresponse as $sponval) {
                                        $sponarr[$splus]['client_name'] = $sponval['client_name'];
                                        $sponarr[$splus]['client_logo'] = change_img_src($sponval['client_logo']);
                                    }

                                    $splus++;
                                }
                            }

                            $responsearr[$v]['trending_type'] = 'comp';
                            $responsearr[$v]['con_type'] = 'text';
                            $responsearr[$v]['type_id'] = $compval['comp_qa_id'];
                            $responsearr[$v]['date'] = $compval['publication_date'];
                            $responsearr[$v]['question'] = $compval['comp_qa_question'];
                            $responsearr[$v]['image'] = change_img_src($compval['comp_qa_file_img']);
                            $responsearr[$v]['color'] = $compval['color'];
                            $responsearr[$v]['answer'] = $compval['comp_qa_answer'];
                            $responsearr[$v]['specialities_ids_and_names'] = $specarr;
                            $responsearr[$v]['sponsor_names_and_logos'] = $sponarr;
                            $responsearr[$v]['deeplink'] = ($env == 'GL') ? (($compval['gl_deeplink'] != '') ? $compval['gl_deeplink'] : 0) : (($compval['deeplink'] != '') ? $compval['deeplink'] : 0); //$compval['deeplink'] ;

                            $v++;
                        }

                        // $v++;

                    }
                }

                // echo $val['sessions']; exit;

                // echo $v; exit;

                $sessions = explode(',', $val['sessions']);
                if (!empty($sessions)) {

                    // $v=0;
                    foreach ($sessions as $sess) {

                        $cache_chan_session = "channel_featured_session_cache_" . $sess;

                        if ($this->myredis->exists($cache_chan_session)) {
                            $sessresponse = $this->myredis->get($cache_chan_session);
                        } else {

                            $this->db->select('ksv.session_id, ksv.session_topic, ksv.session_description, ksv.cover_image, ksv.deeplink, ksv.color, 
                                            ksv.session_status, ksv.start_datetime, ksv.end_datetime, 
                                            GROUP_CONCAT(Distinct sts.specialities_id SEPARATOR ",") as specialities, ksv.deeplink,ksv.gl_deeplink,
                                            GROUP_CONCAT(Distinct stspon.sponsor_id SEPARATOR ",") as sponsor, msc.category_name,clientm.client_name,
                                            GROUP_CONCAT(Distinct stsd.sessions_doctors_id SEPARATOR ",") as doctors');
                            $this->db->from('knwlg_sessions_V1 as ksv');
                            $this->db->join('session_to_specialities as sts', 'ksv.session_id = sts.session_id', 'LEFT');
                            $this->db->join('session_to_sponsor as stspon', 'stspon.session_id = ksv.session_id', 'LEFT');
                            $this->db->join('master_session_category as msc', 'msc.mastersession_category_id = ksv.category_id', 'LEFT');
                            $this->db->join('client_master as clientm', 'clientm.client_master_id = ksv.client_id', 'LEFT');
                            $this->db->join('session_to_sessiondoctor as stsd', 'stsd.session_id = ksv.session_id', 'LEFT');
                            $this->db->where(['ksv.status' => 3, 'ksv.session_id' => $sess]);
                            $this->db->group_by('sts.session_id');

                            // if ($limitTo != '' && $limitFrom != '') {
                            //     $this->db->limit($limitFrom, $limitTo);
                            // }

                            $sessresult = $this->db->get();

                            // echo $this->db->last_query(); exit;

                            if ($sessresult) {
                                $sessresponse = $sessresult->result_array();
                            }
                            $this->myredis->set($cache_chan_session, $sessresponse);
                        }

                        // print_r($sessresponse); exit;

                        $specarr = [];
                        $sponarr = [];
                        // $docarr = [];

                        foreach ($sessresponse as $sessval) {

                            $specialities = explode(',', $sessval['specialities']);
                            if (!empty($specialities)) {

                                $s = 0;

                                foreach ($specialities as $spec) {

                                    $cache_chan_session_spec = "channel_featured_session_spec_cache_" . $spec;

                                    if ($this->myredis->exists($cache_chan_session_spec)) {
                                        $specresponse = $this->myredis->get($cache_chan_session_spec);
                                    } else {

                                        $this->db->select('msv.master_specialities_id, msv.specialities_name');
                                        $this->db->from('master_specialities_V1 as msv');
                                        $this->db->where(['msv.master_specialities_id' => $spec]);

                                        $specresult = $this->db->get();

                                        if ($specresult) {
                                            $specresponse = $specresult->result_array();
                                        }
                                        $this->myredis->set($cache_chan_session_spec, $specresponse);
                                    }

                                    foreach ($specresponse as $specval) {
                                        $specarr[$s]['id'] = $specval['master_specialities_id'];
                                        $specarr[$s]['name'] = $specval['specialities_name'];
                                    }

                                    $s++;
                                }
                            }

                            $sponsor = explode(',', $sessval['sponsor']);
                            if (!empty($sponsor)) {

                                $splus = 0;

                                foreach ($sponsor as $spon) {

                                    $cache_chan_session_spon = "channel_featured_session_sponsor_cache_" . $spon;

                                    if ($this->myredis->exists($cache_chan_session_spon)) {
                                        $sponresponse = $this->myredis->get($cache_chan_session_spon);
                                    } else {
                                        $this->db->select('clientm.client_name, clientm.client_logo');
                                        $this->db->from('client_master as clientm');
                                        $this->db->where(['clientm.client_master_id' => $spon]);

                                        $sponresult = $this->db->get();

                                        if ($sponresult) {
                                            $sponresponse = $sponresult->result_array();
                                        }

                                        $this->myredis->set($cache_chan_session_spon, $sponresponse);
                                    }

                                    foreach ($sponresponse as $sponval) {
                                        $sponarr[$splus]['client_name'] = $sponval['client_name'];
                                        $sponarr[$splus]['client_logo'] = change_img_src($sponval['client_logo']);
                                    }

                                    $splus++;
                                }
                            }

                            $doctors = explode(',', $vidval['doctors']);
                            if (!empty($doctors)) {

                                $d = 0;

                                foreach ($doctors as $doc) {
                                    $cache_chan_session_session_doc = "channel_featured_session_sponsor_cache_" . $doc;

                                    if ($this->myredis->exists($cache_chan_session_session_doc)) {
                                        $docresponse = $this->myredis->get($cache_chan_session_session_doc);
                                    } else {

                                        $this->db->select('ksd.sessions_doctors_id, ksd.doctor_name, ksd.profile_image, ksd.subtitle, ksd.profile');
                                        $this->db->from('knwlg_sessions_doctors as ksd');
                                        $this->db->where(['ksd.sessions_doctors_id' => $doc]);

                                        $docresult = $this->db->get();

                                        if ($docresult) {
                                            $docresponse = $docresult->result_array();
                                        }
                                        $this->myredis->set($cache_chan_session_session_doc, $sponresponse);
                                    }

                                    foreach ($docresponse as $docval) {
                                        $docarr[$d]['session_doctor_id'] = $docval['sessions_doctors_id'];
                                        $docarr[$d]['session_doctor_name'] = $docval['doctor_name'];
                                        $docarr[$d]['session_doctor_image'] = change_img_src($docval['profile_image']);
                                        $docarr[$d]['DepartmentName'] = $docval['subtitle'];
                                        $docarr[$d]['profile'] = $docval['profile'];
                                    }

                                    $d++;
                                }
                            }

                            $responsearr[$v]['trending_type'] = 'session';
                            $responsearr[$v]['session_id'] = $sessval['session_id'];
                            $responsearr[$v]['session_topic'] = $sessval['session_topic'];
                            $responsearr[$v]['session_description'] = $sessval['session_description'];
                            $responsearr[$v]['cover_image'] = change_img_src($sessval['cover_image']);
                            $responsearr[$v]['deeplink'] = ($env == 'GL') ? (($sessval['gl_deeplink'] != '') ? $sessval['gl_deeplink'] : 0) : (($sessval['deeplink'] != '') ? $sessval['deeplink'] : 0); //$sessval['deeplink'] ;
                            $responsearr[$v]['client_name'] = $sessval['client_name'];
                            ;
                            $responsearr[$v]['ms_cat_name'] = $sessval['category_name'];
                            $responsearr[$v]['color'] = $sessval['color'];
                            $responsearr[$v]['session_status'] = $sessval['session_status'];
                            $responsearr[$v]['specialities_ids_and_names'] = $specarr;
                            $responsearr[$v]['sponsor_names_and_logos'] = $sponarr;
                            $responsearr[$v]['session_doctor_entities'] = $docarr;
                            $responsearr[$v]['start_datetime'] = $sessval['start_datetime'];
                            $responsearr[$v]['end_datetime'] = $sessval['end_datetime'];

                            $v++;
                        }

                        // $v++;

                    }
                }

                // echo $val['surveys']; exit;

                $surveys = explode(',', $val['surveys']);
                if (!empty($surveys)) {

                    // print_r($surveys); exit;
                    // $v=0;
                    foreach ($surveys as $surv) {
                        $cache_chan_survey = "channel_featured_survey_" . $surv;

                        if ($this->myredis->exists($cache_chan_survey)) {
                            $survresponse = $this->myredis->get($cache_chan_survey);
                        } else {

                            $this->db->select('surv.survey_id, surv.category, surv.question_count, surv.survey_time, surv.survey_points, surv.points_on_approval, 
                                            surv.survey_title, surv.deeplink,surv.gl_deeplink, surv.survey_description, surv.image, surv.publishing_date, 
                                            GROUP_CONCAT(Distinct sts.speciality_id SEPARATOR ",") as specialities,
                                            GROUP_CONCAT(Distinct stspon.sponsor_id SEPARATOR ",") as sponsor');
                            $this->db->from('survey as surv');
                            $this->db->join('survey_to_speciality as sts', 'surv.survey_id = sts.survey_id', 'LEFT');
                            $this->db->join('survey_to_sponsor as stspon', 'surv.survey_id = stspon.survey_id', 'LEFT');
                            // $this->db->join('master_session_category as msc', 'msc.mastersession_category_id = ksv.category_id', 'LEFT');
                            // $this->db->join('client_master as clientm', 'clientm.client_master_id = ksv.client_id', 'LEFT');
                            // $this->db->join('session_to_sessiondoctor as stsd', 'stsd.session_id = ksv.session_id', 'LEFT');
                            $this->db->where(['surv.status' => 3, 'surv.survey_id' => $surv]);
                            $this->db->group_by('surv.survey_id');

                            // if ($limitTo != '' && $limitFrom != '') {
                            //     $this->db->limit($limitFrom, $limitTo);
                            // }

                            $survresult = $this->db->get();

                            // echo $this->db->last_query(); exit;

                            if ($survresult) {
                                $survresponse = $survresult->result_array();
                            }

                            $this->myredis->set($cache_chan_survey, $survresponse);
                        }

                        // print_r($survresponse); exit;

                        $specarr = [];
                        $sponarr = [];
                        // $docarr = [];

                        foreach ($survresponse as $survval) {

                            $specialities = explode(',', $survval['specialities']);
                            if (!empty($specialities)) {

                                $s = 0;

                                foreach ($specialities as $spec) {

                                    $cache_chan_survey_spec = "channel_featured_survey_spec" . $spec;

                                    if ($this->myredis->exists($cache_chan_survey_spec)) {
                                        $specresponse = $this->myredis->get($cache_chan_survey_spec);
                                    } else {

                                        $this->db->select('msv.master_specialities_id, msv.specialities_name');
                                        $this->db->from('master_specialities_V1 as msv');
                                        $this->db->where(['msv.master_specialities_id' => $spec]);

                                        $specresult = $this->db->get();

                                        if ($specresult) {
                                            $specresponse = $specresult->result_array();
                                        }

                                        $this->myredis->set($cache_chan_survey_spec, $specresponse);
                                    }

                                    foreach ($specresponse as $specval) {
                                        $specarr[$s]['id'] = $specval['master_specialities_id'];
                                        $specarr[$s]['name'] = $specval['specialities_name'];
                                    }

                                    $s++;
                                }
                            }

                            $sponsor = explode(',', $survval['sponsor']);
                            if (!empty($sponsor)) {

                                $splus = 0;

                                foreach ($sponsor as $spon) {

                                    $cache_chan_survey_sponsor = "channel_featured_survey_spons" . $spon;

                                    if ($this->myredis->exists($cache_chan_survey_sponsor)) {
                                        $sponresponse = $this->myredis->get($cache_chan_survey_sponsor);
                                    } else {
                                        $this->db->select('clientm.client_name, clientm.client_logo');
                                        $this->db->from('client_master as clientm');
                                        $this->db->where(['clientm.client_master_id' => $spon]);

                                        $sponresult = $this->db->get();

                                        if ($sponresult) {
                                            $sponresponse = $sponresult->result_array();
                                        }
                                        $this->myredis->set($cache_chan_survey_sponsor, $sponresponse);
                                    }

                                    foreach ($sponresponse as $sponval) {
                                        $sponarr[$splus]['client_name'] = $sponval['client_name'];
                                        $sponarr[$splus]['client_logo'] = change_img_src($sponval['client_logo']);
                                    }

                                    $splus++;
                                }
                            }

                            $responsearr[$v]['trending_type'] = 'survey';
                            $responsearr[$v]['survey_id'] = $survval['survey_id'];
                            $responsearr[$v]['category'] = $survval['category'];
                            $responsearr[$v]['question_count'] = $survval['question_count'];
                            $responsearr[$v]['survey_time'] = $survval['survey_time'];
                            $responsearr[$v]['point'] = $survval['survey_points'];
                            ;
                            $responsearr[$v]['points_on_approval'] = $survval['points_on_approval'];
                            $responsearr[$v]['survey_title'] = $survval['survey_title'];
                            $responsearr[$v]['deeplink'] = ($env == 'GL') ? (($survval['gl_deeplink'] != '') ? $survval['gl_deeplink'] : 0) : (($survval['deeplink'] != '') ? $survval['deeplink'] : 0); //$survval['deeplink'] ;
                            $responsearr[$v]['specialities_ids_and_names'] = $specarr;
                            $responsearr[$v]['sponsor_names_and_logos'] = $sponarr;
                            $responsearr[$v]['survey_description'] = $survval['survey_description'];
                            $responsearr[$v]['image'] = change_img_src($survval['image']);
                            $responsearr[$v]['publishing_date'] = $survval['publishing_date'];

                            $v++;
                        }

                        // $v++;

                    }
                }

                $epub = $this->getepubnew($val['channel_master_id'], $client_ids);
                $responsearr = array_merge($responsearr, $epub);
                //  print_r($responsearr);
                //  exit;
                #print_r($val);exit;
                if (!empty($responsearr)) {
                    $fc = $val['follower_count'];
                    $data[$c]['slno'] = $slno;
                    $data[$c]['type_id'] = $val['channel_master_id'];
                    $data[$c]['type'] = 'channel';
                    $data[$c]['privacy_status'] = $val['privacy_status'];
                    $data[$c]['followed_status'] = $this->checkstatus($val['channel_master_id'], $user_master_id); #$val['status'];
                    $data[$c]['added_on'] = $val['added_on'];
                    $data[$c]['address'] = $val['address'];
                    $data[$c]['follower_count'] = ($val['total'] != '') ? $val['total'] + $val['follower_count'] : $val['follower_count'];
                    $data[$c]['title'] = $val['title'];
                    $data[$c]['cover_image'] = change_img_src($val['cover_image']);
                    $data[$c]['logo'] = change_img_src($val['client_logo']);

                    $data[$c]['is_locked'] = $key_locked;
                    $data[$c]['price'] = $val['key_locked'];
                    $data[$c]['user_content_payment'] = $val['user_contnet_payment_status'];

                    $data[$c]['slug'] = $val['title'];
                    $data[$c]['description'] = $val['description'];
                    $data[$c]['description_view'] = preg_replace('/[\r\n]/', '', html_entity_decode(strip_tags($val['description'])));
                    $data[$c]['specialities'] = $val['specialities'];
                    $data[$c]['deeplink'] = ($env == 'GL') ? (($val['gl_deeplink'] != '') ? $val['gl_deeplink'] : 0) : (($val['deeplink'] != '') ? $val['deeplink'] : 0); //$val['deeplink'];
                    $data[$c]['content'] = $responsearr;

                    $c++;
                    $slno++;
                }
            }
        }

        //  print_r($data);
        //  die;

        return $data;
    }

    public function checkstatus($type_id, $user_master_id)
    {
        $this->db->select('status');
        $this->db->from('channel_to_user');
        $this->db->where(array('channel_master_id' => $type_id, 'user_master_id' => $user_master_id));

        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            return $result[0]->status;
        } else {
            return 0;
        }
    }

    public function getchannelcontent($type_id, $user_master_id)
    {

        //get session detail
        $session_detail = $this->sessiondetail($type_id, $user_master_id);
    }

    public function sessiondetail($type_id, $user_master_id)
    {
        $this->db->select('ksv.session_id');
        $this->db->from('knwlg_sessions_V1 as ksv');
        $this->db->join('channel_to_session as cts', 'cts.session_id = ksv.session_id');
        $this->db->where(array('cts.channel_master_id' => $type_id));
        $this->db->where_not_in('ksv.session_status', [3, 4]);
        #cta($value->cta_type,$value->cta_type_id,$user_master_id,$client_ids);

        $query = $this->db->get();

        if (($query) && ($query->num_rows())) {
            $result = $query->result();
            $detail = $this->cdetail('session', $result[0]->session_id);
        }
    }

    public function cdetail($ctype, $type_id)
    {

        switch ($ctype) {

            case 'comp':
                //MEDWIKI
                $sql = "SELECT
                    cm.comp_qa_id as type_id,
                    cm.comp_qa_question,
                    cm.comp_qa_answer,
                    cm.comp_qa_answer_raw,
                    cm.comp_qa_question_raw,
                    cm.comp_qa_file_img,
                    cm.added_on,
                    cm.deeplink,
                    cm.gl_deeplink,
                    cm.comp_qa_tags,	
                    
                    cm.publication_date,
                    cln.client_name,
                    cln.client_logo,
                    
                    cm.type,
                    cm.vendor,
                    cm.src,
                    cm.start_like,
            
                    
                    
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                    
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                    
                    cm.comp_qa_speciality_id,
                    
                    (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = " . $type_id . " and  rt.post_type='comp' and rt.rating!=0)as averageRating,
                    rtmy.rating  as myrating,
                    
                    (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm 
                        where kcm.type_id = " . $type_id . " and kcm.type = 'comp' and kcm.comment_approve_status = '1' )as count_comment,
                    kv.status as vault  
                    
                    FROM knwlg_compendium_V1 as cm
                    
                    
                    JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
                    JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                    
                    
                    LEFT JOIN compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                    
                    
                    JOIN client_master as cln ON cln.client_master_id = cm.client_id
                    LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.comp_qa_id and  rtmy.post_type='comp' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                    
                    
                    LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and  kv.type_text='comp' and  kv.user_id = " . $user_master_id . "
                    LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.comp_qa_id and  rt.post_type='comp'
                    WHERE cm.status=3
                    AND 
                    cm.comp_qa_id = " . $type_id . "";

                //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id

                //echo $sql; exit;
                $query = $CI->db->query($sql);
                $result = $query->row();
                //banner
                $sqlBanner = "SELECT 
                    mb.*,
                    kca.company_name,
                    kca.company_image_src,
                    kca.company_url,
                    
                    kpa.product_name,
                    kpa.product_image_src,
                    kpa.product_url,
                    
                    
                    kea.event_name,
                    kea.event_image_src 
                    From master_banner as mb 
                    LEFT JOIN knwlg_banner_company_att as kca ON kca.banner_id= mb.banner_id 
                    LEFT JOIN knwlg_banner_product_att as kpa ON kpa.banner_id=mb.banner_id 
                    LEFT JOIN knwlg_banner_event_att as kea ON kea.banner_id = mb.banner_id
                    LEFT JOIN banner_to_content as btc ON btc.banner_master_id = mb.banner_id
                    WHERE mb.status != 2 and btc.type_id=" . $type_id . " and btc.type='comp' order by RAND() limit 1";

                $queryBanner = $CI->db->query($sqlBanner);

                $resultBanner = $queryBanner->result_array();
                //bannner

                $entities = array();
                $i = 0;
                if ($queryBanner->num_rows() > 0) {
                    foreach ($resultBanner as $row) {
                        $entities[$i]['banner_id'] = $row['banner_id'];

                        $entities[$i]['banner_type'] = $row['banner_type'];
                        $entities[$i]['banner_position'] = $row['banner_position'];

                        $entities[$i]['company_name'] = $row['company_name'];

                        $entities[$i]['product_name'] = $row['product_name'];
                        $img_path = "banner_images/";
                        if ($row['banner_type'] == 1) {
                            $img_path .= "company";
                        }
                        if ($row['banner_type'] == 2) {
                            $img_path .= "product";
                        }
                        if ($row['banner_type'] == 3) {
                            $img_path .= "event";
                        }
                        if ($row['banner_position'] == 1) {
                            $img_path .= "/top_images";
                        } else {
                            $img_path .= "/sidebar_images";
                        }
                        if ($row['event_image_src'] != null) {
                            $entities[$i]['event_image_src'] = $img_path . "/" . $row['event_image_src'];
                        } else {
                            $entities[$i]['event_image_src'] = "";
                        }
                        if ($row['product_image_src'] != null) {
                            $entities[$i]['product_image_src'] = $img_path . "/" . $row['product_image_src'];
                            $entities[$i]['product_url'] = $row['product_url'];
                        } else {
                            $entities[$i]['product_image_src'] = "";
                            $entities[$i]['product_url'] = "";
                        }
                        if ($row['company_image_src'] != null) {
                            $entities[$i]['company_image_src'] = $img_path . "/" . $row['company_image_src'];
                            $entities[$i]['company_url'] = $row['company_url'];
                        } else {
                            $entities[$i]['company_image_src'] = "";
                            $entities[$i]['company_url'] = "";
                        }

                        $entities[$i]['event_name'] = $row['event_name'];
                        $entities[$i]['status'] = $row['status'];
                        $i++;
                    }
                }

                if (isset($entities[0]['event_image_src'])) {
                    $bannerImage = base_url() . 'uploads/' . $entities[0]['event_image_src'];
                    $bannerUrl = '';
                } else {
                    $bannerImage = "";
                }

                if (isset($entities[0]['product_image_src'])) {
                    $bannerImage = base_url() . 'uploads/' . $entities[0]['product_image_src'];
                    $bannerUrl = $entities[0]['product_url'];
                } else {
                    $bannerUrl = "";
                }
                if (isset($entities[0]['company_image_src'])) {
                    $bannerImage = base_url() . 'uploads/' . $entities[0]['company_image_src'];
                    $bannerUrl = $entities[0]['company_url'];
                }

                //echo  $bannerImage = base_url().'uploads/'.$entities[0]['company_image_src']; exit;

                //banner end
                //poll start

                $sqlCompl = "SELECT 
                    sv.* 
                    
                    FROM 
                    survey_user_answer sv
                    WHERE 
                    sv.user_master_id = '" . $user_master_id . "'";
                $queryCompl = $CI->db->query($sqlCompl);
                $resultCompl = $queryCompl->result();

                $complID = array();
                foreach ($resultCompl as $valCompl) {

                    $complID[] = $valCompl->survey_id;
                }
                //print_r($complID); exit;
                $sqlInCompl = "SELECT 
                    sv.* 
                    
                    FROM 
                    survey_user_incomplete_answer sv
                    WHERE 
                    sv.status = 3
                    and 
                    sv.user_master_id = '" . $user_master_id . "'";
                $queryInCompl = $CI->db->query($sqlInCompl);
                $resultInCompl = $queryInCompl->result();

                $incomplID = array();
                foreach ($resultInCompl as $valInCompl) {

                    $incomplID[] = $valInCompl->survey_id;
                }
                $arrayFinal = array_unique(array_merge($complID, $incomplID));
                //print_r($arrayFinal); exit;
                $complIDStr = implode(",", array_filter($arrayFinal));
                //echo $complIDStr ; exit;

                if ($complIDStr) {
                    $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
                } else {
                    $qryStr = '';
                }
                $sqlPoll = "SELECT 
                    sv.* ,
                    svd.data,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                    cln.client_name,
                    cln.client_logo,
                    
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                    
                    FROM 
                    survey sv
                    left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id          
                    left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                    JOIN client_master as cln ON cln.client_master_id = sv.client_id
                    
                    
                    
                    LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                    
                    
                    
                    JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
                    JOIN survey_to_medwiki as stm ON stm.survey_id = sv.survey_id
                    
                    left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
                    WHERE 
                    sv.status = 3 
                    and 
                    stm.medwiki_id = " . $type_id . "
                    " . $qryStr . " ";
                $queryPoll = $CI->db->query($sqlPoll);
                $resultPoll = $queryPoll->result();

                //echo $sqlPoll;
                // print_r($resultPoll);
                $vxPoll = array();
                if ($queryPoll->num_rows() > 0) {
                    foreach ($resultPoll as $valSurvey) {
                        $dataArry = unserialize($valSurvey->data);
                        $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                        $str = preg_replace('/\\\"/', "\"", $json);
                        $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);

                        if (count($sponsorLogoArry) > 0) {

                            foreach ($sponsorLogoArry as $valueSponor) {

                                if ($valueSponor) {
                                    $sponsorLogomix[] = '' . $valueSponor;
                                }
                            }
                        } else {

                            if ($valSurvey->sponsor_logo) {
                                $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                            }
                        }
                        if (!empty($sponsorLogomix)) {
                            $sponsorLogo = implode(",", (array)$sponsorLogomix);
                        } else {
                            $sponsorLogo = "";
                        }
                        unset($sponsorLogomix);
                        unset($sponsorLogoArry);
                        if ($valSurvey->survey_id) {

                            $vxPoll[] = array(

                                "survey_id" => $valSurvey->survey_id,
                                "category" => $valSurvey->category,
                                "point" => $valSurvey->survey_points,
                                "json_data" => $str,
                                "survey_title" => $valSurvey->survey_title,
                                "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0), //$valSurvey->deeplink,
                                "survey_description" => substr($valSurvey->survey_description, 0, 150),
                                "image" => change_img_src($valSurvey->image),
                                "specialities_name" => $valSurvey->specialities_name,
                                "client_name" => $valSurvey->client_name,
                                "client_logo" => change_img_src('' . $valSurvey->client_logo),

                                "sponsor_name" => $valSurvey->sponsor,
                                "sponsor_logo" => change_img_src($sponsorLogo),
                                "publishing_date" => $valSurvey->publishing_date,

                            );
                        }
                    }
                }

                //print_r($vxPoll);
                //poll end
                /* if (@getimagesize(base_url() . "uploads/compendium/" . $result->comp_qa_file_img)) {
                         $img = image_thumb_url('uploads/compendium/' . $result->comp_qa_file_img, $result->comp_qa_file_img, 203, 304, '');
                     } else {
                         $img = '';
                     }*/
                if ($result->comp_qa_file_img) {

                    $img = $result->comp_qa_file_img; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;

                } else {

                    $img = '';
                }
                $sponsorLogoArry = explode(",", $result->sponsor_logo);

                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {

                    if ($result->sponsor_logo) {
                        $sponsorLogomix[] = '' . $result->sponsor_logo;
                    }
                }
                if (!empty($sponsorLogomix)) {
                    $sponsorLogo = implode(",", (array)$sponsorLogomix);
                }
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                //$string = htmlentities($result->comp_qa_question_raw, null, 'utf-8');
                $string = $result->comp_qa_question_raw;

                // $string = str_replace("&#39;", "dddd", $string);
                // $string = str_replace("nbsp;", "", $string);
                // $string = str_replace("&amp;", "", $string);
                $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                $main_description = "";
                $main_description = str_replace("\n\t", "\n", $result->comp_qa_answer_raw);
                $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
                if ($result->vendor == "youtube") {

                    $vid_src = "https://www.youtube.com/watch?v=" . $result->src;
                    $vid_code = $result->src;
                } else {

                    $vid_src = "" . $result->src;
                    $vid_code = "";
                }
                $vx = array(

                    "type_id" => $result->type_id,
                    "con_type" => $result->type,
                    "vendor" => $result->vendor,
                    "src" => $vid_src,
                    "src_code" => $vid_code,
                    "type" => 'comp',
                    "date" => date(' jS F y', strtotime($result->publication_date)),
                    "question" => html_entity_decode($string),
                    "answer" => html_entity_decode($main_description), //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),
                    "question_htm" => $result->comp_qa_question,
                    "answer_htm" => $result->comp_qa_answer, //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),
                    "image" => change_img_src($img),
                    "banner_image" => change_img_src($bannerImage),
                    "banner_url" => $bannerUrl,
                    "specialities" => $result->specialities_name,
                    "specialities_id" => $result->comp_qa_speciality_id,
                    "client_name" => $result->client_name,
                    "client_logo" => change_img_src('' . $result->client_logo),
                    "sponsor_name" => $result->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),
                    "comment_count" => $result->count_comment,
                    "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
                    "myrating" => ($result->myrating != '') ? true : false,
                    "vault" => ($result->vault != '') ? $result->vault : 0,
                    "deeplink" => ($env == 'GL') ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
                    "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,

                    //  "disclaimer" => 'All scientific content on the platform is provided for general medical education purposes meant for registered medical practitioners only. The content is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. In no event will CLIRNET be liable for any decision made or action taken in reliance upon the information provided through this content.',
                    "disclaimer" => disclaimer('knowledge'),
                    "survey" => $vxPoll,
                );
                // if ($from_type) {}
                return $vx;
                break;

            case 'gr':

                $sqlInt = "select 
                specialities_id
                from
                user_to_interest
                where 
                user_master_id = " . $user_master_id . "";
                $queryInt = $CI->db->query($sqlInt);
                $resultInt = $queryInt->result_array();

                //print_r($resultInt); exit;
                $specialities = array();
                foreach ($resultInt as $val) {

                    $specialities[] = $val['specialities_id'];
                    //$specialities = array_merge($specialities, $val);

                }
                if (count($specialities) > 0) {

                    $specialityIds = implode(",", (array)$specialities);
                }
                if ($specialityIds != '') {

                    $specialities_query = ' and (' . implode(' OR ', array_map(function ($x) {
                        return "FIND_IN_SET('$x', fd.speciality_id)";
                    }, explode(',', $specialityIds))) . ')';
                } else {
                    $specialities = "";
                }
                //echo $specialities; exit;
                //get user speciality
                if ($client_ids) {

                    //$client_list = ' and '. implode(' OR ', array_map(function($x) { return "FIND_IN_SET('$x', kcp.client_id)"; }, explode(',', $this->session->userdata('client_ids'))));
                    $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                        return "FIND_IN_SET('$x', fd.client_id)";
                    }, explode(',', $client_ids))) . ')';
                }
                // print_r($client_ids);
                // die;

                $sql = "SELECT
                gr.gr_id as type_id,
                gr.gr_title as title,
                gr.title_video as title_video,
                gr.gr_description as description,
                gr.gr_chief_scientific_editor ,
                gr.gr_preview_image,
                
                gr.gr_type,
                gr.gr_video_source,
                gr.vendor,
                gr.start_like,
                
                
                gr.live_video,
                gr.hotline_status,
                gr.qn_status,
                
                gr.start_datetime,
                gr.end_datetime,
                
                gr.added_on,
                gr.gr_date_of_publication as publish_date,
                gr.deeplink,
                gr.gl_deeplink,
                gr.association_status,
                gr.association_setting,
                
                
                cln.client_name,
                cln.client_logo,
                
                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                max( ms.rank) as maxrank,
                
                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id  ORDER BY  grTsdoc.session_doctor_id  ASC ) as session_doctor_id,
                GROUP_CONCAT(DISTINCT grTsdoc.description  ORDER BY  grTsdoc.session_doctor_id  ASC SEPARATOR '----') as gr_doc_description,
                
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                
                (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = gr.gr_id and  rt.post_type='gr')as averageRating,
                rtmy.rating  as myrating,
                
                (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = gr.gr_id and kcm.type = 'gr' and kcm.comment_approve_status = 1)as count_comment,
                kv.status as vault
                
                FROM knwlg_gr_register as gr
                
                JOIN gr_to_specialities  as grTs ON grTs.gr_id = gr.gr_id
                JOIN master_specialities_V1 as ms ON ms.master_specialities_id = grTs.specialities_id
                JOIN client_master as cln ON cln.client_master_id = gr.client_id
                
                LEFT JOIN gr_to_sponsor as grTspon ON grTspon.gr_id = gr.gr_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = grTspon.sponsor_id
                
                
                LEFT JOIN gr_to_session_doctor as grTsdoc ON grTsdoc.gr_id = gr.gr_id
                
                LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = gr.gr_id and  rtmy.post_type='gr' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                LEFT JOIN knwlg_vault as kv ON kv.post_id = gr.gr_id and  kv.type_text='gr' and  kv.user_id = " . $user_master_id . "
                LEFT JOIN knwlg_rating as rt ON rt.post_id = gr.gr_id and  rt.post_type='gr'
                
                WHERE 
                gr.status=3
                and
                gr.gr_id = " . $type_id . "";

                //echo $sql; exit;
                //exit;
                //add child checking in this sql
                //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
                //exit;
                //echo  $sql; exit;

                $query = $CI->db->query($sql);
                //$this->db->cache_off();
                $val = $query->row();
                //print_r($val); exit;
                //echo $val->gr_preview_image;

                $vx = array();
                if ($val->gr_preview_image) {
                    $gr_logic_image = $val->gr_preview_image;
                } else {

                    $gr_logic_image = '';
                }

                $sponsorLogoArry = explode(",", $val->sponsor_logo);

                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = $valueSponor;
                        }
                    }
                } else {

                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = $val->sponsor_logo;
                    }
                }

                $sponsorLogo = implode(",", (array)$sponsorLogomix);

                unset($sponsorLogomix);
                unset($sponsorLogoArry);

                $ses_doc_det_array = array();
                if ($val->session_doctor_id) {

                    $session_doc_array = explode(",", $val->session_doctor_id);
                    $session_gr_doc_description_array = explode("----", $val->gr_doc_description);
                    $inc_pp = 0;
                    foreach ($session_doc_array as $single_doctor) {

                        $var = session_doc_detail($single_doctor);

                        if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {

                            $logic_image = $var[0]['profile_image'];
                        } else {

                            $logic_image = docimg; //$imgPr;

                        }
                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                        $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                        $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $session_gr_doc_description_array[$inc_pp];
                        $inc_pp++;
                    }
                }
                //print_r($ses_doc_det_array);
                //gr.association_status,
                //gr.association_setting,
                $fileArray = array();
                if ($val->type_id) {

                    $fileArray = grFile($val->type_id);
                }

                if ($val->vendor == "youtube") {
                    $vid_src = "https://www.youtube.com/watch?v=" . $val->gr_video_source;
                    $vid_code = $val->gr_video_source;
                } else {

                    $vid_src = "" . $val->gr_video_source;
                }
                $vx[] = array(
                    "type_id" => $val->type_id,
                    "type" => 'gr',
                    "date" => date(' jS F y', strtotime($val->publish_date)),
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "title_video" => html_entity_decode(strip_tags($val->title_video)),

                    "media_type" => $val->gr_type,
                    "image" => change_img_src($gr_logic_image),
                    "video" => $vid_src,
                    "src_code" => $vid_code,
                    "vendor" => $val->vendor,

                    "live_video" => $val->live_video,
                    "hotline_status" => $val->hotline_status,
                    "qn_status" => $val->qn_status,
                    "association_status" => $val->association_status,
                    "association_setting" => $val->association_setting,
                    "start_datetime" => $val->start_datetime,
                    "end_datetime" => $val->end_datetime,

                    "description" => html_entity_decode(strip_tags($val->description)),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "specialities_ids_and_names" => explode_speciality_string($val->specialities_ids_and_names),

                    "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',

                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src($val->client_logo),

                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),

                    "session_doctor_entities" => $ses_doc_det_array,
                    "gr_files" => $fileArray,

                    "comment_count" => $val->count_comment,
                    "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : $val->start_like,
                    "myrating" => ($val->myrating != '') ? true : false,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                    "disclaimer" => disclaimer('knowledge'),
                );
                //    print_r($vx);
                //    die;
                return $vx;
                break;
            case 'session':

                $sql = "SELECT
            ks.session_id,ks.session_topic,ks.description,ks.cover_image,ks.deeplink,ks.color,ks.session_status,ks.start_datetime,ks.end_datetime,ks.gl_deeplink,
            sd.*,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
    
    
            GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
            GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
            GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') as  speciality,
          
          
            FROM knwlg_sessions_V1 as ks
            LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
            LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id
            
            LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
            LEFT JOIN knwlg_sessions_vendor as ksv ON ksv.session_id=ks.session_id
            LEFT JOIN master_vendor as kv ON kv.vendor_id=ksv.vendor_id
            LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
            LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
            LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
            LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id
            LEFT JOIN master_session_status as mst ON mst.master_session_status_id = ks.session_status
           
    
            WHERE
            ks.session_id=" . $type_id . "
    
            GROUP BY ks.session_id
            ORDER BY ks.start_datetime DESC ";
                $query = $this->db->query($sql);

                echo $sql;
                exit; //" . $group_list . "
                //AND (ks.session_status=1 OR ks.session_status=4)
                //echo $CI->db->last_query(); exit();
                $result = $query->result_array();
                //echo count($result); exit();
                // print_r($result); exit();
                // die;
                //banner old start
                //banner old end
                $i = 0;

                $entities = array();

                foreach ($result as $row) {

                    $sql_check = "SELECT
                knwlg_sessions_participant_details.question,
                knwlg_sessions_participant_details.upload_documents,
                knwlg_sessions_participant.knwlg_sessions_participant_id,
                knwlg_sessions_participant.room_link
               
    
                FROM knwlg_sessions_participant
                LEFT JOIN knwlg_sessions_participant_details ON knwlg_sessions_participant_details.sessions_participant_id=knwlg_sessions_participant.knwlg_sessions_participant_id
                
                WHERE
                knwlg_sessions_participant.status=3
                AND knwlg_sessions_participant.participant_type='member'
                AND knwlg_sessions_participant.knwlg_sessions_id=" . $row['session_id'] . "
                AND participant_id=" . $user_master_id . " ";
                    $query_check = $CI->db->query($sql_check);

                    //echo $this->db->last_query(); exit();
                    $result_check = $query_check->row_array();
                    // print_r($result_check); exit();

                    if (!empty($result_check)) {
                        // echo $result_check['knwlg_sessions_participant_id']; exit();
                        //$result_check['knwlg_sessions_participant_id']
                        $entities[$i]['is_booked'] = true;
                        $entities[$i]['room_link'] = $result_check['room_link'];
                        $entities[$i]['asked_query'] = $result_check['question'];
                        $entities[$i]['upload_documents'] = $result_check['upload_documents'];
                        $entities[$i]['my_participant_id'] = $result_check['knwlg_sessions_participant_id'];
                    } else {
                        $entities[$i]['is_booked'] = false;
                        $entities[$i]['room_link'] = '';
                        $entities[$i]['asked_query'] = "";
                        $entities[$i]['my_participant_id'] = "";
                    }
                    $entities[$i]['last_join_date'] = '';
                    $entities[$i]['recorded_video_id'] = '';
                    $entities[$i]['channel_details'] = [];
                    $sql_check_rating = "SELECT 
                * 
                FROM knwlg_session_rating_reviews 
                WHERE session_id=" . $row['session_id'] . " 
                AND user_master_id=" . $user_master_id . "";
                    $query_check_rating = $CI->db->query($sql_check_rating);

                    //echo $this->db->last_query(); exit();
                    $result_check_rating_array = $query_check_rating->row_array();
                    // print_r($result_check); exit();

                    if (!empty($result_check_rating_array)) {
                        // echo $result_check['knwlg_sessions_participant_id']; exit();
                        //$result_check['knwlg_sessions_participant_id']
                        $entities[$i]['review'] = $result_check_rating_array['review'];
                        $entities[$i]['rating'] = $result_check_rating_array['rating'];
                        $entities[$i]['is_rating_review'] = true;
                    } else {

                        $entities[$i]['is_rating_review'] = false;
                    }
                    $sql_check_recording = "SELECT 
                * 
                FROM knwlg_session_recording_request 
                WHERE session_id=" . $row['session_id'] . " 
                AND user_master_id=" . $user_master_id . "";
                    $query_check_recording = $CI->db->query($sql_check_recording);

                    //echo $this->db->last_query(); exit();
                    $result_check_recording_array = $query_check_recording->row_array();
                    // print_r($result_check); exit();

                    if (!empty($result_check_recording_array)) {
                        // echo $result_check['knwlg_sessions_participant_id']; exit();
                        //$result_check['knwlg_sessions_participant_id']

                        $entities[$i]['recording_type'] = $result_check_recording_array['recording_type'];
                        $entities[$i]['is_sent_recording'] = true;
                    } else {

                        $entities[$i]['is_sent_recording'] = false;
                    }

                    $sqlCompl = "SELECT 
                sv.* 
                
                FROM 
                survey_user_answer sv
                WHERE 
                sv.user_master_id = '" . $user_master_id . "'";
                    $queryCompl = $CI->db->query($sqlCompl);
                    $resultCompl = $queryCompl->result();

                    $complID = array();
                    foreach ($resultCompl as $valCompl) {

                        $complID[] = $valCompl->survey_id;
                    }
                    //print_r($complID); exit;
                    $sqlInCompl = "SELECT 
                sv.* 
                
                FROM 
                survey_user_incomplete_answer sv
                WHERE 
                sv.status = 3
                and 
                sv.user_master_id = '" . $user_master_id . "'";

                    $queryInCompl = $CI->db->query($sqlInCompl);
                    $resultInCompl = $queryInCompl->result();
                    $incomplID = array();
                    foreach ($resultInCompl as $valInCompl) {

                        $incomplID[] = $valInCompl->survey_id;
                    }
                    $arrayFinal = array_unique(array_merge($complID, $incomplID));
                    //print_r($arrayFinal); exit;
                    $complIDStr = implode(",", array_filter($arrayFinal));
                    //echo $complIDStr ; exit;
                    if ($complIDStr) {
                        $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
                    } else {
                        $qryStr = '';
                    }
                    $sqlPoll = "SELECT 
                sv.* ,
                svd.data,
                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                cln.client_name,
                cln.client_logo,
                
                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                
                FROM 
                survey sv
                left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id          
                left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                JOIN client_master as cln ON cln.client_master_id = sv.client_id
                
                
                
                LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                
                
                
                JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
                JOIN survey_to_session as stm ON stm.survey_id = sv.survey_id
                
                left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
                WHERE 
                sv.status = 3 
                and 
                stm.session_id = " . $row['session_id'] . "
                " . $qryStr . " ";

                    $queryPoll = $CI->db->query($sqlPoll);
                    $resultPoll = $queryPoll->result();
                    //echo $sqlPoll;
                    // print_r($resultPoll);
                    $vxPoll = array();
                    foreach ($resultPoll as $valSurvey) {
                        $dataArry = unserialize($valSurvey->data);
                        $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                        $str = preg_replace('/\\\"/', "\"", $json);
                        $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);

                        if (count($sponsorLogoArry) > 0) {

                            foreach ($sponsorLogoArry as $valueSponor) {

                                if ($valueSponor) {
                                    $sponsorLogomix[] = '' . $valueSponor;
                                }
                            }
                        } else {

                            if ($valSurvey->sponsor_logo) {
                                $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                            }
                        }

                        $sponsorLogo = implode(",", (array)$sponsorLogomix);

                        unset($sponsorLogomix);
                        unset($sponsorLogoArry);
                        if ($valSurvey->survey_id) {

                            $vxPoll[] = array(

                                "survey_id" => $valSurvey->survey_id,
                                "category" => $valSurvey->category,
                                "point" => $valSurvey->survey_points,
                                "json_data" => $str,
                                "survey_title" => $valSurvey->survey_title,
                                "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0), //$valSurvey->deeplink,
                                "survey_description" => substr($valSurvey->survey_description, 0, 150),
                                "image" => change_img_src($valSurvey->image),
                                "specialities_name" => $valSurvey->specialities_name,
                                "specialities_ids_and_names" => explode_speciality_string($valSurvey->specialities_ids_and_names),
                                "client_name" => $valSurvey->client_name,
                                "client_logo" => change_img_src('' . $valSurvey->client_logo),

                                "sponsor_name" => $valSurvey->sponsor,
                                "sponsor_logo" => change_img_src($sponsorLogo),
                                "publishing_date" => $valSurvey->publishing_date,

                            );
                        }
                    }
                    // $entities[$i]['my_participant_id'] = $row['participant_id'];
                    $entities[$i]['is_available'] = (strtotime($row['start_datetime']) < time()) ? false : true;
                    $entities[$i]['session_id'] = $row['session_id'];
                    $entities[$i]['type_id'] = $row['session_id'];
                    $entities[$i]['type'] = 'session';
                    $entities[$i]['trending_type'] = 'session';
                    $coverImg = base_url() . "uploads/sessionBgDefault.jpeg";
                    $cov_img = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
                    $tempcover = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
                    $coverimageArry = explode(",", $tempcover);

                    $entities[$i]['cover_image'] = change_img_src($coverimageArry);
                    //$entities[$i]['cover_image'] = $row['cover_image'];
                    $entities[$i]['session_topic'] = $row['session_topic'];
                    $entities[$i]['specialities_name'] = $row['specialities_name'];
                    $entities[$i]['specialities_ids_and_names'] = explode_speciality_string($row['specialities_ids_and_names']);
                    $entities[$i]['speciality_id'] = $row['speciality_id'];
                    $entities[$i]['session_description'] = strip_tags($row['session_description']);
                    $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
                    $entities[$i]['client_id'] = $row['client_id'];
                    $entities[$i]['vendor_meta_data'] = $row['meta_data'];
                    $entities[$i]['client_name'] = $row['client_name'];
                    $entities[$i]['status_name'] = $row['status_name'];
                    $entities[$i]['vendor_id'] = $row['vendor_id'];
                    $entities[$i]['room_id'] = $row['room_id'];
                    $entities[$i]['vouchpro_url'] = $row['vouchpro_url'];
                    $entities[$i]['go_to_meeting_url'] = $row['go_to_meeting_url'];
                    $entities[$i]['landing_page_url'] = $row['landing_page_url'];
                    $entities[$i]['video_embed_src'] = $row['video_embed_src'];
                    $entities[$i]['session_cast_type'] = $row['session_cast_type'];
                    $sponserentity = array();
                    $sponsorLogoArry = explode(",", $row['sponsor']);
                    $sponsorNameArry = explode(",", $row['sponsor_logo']);
                    $ii = 0;
                    foreach ($sponsorLogoArry as $singlelogo) {
                        if ($singlelogo != "") {
                            $sponserentity[$ii]['sponsor_name'] = $singlelogo;
                            $sponserentity[$ii]['sponsor_logo'] = change_img_src($sponsorNameArry[$ii]);
                        }
                        $ii++;
                    }
                    $entities[$i]['sponsor_entity'] = $sponserentity;
                    /**
                     * new sponsor logic
                     */
                    $sponsorLogoArry = explode(",", $row['sponsor_logo']);
                    if (count($sponsorLogoArry) > 0) {

                        foreach ($sponsorLogoArry as $valueSponor) {
                            if ($valueSponor) {

                                $sponsorLogomix[] = $valueSponor;
                            }
                        }
                    } else {

                        if ($row['sponsor_logo']) {

                            // if full path exist
                            if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                                $sponsorLogomix[] = $row['sponsor_logo'];
                            } else {
                                $sponsorLogomix[] = ""; //base_url('uploads/logo/') . $row['sponsor_logo'];
                            }
                        }
                    }
                    $sponsorLogo = implode(",", (array)$sponsorLogomix);
                    /**
                     * new sponsor logic
                     */

                    $entities[$i]['sponsor_name'] = $row['sponsor'];
                    $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);

                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);
                    if ($row['document_path'] != "" || $row['document_path'] != null) {
                        $entities[$i]['file_size'] = round((filesize('./uploads/mastersession_docs/' . $row['document_path'] . '') / 1024)) . "Kb";
                        $entities[$i]['document_path_exact_file_name'] = $row['document_path'];
                        $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
                        $entities[$i]['extension_logo_path'] = base_url() . "themes/front/images/" . get_logo_by_file_extension($row['document_path']);
                    } else {
                        $entities[$i]['document_path_exact_file_name'] = "";
                        $entities[$i]['document_path'] = "";
                        $entities[$i]['file_size'] = "";
                        $entities[$i]['extension_logo_path'] = "";
                    }
                    if ($row['comment'] != "" || $row['comment'] != null) {
                        $entities[$i]['comment'] = $row['comment'];
                    } else {
                        $entities[$i]['comment'] = "";
                    }
                    $entities[$i]['category_id'] = $row['category_id'];
                    $entities[$i]['category_name'] = $row['category_name'];
                    $entities[$i]['category_image'] =  change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
                    $entities[$i]['start_datetime'] = $row['start_datetime'];
                    $entities[$i]['start_datetimex'] = strtotime($row['start_datetime']);
                    $entities[$i]['now_datetimex'] = time();
                    $start_time = $row['start_datetime'];
                    $date = new DateTime($start_time);
                    //$start_time = date("g:i A", strtotime($start_time));
                    $now = new DateTime();
                    $diff = date_diff($date, $now);
                    $entities[$i]['days_remaining'] = abs($diff->format("%R%a")) + 1;
                    $end_time = $row['end_datetime'];

                    $end_time = date("g:i A", strtotime($end_time));
                    $entities[$i]['display_time_format'] = $start_time . "-" . $end_time;

                    $post_time = $row['start_datetime'];
                    $phpdate = strtotime($post_time);
                    $mysqldate = date('D, j M `y  ', $phpdate);
                    $entities[$i]['display_date_format'] = $mysqldate;

                    $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
                    $post_date = $row['added_on'];
                    $start_date = $row['start_datetime'];
                    $buffer_day = $row['add_question_buffer_days'];
                    $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
                    $buffer_str = strtotime($last_display_date);
                    $t = time();
                    $date = new DateTime($last_display_date);
                    $now = new DateTime();
                    $now_str = strtotime("now");
                    $diff = date_diff($date, $now);
                    //print_r($diff);
                    if ($t <= $buffer_str) {
                        $dat_diff = abs($diff->format("%R%a"));
                    } else {
                        $dat_diff = 0;
                    }
                    $entities[$i]['view_edit_button_text'] = "";
                    //echo $dat_diff; exit();
                    if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                        $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
                    }

                    if ($question_users->question != "" && $row['session_status'] != 3) {
                        $entities[$i]['view_edit_button_text'] = "View Case/Query";
                    }
                    $is_attended_array = array();
                    $is_attended_array = explode(",", $row['IS_ATTENDED']);

                    $part_array = array();
                    $part_array = explode(",", $row['PartName']);
                    //$user_id = $this->session->userdata['user_master_id'];
                    $inc = 0;
                    foreach ($part_array as $single) {
                        if ($single == $user_master_id) {
                            $key_val = $inc;
                        }
                        $inc++;
                    }

                    $is_att = $is_attended_array[$key_val];
                    $entities[$i]['missed_session_text'] = "";
                    if ($is_att == 2) {
                        $entities[$i]['missed_session_text'] = "You Missed The Session";
                    }
                    $entities[$i]['i_cant_attend_button'] = 0;
                    $end_time = $row['end_datetime'];
                    $end_time = strtotime($end_time);
                    $now_time = date('Y-m-d H:i:s');
                    $now_time = strtotime($now_time);
                    if ($now_time < $end_time) {
                        $entities[$i]['i_cant_attend_button'] = 1;
                    }
                    $cpt_flag = 0;
                    $on_of_booking_button = 0;
                    $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
                    if ($row['users'] == null) {
                        $total_original_booking = 0;
                    } else {
                        $users_array = array();
                        $users_array = explode(",", $row['users']);
                        $total_original_booking = count($users_array);
                    }

                    if ($total_original_booking < $row['total_seats']) {

                        $total_booking = $total_original_booking;
                    }
                    if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                        $minus_flag = $total_after_buffer - $total_original_booking;
                        $total_booking = ($row['total_seats']) - 1;
                    }

                    if ($total_original_booking >= ($total_after_buffer)) {

                        $total_booking = $row['total_seats'];
                        $on_of_booking_button = 1;
                        $cpt_flag = 1;
                    }

                    if ($total_booking > 0) {
                        $available_percent = ($total_booking / $row['total_seats']) * 100;
                    } else {
                        $available_percent = 0;
                    }
                    $available_percent = round($available_percent);
                    if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                        $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;

                        $entities[$i]['total_seat'] = $row['total_seats'];
                        $entities[$i]['total_booking_left'] = $total_booking;
                    } else {
                        $entities[$i]['total_seat'] = $row['total_seats'];
                        $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
                    }
                    $perc = $available_percent;
                    if ($cpt_flag == 0) {
                        $entities[$i]['percentage'] = ceil($perc);
                    } else {
                        $entities[$i]['percentage'] = ceil($perc);
                    }
                    $color = get_progress_color($perc);
                    $entities[$i]['color_profress_bar'] = $color;
                    $entities[$i]['session_status'] = $row['session_status'];
                    $entities[$i]['start_datetime'] = $row['start_datetime'];

                    $end_time = $row['end_datetime'];

                    $end_time = date("g:i A", strtotime($end_time));

                    $start_time = $row['start_datetime'];

                    $start_time = date("g:i A", strtotime($start_time));
                    $entities[$i]['display_date'] = $start_time . "-" . $end_time;
                    $entities[$i]['deeplink'] = ($env == 'GL') ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0); //$row['deeplink'];
                    $entities[$i]['end_datetime'] = $row['end_datetime'];
                    $entities[$i]['specialities_name'] = $row['specialities_name'];
                    $entities[$i]['specialities_ids_and_names'] = explode_speciality_string($row['specialities_ids_and_names']);
                    $entities[$i]['ms_cat_name'] = $row['category_name'];
                    $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
                    $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
                    $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
                    $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
                    $entities[$i]['doctor_name'] = $row['doctor_name'];
                    $entities[$i]['speciality'] = $row['speciality'];
                    $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
                    $session_doc_array = explode(",", $row['session_doctor_id']);

                    $ses_doc_det_array = array();
                    $inc_pp = 0;
                    foreach ($session_doc_array as $single_doctor) {

                        $var = session_doc_detail($single_doctor);

                        //print_r($var);

                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        if ($image) {
                            // $logic_image_path = "uploads/docimg/" . $image;
                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            // $logic_image = $imgPr;
                            /////============================== updated by  ramanath  14-5-21
                            if (stripos($image, "https://storage.googleapis.com") > -1) {
                                $logic_image = $image;
                            } else {
                                // $logic_image_path = docimg; //"uploads/docimg/" . $image;
                                // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                $logic_image = docimg; //$imgPr;

                            }
                            //=======================================

                        } else {

                            $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                        }
                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                        $ses_doc_det_array[$inc_pp]['description'] = $var[0]['description'];
                        $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                        $inc_pp++;
                    }

                    $entities[$i]['sponsor_id'] = $row['sponsor_id'];
                    $sponsor_array = explode(",", $row['sponsor_id']);

                    $sponsor_det_array = array();
                    if (count($sponsor_array) > 1) {

                        $inc_spp = 0;
                        foreach ($sponsor_array as $single_sponsor) {

                            $var = sponsor_detail($single_sponsor);
                            $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);

                            if (stripos($image, "https://storage.googleapis.com") > -1) {

                                $logic_image = $image;
                            } else {

                                $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
                            }
                            $sponsor_det_array[$inc_spp]['sponsor_name'] = $var[0]['client_name'];
                            $sponsor_det_array[$inc_spp]['sponsor_logo'] = change_img_src($logic_image);
                            $sponsor_det_array[$inc_spp]['sponsor_id'] = $var[0]['client_master_id'];

                            $inc_spp++;
                        }
                    } else {

                        if ($row['sponsor_id']) {

                            $var = sponsor_detail($row['sponsor_id']);
                            $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);
                            if (stripos($image, "https://storage.googleapis.com") > -1) {
                                $logic_image = $image;
                            } else {

                                $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
                            }
                            $sponsor_det_array['sponsor_name'] = $var[0]['client_name'];
                            $sponsor_det_array['sponsor_logo'] = change_img_src($logic_image);
                            $sponsor_det_array['sponsor_id'] = $var[0]['client_master_id'];
                        }
                    }
                    //$row['sessions_question'];
                    if ($row['sessions_question'] != '') {

                        $qu_val = explode("#", $row['sessions_question']);
                        $queries = $qu_val;
                    } else {

                        $queries = array();
                    }
                    //$queries = array('I want to know how this system will work', 'I want to know the main topic', 'I want to know when we can start', 'I want to know when we can start', 'I want to know when we can start');

                    $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
                    //$entities[$i]['sponsor_entities'] = $sponsor_det_array;

                    $entities[$i]['sponsor_entities'] = $sponserentity;
                    $entities[$i]['session_queries'] = $queries;
                    $entities[$i]['survey'] = $vxPoll;
                    $entities[$i]['disclaimer'] = 'The information in this educational activity is provided for general medical education purposes meant for registered medical practitioners only. The activity is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. The viewpoints expressed in this CME activity are those of the authors/faculty. They do not represent an endorsement by CLIRNET or the Sponsor. In no event will CLIRNET, the Sponsor or, the authors/faculty be liable for any decision made or action taken in reliance upon the information provided through this CME activity. All CMEs are recorded to be used for research & information purposes only. Clirnet at the request of Sponsor, may share your details such as name, location, recording of the session and session feedback for information purposes only.
    ';

                    $i++;
                }

                return $entities;
                break;
            case 'epub':

                $sql = "SELECT
                cm.epub_id as type_id,
                
                cm.epub_description as description,
                cm.epub_title as title,
                
                cm.epub_img,
                cm.epub_img_thumbnail,
                cm.epub_file,
                cm.start_like,
                cm.added_on,
                cm.publication_date as publish_date,
                cln.client_name,
                cln.client_logo,
                
                
                cm.deeplink,
                cm.deeplink,
                cm.color,
                cm.author,
                
                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                
                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                
                
                (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.epub_id and  rt.post_type='epub')as averageRating,
                rtmy.rating  as myrating,
                
                (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.epub_id and kcm.type = 'epub')as count_comment,
                kv.status as vault
                
                FROM epub_master as cm
                
                JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
                JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                JOIN client_master as cln ON cln.client_master_id = cm.client_id
                
                LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id 
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id 
                
                
                
                LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type_text='epub' and  kv.user_id = " . $user_master_id . "
                LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'
                
                WHERE 
                cm.status=3
                AND 
                cm.epub_id = " . $type_id . "";

                //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
                //echo $sql; exit;

                $query = $CI->db->query($sql);
                $result = $query->row();

                //poll start

                $sqlCompl = "SELECT 
                sv.* 
                
                FROM 
                survey_user_answer sv
                WHERE 
                sv.user_master_id = '" . $user_master_id . "'";
                $queryCompl = $CI->db->query($sqlCompl);
                $resultCompl = $queryCompl->result();

                $complID = array();
                foreach ($resultCompl as $valCompl) {

                    $complID[] = $valCompl->survey_id;
                }
                //print_r($complID); exit;

                //----------------------------------------------------------------
                $sqlAuther = "SELECT * FROM epub_to_author WHERE epub_id = $type_id ";
                $queryAuther = $CI->db->query($sqlAuther);
                $resultAuther = $queryAuther->result();

                $autherArray = array();
                $autherIndex = 0;
                foreach ($resultAuther as $rw) {
                    $autherArray[$autherIndex] = array();
                    $autherArray[$autherIndex]['author_name'] = $rw->author_name;
                    $autherArray[$autherIndex]['author_image'] = change_img_src($rw->author_image);
                    $autherArray[$autherIndex]['author_description'] = $rw->author_description;
                    $autherIndex++;
                }

                //print_r($complID); exit;
                //----------------------------------------------------------------

                $sqlInCompl = "SELECT 
                sv.* 
                
                FROM 
                survey_user_incomplete_answer sv
                WHERE 
                sv.status = 3
                and 
                sv.user_master_id = '" . $user_master_id . "'";
                $queryInCompl = $CI->db->query($sqlInCompl);
                $resultInCompl = $queryInCompl->result();

                $incomplID = array();
                foreach ($resultInCompl as $valInCompl) {

                    $incomplID[] = $valInCompl->survey_id;
                }
                $arrayFinal = array_unique(array_merge($complID, $incomplID));
                //print_r($arrayFinal); exit;
                $complIDStr = implode(",", array_filter($arrayFinal));
                //echo $complIDStr ; exit;

                if ($complIDStr) {
                    $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
                } else {
                    $qryStr = '';
                }
                $sqlPoll = "SELECT 
                sv.* ,
                svd.data,
                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                cln.client_name,
                cln.client_logo,
                
                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                
                FROM 
                survey sv
                left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id          
                left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                JOIN client_master as cln ON cln.client_master_id = sv.client_id
                
                
                
                LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                
                
                
                JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
                JOIN survey_to_medwiki as stm ON stm.survey_id = sv.survey_id
                
                left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
                WHERE 
                sv.status = 3 
                and 
                stm.medwiki_id = " . $type_id . "
                " . $qryStr . " ";

                $queryPoll = $CI->db->query($sqlPoll);
                $resultPoll = $queryPoll->result();

                //echo $sqlPoll;
                // print_r($resultPoll);
                $vxPoll = array();
                foreach ($resultPoll as $valSurvey) {
                    $dataArry = unserialize($valSurvey->data);
                    $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                    $str = preg_replace('/\\\"/', "\"", $json);
                    $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);

                    if (count($sponsorLogoArry) > 0) {

                        foreach ($sponsorLogoArry as $valueSponor) {

                            if ($valueSponor) {
                                $sponsorLogomix[] = '' . $valueSponor;
                            }
                        }
                    } else {

                        if ($valSurvey->sponsor_logo) {
                            $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                        }
                    }

                    $sponsorLogo = implode(",", (array)$sponsorLogomix);

                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);
                    if ($valSurvey->survey_id) {

                        $vxPoll[] = array(

                            "survey_id" => $valSurvey->survey_id,
                            "category" => $valSurvey->category,
                            "point" => $valSurvey->survey_points,
                            "json_data" => $str,
                            "survey_title" => $valSurvey->survey_title,
                            "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0), //$valSurvey->deeplink,
                            "survey_description" => substr($valSurvey->survey_description, 0, 150),
                            "image" => change_img_src($valSurvey->image),
                            "specialities_name" => $valSurvey->specialities_name,
                            "client_name" => $valSurvey->client_name,
                            "client_logo" => change_img_src('' . $valSurvey->client_logo),

                            "sponsor_name" => $valSurvey->sponsor,
                            "sponsor_logo" => change_img_src($sponsorLogo),
                            "publishing_date" => $valSurvey->publishing_date,

                        );
                    }
                }

                //print_r($vxPoll);
                //poll end
                if ($result->epub_img_thumbnail) {

                    $img = $result->epub_img_thumbnail; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;

                } else {

                    $img = '';
                }

                $sponsorLogoArry = explode(",", $result->sponsor_logo);

                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {

                    if ($result->sponsor_logo) {
                        $sponsorLogomix[] = '' . $result->sponsor_logo;
                    }
                }

                $sponsorLogo = implode(",", (array)$sponsorLogomix);

                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                $string = $result->comp_qa_question_raw;
                $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                $main_description = "";
                $main_description = str_replace("\n\t", "\n", $result->comp_qa_answer_raw);
                $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
                $vx = array(

                    "type_id" => $result->type_id,

                    "type" => 'epub',
                    "date" => date(' jS F y', strtotime($result->publication_date)),
                    "title" => html_entity_decode($result->title),
                    "description" => html_entity_decode($result->description), //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),

                    "epub_file" => $result->epub_file,
                    "author" => $result->author,
                    "image" => change_img_src($img),
                    "author_entities" => $autherArray,

                    "specialities" => $result->specialities_name,

                    "specialities_id" => $result->comp_qa_speciality_id,
                    "client_name" => $result->client_name,
                    "client_logo" => change_img_src('' . $result->client_logo),
                    "sponsor_name" => $result->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),
                    "start_like" => $result->start_like,

                    "comment_count" => $result->count_comment,
                    "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
                    "myrating" => ($result->myrating != '') ? true : false,
                    "vault" => ($result->vault != '') ? $result->vault : 0,
                    "deeplink" => ($env == 'GL') ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
                    "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,

                    //  "disclaimer" => 'All scientific content on the platform is provided for general medical education purposes meant for registered medical practitioners only. The content is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. In no event will CLIRNET be liable for any decision made or action taken in reliance upon the information provided through this content.',
                    "disclaimer" => disclaimer('knowledge'),
                    "survey" => $vxPoll,

                );
                // if ($from_type) {}
                //add child checking in this sql
                //echo $sql;
                //exit;

                return $vx;
                break;
            case 'channel':

                $sqlInt = "select 
                count(channel_master_id) as total
                from
                channel_to_user
                where 
                status = 3
                and
                channel_master_id = " . $type_id . "";
                $queryInt = $CI->db->query($sqlInt);
                $resultInt = $queryInt->row();
                //echo $specialities; exit;
                //get user speciality
                if ($client_ids) {

                    //$client_list = ' and '. implode(' OR ', array_map(function($x) { return "FIND_IN_SET('$x', kcp.client_id)"; }, explode(',', $this->session->userdata('client_ids'))));
                    $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                        return "FIND_IN_SET('$x', fd.client_id)";
                    }, explode(',', $client_ids))) . ')';
                }
                $sql = "SELECT
                ch.channel_master_id as type_id,
                ch.title as title,
                ch.description as description,
                ch.short_description as short_description,
                ch.membershipformurl,
                cTus.status as followed_status,
                
                ch.cover_image,
                ch.privacy_status,
                
                ch.logo,
                ch.address,
                ch.added_on,
                ch.deeplink,
                ch.gl_deeplink,
                ch.featured_video,
                bnd.logo,
                cln.client_name,
                cln.client_logo,
                
                GROUP_CONCAT(DISTINCT chTsdoc.session_doctor_id  ORDER BY  chTsdoc.session_doctor_id  ASC ) as session_doctor_id,
                GROUP_CONCAT(DISTINCT chTsdoc.description  ORDER BY  chTsdoc.session_doctor_id  ASC SEPARATOR '----') as ch_doc_description
                
                
                
                FROM channel_master as ch
                LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = ch.channel_master_id
                LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
                LEFT JOIN client_master as cln ON cln.client_master_id = ch.client_id   
                LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = ch.brand_id   
                
                
                LEFT JOIN channel_to_session_doctor as chTsdoc ON chTsdoc.channel_master_id = ch.channel_master_id 
                
                LEFT JOIN channel_to_user  as cTus ON (cTus.channel_master_id = ch.channel_master_id and user_master_id = " . $user_master_id . "   )        
                
                WHERE 
                ch.status=3
                and
                ch.channel_master_id = " . $type_id . "";

                //echo $sql; exit;
                //exit;
                //add child checking in this sql
                //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
                //exit;
                //echo  $sql; exit;

                $query = $CI->db->query($sql);
                //$this->db->cache_off();
                $val = $query->row();
                //print_r($result); exit;
                //echo $val->gr_preview_image;

                $ses_doc_det_array = array();
                if ($val->session_doctor_id) {

                    $session_doc_array = explode(",", $val->session_doctor_id);
                    $session_gr_doc_description_array = explode("----", $val->ch_doc_description);
                    $inc_pp = 0;
                    foreach ($session_doc_array as $single_doctor) {

                        $var = session_doc_detail($single_doctor);

                        if ($session_gr_doc_description_array[$inc_pp]) {

                            $gr_doc_descriptionLa = $session_gr_doc_description_array[$inc_pp];
                        } else {

                            $gr_doc_descriptionLa = '';
                        }
                        //print_r($var);

                        if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {

                            $logic_image = $var[0]['profile_image'];
                        } else {
                            // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            // $logic_image_path = docimg; //"uploads/docimg/" . $image;
                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = docimg;
                            //$logic_image = $var[0]['profile_image'];
                        }

                        $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                        $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                        // $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $var[0]['profile'];//$var[0]['description']; //$session_gr_doc_description_array[$inc_pp];
                        $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $gr_doc_descriptionLa; //$session_gr_doc_description_array[$inc_pp];
                        $inc_pp++;
                    }
                }
                $vx = array();

                $vx[] = array(
                    "type_id" => $val->type_id,
                    "type" => 'channel',
                    "added_on" => date(' jS F y', strtotime($val->added_on)),
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "cover_image" => change_img_src($val->cover_image),
                    "logo" => change_img_src($val->client_logo),
                    "followed_status" => ($val->followed_status != '') ? $val->followed_status : 0,

                    "follower_count" => $resultInt->total,
                    "address" => $val->address,

                    "privacy_status" => $val->privacy_status,

                    //"user_subs_status" => $val->address,

                    "featured_video" => archiveVideoDetial($val->featured_video, $user_master_id),
                    "featured_video_id" => $val->featured_video,

                    "description" => $val->description,
                    "short_description" => html_entity_decode(strip_tags(substr($val->short_description, 0, 300))),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',

                    "channel_doctor_entities" => $ses_doc_det_array,
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                    "disclaimer" => disclaimer('knowledge'),
                    "membershipformurl" => $val->membershipformurl,

                );

                return $vx;
                break;
            default:
                return '';
                break;
        }
    }

    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function all_channel($user_master_id, $client_ids, $limitTo, $limitFrom)
    {
        if (!empty($user_master_id)) {

            //get user speciality
            // $sqlInt = "select
            // specialities_id
            // from
            // user_to_interest
            // where
            // user_master_id = " . $user_master_id . "";
            // $queryInt = $this->db->query($sqlInt);
            // $resultInt = $queryInt->result_array();

            // //print_r($resultInt); exit;
            // $specialities = array();
            // foreach ($resultInt as $val) {

            //     $specialities[] = $val['specialities_id'];
            //     //$specialities = array_merge($specialities, $val);

            // }
            // if (!empty($specialities)) {

            //     $specialityIds = implode(",", (array)$specialities);
            // }
            // if ($specialityIds != '') {

            //     $specialities_query = ' and (' . implode(' OR ', array_map(function ($x) {
            //         return "FIND_IN_SET('$x', fd.speciality_id)";
            //     }, explode(',', $specialityIds))) . ')';
            // } else {
            //     $specialities = "";
            // }
            //echo $specialities; exit;
            //get user speciality
            // if ($client_ids) {
            //     //$client_list = ' and '. implode(' OR ', array_map(function($x) { return "FIND_IN_SET('$x', kcp.client_id)"; }, explode(',', $this->session->userdata('client_ids'))));

            //     $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
            //         return "FIND_IN_SET('$x', fd.client_id)";
            //     }, explode(',', $client_ids))) . ')';
            // }
            if ($limitFrom != '' && $limitTo != '') {

                $limit = "limit " . $limitFrom . " , " . $limitTo;
            } else {
                $limit = "limit 0,5";
            }

            $sql = "SELECT
            ch.channel_master_id as type_id,
            ch.title as title,
            ch.description as description,
            
            ch.cover_image,
            ch.added_on,
            ch.deeplink,
            ch.gl_deeplink,
            bnd.logo,
            cln.client_name,
            cln.client_logo
            FROM channel_master as ch
            LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = ch.channel_master_id
            LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
            LEFT JOIN client_master as cln ON cln.client_master_id = ch.client_id   
            LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = ch.brand_id   
            
            LEFT JOIN channel_to_user  as cTus ON (cTus.channel_master_id = ch.channel_master_id  and (cTus.status = 1 or cTus.status = 3)  )
            
            WHERE 
            ch.status=3
            
            and 
            cTus.user_master_id != " . $user_master_id . "
            
            group by ch.channel_master_id
            order by  ch.added_on desc " . $limit . "";
            //echo $sql; exit;
            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;
            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();
            //print_r($result); exit;
            $i = 1;
            $vx = array();
            foreach ($result as $val) {
                $vx[] = array(

                    "slno" => $i,
                    "type_id" => $val->type_id,
                    "type" => 'channel',
                    "added_on" => date(' jS F y', strtotime($val->added_on)),
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "cover_image" => change_img_src($val->cover_image),
                    "logo" => change_img_src($val->logo),

                    "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "slug" => str_replace(' ', '-', strip_tags($val->title)), //html_entity_decode(strip_tags($val->title)),

                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0) //($val->deeplink != '') ? $val->deeplink : 0,

                );

                $i++;
            }
            return $vx;
        }
    }

    /**
     * @param $user_master_id
     * @param $client_ids
     * @param $limitTo
     * @param $limitFrom
     * @return array
     */
    public function all_channelSuggestion($user_master_id, $client_ids, $limitTo, $limitFrom, $type_id)
    {
        if (!empty($user_master_id)) {

            // $sqlIntcount = "select
            // count(DISTINCT(user_master_id)) as total
            // from
            // channel_to_user
            // where
            // status = 3
            // and
            // channel_master_id = " . $type_id . "";
            // $queryIntcount = $this->db->query($sqlIntcount);
            // $resultIntcount = $queryIntcount->row();
            // print_r($queryIntcount); exit;

            //get user selection
            $sqlInt = "select 
            channel_master_id
            from
            channel_to_user
            where 
            ( status = 3 or status = 1 )
            and
            user_master_id = " . $user_master_id . "";
            $queryInt = $this->db->query($sqlInt);
            $resultInt = $queryInt->result_array();

            //print_r($resultInt); exit;
            $channelMasterIds = array();
            foreach ($resultInt as $val) {

                $channelMasterIds[] = $val['channel_master_id'];
                //$specialities = array_merge($specialities, $val);

            }
            if (count($channelMasterIds) > 0) {

                $channelIds = implode(",", (array)$channelMasterIds);
            }
            if ($channelIds != '') {

                $channelIds_query = ' and   ch.channel_master_id  not in (' . $channelIds . ')';
            } else {
                $channelIds_query = "";
            }
            //echo $specialities; exit;
            //get user selection

            if ($client_ids) {

                //$client_list = ' and '. implode(' OR ', array_map(function($x) { return "FIND_IN_SET('$x', kcp.client_id)"; }, explode(',', $this->session->userdata('client_ids'))));
                $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                    return "FIND_IN_SET('$x', fd.client_id)";
                }, explode(',', $client_ids))) . ')';
            }
            if ($limitFrom == 0 and $limitTo != '') {

                $limit = "limit " . $limitFrom . " , " . $limitTo;
            } else {
                $limit = "limit " . $limitFrom . " , " . $limitTo;
            }

            if ($type_id) {

                $typeQury = "and ch.channel_master_id != " . $type_id . "";
            } else {

                $typeQury = "";
            }

            // ====================== fetching env_id  ======================//
            $env = get_user_env_id($user_master_id);
            if ($env) {
                if ($env != 2) {
                    $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                } else {
                    $envStatus = "AND cTenv.env =" . $env . "";
                }
            } else {
                $envStatus = "";
            }

            $key_locked = get_user_package($user_master_id, 'channel');
            // if($key_locked == '') {
            //     return NULL;
            // }
            // ====================== fetching env_id  ======================//
            $sql = "SELECT
            ch.channel_master_id as type_id,
            ch.title as title,
            ch.description as description,
            ch.privacy_status,
            cTus.status as sub_stat,
            ch.cover_image,
            ch.is_share,
            ch.added_on,
            ch.deeplink,
            (select count(DISTINCT(user_master_id)) as total from channel_to_user where status = 3 and channel_master_id = ch.channel_master_id) as total,
            cTenv.price,
            uTpyCont.status as user_contnet_payment_status,

            ch.gl_deeplink,
            ch.follower_count,
            bnd.logo,
            cln.client_name,
            
            cln.client_logo
            FROM channel_master as ch
            LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = ch.channel_master_id
            LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
            LEFT JOIN client_master as cln ON cln.client_master_id = ch.client_id   
            LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = ch.brand_id  
            LEFT JOIN channel_to_user  as cTus ON ( cTus.channel_master_id = ch.channel_master_id AND cTus.status = 0)
            
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = ch.channel_master_id and  cTenv.type = 11
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = ch.channel_master_id and  uTpyCont.type = 11 and 	uTpyCont.user_master_id = " . $user_master_id . "
  
            
            
            WHERE 
            ch.status=3 and ch.privacy_status = 0
            " . $typeQury . "
            " . $channelIds_query . "
            " . $envStatus . "
                    
            group by ch.channel_master_id
            order by  ch.added_on desc " . $limit . "";
            //echo $sql; exit;

            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;
            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();
            //print_r($result); exit;
            $i = 1;
            $vx = array();
            foreach ($result as $val) {
                if ($val->sub_stat == null || $val->sub_stat == '') {
                    $fstat = 0;
                } else {
                    $fstat = $val->sub_stat;
                }

                $vx[] = array(

                    "slno" => $i,
                    "type_id" => $val->type_id,
                    "is_share" => get_a_content_is_share_status($val->type_id, '11'),
                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "price" => $val->price,
                    "user_content_payment" => $val->user_contnet_payment_status,
                    //============ integrated for subscription ============//

                    "type" => 'channel',
                    "privacy_status" => $val->privacy_status,
                    "followed_status" => $fstat,
                    "a_followers" => $val->total,
                    "added_on" => date(' jS F y', strtotime($val->added_on)),
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "cover_image" => change_img_src($val->cover_image),
                    "logo" => change_img_src($val->client_logo),
                    "slug" => str_replace(' ', '-', strip_tags($val->title)), //html_entity_decode(strip_tags($val->title)),
                    "description" => html_entity_decode(strip_tags($val->description)),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0) //($val->deeplink != '') ? $val->deeplink : 0,

                );
                $i++;
            }
            return $vx;
        }
    }

    /**
     * @param $user_master_id
     * @param $client_ids
     * @param $limitTo
     * @param $limitFrom
     * @return array
     */
    public function all_userChannel($user_master_id, $client_ids, $limitTo, $limitFrom)
    {
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'channel');
        // if($key_locked == '') {
        //     return NULL;
        //     }

        // ====================== fetching env_id  ======================//

        // if user is a GL user then the channel will not be fetched
        // echo "<pre>";print_r($env);die;
        // if($env == 2) {
        //     return NULL;
        // }

        if (!empty($user_master_id)) {

            //get user speciality
            // $sqlInt = "select
            // specialities_id
            // from
            // user_to_interest
            // where
            // user_master_id = " . $user_master_id . "";
            // $queryInt = $this->db->query($sqlInt);
            // $resultInt = $queryInt->result_array();

            // //print_r($resultInt); exit;
            // $specialities = array();
            // foreach ($resultInt as $val) {

            //     $specialities[] = $val['specialities_id'];
            //     //$specialities = array_merge($specialities, $val);

            // }
            // if (count($specialities) > 0) {

            //     $specialityIds = implode(",", (array)$specialities);
            // }
            // if ($specialityIds != '') {

            //     $specialities_query = ' and (' . implode(' OR ', array_map(function ($x) {
            //         return "FIND_IN_SET('$x', fd.speciality_id)";
            //     }, explode(',', $specialityIds))) . ')';
            // } else {
            //     $specialities = "";
            // }
            // //echo $specialities; exit;
            // //get user speciality
            // if ($client_ids) {

            //     //$client_list = ' and '. implode(' OR ', array_map(function($x) { return "FIND_IN_SET('$x', kcp.client_id)"; }, explode(',', $this->session->userdata('client_ids'))));
            //     $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
            //         return "FIND_IN_SET('$x', fd.client_id)";
            //     }, explode(',', $client_ids))) . ')';
            // }
            if ($limitFrom != '' && $limitTo != '') {
                $limit = "limit " . $limitFrom . " , " . $limitTo;
            } else {
                $limit = "limit 0, 5 ";
            }

            $sql = "SELECT
            ch.channel_master_id as type_id,
            ch.title as title,
            ch.description as description,
            ch.privacy_status,
            ch.logo,
            ch.is_share,
            ch.cover_image,
            ch.added_on,
            ch.deeplink,
            ch.gl_deeplink,
            bnd.logo,
            cln.client_name,
            cln.client_logo,
            cTus.status as followed_status,
            
            cTenv.price,
            uTpyCont.status as user_contnet_payment_status

            
            FROM channel_master as ch
            LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = ch.channel_master_id
            LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
            LEFT JOIN client_master as cln ON cln.client_master_id = ch.client_id   
            LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = ch.brand_id  
             
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = ch.channel_master_id and  cTenv.type = 11
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = ch.channel_master_id and  uTpyCont.type = 11 and 	uTpyCont.user_master_id = " . $user_master_id . "
 
            LEFT JOIN channel_to_user  as cTus ON (cTus.channel_master_id = ch.channel_master_id  and (cTus.status = 1 or cTus.status = 3) )
            
            WHERE 
            ch.status=3
            " . $envStatus . "
            and ch.privacy_status = 0 and
            cTus.user_master_id = " . $user_master_id . "
            group by ch.channel_master_id
            order by  ch.added_on desc " . $limit . "";
            //echo $sql; exit;
            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;
            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();
            //print_r($result); exit;
            $i = 1;
            $vx = array();
            foreach ($result as $val) {
                $vx[] = array(

                    "slno" => $i,
                    "type_id" => $val->type_id,
                    "is_share" => get_a_content_is_share_status($val->type_id, '11'),
                    "type" => 'channel',
                    "privacy_status" => $val->privacy_status,
                    "followed_status" => $val->followed_status,
                    "added_on" => date(' jS F y', strtotime($val->added_on)),
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "logo" => change_img_src($val->logo),
                    "cover_image" => change_img_src($val->cover_image),
                    "logo" => change_img_src($val->client_logo),
                    "slug" => str_replace(' ', '-', strip_tags($val->title)), //html_entity_decode(strip_tags($val->title)),
                    "description" => html_entity_decode(strip_tags($val->description)),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "price" => $val->price,
                    "user_content_payment" => $val->user_contnet_payment_status,
                    //============ integrated for subscription ============//
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0) //($val->deeplink != '') ? $val->deeplink : 0,

                );
                $i++;
            }
            return $vx;
        }
    }

    /**
     * @param $user_master_id
     * @param $client_ids
     * @param $limitTo
     * @param $limitFrom
     * @return array
     */
    public function all_popular($user_master_id, $client_ids, $limitTo, $limitFrom)
    {
        if (!empty($user_master_id)) {

            $type_id = 43;

            $limitToFn = floor($limitTo / 3);
            $limitFromFn = floor($limitFrom / 3);

            $compArry = $this->getcomp($user_master_id, $limitFromFn, $limitToFn, $type_id);

            $spqArry = $this->getspq($user_master_id, $limitFromFn, $limitToFn, $type_id);

            $sessionpArry = $this->getsession($user_master_id, $limitFromFn, $limitToFn, $type_id);

            $archiveVideoArry = $this->getarchiveVideo($type_id, $user_master_id, $client_ids, $limitFrom, $limitTo);

            $finalArry = array_merge_recursive($sessionpArry, $compArry, $spqArry, $archiveVideoArry);

            //$menuItems = array_slice( $finalArry, $limitFrom, $limitTo );
            //print_r($menuItems);
            return $finalArry;
        }
    }

    /**
     * @param string $user_master_id
     * @param string $client_ids
     * @param string $type_id
     * @param string $limitTo
     * @param string $limitFrom
     * @return array
     *
     */
    public function all_list_detail($user_master_id = '', $client_ids = '', $type_id = '', $limitTo = '', $limitFrom = '', $env, $convert)
    {
        if (!empty($user_master_id)) {

            if (empty($limitTo) || $limitTo == '') {
                $limitToFn = 10;
            } else {
                $limitToFn = $limitTo;
            }

            if (empty($limitFrom) || $limitFrom == '') {
                $limitFromFn = 0;
            } else {
                $limitFromFn = $limitFrom;
            }

            $header_data = array_change_key_case($this->input->request_headers(), CASE_LOWER);
            if (isset($header_data['version'])) {
                $version = $header_data['version'];
            } else {
                $version = "";
            }
            $comp_id_array = array();

            $compArry = $this->getcomp($user_master_id, $limitFromFn, $limitToFn, $type_id, $env);

            $spqArry = $this->getspq($user_master_id, $limitFromFn, $limitToFn, $type_id, $env);

            $sessionpArry = $this->getsession($user_master_id, $limitFromFn, $limitToFn, $type_id, $env);

            $archiveVideoArry = $this->getarchiveVideo($type_id, $user_master_id, $client_ids, $limitFrom, $limitTo, $env);

            $epubArray = $this->getepub($type_id, $user_master_id, $client_ids, $limitFrom, $limitTo, $convert);

            $postArry = header_checker($version, json_decode(version_check, true), null, $this->getPost($user_master_id, $limitFromFn, $limitToFn, $type_id));
            $docmtArry = header_checker($version, json_decode(version_check, true), null, $this->getDocmnt($user_master_id, $limitFromFn, $limitToFn, $type_id));

            // $postArry = $this->getPost($user_master_id, $limitFromFn, $limitToFn, $type_id);

            // $docmtArry = $this->getDocmnt($user_master_id, $limitFromFn, $limitToFn, $type_id);

            $courseArry = $this->gettraining($user_master_id, $limitFromFn, $limitToFn, $type_id);
            // $finalArry = array_merge_recursive((array)$sessionpArry, (array)$compArry, (array)$spqArry, (array)$archiveVideoArry, (array)$epubArray, (array)$courseArry);
            $finalArry = array_merge_recursive((array)$sessionpArry, (array)$compArry, (array)$spqArry, (array)$archiveVideoArry, (array)$epubArray, (array)$postArry, (array)$docmtArry, (array)$courseArry);
            usort($finalArry, function ($a, $b) {
                $dateA = strtotime($a['date']);
                $dateB = strtotime($b['date']);

                if ($dateA == $dateB) {
                    return 0;
                }

                return ($dateA < $dateB) ? 1 : -1;
            });
            //print_r($finalArry); exit;
            //$menuItems = array_slice( $finalArry, $limitFrom, $limitTo );
            //print_r($menuItems);
            return $finalArry;
        }
    }

    /**
     * @param string $user_master_id
     * @param string $client_ids
     * @param string $type_id
     * @param string $limitTo
     * @param string $limitFrom
     * @return array
     *
     */
    public function all_list_count($user_master_id = '', $client_ids = '', $type_id = '', $env, $convert)
    {
        $count = 0;
        if (!empty($user_master_id)) {
            // echo $env; exit;
            $cachename = "channel-all-" . $type_id;
            if ($this->myredis->exists($cachename)) {
                $finalArry['total_activity'] = $this->myredis->get($cachename);
                //print_r( $val); exit;
            } else {
                $envStatus = "";
                if ($env == 'IN') {
                    $envStatus = " and (cm.env='IN' OR cm.env='GL')";
                } else {
                    $envStatus = " and cm.env = 'GL'";
                }
                /** count as per data */
                /** channel to compendium */
                $sqlcomp = "SELECT count(cm.comp_qa_id) as count
                FROM knwlg_compendium_V1 as cm

                LEFT JOIN channel_to_compendium as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
                WHERE 
                cm.status=3 and cm.privacy_status in (0,1) and cm.publication_date <= CURDATE()
                and cmTs.channel_master_id= " . $type_id . "" . $envStatus . "";
                //echo $sqlcomp; exit;
                $querycomp = $this->db->query($sqlcomp)->row();
                $count += $querycomp->count;

                /** channel to compendium */

                /** channel to survey */
                $sqlsurvey = "SELECT count(sv.survey_id) as count
                        FROM survey sv
                        LEFT JOIN  channel_to_survey AS cts ON cts.survey_id = sv.survey_id
                        WHERE 
                        cts.channel_master_id= " . $type_id . " " . $envstatus . " and
                        sv.status = 3 and sv.privacy_status in (0,1) and sv.publishing_date <= CURDATE()";
                $querysurvey = $this->db->query($sqlsurvey)->row();
                $count += $querysurvey->count;

                /** channel to survey */

                /** channel to session */
                $sqlsession = "SELECT
                    count(ks.session_id) as count
                    FROM knwlg_sessions_V1 as ks
                    LEFT JOIN channel_to_session AS cts ON cts.session_id = ks.session_id 
                    WHERE 
                    ks.status = 3 and cts.channel_master_id= " . $type_id . " and ks.privacy_status != 2 and ks.session_status not in (3,5,6)
                    " . $envstatus . "";
                $querysession = $this->db->query($sqlsession)->row();
                $count += $querysession->count;
                /** channel to session */

                /** archieve to video to channel */
                $sqlclinicalvideo = "SELECT count(cm.video_archive_id) as count
                        FROM knwlg_video_archive as cm 
                        LEFT JOIN channel_to_video_archive as ctva ON cm.video_archive_id = ctva.video_archive_id 
                        WHERE 
                        cm.status=3 and cm.privacy_status != 2 and cm.publication_date <= CURDATE()
                        and ctva.channel_master_id = " . $type_id . " " . $envStatus . "";
                $queryclinicalvideo = $this->db->query($sqlclinicalvideo)->row();
                $count += $queryclinicalvideo->count;
                /** archieve to video to channel */

                /** channel to epub */
                $clientsql = "select client_id from channel_master where channel_master_id = " . $type_id;
                $queryclient = $this->db->query($clientsql)->row();

                $sqlepub = "SELECT count(cm.epub_id) as count
                FROM epub_master as cm
                
                WHERE cm.client_id = " . $queryclient->client_id . " and
                cm.status=3 and cm.privacy_status != 2 and cm.publication_date <= CURDATE() and cm.is_converted = 1 " . $envStatus;
                $queryepub = $this->db->query($sqlepub)->row();

                $count += $queryepub->count;

                /** channel to epub */

                /** post */
                $sqlpost = "select count(id) as count
                            from
                            channel_to_post 
                            where status = 3 AND 
                            post_date <= CURDATE() AND 
                            channel_master_id = '$type_id' ";
                $querypost = $this->db->query($sqlpost)->row();
                $count += $querypost->count;

                /** document */
                $sqldocument = "select count(id) as count  
                            from
                            channel_to_document 
                            where  status = 3 and 
                            channel_master_id = " . $type_id;
                $querydocument = $this->db->query($sqldocument)->row();
                $count += $querydocument->count;
                /** document */

                /** channel to course */
                $sqlcourse = "select count(cm.id) as count from training_master as cm join channel_to_course as ctc on ctc.course_id=cm.id where  cm.status=3 and cm.privacy_status != 2 and cm.published_date <= CURDATE()  AND 
                            channel_master_id = '$type_id'" . $envStatus;
                $querycourse = $this->db->query($sqlcourse)->row();
                $count += $querycourse->count;
                /** channel to course */
                $finalArry['total_activity'] = $count;
                //print_r($finalArry); exit;
                $this->myredis->set($cachename, $count);
            }
            return $finalArry;
        }
    }

    public function gettraining($user_master_id, $limitTo, $limitFrom, $type_id)
    {

        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $env_arr = array(2, $env);
            } else {
                $env_arr = array($env);
            }
        } else {
            $env_arr = array("");
        }

        $key_locked = get_user_package($user_master_id, 'training');
        if ($key_locked == '') {
            return null;
        }

        // ====================== fetching env_id  ======================//
        $response = array();
        $id = array();
        $vx = array();
        $this->db->select("tm.*, cTenv.price, uTpyCont.status as user_contnet_payment_status,ctc.is_featured,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id) )as master_spec_id,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,(select count(id) from training_module where training_module.training_id=tm.id and training_module.status=3) as count_module,(select count(id) from training_module_content where training_module_content.type='live_video' and status=3 and training_module_content.training_id=tm.id) as total_live_training");
        $this->db->from('training_master as tm');
        $this->db->join('channel_to_course as ctc', "(ctc.course_id = tm.id and ctc.channel_master_id=$type_id)");
        $this->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
        $this->db->join('client_master as clintspon', 'ts.sponsor_id=clintspon.client_master_id', 'left');
        $this->db->join('training_to_speciality tts', 'tts.training_id=tm.id', 'left');

        $this->db->join('content_to_env as cTenv', "cTenv.type_id = tm.id and  cTenv.type = 4", "left");
        $this->db->join('payment_user_to_content as uTpyCont', "uTpyCont.type_id = tm.id and  uTpyCont.type = 4", "left");
        $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
        $this->db->where('tm.status', 3);

        $this->db->where('date(tm.published_date)<=', date('Y-m-d'));
        $this->db->where_in('tm.privacy_status', [0, 1]);

        $this->db->where_in('cTenv.env', $env_arr);

        $this->db->group_by('tm.id');
        $this->db->order_by('tm.published_date', 'desc');

        if (($limitTo != '') || ($limitFrom != '')) {

            $this->db->limit($limitFrom, $limitTo);
        }

        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        $i = 1;
        if (($query) && ($query->num_rows())) {
            foreach ($query->result() as $key => $val) {
                $content_count['comp'] = $val->total_medwiki;
                $content_count['session'] = $val->total_session;
                $content_count['survey'] = $val->total_survey;
                $content_count['video_archieve'] = $val->total_clinical_video;
                $content_count['live_training'] = $val->total_live_training;
                $sponsorLogoArry = explode(",", $val->sponsor_logo);
                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = $valueSponor;
                        }
                    }
                } else {
                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = $val->sponsor_logo;
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                $ses_doc_det_array = array();
                if ($val->session_doctor_id) {
                    $session_doc_array = explode(",", $val->session_doctor_id);
                    $ses_doc_det_array = array();
                    $inc_pp = 0;
                    foreach ($session_doc_array as $single_doctor) {
                        $var = session_doc_detail($single_doctor);
                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                            $logic_image = $var[0]['profile_image'];
                        } else {
                            // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            // $logic_image_path = docimg; //"uploads/docimg/" . $image;
                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = docimg; //$imgPr;
                        }
                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                        $inc_pp++;
                    }
                }
                $completestatus = $this->complete_status($val->id, $user_master_id);
                $temp = array(
                    "slno" => $i,
                    "id" => $val->id,
                    "is_share" => get_a_content_is_share_status($val->id, '4'),
                    "is_like" => filter_var($val->is_like, FILTER_VALIDATE_BOOLEAN),
                    "is_commentable" => filter_var($val->is_commentable, FILTER_VALIDATE_BOOLEAN),
                    "trending_type" => "training",
                    "type" => "training",
                    "type_id" => $val->id,
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "image" => change_img_src($val->preview_image),
                    "featured_video" => $val->featured_video,
                    "is_featured" => $val->is_featured,
                    "color" => ($val->color != '') ? $val->color : '#eb34e5',
                    "description" => (strlen(strip_tags($val->description)) > 300) ? html_entity_decode(strip_tags(substr($val->description, 0, 300))) . "..." : html_entity_decode(strip_tags($val->description)),
                    "module_Count" => $val->count_module, //$this->count_module($val->id),
                    "live_session_status" => $this->livestatus($val->id),
                    "training_module_content" => $content_count, //$this->content_count($val->id),
                    "specialities" => $this->explode_speciality_string($val->specialities_ids_and_names, 0), //$this->specialityname($val->master_spec_id), #($val->specialities_name != '') ? $val->specialities_name : '',
                    "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                    "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src($val->client_logo),
                    "sponsor_name" => $val->sponsor,

                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "price" => get_a_content_price($val->id, 4, $env),
                    "user_content_payment" => get_user_content_status($val->id, 4, $user_master_id),
                    //============ integrated for subscription ============//

                    "sponsor_logo" => change_img_src($sponsorLogo),
                    "session_doctor_entities" => $ses_doc_det_array,
                    "duration" => $val->duration,
                    "is_completed" => $completestatus, #$this->complete_status($val->id,$user_master_id),
                    "is_certificate" => ($val->cert_template_id != '') ? true : false,
                    "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                    "date" => date(' jS F y', strtotime($val->published_date)),
                );

                $vx[] = $temp;
                $i++;
            }
        }
        return $vx;
    }

    public function livestatus($id)
    {
        $currentdatetime = date('Y-m-d H:i:s');
        $status = 0;
        if ($id != '') {
            $this->db->select('tmc.id,tmc.type_id');
            $this->db->from('training_module_content as tmc');
            $this->db->join('knwlg_sessions_V1 as ks', 'ks.session_id = tmc.type_id');
            $this->db->where(array('tmc.type' => 'session', 'tmc.training_id' => $id, 'ks.session_status' => 2, "tmc.status" => 3));
            $this->db->where("'" . $currentdatetime . "' BETWEEN ks.start_datetime and ks.end_datetime");

            $query = $this->db->get();
            if (($query) && ($query->num_rows() > 0)) {
                $status = 1;
            } else {
                $this->db->select('id');
                $this->db->from('training_module_content');
                $this->db->where(array('type' => 'live_video', 'training_id' => $id));
                $this->db->where("'" . $currentdatetime . "' BETWEEN start_datetime and end_datetime");

                $querylivevideo = $this->db->get();
                if (($querylivevideo) && ($querylivevideo->num_rows() > 0)) {
                    $status = 1;
                }
            }
        }
        // print_r($status);
        // die;
        return $status;
    }

    public function explode_speciality_string($string, $status)
    {
        $final = array();
        if (!empty($string)) {
            $temp_sp_array = explode(",", $string);
            foreach ($temp_sp_array as $ky => $sp_id_name) {
                $sp_id_name_array = explode("#", $sp_id_name);
                $final[$ky] = array();
                $final[$ky]['id'] = $sp_id_name_array[0];
                $final[$ky]['name'] = $sp_id_name_array[1];
                $result[] = $sp_id_name_array[1];
            }
        }

        if ($status == 0) {
            $final = implode(",", (array)$result);
        }

        return $final;
    }

    public function complete_status($training_id, $user_master_id)
    {
        $response = 0;
        $i = 0;
        $total_tmc = array();
        $total = 0;
        if (($training_id != '') && ($user_master_id != '')) {

            $this->db->select('id');
            $this->db->from('training_module');
            $this->db->where(array('training_id' => $training_id, 'status' => 3));

            $query_tm = $this->db->get();

            if (($query_tm) && ($query_tm->num_rows() > 0)) {
                #print_r($query_tm->result());
                foreach ($query_tm->result() as $keytm => $valuetm) {
                    # print_r($valuetm);
                    $module[] = $valuetm->id;
                }
            }

            $this->db->select('count(id) as total');
            $this->db->from('training_module_content');
            $this->db->where_in('module_id', $module);
            $this->db->where(array('training_id' => $training_id, 'status' => 3));

            $query_tmc = $this->db->get();

            if (($query_tmc) && ($query_tmc->num_rows() > 0)) {
                $total_tmc = $query_tmc->result();
                $total = $total_tmc[0]->total;
            }

            $this->db->select('id,training_details');
            $this->db->from('training_user_tracking');
            $this->db->where(array('training_id' => $training_id, 'user_master_id' => $user_master_id));
            $query = $this->db->get();
            #print_r($this->db->last_query()); die;
            if (($query) && ($query->num_rows() > 0)) {
                $result = $query->result();

                foreach ($result as $key => $value) {
                    foreach (json_decode($value->training_details) as $key => $value) {
                        $i = $i + 1;
                    }
                }

                # print_r($i);
                # $response = $result[0]->view_percentage;
                #print_r($response);
                #return $response;
            }
            if (($total != 0) && ($i != 0)) {
                $response = ($i / $total) * 100;
            }
        }
        #die;
        return $response;
    }

    public function getepub($type_id, $user_master_id, $client_ids, $limitFrom, $limitTo, $convert)
    {
        $vx = array();
        $this->db->select('client_id');
        $this->db->from('channel_master');
        $this->db->where('channel_master_id', $type_id);

        $query_clientid = $this->db->get();
        #print_r($this->db->last_query()); exit;

        if (($query_clientid) && ($query_clientid->num_rows() > 0)) {

            $c_id = $query_clientid->result();

            $client_id = $c_id[0]->client_id;
            if ($limitFrom != '' and $limitTo != '') {

                $limit = "limit " . $limitFrom . " , " . $limitTo;
            } else {

                $limit = "";
            }

            if ($convert == 1) {
                $mobileview = " and cm.is_converted = 1";
            } else {
                $mobileview = "";
            }
            if ($client_ids) {

                $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                    // return " FIND_IN_SET('$x', cm.client_id)";
                    return " cm.client_id IN({$x})";
                }, explode(',', $client_id))) . ')';
            }

            // ====================== fetching env_id  ======================//
            $env = get_user_env_id($user_master_id);
            if ($env) {
                if ($env != 2) {
                    $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                } else {
                    $envStatus = "AND cTenv.env =" . $env . "";
                }
            } else {
                $envStatus = "";
            }

            $key_locked = get_user_package($user_master_id, 'epub');
            if ($key_locked == '') {
                return null;
            }

            // ====================== fetching env_id  ======================//

            $sql = "SELECT
            cm.epub_id as type_id,
            
            cm.epub_description as description,
            cm.epub_title as title,
            
            cm.epub_img,
            cm.epub_img_thumbnail,
            cm.epub_file,
            cm.author,
            cm.is_share,
            cm.is_like,
            cm.is_comment,
            
            cm.added_on,
            cm.publication_date as publish_date,
            cln.client_name,
            cln.client_logo,
            cTenv.price,
            uTpyCont.status as user_contnet_payment_status,

            cm.display_in_dashboard,
            cm.deeplink,
            cm.color,
            cm.start_like,
            
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
            
            
            (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.epub_id and  rt.post_type='epub')as averageRating,
            rtmy.rating  as myrating,
            
            (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.epub_id and kcm.type = 'epub')as count_comment,
            kv.status as vault
            
            FROM epub_master as cm
            
            JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
            JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
            JOIN client_master as cln ON cln.client_master_id = cm.client_id
            
            LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.epub_id and  cTenv.type = 9
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.epub_id and  uTpyCont.type = 9 and 	uTpyCont.user_master_id = " . $user_master_id . "

            
            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 
            LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type = 5
            LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'
            
            WHERE 
            
            cm.status=3 and cm.privacy_status != 2 
            
            
            " . $client_list . " " . $mobileview . " " . $envStatus . " 
            
            group by cm.epub_id
            order by  cm.publication_date desc " . $limit;

            // \echo $sql; exit;
            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            #echo  $sql; exit;
            $query = $this->db->query($sql);

            //$this->db->cache_off();
            $result = $query->result();
            #print_r($result); exit;
            $i = 1;
            $vx = array();
            foreach ($result as $val) {

                if ($val->epub_img_thumbnail) {
                    // $logic_image_path = "uploads/compendium/" . $val->comp_qa_file_img;
                    // $imgPr = image_thumb_url($logic_image_path, $val->comp_qa_file_img, 450, 250, '');
                    $logic_image = $val->epub_img_thumbnail;
                } else {

                    $logic_image = '';
                }

                $sponsorLogoArry = explode(",", $val->sponsor_logo);

                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {
                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = '' . $val->sponsor_logo;
                    }
                }

                if (!empty($sponsorLogomix)) {
                    $sponsorLogo = implode(",", $sponsorLogomix);
                } else {
                    $sponsorLogo = null;
                }

                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                // print_r($val); exit;
                $vx[] = array(

                    "slno" => $i,
                    "type_id" => $val->type_id,
                    "trending_type" => 'epub',
                    "type" => 'epub',
                    "date" => date(' jS F y', strtotime($val->publish_date)),
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "description" => (strlen($val->description) > 300) ? html_entity_decode(strip_tags(substr($val->description, 0, 300))) . "..." : html_entity_decode(strip_tags($val->description)),
                    "description_short" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "author" => $val->author,
                    "epub_file" => $val->epub_file,
                    "is_featured" => $val->display_in_dashboard,

                    "image" => change_img_src($logic_image),
                    "color" => ($val->color != '') ? $val->color : '#918c91',

                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "price" => get_a_content_price($val->type_id, 9, $env),
                    "user_content_payment" => $val->user_contnet_payment_status,
                    //============ integrated for subscription ============//

                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src('' . $val->client_logo),

                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),

                    "comment_count" => $val->count_comment,
                    "is_share" => get_a_content_is_share_status($val->type_id, '9'),
                    "is_like" => filter_var($val->is_like, FILTER_VALIDATE_BOOLEAN),
                    "is_commentable" => filter_var($val->is_commentable, FILTER_VALIDATE_BOOLEAN),
                    "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : $val->start_like,
                    "myrating" => ($val->myrating != '') ? true : false,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

                );
                $i++;
            }
            // print_r($vx);
            // exit;
            return $vx;
        }
    }

    public function getepubnew($type_id, $client_ids)
    {
        $vx = array();
        $this->db->select('client_id');
        $this->db->from('channel_master');
        $this->db->where('channel_master_id', $type_id);

        $query_clientid = $this->db->get();
        #print_r($this->db->last_query()); exit;

        if (($query_clientid) && ($query_clientid->num_rows() > 0)) {

            $c_id = $query_clientid->result();

            $client_id = $c_id[0]->client_id;
            // if ($limitFrom != '' and $limitTo != '') {

            //     $limit = "limit " . $limitFrom . " , " . $limitTo;

            // } else {

            //     $limit = "";
            // }

            if ($client_ids) {

                $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                    return "FIND_IN_SET('$x', cm.client_id)";
                }, explode(',', $client_id))) . ')';
            }
            $sql = "SELECT
                        cm.epub_id as type_id,
           
                        cm.epub_description as description,
                        cm.epub_title as title,
            
                        cm.epub_img,
                        cm.epub_img_thumbnail,
                        cm.epub_file,
                        cm.author,
            
                        cm.added_on,
                        cm.publication_date as publish_date,
                        cln.client_name,
                        cln.client_logo,
            
                        cm.display_in_dashboard,
                        cm.deeplink,
                        cm.color,
            
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            
                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
            
            
                        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.epub_id and  rt.post_type='epub')as averageRating,
            rtmy.rating  as myrating,
            
                        (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.epub_id and kcm.type = 'epub')as count_comment,
            kv.status as vault
            
                        FROM epub_master as cm
            
                        JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
                        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                        JOIN client_master as cln ON cln.client_master_id = cm.client_id
            
                        LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id
                        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            
            
            
                        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 
                        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type_text='epub'
                        LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'
            
                        WHERE 
                        
                        cm.status=3
          
           
                        " . $client_list . "
           
                        group by cm.epub_id
                        order by  cm.publication_date desc limit 1";
            //echo $sql; exit;

            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            #echo  $sql; exit;
            $query = $this->db->query($sql);

            //$this->db->cache_off();
            $result = $query->result();
            #print_r($result); exit;
            $i = 1;
            $vx = array();
            foreach ($result as $val) {

                if ($val->epub_img_thumbnail) {
                    // $logic_image_path = "uploads/compendium/" . $val->comp_qa_file_img;
                    // $imgPr = image_thumb_url($logic_image_path, $val->comp_qa_file_img, 450, 250, '');
                    $logic_image = $val->epub_img_thumbnail;
                } else {

                    $logic_image = '';
                }

                $sponsorLogoArry = explode(",", $val->sponsor_logo);

                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {

                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = '' . $val->sponsor_logo;
                    }
                }

                $sponsorLogo = implode(",", (array)$sponsorLogomix);

                unset($sponsorLogomix);
                unset($sponsorLogoArry);

                $vx[] = array(

                    "slno" => $i,
                    "type_id" => $val->type_id,
                    "trending_type" => 'epub',
                    "type" => 'epub',
                    "date" => date(' jS F y', strtotime($val->publish_date)),
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "description_short" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "is_featured" => $val->display_in_dashboard,
                    "author" => $val->author,
                    "epub_file" => $val->epub_file,
                    "image" => change_img_src($logic_image),
                    "color" => ($val->color != '') ? $val->color : '#918c91',
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src('' . $val->client_logo),

                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),

                    "comment_count" => $val->count_comment,
                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                    "myrating" => ($val->myrating != '') ? true : false,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

                );
                $i++;
            }
            // print_r($vx);
            // exit;
            return $vx;
        }
    }
    public function getcomp($user_master_id, $limitFromFn, $limitToFn, $type_id, $env)
    {

        $comp_id_array = array();

        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }

        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'comp');
        if ($key_locked == '') {
            return null;
        }

        // ====================== fetching env_id  ======================//

        $sql = "SELECT
        cm.comp_qa_id as type_id,
        cm.comp_qa_question ,
        cm.comp_qa_answer ,
        cm.comp_qa_answer_raw as description,
        cm.comp_qa_question_raw as title,
        cm.comp_qa_file_img,
        cm.comp_qa_file_img_thumbnail,
        cm.added_on,

        cTenv.price,
        uTpyCont.status as user_contnet_payment_status,
        cm.publication_date as publish_date,
        cln.client_name,
        cln.client_logo,
        cm.color,
        cm.type,
        cm.vendor,
        cm.is_share,
        cm.is_like,
        cm.is_comment,
        cm.src,
        cm.deeplink,
        cm.start_like,
        cm.gl_deeplink,
        kv.status as vault,
        
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        
        
        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.comp_qa_id and  rt.post_type='comp')as averageRating,
        
        ctc.is_featured as channel_featured,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsorCM,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logoCM,
        ctc.is_featured,
        cm.comp_qa_speciality_id
        
        
        FROM knwlg_compendium_V1 as cm
        
        LEFT JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
        LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        LEFT JOIN client_master as cln ON cln.client_master_id = cm.client_id
        LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id and  cTenv.type = 1
        LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.comp_qa_id and  uTpyCont.type = 1 and 	uTpyCont.user_master_id = " . $user_master_id . "
        LEFT JOIN compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
        
        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and  kv.type_text='comp' and  kv.user_id = " . $user_master_id . "
        LEFT JOIN channel_to_compendium  as ctc on ctc.comp_qa_id = cm.comp_qa_id 
        WHERE 
        cm.status=3 and cm.privacy_status in (0,1)
        and ctc.channel_master_id= " . $type_id . "
        " . $envStatus . "
        group by cm.comp_qa_id
        order by  cm.comp_qa_id desc  " . $limit . "";
        $query = $this->db->query($sql);
        // print_r($query); exit;
        // print_r($this->db->last_query());
        // exit;

        if (($query) && ($query->num_rows() > 0)) {
            $resultInt = $query->result();

            $i = 1;
            $j = 0;
            $compArry = array();
            foreach ($resultInt as $val) {
                if ($val->comp_qa_file_img_thumbnail) {
                    $img = $val->comp_qa_file_img_thumbnail; //base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
                } else {
                    // $img = 'https://storage.googleapis.com/medwiki/35_server/test/images/thumbnail.png';//$val->comp_qa_file_img;
                    $img = ''; //$val->comp_qa_file_img;
                }
                $sponsorCMLogoArry = explode(",", $val->sponsor_logoCM);
                if (count($sponsorCMLogoArry) > 0) {
                    foreach ($sponsorCMLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorCMLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {
                    if ($val->sponsor_logoCM) {
                        $sponsorCMLogomix[] = '' . $val->sponsor_logoCM;
                    }
                }
                $sponsorCMLogo = implode(",", (array)$sponsorCMLogomix);
                unset($sponsorCMLogomix);
                unset($sponsorCMLogoArry);
                $string = htmlentities($val->title, null, 'utf-8');
                //$string = html_entity_decode($string);
                $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                $main_description = "";
                $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
                $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

                //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
                $compArry[] = array(

                    "slno" => $i,
                    "trending_type" => "comp",
                    "type" => "comp",
                    "con_type" => $val->type,
                    "vendor" => $val->vendor,
                    "src" => $val->src_cm,
                    "sponsor_name" => $val->sponsorCM,
                    "sponsor_logo" => change_img_src($sponsorCMLogo),
                    "is_featured" => $val->channel_featured,
                    "is_share" => get_a_content_is_share_status($val->type_id, '1'),
                    "is_like" => filter_var($val->is_like, FILTER_VALIDATE_BOOLEAN),
                    "is_commentable" => filter_var($val->is_commentable, FILTER_VALIDATE_BOOLEAN), //$val->is_comment,
                    "type_id" => $val->type_id,
                    //"type" => $val->type,
                    "date" => date(' jS F y', strtotime($val->publish_date)),
                    "question" => html_entity_decode(strip_tags($string)),
                    "image" => change_img_src($img),
                    "imageF" => $val->comp_qa_file_img_thumbnail,
                    "answer" => (strlen($main_description) > 300) ? html_entity_decode(strip_tags(substr($main_description, 0, 300))) . "..." : html_entity_decode(strip_tags($main_description)),
                    // "answer_prev" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                    "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : $val->start_like,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "price" => get_a_content_price($val->type_id, 1, $env),
                    "user_content_payment" => get_user_content_status($val->type_id, 1, $user_master_id),
                    //============ integrated for subscription ============//

                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "color" => ($val->color != '') ? $val->color : '#918c91',
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src('' . $val->client_logo),
                    "deeplink" => ($env == '2') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0) //($val->deeplink != '') ? $val->deeplink : 0,

                );

                $j++;
                $i++;
            }
        }
        return $compArry;
    }

    /**
     * @param $user_master_id
     * @param $limitFromFn
     * @param $limitToFn
     * @param $type_id
     * @return array
     */
    public function getPost($user_master_id, $limitFromFn, $limitToFn, $type_id)
    {

        $todays_date = date('Y-m-d H:i:s', time());
        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        $sqlInt = "select 
        id,
        post,
        post_date, 
        added_on
        from
        channel_to_post 
        where status = 3 AND 
        post_date <= '$todays_date' AND 
        channel_master_id = '$type_id' " . $limit;
        $queryInt = $this->db->query($sqlInt);
        $resultInt = $queryInt->result();
        //print_r($this->db->last_query()); exit;

        $i = 1;
        $vx = array();
        foreach ($resultInt as $val) {
            $vx[] = array(

                "slno" => $i,
                "trending_type" => "post",
                "type" => "post",
                "type_id" => $val->id,
                "value" => $val->post,
                "post_date" => $val->post_date,
                "date" => date(' jS F y', strtotime($val->post_date)),
                "added_on" => $val->added_on,

            );
            $i++;
        }
        return $vx;
    }

    /**
     * @param $user_master_id
     * @param $limitFromFn
     * @param $limitToFn
     * @param $type_id
     * @return array
     */
    public function getDocmnt($user_master_id, $limitFromFn, $limitToFn, $type_id)
    {
        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }

        $sqlInt = "select 
        id,
        document_path, 
        document_name,
        thumbnail_image,
        added_on
        from
        channel_to_document 
        where  status = 3 and 
        channel_master_id = " . $type_id . " " . $limit;
        $queryInt = $this->db->query($sqlInt);
        $resultInt = $queryInt->result();
        //print_R($this->db->last_query()); exit;
        $i = 1;
        $vx = array();
        foreach ($resultInt as $val) {
            $vx[] = array(

                "slno" => $i,
                "document_name" => $val->document_name,
                "original_file" => $val->document_path,
                "added_on" => $val->added_on,
                "thumbnail_image" => change_img_src($val->thumbnail_image),
                "trending_type" => "document_path",
                "type" => "document_path",
                "type_id" => $val->id,
                "value" => $val->document_path,

            );
            $i++;
        }
        return $vx;
    }

    /**
     * @param $user_master_id
     */
    public function getspq($user_master_id, $limitFromFn, $limitToFn, $type_id, $env)
    {

        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'survey');
        if ($key_locked == '') {
            return null;
        }
        // ====================== fetching env_id  ======================//

        $sqlCompl = "SELECT 
        sv.* 
        
        FROM 
        survey_user_answer sv
        WHERE 
        sv.user_master_id = '" . $user_master_id . "'";

        $queryCompl = $this->db->query($sqlCompl);
        $resultCompl = $queryCompl->result();

        $complID = array();
        foreach ($resultCompl as $valCompl) {

            $complID[] = $valCompl->survey_id;
        }
        //print_r($complID); exit;
        $sqlInCompl = "SELECT 
        sv.* 
        
        FROM 
        survey_user_incomplete_answer sv
        WHERE 
        sv.status = 3
        and 
        sv.user_master_id = '" . $user_master_id . "'";

        $queryInCompl = $this->db->query($sqlInCompl);
        $resultInCompl = $queryInCompl->result();

        $incomplID = array();
        foreach ($resultInCompl as $valInCompl) {

            $incomplID[] = $valInCompl->survey_id;
        }
        $arrayFinal = array_unique(array_merge($complID, $incomplID));
        //print_r($arrayFinal); exit;
        $complIDStr = implode(",", array_filter($arrayFinal));
        //echo $complIDStr ; exit;

        if ($complIDStr) {
            $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
        } else {
            $qryStr = '';
        }
        $sql = "SELECT 
        sv.* ,

        cTenv.price,
        uTpyCont.status as user_contnet_payment_status,

        svd.data,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        cln.client_name,
        cln.client_logo,
        cts.is_featured as channel_featured,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
        
        FROM 
        survey sv
        left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id          
        left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
        JOIN client_master as cln ON cln.client_master_id = sv.client_id
        
        
        
        LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
        
        LEFT JOIN content_to_env as cTenv ON cTenv.type_id = sv.survey_id  and  cTenv.type = 6
        LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = sv.survey_id  and  uTpyCont.type = 6 and 	uTpyCont.user_master_id = " . $user_master_id . "
 
        
        JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
        left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
        LEFT JOIN  channel_to_survey AS cts ON cts.survey_id = sv.survey_id
        WHERE 
        cts.channel_master_id= " . $type_id . " 
        and
        sv.status = 3 and sv.privacy_status in (0,1)
        " . $qryStr . " " . $envStatus . "
        group by sv.survey_id order by sv.survey_id DESC, sv.publishing_date DESC " . $limit . "";

        // echo $sql;exit;
        $query = $this->db->query($sql);
        //  print_r($this->db->last_query());
        // exit;
        //$this->db->cache_off();
        if (($query) && ($query->num_rows() > 0)) {
            $resultsurvey = $query->result();

            $spqArry = array();
            $j = 0;

            foreach ($resultsurvey as $val) {

                $dataArry = unserialize($val->data);
                $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                $str = preg_replace('/\\\"/', "\"", $json);
                $sponsorLogoArry = explode(",", $val->sponsor_logo);

                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {

                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = '' . $val->sponsor_logo;
                    }
                }

                $sponsorLogo = implode(",", (array)$sponsorLogomix);

                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                $spqArry[] = array(

                    "survey_id" => $val->survey_id,
                    "trending_type" => "survey",
                    "type_id" => $val->survey_id,
                    "type" => "survey",
                    "category" => $val->category,
                    "point" => $val->survey_points,
                    "question_count" => $val->question_count,
                    "survey_time" => $val->survey_time,
                    "survey_duration" => timeconversion($val->survey_time),
                    "json_data" => $str,
                    "survey_title" => $val->survey_title,
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                    "survey_description" => (strlen($val->survey_description) > 150) ? substr($val->survey_description, 0, 150) . "..." : $val->survey_description,
                    "image" => change_img_src($val->image),
                    "specialities_name" => $val->specialities_name,
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src('' . $val->client_logo),
                    "is_featured" => $val->channel_featured, //is_featured,
                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),

                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "is_share" => get_a_content_is_share_status($val->survey_id, '6'),
                    "is_like" => filter_var($val->is_like, FILTER_VALIDATE_BOOLEAN),
                    "is_commentable" => filter_var($val->is_commentable, FILTER_VALIDATE_BOOLEAN),
                    "price" => get_a_content_price($val->survey_id, 6, $env),
                    "user_content_payment" => $val->user_contnet_payment_status,
                    //============ integrated for subscription ============//

                    "publishing_date" => $val->publishing_date,
                    "date" => date(' jS F y', strtotime($val->publishing_date)),

                );

                $j++;
            }
        }
        return $spqArry;
    }

    /**
     * @param string $booked_id
     * @param string $user_master_id
     * @return array
     */
    public function getsession($user_master_id = '', $limitFromFn, $limitToFn, $type_id, $env)
    {

        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }

        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'session');
        if ($key_locked == '') {
            return null;
        }

        // ====================== fetching env_id  ======================//

        $sql = "SELECT
        ksp.participant_id,
        ks.session_id,
        ks.*,
        sd.*,
        cln.client_name,
        cln.client_logo,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,

        cts.is_featured AS channel_featured,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,

        cTenv.price,
        uTpyCont.status as user_contnet_payment_status,
        msct.category_name,
        msct.category_logo,

        sd.document_path,
        sd.comment,
        ksd.knwlg_sessions_docs_id,
        ksd.document_path,
        ksd.comment,
        -- GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
        
        
        -- GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
        -- GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') as  speciality,
        -- GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as  profile,
        -- GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as  profile_images,
        
        GROUP_CONCAT(ksp.participant_id) as  PartName,
        GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED,
        (ks.total_buffer + ks.total_seats) as tot_seat,

        mst.status_name,
        kva.video_archive_id
        FROM knwlg_sessions_V1 as ks

        LEFT JOIN session_to_specialities as stsp ON stsp.session_id = ks.session_id
        LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = stsp.specialities_id

        -- LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
        LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id

        LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
        LEFT JOIN content_to_env as cTenv ON cTenv.type_id = ks.session_id and  cTenv.type = 2
        LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = ks.session_id and  uTpyCont.type = 2 and 	uTpyCont.user_master_id = {$user_master_id}
        LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
        
        -- LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
        
        LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
        LEFT JOIN knwlg_sessions_participant as ksp ON (ksp.knwlg_sessions_id = ks.session_id and ksp.participant_id = " . $user_master_id . " and ksp.participant_type = 'member')
        LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id
        LEFT JOIN master_session_status as mst ON mst.master_session_status_id = ks.session_status

        LEFT JOIN knwlg_video_archive as kva ON kva.video_archive_session_id = ks.session_id
        LEFT JOIN channel_to_session AS cts ON cts.session_id = ks.session_id 
        WHERE 
        ks.status = 3 and cts.channel_master_id= " . $type_id . " and ks.privacy_status != 2 and ks.session_status not in (3,5,6)
        " . $envStatus . "

        GROUP BY ks.session_id   
        ORDER BY ks.start_datetime ASC " . $limit . "";
        // echo $sql; exit();
        //" . $envsessionStatus . " removeing from query"
        $query = $this->db->query($sql);
        //$this->db->cache_off();

        //
        $result = $query->result();
        // echo "<pre>"; print_r($result); exit();
        //ks.session_status
        $i = 0;
        $j = 0;
        $vx = array();
        foreach ($result as $val) {

            if (stripos($val->profile_image, "https://storage.googleapis.com") > -1) {
                $logic_image = $val->profile_image;
            } else {

                $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
            }

            $start_time = $val->start_datetime;
            $start_time = date("g:i A", strtotime($start_time));
            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {

                    $var = session_doc_detail($single_doctor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                      $logic_image = base_url() . "uploads/docimg/" . $image;
                  } else {

                      $logic_image = base_url() . "uploads/docimg/MConsult.png";
                  }*/
                    if ($image) {
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        // $logic_image = $imgPr;
                        /////============================== updated by  ramanath  14-5-21
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            //  $logic_image_path = docimg; //"uploads/docimg/" . $image;
                            //  $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = docimg; //$imgPr;

                        }
                        //=======================================
                    } else {

                        $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                    }

                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($var[0]['profile_image']);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }
            //$entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $end_time = $val->end_datetime;
            $end_time = date("g:i A", strtotime($end_time));

            /**
             * new sponsor logic
             */
            $sponsorSESLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorSESLogoArry) > 0) {

                foreach ($sponsorSESLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorSESLogomix[] = '' . $valueSponor;
                    }
                }
            } else {

                if ($val->sponsor_logoSES) {
                    $sponsorSESLogomix[] = '' . $val->sponsor_logoSES;
                }
            }
            $sponsorLogoSES = implode(",", (array)$sponsorSESLogomix);

            unset($sponsorSESLogomix);
            unset($sponsorSESLogoArry);
            $vx[] = array(

                "slno" => $i,
                "trending_type" => "session",
                "type_id" => $val->session_id,
                "is_share" => get_a_content_is_share_status($val->session_id, '2'),
                "is_like" => filter_var($val->is_like, FILTER_VALIDATE_BOOLEAN),
                "is_commentable" => filter_var($val->is_commentable, FILTER_VALIDATE_BOOLEAN),
                "session_id" => $val->session_id,
                "type" => 'session',
                // "doctor_name" => $val->doctor_name,
                "session_doctor_id" => $val->session_doctor_id,
                "date" => date(' jS F y', strtotime($val->start_datetime)),
                "end_datetime" => date('Y-m-d h:i:s', strtotime($val->end_datetime)),

                "start_datetime" => date('Y-m-d H:i:s', strtotime($val->start_datetime)),
                "display_date" => $start_time . "-" . $end_time,
                "ms_cat_name" => $val->category_name,
                "is_featured" => $val->channel_featured, //$is_featured,
                "cover_image" => change_img_src($val->cover_image),

                "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogoSES),

                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => get_a_content_price($val->session_id, 2, $env),
                "user_content_payment" => $val->user_contnet_payment_status,
                //============ integrated for subscription ============//

                "image" => change_img_src($logic_image),
                "image_raw_name" => $val->profile_image,
                "session_status" => $val->session_status,
                "status_name" => $val->status_name,
                // "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),

                "seesion_description" => (strlen($val->description) > 300) ? html_entity_decode(strip_tags((substr($val->description, 0, 300)))) . "..." : html_entity_decode(strip_tags($val->description)),

                "session_topic" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "color" => ($val->color != '') ? $val->color : '#08cc9e',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                "session_doctor_entities" => $ses_doc_det_array,
                'cpddetail' => $this->getcpddetails($val->session_id)
            );

            $j++;
        }
        return $vx;
    }

    /**
     * @param string $type_id
     * @param string $user_master_id
     * @param string $client_ids
     * @param $limitFrom
     * @param $limitTo
     * @return array
     */
    public function getarchiveVideo($type_id = '', $user_master_id = '', $client_ids = '', $limitFrom = '', $limitTo = '', $env = '')
    {

        if ($limitFrom == 0 and $limitTo != '') {

            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {
            $limit = "limit " . $limitFrom . " , " . $limitTo;
        }

        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'video_archived');
        if ($key_locked == '') {
            return null;
        }

        // ====================== fetching env_id  ======================//
        $sql = "SELECT
        cm.video_archive_id as type_id,
        cm.video_archive_question,
        cm.video_archive_answer,
        cm.video_archive_question_raw,
        cm.video_archive_answer_raw,
        cm.video_archive_file_img,
        cm.video_archive_file_img_thumbnail,
        cm.deeplink,
        cm.gl_deeplink,
        ctva.is_featured as channel_featured,
        cm.added_on,
        cm.publication_date,
        cln.client_name,
        cln.client_logo,
        
        cm.start_like,
    
        cm.type,
        cm.vendor,
        cm.src,
        cm.is_share,
        cm.is_like,
        cm.is_comment,
        ks.session_doctor_id,
        msct.category_name,
		msct.category_logo,
        
        cTenv.price,
        uTpyCont.status as user_contnet_payment_status,

        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
        
        
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        cm.video_archive_speciality_id ,
        kv.status as vault,
        
        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.video_archive_id and  rt.post_type='video_archive' and rt.rating!=0) as averageRating,
        rtmy.rating  as myrating
        
        
        FROM knwlg_video_archive as cm 
        JOIN video_archive_to_specialities  as cmTs ON cmTs.video_archive_id = cm.video_archive_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        
        
        LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
        
        LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.video_archive_id and  cTenv.type = 3
        LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.video_archive_id and  uTpyCont.type = 3 and 	uTpyCont.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
        LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
        
        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id and  kv.type_text='video_archive' and  kv.user_id = " . $user_master_id . "
        LEFT JOIN channel_to_video_archive as ctva ON cm.video_archive_id = ctva.video_archive_id 
        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id and  rtmy.post_type='video_archive' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id and  rt.post_type='video_archive'
        JOIN client_master as cln ON cln.client_master_id = cm.client_id
        WHERE 
        cm.status=3 and cm.privacy_status != 2
        and ctva.channel_master_id = " . $type_id . " " . $envStatus . "    
        GROUP BY cm.video_archive_id
        order by cm.publication_date DESC
        " . $limit . "";

        //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
        //echo $sql; exit;
        //and
        //cm.publication_date <= CURDATE()

        $query = $this->db->query($sql);
        //  print_r($this->db->last_query());
        //  die;
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $j = 0;
        $vx = array();
        foreach ($result as $val) {

            if ($val->video_archive_file_img) {
                $img = $val->video_archive_file_img;
            } else {
                $img = 'https://storage.googleapis.com/medwiki/35_server/test/images/thumbnail.png';
            }
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {

                foreach ($sponsorLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {

                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $session_doc_array = explode(",", $val->session_doctor_id);
            $ses_doc_det_array = array();
            $inc_pp = 0;

            foreach ($session_doc_array as $single_doctor) {

                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                //echo $image; exit;
                if ($image) {
                    // $logic_image_path = $image;
                    // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    //$logic_image = docimg;//$image;
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        // $logic_image_path = docimg;//"uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = docimg; //$imgPr;

                    }
                } else {

                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image); //$var[0]['profile_image'];
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(
                "slno" => $i,
                "trending_type" => $val->type,
                "type" => $val->type,
                "type_id" => $val->type_id,
                "vendor" => $val->vendor,
                "src" => $val->src,

                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                "answer" => (strlen($main_description) > 300) ? html_entity_decode(strip_tags(substr($main_description, 0, 300))) . "..." : html_entity_decode(strip_tags($main_description)),
                // "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "is_featured" => $val->channel_featured,
                "is_share" => get_a_content_is_share_status($val->type_id, '3'),
                "is_like" => filter_var($val->is_like, FILTER_VALIDATE_BOOLEAN),
                "is_commentable" => filter_var($val->is_commentable, FILTER_VALIDATE_BOOLEAN),
                "category_name" => $val->category_name,
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => get_a_content_price($val->type_id, 3, $env),
                "user_content_payment" => $val->user_contnet_payment_status,
                //============ integrated for subscription ============//
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),

                "comment_count" => $val->count_comment,
                "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : $val->start_like,
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,

                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,

            );
            $i++;
        }
        //print_R($vx); exit;
        return $vx;
    }
    public function detail($user_master_id, $client_ids, $type_id)
    {
        if (!empty($type_id)) {

            $env = get_user_env($user_master_id);

            //get user speciality
            $sqlInt = "select 
            count(DISTINCT(user_master_id)) as total
            from
            channel_to_user
            where 
            status = 3
            and
            channel_master_id = " . $type_id . "";
            $queryInt = $this->db->query($sqlInt);
            $resultInt = $queryInt->row();

            $cachename = "channelnew_detail_" . $type_id;
            $cachenamefollowedstatus = "channelnew_detail_followed_status" . $type_id . "_" . $user_master_id;
            if (!empty($type_id)) {

                // checking for internal user
                $user_type = get_master_user_type_id($user_master_id);
                // echo "<pre>";print_r($user_type);die;
                if (!$user_type) { // garbage value present in the place of user_type_id
                    return;
                } elseif ($user_type == 5) { //internal then fetch data what is active(==3) and draft(==5) and dont need cache
                    //echo $specialities; exit;
                    //get user speciality
                    if ($client_ids) {

                        //$client_list = ' and '. implode(' OR ', array_map(function($x) { return "FIND_IN_SET('$x', kcp.client_id)"; }, explode(',', $this->session->userdata('client_ids'))));
                        $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                            return "FIND_IN_SET('$x', fd.client_id)";
                        }, explode(',', $client_ids))) . ')';
                    }
                    $sql = "SELECT
                    ch.channel_master_id as type_id,
                    ch.title as title,
                    ch.env,
                    ch.logo_type,
                    ch.description as description,
                    ch.short_description as short_description,
                    ch.membershipformurl,
                    ch.consent_text,
                    cTus.status as followed_status,   
                    ch.follower_count,                     
                    ch.cover_image,
                    ch.privacy_status,
                    ch.channel_video,
                    ch.logo,
                    ch.address,
                    ch.added_on,
                    ch.deeplink,
                    ch.gl_deeplink,
                    ch.featured_video,
                    ch.is_followers,
                    ch.is_activities,
                    bnd.logo,
                    CONCAT(cta.state_id,',', cta.region_id) as region_info,
                    cln.client_name,
                    cln.client_logo,
                    count(DISTINCT(cTus.user_master_id)) as total,
                    GROUP_CONCAT(DISTINCT chTsdoc.session_doctor_id  ORDER BY  chTsdoc.session_doctor_id  ASC ) as session_doctor_id,
                    GROUP_CONCAT(DISTINCT chTsdoc.description  ORDER BY  chTsdoc.session_doctor_id  ASC SEPARATOR '----') as ch_doc_description
                    FROM channel_master as ch
                    LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = ch.channel_master_id
                    LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
                    LEFT JOIN client_master as cln ON cln.client_master_id = ch.client_id   
                    LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = ch.brand_id   
                    LEFT JOIN channel_to_session_doctor as chTsdoc ON chTsdoc.channel_master_id = ch.channel_master_id 
                    LEFT JOIN channel_to_user  as cTus ON (cTus.channel_master_id = ch.channel_master_id and cTus.user_master_id = " . $user_master_id . ")        
                    left JOIN channel_to_address as cta ON cta.channel_id = ch.channel_master_id        

                   WHERE 
                    ch.status in (3,5) 
                    
                   
                    and
                    ch.privacy_status in (0,1,2)
                    and
                    ch.channel_master_id = " . $type_id . "";

                    // echo $sql; exit;
                    //exit;and ch.privacy_status in (0,1)
                    //add child checking in this sql
                    //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
                    //exit;
                    //echo  $sql; exit;

                    $query = $this->db->query($sql);

                    $val = $query->row();
                } else { // not internal user

                    //checking for cache
                    if ($this->myredis->exists($cachename)) { //checking for cache

                        $val = $this->myredis->get($cachename);
                    } else {

                        if ($client_ids) {
                            //$client_list = ' and '. implode(' OR ', array_map(function($x) { return "FIND_IN_SET('$x', kcp.client_id)"; }, explode(',', $this->session->userdata('client_ids'))));

                            $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                                return "FIND_IN_SET('$x', fd.client_id)";
                            }, explode(',', $client_ids))) . ')';
                        }

                        if ($env) {

                            if ($env != 'GL') {

                                $envStatus = "AND (ch.env ='GL' or ch.env ='" . $env . "')";
                            } else {

                                $envStatus = "AND ch.env ='" . $env . "'";
                            }
                        } else {

                            $envStatus = "";
                        }

                        //echo $envStatus; exit;
                        $sql = "SELECT
                        ch.channel_master_id as type_id,
                        ch.title as title,
                        ch.env,
                        ch.logo_type,
                        ch.description as description,
                        ch.short_description as short_description,
                        ch.membershipformurl,
                        ch.consent_text,
                        cTus.status as followed_status,   
                        ch.follower_count,                     
                        ch.cover_image,
                        ch.privacy_status,
                        ch.channel_video,
                        ch.logo,
                        ch.address,
                        ch.added_on,
                        ch.deeplink,
                        ch.gl_deeplink,
                        ch.featured_video,
                        ch.is_followers,
                        ch.is_activities,
                        bnd.logo,
                        CONCAT(cta.state_id, cta.region_id) as region_info,
                        
                        
                        cln.client_name,
                        cln.client_logo,
                        count(DISTINCT(cTus.user_master_id)) as total,
                        GROUP_CONCAT(DISTINCT chTsdoc.session_doctor_id  ORDER BY  chTsdoc.session_doctor_id  ASC ) as session_doctor_id,
                        GROUP_CONCAT(DISTINCT chTsdoc.description  ORDER BY  chTsdoc.session_doctor_id  ASC SEPARATOR '----') as ch_doc_description
                        FROM channel_master as ch
                        LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = ch.channel_master_id
                        LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
                        LEFT JOIN client_master as cln ON cln.client_master_id = ch.client_id   
                        LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = ch.brand_id   
                        LEFT JOIN channel_to_session_doctor as chTsdoc ON chTsdoc.channel_master_id = ch.channel_master_id 
                        LEFT JOIN channel_to_user  as cTus ON (cTus.channel_master_id = ch.channel_master_id and cTus.user_master_id = " . $user_master_id . ")
                        left JOIN channel_to_address as cta ON cta.channel_id = ch.channel_master_id        
                        WHERE 
                        ch.status = 3 
                        
                       
                        and
                        ch.privacy_status in (0,1,2)
                        and
                        ch.channel_master_id = " . $type_id . "";

                        //echo $sql; exit;
                        //exit;and ch.privacy_status in (0,1)
                        //add child checking in this sql
                        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
                        //exit;
                        $query = $this->db->query($sql);
                        //print_r($this->db->last_query()); exit;
                        //$this->db->cache_off();

                        $chanel_sql = "SELECT *
                                FROM 
                                    clircert_certificate_master
                                WHERE 
                                    user_master_id = {$user_master_id} AND type = 'membership' AND type_id = {$type_id}";

                        $chan_cert = $this->db->query($chanel_sql);
                        $result = $chan_cert->row();

                        if (empty($result)) {

                            $cert_details = "[['NULL']]";
                        } else {
                            $cert_details = array(
                                "type" => $result->type,
                                "type_id" => $result->type_id,
                                "id" => $result->id

                            );
                        }
                        $val = $query->row();
                        $this->myredis->set($cachename, $val);
                    }
                }

                // echo "<pre>";print_r($val);die;

                $faqSQL = "SELECT 
                                faq_json
                            FROM
                                channel_faq
                            WHERE
                                channel_master_id = {$type_id}    
                        ";

                $faqQuery = $this->db->query($faqSQL);
                $faqResult = $faqQuery->row();

                if (!empty($faqResult) && isset($faqResult->faq_json)) {
                    $faqJSON = json_decode($faqResult->faq_json);
                } else {
                    $faqJSON = null;
                }
                $chanelDocumentsql = "
                        SELECT 
                            thumbnail_image,
                            document_name,
                            document_path,
                            added_on
                        FROM 
                            channel_to_document
                        WHERE 
                            channel_master_id = {$type_id} 
                            AND status = 3";
                $chanelDocumentQuery = $this->db->query($chanelDocumentsql);
                $documentResult = [];
                if ($chanelDocumentQuery && $chanelDocumentQuery->num_rows() > 0) {
                    foreach ($chanelDocumentQuery->result_array() as $row) {
                        $documentResult[] = [
                            'thumbnail_image' => change_img_src($row['thumbnail_image']),
                            'document_name'   => $row['document_name'],
                            'document_path'   => $row['document_path'],
                            'added_on'        => $row['added_on'],
                        ];
                    }
                } else {
                    $documentResult = [];
                }

                $discussionButtonSQL = "SELECT 
                                                discussion_id
                                            FROM
                                                channel_to_discussion
                                            WHERE
                                                channel_id = {$type_id}    
                                        ";

                $discussionQuery = $this->db->query($discussionButtonSQL);
                $discussionResult = $faqQuery->row();

                if ($discussionQuery->num_rows() == 0) {
                    $discussionButton = true;
                } else {
                    $discussionButton = false;
                }

                // echo "<pre>";print_r($discussionButton);die;
                if ($val->type_id != '') {

                    $ses_doc_det_array = array();
                    if ($val->session_doctor_id) {

                        $session_doc_array = explode(",", $val->session_doctor_id);
                        $session_gr_doc_description_array = explode("----", $val->ch_doc_description);
                        $inc_pp = 0;
                        foreach ($session_doc_array as $single_doctor) {

                            $var = session_doc_detail_v1($single_doctor);

                            if ($session_gr_doc_description_array[$inc_pp]) {

                                $gr_doc_descriptionLa = $session_gr_doc_description_array[$inc_pp];
                            } else {

                                $gr_doc_descriptionLa = '';
                            }
                            //print_r($var);

                            if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {

                                $logic_image = $var[0]['profile_image'];
                            } else {
                                // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                // $logic_image_path = docimg;//"uploads/docimg/" . $image;
                                // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                $logic_image = docimg; //$imgPr;
                                //$logic_image = $var[0]['profile_image'];
                            }

                            $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                            $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                            // $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $var[0]['profile'];//$var[0]['description']; //$session_gr_doc_description_array[$inc_pp];
                            $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $gr_doc_descriptionLa; //$session_gr_doc_description_array[$inc_pp];
                            $inc_pp++;
                        }
                    }

                    $this->db->select('status');
                    $this->db->from('channel_to_user');
                    $this->db->where(array('channel_master_id' => $val->type_id, 'user_master_id' => $user_master_id));
                    $queryfstatus = $this->db->get();
                    //    print_r($this->db->last_query()); exit;
                    if (($queryfstatus) && ($queryfstatus->num_rows() > 0)) {
                        $fstatus = $queryfstatus->row();
                        // print_r($fstatus->status);
                        $followedstatus = ($fstatus->status != '') ? $fstatus->status : 0;
                    } else {

                        $followedstatus = 0;
                        //
                    }
                    // print_r($followedstatus);
                    // exit;
                    $vx = array();

                    $vx[] = array(
                        "type_id" => $val->type_id,
                        "type" => 'channel',
                        "env" => $val->env,
                        "added_on" => date(' jS F y', strtotime($val->added_on)),
                        "title" => html_entity_decode(strip_tags($val->title)),
                        "cover_image" => change_img_src($val->cover_image),
                        "logo" => change_img_src($val->client_logo),
                        "channel_video" => $val->channel_video,
                        "location" => $val->region_info,
                        "followed_status" => $followedstatus, //($val->followed_status != '') ? $val->followed_status : 0,
                        "follower_count" => $resultInt->total + $val->follower_count,
                        "a_followers" => $resultInt->total,
                        "is_followers" => filter_var($val->is_followers, FILTER_VALIDATE_BOOLEAN), //$val->is_followers,
                        "is_activities" => filter_var($val->is_activities, FILTER_VALIDATE_BOOLEAN),
                        "address" => $val->address,
                        "privacy_status" => $val->privacy_status,
                        "featured_video" => $this->archiveVideoDetial($val->featured_video, $user_master_id),
                        "featured_video_id" => $val->featured_video,
                        "description" => $val->description,
                        "channel_description" =>  html_entity_decode(strip_tags(preg_replace("/[\r\n\t]/", "", $val->description))),
                        "short_description" => html_entity_decode(strip_tags(substr($val->short_description, 0, 300))),
                        "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                        "channel_doctor_entities" => $ses_doc_det_array,
                        "client_name" => $val->client_name,
                        "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                        "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                        "disclaimer" => disclaimer('knowledge'),
                        "membershipformurl" => $val->membershipformurl,
                        "faq" => $faqJSON->data,
                        "resources" => $documentResult,
                        "start_discussion_button" => $discussionButton,
                        "is_share" => get_a_content_is_share_status($val->type_id, '11'),
                        "logo_type" => $val->logo_type,
                        "consent_text" => $val->consent_text,
                        "channel_certificate_details" => json_encode($cert_details),
                        // "channel_certificate_details" => $val->certificate_details,
                    );
                }

                $campaign  = getContentCampiagn($user_master_id, '', $type_id, 'channel');
                $vx[0]['display_banner'] = $campaign['banner_dispaly'];
                $vx[0]['campaign_data'] = $campaign['creative_data'];
                if ($user_type != 5) {
                    if ($env == "GL" && $vx[0]['env'] == "IN") {
                        $vx = [];
                    }
                }
                return $vx;
            }
        }
    }

    /**
     * @param $user_master_id
     * @param $client_ids
     * @param $limitTo
     * @param $limitFrom
     * @return array
     */
    public function medwiki($user_master_id, $client_ids, $type_id, $limitTo = '', $limitFrom = '')
    {
        if (!empty($type_id)) {
            if ($limitFrom == 0 and $limitTo != '') {

                $limit = "limit " . $limitFrom . " , " . $limitTo;
            } else {
                $limit = "limit " . $limitFrom . " , " . $limitTo;
            }
            //get user speciality
            $sqlInt = "select 
            ctc.comp_qa_id
            from
            channel_to_compendium  ctc
            left join knwlg_compendium_V1 kcv ON ctc.comp_qa_id = kcv.comp_qa_id
            where 
            ctc.channel_master_id = " . $type_id . " and kcv.status = 3";
            $queryInt = $this->db->query($sqlInt);
            $resultInt = $queryInt->result_array();

            //print_r($resultInt); exit;
            $comp_id_array = array();
            foreach ($resultInt as $val) {

                $comp_id_array[] = $val['comp_qa_id'];
                //$specialities = array_merge($specialities, $val);

            }
            if (count($comp_id_array) > 0) {

                $compIds = implode(",", (array)$comp_id_array);
            }
            if ($compIds != '') {

                $compIds_query = ' and cm.comp_qa_id  in (' . $compIds . ')';
            } else {
                $compIds_query = ' and cm.comp_qa_id  in (0)';
            }
            //echo $specialities; exit;

            //get user speciality
            $sql = "SELECT
            cm.comp_qa_id as type_id,
            cm.comp_qa_question ,
            cm.comp_qa_answer ,
            cm.comp_qa_answer_raw as description,
            cm.comp_qa_question_raw as title,
            cm.comp_qa_file_img,
            cm.comp_qa_file_img_thumbnail,
            cm.added_on,
            cm.color,
            cm.publication_date as publish_date,
            cln.client_name,
            cln.client_logo,
            
            cm.type,
            cm.vendor,
            cm.src,
            cm.deeplink,
            cm.gl_deeplink,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            max( ms.rank) as maxrank,
            
            
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
            
            cm.comp_qa_speciality_id,
            (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.comp_qa_id and  rt.post_type='comp')as averageRating,
            rtmy.rating  as myrating,
            (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.comp_qa_id and kcm.type = 'comp')as count_comment,
            kv.status as vault
            
            FROM knwlg_compendium_V1 as cm
            
            LEFT JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
            LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
            LEFT JOIN client_master as cln ON cln.client_master_id = cm.client_id
            
            LEFT JOIN compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            
            
            
            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.comp_qa_id and  rtmy.post_type='comp' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
            LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and  kv.type_text='comp' and  kv.user_id = " . $user_master_id . "
            LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.comp_qa_id and  rt.post_type='comp'
            
            WHERE 
            cm.status=3
            " . $compIds_query . "
            
            group by cm.comp_qa_id
            order by  cm.publication_date desc, maxrank DESC " . $limit . "";
            //echo $sql; exit;
            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;
            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();
            //print_r($result); exit;
            $i = 1;
            $vx = array();
            foreach ($result as $val) {
                //its taking too much time, we have to figure it out

                if ($val->comp_qa_file_img_thumbnail) {
                    // $logic_image_path = "uploads/compendium/" . $val->comp_qa_file_img;
                    // $imgPr = image_thumb_url($logic_image_path, $val->comp_qa_file_img, 450, 250, '');
                    $logic_image = $val->comp_qa_file_img_thumbnail;
                } else {

                    $logic_image = 'https://storage.googleapis.com/medwiki/35_server/test/images/thumbnail.png'; //$val->comp_qa_file_img;

                }

                $sponsorLogoArry = explode(",", $val->sponsor_logo);
                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {

                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = '' . $val->sponsor_logo;
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);

                $vx[] = array(

                    "slno" => $i,
                    "con_type" => $val->type,
                    "vendor" => $val->vendor,
                    "color" => ($val->color != '') ? $val->color : '#eb34e5',
                    "src" => $val->src,
                    "type_id" => $val->type_id,
                    "type" => 'comp',
                    "date" => date(' jS F y', strtotime($val->publish_date)),
                    "question" => html_entity_decode(strip_tags($val->title)),
                    "image" => change_img_src($logic_image),

                    "answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),

                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),

                    "comment_count" => $val->count_comment,
                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                    "myrating" => ($val->myrating != '') ? true : false,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0) //($val->deeplink != '') ? $val->deeplink : 0,

                );
                $i++;
            }
            //print_r($vx);
            //exit;
            return $vx;
        }
    }

    /**
     * @param $user_master_id
     * @param $client_ids
     * @param $limitTo
     * @param $limitFrom
     * @return array
     */
    public function session($user_master_id, $client_ids, $type_id, $limitTo = '', $limitFrom = '')
    {
        if (!empty($type_id)) {

            if ($limitFrom == 0 and $limitTo != '') {

                $limit = "limit " . $limitFrom . " , " . $limitTo;
            } else {
                $limit = "limit " . $limitFrom . " , " . $limitTo;
            }

            //get user speciality
            $sqlInt = "select 
            session_id
            from
            channel_to_session 
            where 
            channel_master_id = " . $type_id . "";
            $queryInt = $this->db->query($sqlInt);
            $resultInt = $queryInt->result_array();
            //print_r($resultInt); exit;
            $comp_id_array = array();
            foreach ($resultInt as $val) {

                $session_id_id_array[] = $val['session_id'];
                //$specialities = array_merge($specialities, $val);

            }
            if (count($session_id_id_array) > 0) {

                $sessionIds = implode(",", (array)$session_id_id_array);
            }
            if ($sessionIds != '') {

                $sessionIds_query = ' and   ks.session_id  in (' . $sessionIds . ')';
            } else {
                $sessionIds_query = ' and   ks.session_id  in (0)';
            }
            //echo $specialities; exit;

            //get user speciality
            $sql = "SELECT
            ksp.participant_id,
            ks.session_id,
            ks.*,
            sd.*,
            cln.client_name,
            cln.client_logo,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            
            
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
            
            
            msct.category_name,
            msct.category_logo,
            
            sd.document_path,
            sd.comment,
            ksd.knwlg_sessions_docs_id,
            ksd.document_path,
            ksd.comment,
            GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
            GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
            GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') as  speciality,
            
            
            
            
            GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as  profile,
            GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as  profile_images,
            GROUP_CONCAT(ksp.participant_id) as  PartName,
            GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED,
            
            
            
            
            (ks.total_buffer + ks.total_seats) as tot_seat,
            
            mst.status_name,
            kva.video_archive_id
            
            
            FROM knwlg_sessions_V1 as ks
            LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
            LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id
            
            LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
            
            
            LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
            LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
            LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
            LEFT JOIN knwlg_sessions_participant as ksp ON (ksp.knwlg_sessions_id = ks.session_id and ksp.participant_id = " . $user_master_id . " and ksp.participant_type = 'member')
            LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id
            LEFT JOIN master_session_status as mst ON mst.master_session_status_id = ks.session_status
            
            LEFT JOIN knwlg_video_archive as kva ON kva.video_archive_session_id = ks.session_id
            
            
            
            WHERE 
            ks.status = 3
            " . $sessionIds_query . "
             
            GROUP BY ks.session_id   
            ORDER BY ks.start_datetime ASC " . $limit . "";
            //echo $sql; exit();
            //and GROUP_CONCAT(ksp.participant_id) as str_participant_id,
            //ks.start_datetime >= NOW()
            $query = $this->db->query($sql);
            //
            $result = $query->result_array();
            //print_r($result); exit();
            //ks.session_status
            $i = 0;
            $entities = array();
            foreach ($result as $row) {

                $entities[$i]['participant_id'] = ''; //solution for cph and gr page i can't attend button

                $entities[$i]['session_id'] = $row['session_id'];
                $entities[$i]['type_id'] = $row['session_id'];
                $entities[$i]['type'] = 'session';
                $entities[$i]['trending_type'] = 'session';
                $entities[$i]['session_topic'] = $row['session_topic'];
                $entities[$i]['session_description'] = strip_tags($row['session_description']);
                $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
                $entities[$i]['client_id'] = $row['client_id'];
                $entities[$i]['client_name'] = $row['client_name'];
                $entities[$i]['color'] = ($row['color'] != '') ? $row['color'] : '#eb34e5';

                /**
                 * new sponsor logic
                 */
                $sponsorLogoArry = explode(",", $row['sponsor_logo']);
                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {

                    if ($row['sponsor_logo']) {
                        $sponsorLogomix[] = '' . $row['sponsor_logo'];
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                /**
                 * new sponsor logic
                 */

                $entities[$i]['sponsor_name'] = $row['sponsor'];
                $entities[$i]['sponsor_logo'] =  change_img_src($sponsorLogo);

                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                if ($row['document_path'] != "" || $row['document_path'] != null) {
                    $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
                } else {
                    $entities[$i]['document_path'] = "";
                }
                if ($row['comment'] != "" || $row['comment'] != null) {
                    $entities[$i]['comment'] = $row['comment'];
                } else {
                    $entities[$i]['comment'] = "";
                }
                $entities[$i]['category_id'] = $row['category_id'];
                $entities[$i]['category_name'] = $row['category_name'];
                $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
                $entities[$i]['start_datetime'] = date('Y-m-d h:i:s', strtotime($row['start_datetime']));

                $start_time = $row['start_datetime'];
                $start_time = date("g:i A", strtotime($start_time));
                $end_time = $row['end_datetime'];
                $end_time = date("g:i A", strtotime($end_time));
                $entities[$i]['display_date'] = $start_time . "-" . $end_time;
                $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
                $post_date = $row['added_on'];
                $start_date = $row['ms_start_datetime'];
                $buffer_day = $row['add_question_buffer_days'];
                $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
                $buffer_str = strtotime($last_display_date);
                $t = time();
                $date = new DateTime($last_display_date);
                $now = new DateTime();
                $now_str = strtotime("now");
                $diff = date_diff($date, $now);
                //print_r($diff);
                if ($t <= $buffer_str) {
                    $dat_diff = abs($diff->format("%R%a"));
                } else {
                    $dat_diff = 0;
                }
                $entities[$i]['view_edit_button_text'] = "";
                //echo $dat_diff; exit();
                if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                    $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
                }

                if ($question_users->question != "" && $row['session_status'] != 3) {
                    $entities[$i]['view_edit_button_text'] = "View Case/Query";
                }
                $is_attended_array = array();
                $is_attended_array = explode(",", $row['IS_ATTENDED']);

                $part_array = array();
                $part_array = explode(",", $row['PartName']);
                //$user_id = $this->session->userdata['user_master_id'];
                $inc = 0;
                foreach ($part_array as $single) {
                    if ($single == $user_master_id) {
                        $key_val = $inc;
                    }
                    $inc++;
                }

                $is_att = $is_attended_array[$key_val];
                $entities[$i]['missed_session_text'] = "";
                if ($is_att == 2) {
                    $entities[$i]['missed_session_text'] = "You Missed The Session";
                }
                $entities[$i]['i_cant_attend_button'] = 0;
                $end_time = $row['end_datetime'];
                $end_time = strtotime($end_time);
                $now_time = date('Y-m-d H:i:s');
                $now_time = strtotime($now_time);
                if ($now_time < $end_time) {
                    $entities[$i]['i_cant_attend_button'] = 1;
                }

                if ($row['session_status'] == 2) {

                    $liveMode = true;
                } else {

                    $liveMode = false;
                }

                $entities[$i]['deeplink'] = ($env == 'GL') ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0); //$row['deeplink'];
                $entities[$i]['session_status'] = $row['session_status'];
                $entities[$i]['status_name'] = $row['status_name'];
                $entities[$i]['video_archive_id'] = $row['video_archive_id'];
                $entities[$i]['live_mode'] = $liveMode;
                $entities[$i]['template'] = "blue_master_cast";
                $entities[$i]['ms_start_datetime'] = $row['start_datetime'];
                $entities[$i]['end_datetime'] = $row['end_datetime'];
                $entities[$i]['specialities_name'] = $row['specialities_name'];
                $entities[$i]['ms_cat_name'] = $row['category_name'];
                $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
                $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
                $entities[$i]['ms_cat_logo'] =  change_img_src($row['category_logo']);
                $entities[$i]['doctor_name'] = $row['doctor_name'];
                $entities[$i]['speciality'] = $row['speciality'];

                $entities[$i]['cover_image'] = change_img_src($row['cover_image']);
                $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
                $session_doc_array = explode(",", $row['session_doctor_id']);

                $ses_doc_det_array = array();
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {

                    $var = session_doc_detail($single_doctor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                         $logic_image = base_url() . "uploads/docimg/" . $image;
                     } else {

                         $logic_image = base_url() . "uploads/docimg/MConsult.png";
                     }*/
                    if ($image) {
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        // $logic_image = $imgPr;
                        /////============================== updated by  ramanath  14-5-21
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            // $logic_image_path = docimg;//"uploads/docimg/" . $image;
                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = docimg; //$imgPr;

                        }
                        //=======================================
                    } else {

                        $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                    }

                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($image); //$logic_image;
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
                $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
                $i++;
            }
            return $entities;
        }
    }

    /**
     * @param $user_master_id
     * @param $client_ids
     * @param $limitTo
     * @param $limitFrom
     * @return array
     */
    public function survey($user_master_id, $client_ids, $type_id, $limitTo = '', $limitFrom = '')
    {
        if (!empty($type_id)) {

            if ($limitFrom == 0 and $limitTo != '') {

                $limit = "limit " . $limitFrom . " , " . $limitTo;
            } else {
                $limit = "limit " . $limitFrom . " , " . $limitTo;
            }

            //get user speciality
            $sqlInt = "select 
            survey_id
            from
            channel_to_survey 
            where 
            channel_master_id = " . $type_id . "";
            $queryInt = $this->db->query($sqlInt);
            $resultInt = $queryInt->result_array();

            //print_r($resultInt); exit;
            $comp_id_array = array();
            foreach ($resultInt as $val) {

                $survey_id_array[] = $val['survey_id'];
                //$specialities = array_merge($specialities, $val);

            }
            if (count($survey_id_array) > 0) {

                $surveyIds = implode(",", (array)$survey_id_array);
            }
            if ($surveyIds != '') {

                $surveyIds_query = ' and sv.survey_id  in (' . $surveyIds . ')';
            } else {
                $surveyIds_query = ' and sv.survey_id  in (0)';
            }
            //echo $specialities; exit;

            //get user speciality
            $sqlCompl = "SELECT 
            sv.* 
            
            FROM 
            survey_user_answer sv
            WHERE 
            sv.user_master_id = '" . $user_master_id . "'";

            $queryCompl = $this->db->query($sqlCompl);
            $resultCompl = $queryCompl->result();

            $complID = array();
            foreach ($resultCompl as $valCompl) {

                $complID[] = $valCompl->survey_id;
            }
            //print_r($complID); exit;
            $sqlInCompl = "SELECT 
            sv.* 
            
            FROM 
            survey_user_incomplete_answer sv
            WHERE 
            sv.status = 3
            and 
            sv.user_master_id = '" . $user_master_id . "'";

            $queryInCompl = $this->db->query($sqlInCompl);
            $resultInCompl = $queryInCompl->result();

            $incomplID = array();
            foreach ($resultInCompl as $valInCompl) {

                $incomplID[] = $valInCompl->survey_id;
            }
            $arrayFinal = array_unique(array_merge($complID, $incomplID));
            //print_r($arrayFinal); exit;
            $complIDStr = implode(",", array_filter($arrayFinal));
            //echo $complIDStr ; exit;

            if ($complIDStr) {
                $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
            } else {
                $qryStr = '';
            }
            $sql = "SELECT 
            sv.* ,
            svd.data,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            cln.client_name,
            cln.client_logo,
            
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
            
            FROM 
            survey sv
            left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id          
            left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
            JOIN client_master as cln ON cln.client_master_id = sv.client_id
            
            
            
            LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
            
            
            
            JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
            left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
            WHERE 
            sv.status = 3 
            
            
            " . $qryStr . "
            " . $surveyIds_query . "
            group by sv.survey_id  " . $limit . "";

            //echo $sql; exit;
            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;

            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();

            foreach ($result as $val) {
                $dataArry = unserialize($val->data);
                $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                $str = preg_replace('/\\\"/', "\"", $json);

                $sponsorLogoArry = explode(",", $val->sponsor_logo);

                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {

                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = '' . $val->sponsor_logo;
                    }
                }

                $sponsorLogo = implode(",", (array)$sponsorLogomix);

                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                $vx[] = array(

                    "survey_id" => $val->survey_id,
                    "category" => $val->category,
                    "point" => $val->survey_points,
                    "json_data" => $str,
                    "survey_title" => $val->survey_title,
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                    "survey_description" => substr($val->survey_description, 0, 150),
                    "image" => change_img_src($val->image),
                    "specialities_name" => $val->specialities_name,
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),

                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),
                    "publishing_date" => $val->publishing_date,

                );
            }
            return $vx;
        }
    }

    /**
     * @param string $user_master_id
     * @param string $client_ids
     * @param string $group_ids
     * @param string $type
     * @param string $spIds
     * @param $type_id
     * @return array
     */
    public function all_archiveVideo($type_id = '', $user_master_id = '', $client_ids = '', $limitFrom = '', $limitTo = '')
    {

        //get user speciality
        $sqlInt = "select 
        video_archive_id
        from
        channel_to_video_archive 
        where 
        channel_master_id = " . $type_id . "";
        $queryInt = $this->db->query($sqlInt);
        $resultInt = $queryInt->result_array();
        //print_r($resultInt); exit;
        $comp_id_array = array();
        foreach ($resultInt as $val) {

            $video_archive_id_array[] = $val['video_archive_id'];
            //$specialities = array_merge($specialities, $val);

        }
        if (count($video_archive_id_array) > 0) {

            $video_archiveIds = implode(",", (array)$video_archive_id_array);
        }
        if ($video_archiveIds != '') {

            $video_archiveIds_query = ' and   cm.video_archive_id  in (' . $video_archiveIds . ')';
        } else {
            $video_archiveIds_query = ' and   cm.video_archive_id  in (0)';
        }

        //echo $video_archiveIds_query; exit;
        if ($limitFrom == 0 and $limitTo != '') {

            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {
            $limit = "limit " . $limitFrom . " , " . $limitTo;
        }
        $sql = "SELECT
        cm.video_archive_id as type_id,
        cm.video_archive_question,
        cm.video_archive_answer,
        cm.video_archive_question_raw,
        cm.video_archive_answer_raw,
        cm.video_archive_file_img,
        cm.video_archive_file_img_thumbnail,
        cm.deeplink,
        cm.gl_deeplink,
        cm.added_on,
        cm.publication_date,
        cln.client_name,
        cln.client_logo,
        
        cm.type,
        cm.vendor,
        cm.src,
        ks.session_doctor_id,
        msct.category_name,
		msct.category_logo,
        
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
        
        
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        cm.video_archive_speciality_id ,
        kv.status as vault,
        
        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.video_archive_id and  rt.post_type='video_archive' and rt.rating!=0) as averageRating,
        rtmy.rating  as myrating
        
        
        FROM knwlg_video_archive as cm 
        JOIN video_archive_to_specialities  as cmTs ON cmTs.video_archive_id = cm.video_archive_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        
        
        LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
        
        LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
        LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
        
        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id and  kv.type_text='video_archive' and  kv.user_id = " . $user_master_id . "
        
        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id and  rtmy.post_type='video_archive' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id and  rt.post_type='video_archive'
        
        JOIN client_master as cln ON cln.client_master_id = cm.client_id
        WHERE 
        cm.status=3 
        " . $video_archiveIds_query . "       
        GROUP BY cm.video_archive_id
        order by cm.publication_date DESC
        " . $limit . "";

        //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
        //echo $sql; exit;
        //and
        //cm.publication_date <= CURDATE()

        $query = $this->db->query($sql);
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {

            if ($val->video_archive_file_img) {
                $img = $val->video_archive_file_img;
            } else {
                $img = '';
            }
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {

                foreach ($sponsorLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {

                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $session_doc_array = explode(",", $val->session_doctor_id);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {

                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if ($image) {
                    // $logic_image_path = $image;
                    // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    //$logic_image = $image; //$imgPr;
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        // $logic_image_path = docimg;//"uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = docimg; //$imgPr;

                    }
                } else {

                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                }

                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(
                "slno" => $i,
                "con_type" => $val->type,
                "type_id" => $val->type_id,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "type_id" => $val->type_id,
                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),

                "category_name" => $val->category_name,

                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),

                "comment_count" => $val->count_comment,
                "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), // $val->deeplink,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,

                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,

            );
            $i++;
        }

        return $vx;
    }

    /**
     * @param string $type_id
     * @param string $user_master_id
     * @param string $client_ids
     * @param string $group_ids
     * @param string $spIds
     * @param $limitFrom
     * @param $limitTo
     * @return array
     */
    public function archiveVideoDetial($type_id = '', $user_master_id = '')
    {

        if ($type_id) {
            $cachename = "archieve_detail_" . $type_id;
            if ($this->myredis->exists($cachename)) {
                $val = $this->myredis->get($cachename);
                // print_r($res); exit;
            } else {
                $sql = "SELECT
        cm.video_archive_id as type_id,
        cm.video_archive_question,
        cm.video_archive_answer,
        cm.video_archive_question_raw,
        cm.video_archive_answer_raw,
        cm.video_archive_file_img,
        cm.video_archive_file_img_thumbnail,
        cm.deeplink,
        cm.gl_deeplink,
        cm.start_like,
        
        cm.added_on,
        cm.publication_date,
        cln.client_name,
        cln.client_logo,
        
        cm.type,
        cm.vendor,
        cm.src,
        ks.session_doctor_id,
        msct.category_name,
		msct.category_logo,
        
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
        
        
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        cm.video_archive_speciality_id ,
        kv.status as vault,
        
        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.video_archive_id and  rt.post_type='video_archive' and rt.rating!=0) as averageRating,
        rtmy.rating  as myrating
        
        
        FROM knwlg_video_archive as cm 
        JOIN video_archive_to_specialities  as cmTs ON cmTs.video_archive_id = cm.video_archive_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        
        
        LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
        
        LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
        LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
        
        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id and  kv.type_text='video_archive' and  kv.user_id = " . $user_master_id . "
        
        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id and  rtmy.post_type='video_archive' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id and  rt.post_type='video_archive'
        
        JOIN client_master as cln ON cln.client_master_id = cm.client_id
        WHERE 
        cm.status=3 
        and 
        cm.video_archive_id = " . $type_id . "
            
        GROUP BY cm.video_archive_id
        order by cm.publication_date DESC ";
                //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
                //echo $sql; exit;
                //and
                //cm.publication_date <= CURDATE()

                $query = $this->db->query($sql);
                $val = $query->row();
            }
            //print_r($result); exit;
            $i = 1;
            $vx = array();

            if ($val->video_archive_file_img) {
                $img = $val->video_archive_file_img;
            } else {
                $img = '';
            }
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {

                foreach ($sponsorLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {

                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $session_doc_array = explode(",", $val->session_doctor_id);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {

                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if ($image) {
                    // $logic_image_path = $image;
                    // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    //$logic_image = $image;//$imgPr;
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        // $logic_image_path = docimg;//"uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = docimg; //$imgPr;

                    }
                } else {

                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                }

                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";

            if ($val->type_id) {
                $vx[] = array(
                    "slno" => $i,
                    "con_type" => $val->type,
                    "type_id" => $val->type_id,
                    "vendor" => $val->vendor,
                    "src" => $val->src,
                    "type_id" => $val->type_id,
                    "date" => date(' jS F y', strtotime($val->publication_date)),
                    "question" => html_entity_decode(strip_tags($string)),
                    "image" => change_img_src($img),
                    //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src('' . $val->client_logo),

                    "category_name" => $val->category_name,

                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),

                    "comment_count" => $val->count_comment,
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                    "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : '',
                    "myrating" => ($val->myrating != '') ? true : false,
                    "vault" => ($val->vault != '') ? $val->vault : 0,

                    "session_doctor_id" => $val->session_doctor_id,
                    "session_doctor_entities" => $ses_doc_det_array,

                );
            }
        }
        return $vx;
    }

    /**
     * @param $postid
     * @param $user_id
     * @param string $type
     * @return mixed
     */
    public function save_follow($postid = '', $user_id = '', $userstatus = '')
    {
        $time = date('Y-m-d H:i:s');
        $this->insertdb = $this->load->database('insert', true);
        $sqlPv = "select 
        privacy_status
        from channel_master 
        
        where 
        channel_master_id = " . $postid . " ";

        //echo $sqlPv; exit;
        $queryPv = $this->db->query($sqlPv);
        //exit;
        $resultPv = $queryPv->row();
        //print_r($resultPv); exit;
        //echo $resultPv->privacy_status; exit;
        if ($resultPv->privacy_status == 1) {

            //privet

            $sql = "select 
            id,
            status
            from channel_to_user 
            where 
            channel_master_id = " . $postid . " 
            and  
            user_master_id = " . $user_id . "";
            $query = $this->db->query($sql);

            //exit;
            $result = $query->row();
            //print_r($result->id); exit;
            if ($result->id) {

                if ($result->status == 3) {

                    $sql = "UPDATE  channel_to_user SET status=?,updated_on=? WHERE channel_master_id=? AND user_master_id=?";
                    $query = $this->insertdb->query($sql, array(0, $time, $postid, $user_id));

                    $return = 0;
                } else {
                    if ($userstatus == 1) {
                        $sql = "UPDATE  channel_to_user SET status=?,updated_on=? WHERE channel_master_id=? AND user_master_id=? ";
                        $query = $this->insertdb->query($sql, array(3, $time, $postid, $user_id));
                        $return = 1;
                    } else {
                        $sql = "UPDATE  channel_to_user SET status=?,updated_on=? WHERE channel_master_id=? AND user_master_id=? ";
                        $query = $this->insertdb->query($sql, array(1, $time, $postid, $user_id));
                        $return = 1;
                    }
                }
            } else {
                if ($userstatus == 1) {
                    $sql = "INSERT INTO channel_to_user (status, channel_master_id, user_master_id,added_on) VALUES (?, ?,?,?);";
                    $query = $this->insertdb->query($sql, array(3, $postid, $user_id, $time));
                    $return = 1;
                } else {
                    $sql = "INSERT INTO channel_to_user (status, channel_master_id, user_master_id,added_on) VALUES (?, ?,?,?);";
                    $query = $this->insertdb->query($sql, array(1, $postid, $user_id, $time));
                    $return = 1;
                }
            }
        } else {

            //public

            $sql = "select 
            id,
            status
            from channel_to_user 
            where 
            channel_master_id = " . $postid . " 
            and  
            user_master_id = " . $user_id . "";
            $query = $this->db->query($sql);

            //exit;
            $result = $query->row();

            if ($result->id) {

                if ($result->status == 3) {

                    $sql = "UPDATE  channel_to_user SET status=?,updated_on=? WHERE channel_master_id=? AND user_master_id=?";
                    $query = $this->insertdb->query($sql, array(0, $time, $postid, $user_id));

                    $return = 0;
                } else {

                    $sql = "UPDATE  channel_to_user SET status=?,updated_on=? WHERE channel_master_id=? AND user_master_id=? ";
                    $query = $this->insertdb->query($sql, array(3, $time, $postid, $user_id));
                    $return = 3;
                }
            } else {

                $sql = "INSERT INTO channel_to_user (status, channel_master_id, user_master_id,added_on) VALUES (?, ?,?,?);";
                $query = $this->insertdb->query($sql, array(3, $postid, $user_id, $time));
                $return = 3;
            }
        }
        $cachenamefollowedstatus = "channelnew_detail_followed_status" . $postid . "_" . $user_id;
        $this->myredis->del($cachenamefollowedstatus);

        //die('hbjhj');
        return $return;
    }
    // public function save_follow($postid = '', $user_id = '',$userstatus='')
    // {
    //     $this->insertdb = $this->load->database('insert', TRUE);
    //     $sqlPv = "select
    //     privacy_status
    //     from channel_master

    //     where
    //     channel_master_id = " . $postid . " ";

    //     //echo $sqlPv; exit;
    //     $queryPv = $this->db->query($sqlPv);
    //     //exit;
    //     $resultPv = $queryPv->row();
    //     //print_r($resultPv); exit;
    //     //echo $resultPv->privacy_status; exit;
    //     if ($resultPv->privacy_status == 1) {

    //         //privet

    //         $sql = "select
    //         id,
    //         status
    //         from channel_to_user
    //         where
    //         channel_master_id = " . $postid . "
    //         and
    //         user_master_id = " . $user_id . "";
    //         $query = $this->db->query($sql);

    //         //exit;
    //         $result = $query->row();

    //         if ($result->id) {

    //             if ($result->status == 3) {

    //                 $sql = "UPDATE  channel_to_user SET status=? WHERE channel_master_id=? AND user_master_id=?";
    //                 $query = $this->insertdb->query($sql, array(0, $postid, $user_id));

    //                 $return = 0;

    //             } else {
    //                 if($userstatus == 1){
    //                     $sql = "UPDATE  channel_to_user SET status=? WHERE channel_master_id=? AND user_master_id=? ";
    //                     $query = $this->insertdb->query($sql, array(3, $postid, $user_id));
    //                     $return = 1;
    //                 }else{
    //                     $sql = "UPDATE  channel_to_user SET status=? WHERE channel_master_id=? AND user_master_id=? ";
    //                     $query = $this->insertdb->query($sql, array(1, $postid, $user_id));
    //                     $return = 1;
    //                 }

    //             }
    //         } else {
    //             if($userstatus == 1){
    //                 $sql = "INSERT INTO channel_to_user (status, channel_master_id, user_master_id) VALUES (?, ?,?);";
    //                 $query = $this->insertdb->query($sql, array(3, $postid, $user_id));
    //                 $return = 1;
    //             }else{
    //                 $sql = "INSERT INTO channel_to_user (status, channel_master_id, user_master_id) VALUES (?, ?,?);";
    //                 $query = $this->insertdb->query($sql, array(1, $postid, $user_id));
    //                 $return = 1;
    //             }
    //         }
    //     } else {

    //         //public

    //         $sql = "select
    //         id,
    //         status
    //         from channel_to_user
    //         where
    //         channel_master_id = " . $postid . "
    //         and
    //         user_master_id = " . $user_id . "";
    //         $query = $this->db->query($sql);

    //         //exit;
    //         $result = $query->row();

    //         if ($result->id) {

    //             if ($result->status == 3) {

    //                 $sql = "UPDATE  channel_to_user SET status=? WHERE channel_master_id=? AND user_master_id=?";
    //                 $query = $this->insertdb->query($sql, array(0, $postid, $user_id));

    //                 $return = 0;

    //             } else {

    //                 $sql = "UPDATE  channel_to_user SET status=? WHERE channel_master_id=? AND user_master_id=? ";
    //                 $query = $this->insertdb->query($sql, array(3, $postid, $user_id));
    //                 $return = 3;
    //             }
    //         } else {

    //             $sql = "INSERT INTO channel_to_user (status, channel_master_id, user_master_id) VALUES (?, ?,?);";
    //             $query = $this->insertdb->query($sql, array(3, $postid, $user_id));
    //             $return = 3;

    //         }

    //     }
    //     $cachenamefollowedstatus = "channelnew_detail_followed_status".$postid."_".$user_id;
    //      $this->myredis->del($cachenamefollowedstatus);

    //     //die('hbjhj');
    //     return $return;

    // }

    public function getcpddetails($session_id)
    {
        $this->db->select('id');
        $this->db->from('Master_service');
        $this->db->where('name', 'session');

        $query = $this->db->get();

        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();

            $this->db->select('ctc.id,ctc.points,c.name,c.short_name');
            $this->db->from('content_to_cpd as ctc');
            $this->db->join('council as c', 'c.id=ctc.mc_id');

            $this->db->where(array('ctc.type_id' => $session_id, 'ctc.status' => 3, 'ctc.type' => $result[0]->id));
            $query = $this->db->get();
            #print_r($this->db->last_query()); exit;
            if (($query) && ($query->num_rows())) {
                return $query->result();
            } else {
                return array();
            }
        } else {
            return array();
        }
    }

    public function compcount($channel_id, $env)
    {
        $this->db->select('count(ctc.comp_qa_id) as total');
        $this->db->from('channel_to_compendium as ctc');
        $this->db->join('knwlg_compendium_V1 as kcv', 'kcv.comp_qa_id=ctc.comp_qa_id');
        $this->db->where(array('ctc.channel_master_id' => $channel_id, 'kcv.status' => 3, 'kcv.privacy_status' => 0));

        if ($env) {

            if ($env != 'GL') {
                $this->db->where('kcv.env', 'GL');
                $this->db->or_where('kcv.env', $env);
            } else {
                $this->db->where('kcv.env', 'GL');
            }
        } else {

            $envStatus = "";
        }
        $queryInt = $this->db->get();
        if (($queryInt) && ($queryInt->num_rows() > 0)) {
            $result = $queryInt->result();
            return $result[0]->total;
        } else {
            return 0;
        }
    }

    public function spqcount($channel_id, $env)
    {
        $this->db->select('count(cts.survey_id) as total');
        $this->db->from('channel_to_survey as cts');
        $this->db->join('survey as s', 's.survey_id = cts.survey_id');
        $this->db->where(array('cts.channel_master_id' => $channel_id, 's.status' => 3, 's.privacy_status' => 0));

        if ($env) {

            if ($env != 'GL') {
                $this->db->where('s.env', 'GL');
                $this->db->or_where('s.env', $env);
            } else {
                $this->db->where('s.env', 'GL');
            }
        } else {

            $envStatus = "";
        }
        $queryInt = $this->db->get();
        // print_R($this->db->last_query());
        // exit;
        if (($queryInt) && ($queryInt->num_rows() > 0)) {
            $result = $queryInt->result();
            return $result[0]->total;
        } else {
            return 0;
        }
    }

    public function videocount($channel_id, $env)
    {
        $this->db->select('count(ctva.video_archive_id) as total');
        $this->db->from('channel_to_video_archive as ctva');
        $this->db->join('knwlg_video_archive kva', 'kva.video_archive_id = ctva.video_archive_id');
        $this->db->where(array('ctva.channel_master_id' => $channel_id, 'kva.status' => 3, 'kva.privacy_status' => 0));

        if ($env) {

            if ($env != 'GL') {
                $this->db->where('kva.env', 'GL');
                $this->db->or_where('kva.env', $env);
            } else {
                $this->db->where('kva.env', 'GL');
            }
        } else {

            $envStatus = "";
        }
        $queryInt = $this->db->get();
        // print_R($this->db->last_query());
        // exit;
        if (($queryInt) && ($queryInt->num_rows() > 0)) {
            $result = $queryInt->result();
            return $result[0]->total;
        } else {
            return 0;
        }
    }

    public function sessioncount($channel_id, $env)
    {
        $this->db->select('count(cts.session_id) as total');
        $this->db->from('channel_to_session as cts');
        $this->db->join('knwlg_sessions_V1 as ksv', 'ksv.session_id = cts.session_id');
        $this->db->where(array('cts.channel_master_id' => $channel_id, 'ksv.status' => 3, 'ksv.privacy_status' => 0, 'ksv.session_status!=' => 6));

        if ($env) {

            if ($env != 'GL') {
                $this->db->where('ksv.env', 'GL');
                $this->db->or_where('ksv.env', $env);
            } else {
                $this->db->where('ksv.env', 'GL');
            }
        } else {

            $envStatus = "";
        }
        $queryInt = $this->db->get();
        // print_R($this->db->last_query());
        // exit;
        if (($queryInt) && ($queryInt->num_rows() > 0)) {
            $result = $queryInt->result();
            return $result[0]->total;
        } else {
            return 0;
        }
    }

    public function postcount($channel_id)
    {
        $todays_date = date('Y-m-d H:i:s', time());

        $this->db->select('count(id) as total');
        $this->db->from('channel_to_post as ctp');

        $this->db->where(array('channel_master_id' => $channel_id, 'status' => 3, ' post_date <=' => $todays_date));

        $queryInt = $this->db->get();
        // print_R($this->db->last_query());
        // exit;
        if (($queryInt) && ($queryInt->num_rows() > 0)) {
            $result = $queryInt->result();
            return $result[0]->total;
        } else {
            return 0;
        }
    }

    public function trainingcount($channel_id)
    {
        $this->db->select('count(Distinct(tm.id)) as total');
        $this->db->from('channel_to_course as ctc');
        $this->db->join('training_master as tm', 'tm.id =ctc.course_id');
        $this->db->where(array('ctc.channel_master_id' => $channel_id, 'tm.status' => 3, 'tm.privacy_status' => 0));
        $this->db->where('date(tm.published_date)<=', date('Y-m-d'));
        $this->db->where('tm.privacy_status', 0);
        $this->db->order_by('tm.published_date', 'desc');

        $queryInt = $this->db->get();
        // print_R($this->db->last_query());
        // exit;
        if (($queryInt) && ($queryInt->num_rows() > 0)) {
            $result = $queryInt->result();
            return $result[0]->total;
        } else {
            return 0;
        }
    }
    public function documentcount($channel_id)
    {
        $this->db->select('count(id) as total');
        $this->db->from('channel_to_document');
        $this->db->where(array('channel_master_id' => $channel_id, 'status' => 3));

        $queryInt = $this->db->get();
        // print_R($this->db->last_query());
        // exit;
        if (($queryInt) && ($queryInt->num_rows() > 0)) {
            $result = $queryInt->result();
            return $result[0]->total;
        } else {
            return 0;
        }
    }

    public function epubcount($channel_id, $client_ids, $env)
    {
        $this->db->select('client_id');
        $this->db->from('channel_master');
        $this->db->where('channel_master_id', $channel_id);

        $query_clientid = $this->db->get();
        #print_r($this->db->last_query()); exit;

        if (($query_clientid) && ($query_clientid->num_rows() > 0)) {

            $c_id = $query_clientid->result();

            $client_id = $c_id[0]->client_id;
            // if ($limitFrom != '' and $limitTo != '') {

            //     $limit = "limit " . $limitFrom . " , " . $limitTo;

            // } else {

            //     $limit = "";
            // }

            if ($client_ids) {

                $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                    return "FIND_IN_SET('$x', cm.client_id)";
                }, explode(',', $client_id))) . ')';
            }
            $sql = "SELECT
                        count(Distinct(cm.epub_id)) as  total
           
                        FROM epub_master as cm
            
                        JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
                        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                        JOIN client_master as cln ON cln.client_master_id = cm.client_id
            
                        LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id
                        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            
             
                        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 
                        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type_text='epub'
                        LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'
            
                        WHERE 
                       
                        cm.status=3 and cm.privacy_status = 0 
          
           
                        " . $client_list . "
           
                        order by  cm.publication_date desc ";
            //echo $sql; exit;

            $query = $this->db->query($sql);

            if (($query) && ($query->num_rows() > 0)) {
                $result = $query->result();
                return $result[0]->total;
            } else {
                return 0;
            }
        }
    }

    public function channel_to_consent_clicks($value)
    {
        // print_r($value);
        // exit;
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($value)) {
            $this->insertdb->insert('channel_to_consent_clicks', $value);

            //  print_r($this->insertdb->last_query()); die;
            $insert_id = $this->insertdb->insert_id();
        }
        //print_r($insert_id);exit;
        return $insert_id;
    }
}
