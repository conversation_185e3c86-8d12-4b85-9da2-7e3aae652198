<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Discover_model extends CI_Model
{
    private $ci;
    private $cacheName;
    public function __construct()
    {
        $this->ci = get_instance();
        $this->load->load->helper('image');
        $this->load->load->model('Training_model');
        $this->load->load->model('Epub_model');
        $this->load->load->model('Knwlg_model');
        $this->load->load->model('Survey_model');
        $this->load->load->model('Gr_model');
        $this->load->model('Knwlgmastersessionnew_model');



        //$this->load->model('Knwlg_model');


    }
    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function poll($user_master_id = '', $category)
    {
        $sqlCompl = "SELECT
        sv.*
        FROM
        survey_user_answer sv
        WHERE
        sv.user_master_id = '" . $user_master_id . "'";
        $queryCompl = $this->db->query($sqlCompl);
        $resultCompl = $queryCompl->result();
        $complID = array();
        foreach ($resultCompl as $valCompl) {
            $complID[] = $valCompl->survey_id;
        }
        //print_r($complID); exit;
        $sqlInCompl = "SELECT
        sv.*
        FROM
        survey_user_incomplete_answer sv
        WHERE
        sv.status = 3
        and
        sv.user_master_id = '" . $user_master_id . "'";
        $queryInCompl = $this->db->query($sqlInCompl);
        $resultInCompl = $queryInCompl->result();
        $incomplID = array();
        foreach ($resultInCompl as $valInCompl) {
            $incomplID[] = $valInCompl->survey_id;
        }
        $arrayFinal = array_unique(array_merge($complID, $incomplID));
        //print_r($arrayFinal); exit;
        $complIDStr = implode(",", (array)$arrayFinal);
        //echo $complIDStr ; exit;
        if ($complIDStr) {
            $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
        } else {
            $qryStr = '';
        }
        if ($category) {
            $sql = "SELECT
                        sv.survey_id,
                        sv.category,
                        sv.survey_title,
                        sv.survey_description,
                        sv.image,
                        sv.survey_points,
                        sv.survey_time,
                        sv.question_count,
                        sv.publishing_date,
                        sv.client_id,
                        sv.sponsor_ids,
                        sv.deeplink,
                        sv.gl_deeplink,
                        sv.verified,
                        sv.display_in_dashboard,
                        sv.privacy_status,
                        sv.template_id,
                        sv.color,
                        sv.points_on_approval,
                        sv.added_on,
                        sv.added_by,
                        sv.modified_on,
                        sv.modified_by,
                        sv.status,
                        sv.is_available_survey_portal,
                        sv.available_for_live_session,
                        sv.env,
                        sv.is_share,
                        sv.is_like,
                        sv.is_comment,
                        sv.approved_by,
                        sv.img_credits,
                        svd.data,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                        cln.client_name,
                        cln.client_logo,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                    FROM survey sv
                    LEFT JOIN survey_to_speciality AS svts ON svts.survey_id = sv.survey_id
                    LEFT JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = svts.speciality_id
                    JOIN client_master AS cln ON cln.client_master_id = sv.client_id
                    LEFT JOIN survey_to_sponsor AS suvTspon ON suvTspon.survey_id = sv.survey_id
                    LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                    JOIN survey_detail AS svd ON svd.survey_id = sv.survey_id
                    LEFT JOIN survey_user_answer AS sua ON sua.survey_id = sv.survey_id
                    WHERE
                        sv.status = 3
                        AND sv.category = '" . $category . "'
                        AND sv.display_in_dashboard = 1
                        " . $qryStr . "
                    GROUP BY
                        sv.survey_id,
                        sv.category,
                        sv.survey_title,
                        sv.survey_description,
                        sv.image,
                        sv.survey_points,
                        sv.survey_time,
                        sv.question_count,
                        sv.publishing_date,
                        sv.client_id,
                        sv.sponsor_ids,
                        sv.deeplink,
                        sv.gl_deeplink,
                        sv.verified,
                        sv.display_in_dashboard,
                        sv.privacy_status,
                        sv.template_id,
                        sv.color,
                        sv.points_on_approval,
                        sv.added_on,
                        sv.added_by,
                        sv.modified_on,
                        sv.modified_by,
                        sv.status,
                        sv.is_available_survey_portal,
                        sv.available_for_live_session,
                        sv.env,
                        sv.is_share,
                        sv.is_like,
                        sv.is_comment,
                        sv.approved_by,
                        sv.img_credits,
                        svd.data,
                        cln.client_name,
                        cln.client_logo";
        } else {
            $sql = "SELECT
                        sv.survey_id,
                        sv.category,
                        sv.survey_title,
                        sv.survey_description,
                        sv.image,
                        sv.survey_points,
                        sv.survey_time,
                        sv.question_count,
                        sv.publishing_date,
                        sv.client_id,
                        sv.sponsor_ids,
                        sv.deeplink,
                        sv.gl_deeplink,
                        sv.verified,
                        sv.display_in_dashboard,
                        sv.privacy_status,
                        sv.template_id,
                        sv.color,
                        sv.points_on_approval,
                        sv.added_on,
                        sv.added_by,
                        sv.modified_on,
                        sv.modified_by,
                        sv.status,
                        sv.is_available_survey_portal,
                        sv.available_for_live_session,
                        sv.env,
                        sv.is_share,
                        sv.is_like,
                        sv.is_comment,
                        sv.approved_by,
                        sv.img_credits,
                        svd.data,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                        cln.client_name,
                        cln.client_logo,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                    FROM survey sv
                    LEFT JOIN survey_to_speciality AS svts ON svts.survey_id = sv.survey_id
                    LEFT JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = svts.speciality_id
                    JOIN client_master AS cln ON cln.client_master_id = sv.client_id
                    LEFT JOIN survey_to_sponsor AS suvTspon ON suvTspon.survey_id = sv.survey_id
                    LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                    JOIN survey_detail AS svd ON svd.survey_id = sv.survey_id
                    LEFT JOIN survey_user_answer AS sua ON sua.survey_id = sv.survey_id
                    WHERE
                        sv.status = 3
                        AND sv.category = 'poll'
                        AND sv.display_in_dashboard = 1
                        " . $qryStr . "
                    GROUP BY
                        sv.survey_id,
                        sv.category,
                        sv.survey_title,
                        sv.survey_description,
                        sv.image,
                        sv.survey_points,
                        sv.survey_time,
                        sv.question_count,
                        sv.publishing_date,
                        sv.client_id,
                        sv.sponsor_ids,
                        sv.deeplink,
                        sv.gl_deeplink,
                        sv.verified,
                        sv.display_in_dashboard,
                        sv.privacy_status,
                        sv.template_id,
                        sv.color,
                        sv.points_on_approval,
                        sv.added_on,
                        sv.added_by,
                        sv.modified_on,
                        sv.modified_by,
                        sv.status,
                        sv.is_available_survey_portal,
                        sv.available_for_live_session,
                        sv.env,
                        sv.is_share,
                        sv.is_like,
                        sv.is_comment,
                        sv.approved_by,
                        sv.img_credits,
                        svd.data,
                        cln.client_name,
                        cln.client_logo
                    ORDER BY sv.publishing_date DESC";
        }
        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        foreach ($result as $val) {
            $dataArry = unserialize($val->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $vx[] = array(
                "survey_id" => $val->survey_id,
                "category" => $val->category,
                "point" => $val->survey_points,
                "json_data" => $str,
                "survey_title" => $val->survey_title,
                "deeplink" => $val->deeplink,
                "survey_description" => substr($val->survey_description, 0, 150),
                "image" => change_img_src($val->image),
                "specialities_name" => $val->specialities_name,
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "publishing_date" => $val->publishing_date,
            );
        }
        return $vx;
    }
    /**
     * @param string $answer_json
     * @param string $user_master_id
     * @param string $survey_id
     * @return mixed
     */
    public function getPoll($survey_id, $user_master_id)
    {
        if (!empty($survey_id)) {            /*$sql = "SELECT
            sd.*
            FROM
            survey_detail sd
            WHERE
            sd.survey_id = " . $survey_id . "
            ";
            $query = $this->db->query($sql);
            $result = $query->row_array();
            // $result['data'];
            $dataArry = unserialize($result['data']);
            //print_r($dataArry['surveys']);*/
            $sql = "SELECT
            sd.*
            FROM
            survey_user_answer sd
            WHERE
            sd.survey_id = " . $survey_id . "
            and
            sd.user_master_id = " . $user_master_id . "
            ";
            $query = $this->db->query($sql);
            $result = $query->row_array();
            // $result['data'];
            //$dataArry = unserialize($result['data']);
            $dataArry = json_decode($result['data'], true);
            //print_r($dataArry);
            //exit;
            //Total vote count
            $sqlAllC = "SELECT
            count(*) as totalall
            FROM
            survey_poll_result
            WHERE
            survey_id = " . $survey_id . " ";
            $queryAllc = $this->db->query($sqlAllC);
            $resultAllc = $queryAllc->row_array();
            $totalvote = $resultAllc['totalall'];
            //Total vote count
            foreach ($dataArry as $key => $v) {
                //print_r($v['options']);
                foreach ($v['options'] as $ky => $vm) {
                    $sqlC = "SELECT
                    count(*) as total
                    FROM
                    survey_poll_result
                    WHERE
                    survey_id = " . $survey_id . "
                    and
                    value = " . $vm['value'] . "
                    ";
                    $queryc = $this->db->query($sqlC);
                    $resultc = $queryc->row_array();
                    $v['options'][$ky]['vote'] = round(($resultc['total'] / $totalvote) * 100);
                    ////$v['options'] = $vm;
                }
                $dataArry[$key]['options'] = $v['options'];
                //print_r($v['options']);
                //$dataArry['surveys'] = $v['options'];
            }
            //print_r($dataArry);
            //exit;
            return json_encode($dataArry);
            //echo $dataArry['surveys']['question'];
        }
    }
    /**
     * @param $user_master_id
     * @return string
     */
    public function announcement($user_master_id)
    {
        if (!empty($user_master_id)) {
            $sqlUsr = "SELECT
            announcement_id
            FROM
            user_announcement_activity
            WHERE
            user_master_id = " . $user_master_id . "";
            $queryUsr = $this->db->query($sqlUsr);
            $resultUsr = $queryUsr->result_array();
            $announcement = array();
            foreach ($resultUsr as $val) {
                $announcement[] = $val['announcement_id'];
            }
            if (count($announcement) > 0) {
                $announcementIds = implode(",", (array)$announcement);
            }
            if ($announcementIds != '') {
                $announcement_query = "and id  NOT IN(" . $announcementIds . ")";
            } else {
                $announcement_query = "";
            }
            $sql = "SELECT
            *
            FROM
            knwlg_announcement
            WHERE
            type = 'box'
            " . $announcement_query . "  ";
            $query = $this->db->query($sql);
            $result = $query->result_array();
            //print_r($sql);
            $entities = array();
            $i = 0;
            foreach ($result as $row) {
                $entities['id'] = $row['id'];
                $entities['title'] = $row['describtion'];
                $entities['text'] = $row['describtion'];
                $entities['url'] = $row['url'];
                $entities['url_type'] = $row['url_type'];
                $i++;
            }
            return $entities;
            //echo $dataArry['surveys']['question'];
        }
    }
    /**
     * @param $user_master_id
     * @param $type_id
     * @return array
     */
    public function announcement_close($user_master_id, $type_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($user_master_id)) {
            $data = array(
                'user_master_id' => $user_master_id,
                'announcement_id' => $type_id,
            );
            $id_insert = $this->insertdb->insert_id();
            if ($this->insertdb->insert('user_announcement_activity', $data)) {
                $all_data = array(
                    "insert_id" => $id_insert,
                    "type_id" => $type_id
                );
            }
            return $all_data;
            //echo $dataArry['surveys']['question'];
        }
    }
    /**
     * @param $user_master_id
     * @return array
     */
    public function marquee($user_master_id)
    {
        if (!empty($user_master_id)) {
            //$this->db->cache_on();
            $sql = "SELECT
             *
            FROM
            knwlg_announcement sd
            WHERE
            type = 'head'
            and
            publish_date = CURRENT_DATE()
            order  by rand()
            ";
            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result_array();
            $entities = array();
            $i = 0;
            foreach ($result as $row) {
                $entities[$i]['id'] = $row['id'];
                $entities[$i]['text'] = $row['describtion'];
                $entities[$i]['url'] = $row['url'];
                $entities[$i]['url_type'] = $row['url_type'];
                $i++;
            }
            return $entities;
            //echo $dataArry['surveys']['question'];
        }
    }
    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function all_bookedmastersession($user_master_id = '')
    {
        if (!empty($user_master_id)) {
            $this->db->select('knwlg_sessions_id');
            $this->db->where('participant_id', $user_master_id);
            $this->db->where('participant_type', 'member');
            $this->db->where_not_in('session_approval_status', 3);
            $query = $this->db->get('knwlg_sessions_participant');
            $result = $query->result();
            //echo $this->db->last_query(); exit();
            return $result;
        }
    }
    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function session($booked_id = '', $user_master_id = '')
    {
        $new_arr = array();
        foreach ($booked_id as $sing) {
            $new_arr[] = $sing->knwlg_sessions_id;
        }
        $res_arr = implode(',', $new_arr);
        $sqlStr = '';
        /* if ($res_arr) {
            $sqlStr = " and ( ks.session_id  NOT IN(" . $res_arr . ") AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "')  ";
        } else {
            $sqlStr = " and (ks.start_datetime >= '" . date("Y-m-d H:i:s") . "')";
        }*/
        if ($res_arr != '') {
            $sqlStr = " and  ks.session_id  NOT IN(" . $res_arr . ") ";
        }
        $sql_start_time_query  = " and ks.start_datetime >= '" . date("Y-m-d H:i:s") . "'";
        $sql = "SELECT
            ksp.participant_id,
            ks.session_id AS ks_session_id,
            ks.session_doctor_id,
            ks.session_topic,
            ks.session_description,
            ks.sessions_question,
            ks.master_tag_ids,
            ks.client_id,
            ks.sponsor_id,
            ks.user_group_id,
            ks.category_id,
            ks.start_datetime,
            ks.end_datetime,
            ks.speciality_id,
            ks.total_seats,
            ks.total_buffer,
            ks.add_question_buffer_days,
            ks.session_link,
            ks.master_conf_provider_id,
            ks.session_access_code,
            ks.deeplink,
            ks.in_deeplink,
            ks.gl_deeplink,
            ks.template_id,
            ks.cert_template_id,
            ks.display_in_dashboard,
            ks.conf_phone_no,
            ks.privacy_status,
            ks.color,
            ks.added_on,
            ks.added_by,
            ks.session_status,
            ks.cover_image,
            ks.modified_on,
            ks.modified_by,
            ks.is_recommended,
            ks.is_multiday_session,
            ks.break_json,
            ks.status,
            ks.is_featured,
            ks.rating_flag,
            ks.remarks,
            ks.crm_id,
            ks.img_credits,
            ks.session_json,
            ks.certified,
            ks.env,
            ks.notification_template,
            ks.shortlink,
            ks.invitefile,
            ks.exitroute,
            ks.is_share,
            ks.is_like,
            ks.is_comment,
            sd.knwlg_sessions_docs_id,
            sd.knwlg_sessions_id,
            sd.added_on,
            sd.added_by,
            sd.modified_on,
            sd.modified_by,
            sd.updated_at,
            sd.updated_by,
            sd.status,
            cln.client_name,
            cln.client_logo,
            GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
            GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
            msct.category_name,
            msct.category_logo,
            sd.document_path,
            sd.comment,
            ksd.knwlg_sessions_docs_id,
            ksd.document_path,
            ksd.comment,
            GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
            GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
            GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') AS speciality,
            GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
            GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images,
            GROUP_CONCAT(ksp.participant_id) AS PartName,
            GROUP_CONCAT(ksp.is_attended) AS IS_ATTENDED,
            (ks.total_buffer + ks.total_seats) AS tot_seat
            FROM knwlg_sessions_V1 AS ks
            LEFT JOIN master_specialities_V1 AS ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
            LEFT JOIN client_master AS cln ON cln.client_master_id = ks.client_id
            LEFT JOIN session_to_sponsor AS sTspon ON sTspon.session_id = ks.session_id
            LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = sTspon.sponsor_id
            LEFT JOIN master_session_category AS msct ON msct.mastersession_category_id = ks.category_id
            LEFT JOIN knwlg_sessions_doctors AS sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
            LEFT JOIN knwlg_sessions_documents AS sd ON sd.knwlg_sessions_id = ks.session_id
            LEFT JOIN knwlg_sessions_participant AS ksp ON ksp.knwlg_sessions_id = ks.session_id
            LEFT JOIN knwlg_sessions_documents AS ksd ON ksd.knwlg_sessions_id = ks.session_id
            WHERE ks.status = 3
            AND (ks.session_status = 1)
            GROUP BY ks.session_id,
                    ksp.participant_id,
                    ks.session_doctor_id,
                    ks.session_topic,
                    ks.session_description,
                    ks.sessions_question,
                    ks.master_tag_ids,
                    ks.client_id,
                    ks.sponsor_id,
                    ks.user_group_id,
                    ks.category_id,
                    ks.start_datetime,
                    ks.end_datetime,
                    ks.speciality_id,
                    ks.total_seats,
                    ks.total_buffer,
                    ks.add_question_buffer_days,
                    ks.session_link,
                    ks.master_conf_provider_id,
                    ks.session_access_code,
                    ks.deeplink,
                    ks.in_deeplink,
                    ks.gl_deeplink,
                    ks.template_id,
                    ks.cert_template_id,
                    ks.display_in_dashboard,
                    ks.conf_phone_no,
                    ks.privacy_status,
                    ks.color,
                    ks.added_on,
                    ks.added_by,
                    ks.session_status,
                    ks.cover_image,
                    ks.modified_on,
                    ks.modified_by,
                    ks.is_recommended,
                    ks.is_multiday_session,
                    ks.break_json,
                    ks.status,
                    ks.is_featured,
                    ks.rating_flag,
                    ks.remarks,
                    ks.crm_id,
                    ks.img_credits,
                    ks.session_json,
                    ks.certified,
                    ks.env,
                    ks.notification_template,
                    ks.shortlink,
                    ks.invitefile,
                    ks.exitroute,
                    ks.is_share,
                    ks.is_like,
                    ks.is_comment,
                    sd.knwlg_sessions_docs_id,
                    sd.knwlg_sessions_id,
                    sd.added_on,
                    sd.added_by,
                    sd.modified_on,
                    sd.modified_by,
                    sd.updated_at,
                    sd.updated_by,
                    sd.status,
                    cln.client_name,
                    cln.client_logo,
                    msct.category_name,
                    msct.category_logo,
                    sd.document_path,
                    sd.comment,
                    ksd.knwlg_sessions_docs_id,
                    ksd.document_path,
                    ksd.comment
            ORDER BY ks.start_datetime ASC
            LIMIT 0,5";
        //echo $sql; exit();
        $query = $this->db->query($final_sql);
        $result = $query->result_array();
        if (count($result)  < 1) {
            $final_sql =  $sql .  $sql_start_time_query  . "
            AND (ks.session_status=1)
            GROUP BY ks.session_id
            ORDER BY ks.start_datetime ASC limit 0,5";
            //echo $sql; exit();
            $query = $this->db->query($final_sql);
            $result = $query->result_array();
        }
        //print_r($result); exit();
        //ks.session_status
        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            //$entities[$i]['participant_id'] = $row['participant_id'];
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $entities[$i]['session_topic'] = $row['session_topic'];
            $entities[$i]['session_description'] = strip_tags($row['session_description']);
            $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            $entities[$i]['client_id'] = $row['client_id'];
            $entities[$i]['client_name'] = $row['client_name'];
            $entities[$i]['color'] = ($row['color'] != '') ? $row['color'] : '#918c91';
            /**
             * new sponsor logic
             */
            $sponsorLogoArry = explode(",", $row['sponsor_logo']);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        // if full path exist
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                }
            } else {
                if ($row['sponsor_logo']) {
                    /// if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = '' . $row['sponsor_logo'];
                    }
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($row['document_path'] != "" || $row['document_path'] != null) {
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
            } else {
                $entities[$i]['document_path'] = "";
            }
            if ($row['comment'] != "" || $row['comment'] != null) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['category_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['start_datetime'] = $row['start_datetime']; // date(' jS M y', strtotime($row['start_datetime']));
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            $post_date = $row['added_on'];
            $start_date = $row['ms_start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            $entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $this->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            $entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }
            $entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }
            if ($row['session_status'] == 2) {
                $liveMode = true;
            } else {
                $liveMode = false;
            }
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['live_mode'] = $liveMode;
            $entities[$i]['template'] = "blue_master_cast";
            $entities[$i]['ms_start_datetime'] = $row['start_datetime'];
            $entities[$i]['end_datetime'] = $row['end_datetime'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = $row['category_logo'];
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['deeplink'] = $row['deeplink'];
            $coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
            $entities[$i]['cover_image'] = ($row['cover_image'] != '') ? change_img_src($row['cover_image']) : $coverImg;
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            $session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                     $logic_image = base_url() . "uploads/docimg/" . $image;
                 } else {
                     $logic_image = base_url() . "uploads/docimg/MConsult.png";
                 }*/
                if ($image) {
                    // $logic_image_path = "uploads/docimg/" . $image;
                    // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    // $logic_image = $imgPr;
                    /////============================== updated by  ramanath  14-5-21
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = docimg; //$imgPr;
                    }
                    //=======================================
                } else {
                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $i++;
        }
        return $entities;
        //print_r($entities); exit();
    }
    /**
     * @param string $user_master_id
     * @return mixed
     */

    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function trending(
        $type,
        $user_master_id,
        $client_ids,
        $limitTo,
        $limitFrom,
        $spIds,
        $type_id
    ) {
        if (!empty($user_master_id)) {
            //get user speciality
            $sqlInt = "select
            specialities_id
            from
            user_to_interest
            where
            user_master_id = " . $user_master_id . "";
            $queryInt = $this->db->query($sqlInt);
            $resultInt = $queryInt->result_array();
            //print_r($resultInt); exit;
            $specialities = array();
            foreach ($resultInt as $val) {
                $specialities[] = $val['specialities_id'];
                //$specialities = array_merge($specialities, $val);
            }
            if (!empty($specialities)) {
                $specialityIds = implode(",", (array)$specialities);
            }
            if ($specialityIds != '') {
                $specialities_query = ' and (' . implode(' OR ', array_map(function ($x) {
                    return "FIND_IN_SET('$x', fd.speciality_id)";
                }, explode(',', $specialityIds))) . ')';
            } else {
                $specialities_query = "";
            }
            $limit = "limit " . $limitFrom . " , " . $limitTo;
            $env = get_user_env_id($user_master_id);
            if ($env) {
                if ($env != 2) {
                    $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                } else {
                    $envStatus = "AND cTenv.env =" . $env . "";
                }
            } else {
                $envStatus = "";
            }
            switch ($type) {
                case 'comp':
                    $key_locked = get_user_package($user_master_id, 'comp');
                    $sql = "SELECT
                        cm.comp_qa_id as type_id,
                        cm.comp_qa_question,
                        cm.comp_qa_answer,
                        cm.comp_qa_answer_raw as description,
                        cm.comp_qa_question_raw as title,
                        cm.comp_qa_file_img,
                        cm.comp_qa_file_img_thumbnail,
                        cm.added_on,
                        cm.publication_date as publish_date,
                        cln.client_name,
                        cln.client_logo,
                        cm.color,
                        cm.type,
                        cm.vendor,
                        cm.src,
                        cm.deeplink,
                        cTenv.price,
                        uTpyCont.status as user_contnet_payment_status,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names,
                        MAX(ms.rank) as maxrank,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                        cm.comp_qa_speciality_id,
                        (SELECT COUNT(rt.rating)
                        FROM knwlg_rating rt
                        WHERE rt.post_id = cm.comp_qa_id AND rt.post_type = 'comp') AS averageRating,
                        rtmy.rating AS myrating,
                        kv.status AS vault
                    FROM knwlg_compendium_V1 AS cm
                    JOIN compendium_to_specialities AS cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
                    JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                    JOIN client_master AS cln ON cln.client_master_id = cm.client_id
                    LEFT JOIN compendium_to_sponsor AS cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
                    LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                    LEFT JOIN content_to_env AS cTenv ON cTenv.type_id = cm.comp_qa_id AND cTenv.type = 1
                    LEFT JOIN payment_user_to_content AS uTpyCont ON uTpyCont.type_id = cm.comp_qa_id AND uTpyCont.type = 1 AND uTpyCont.user_master_id = " . $user_master_id . "
                    LEFT JOIN knwlg_rating AS rtmy ON rtmy.post_id = cm.comp_qa_id
                        AND rtmy.post_type = 'comp'
                        AND rtmy.rating != 0
                        AND rtmy.user_master_id = " . $user_master_id . "
                    LEFT JOIN knwlg_vault AS kv ON kv.post_id = cm.comp_qa_id
                        AND kv.type_text = 'comp'
                        AND kv.user_id = " . $user_master_id . "
                    WHERE
                        cm.status = 3
                        " . $envStatus . "
                        AND cm.display_in_dashboard = 1
                        AND cm.privacy_status = 0
                        AND cm.publication_date <= CURDATE()
                    GROUP BY
                        cm.comp_qa_id,
                        cm.comp_qa_question,
                        cm.comp_qa_answer,
                        cm.comp_qa_answer_raw,
                        cm.comp_qa_question_raw,
                        cm.comp_qa_file_img,
                        cm.comp_qa_file_img_thumbnail,
                        cm.added_on,
                        cm.publication_date,
                        cln.client_name,
                        cln.client_logo,
                        cm.color,
                        cm.type,
                        cm.vendor,
                        cm.src,
                        cm.deeplink,
                        cTenv.price,
                        uTpyCont.status,
                        cm.comp_qa_speciality_id,
                        rtmy.rating,
                        kv.status
                    ORDER BY cm.publication_date DESC
                    LIMIT 0, 5";
                    #echo $sql; exit;
                    $query = $this->db->query($sql);
                    $result = $query->result();
                    //print_r($result); exit;
                    $i = 1;
                    $vx = array();
                    foreach ($result as $val) {
                        if ($val->comp_qa_file_img_thumbnail) {
                            // $logic_image_path = "uploads/compendium/" . $val->comp_qa_file_img;
                            // $imgPr = image_thumb_url($logic_image_path, $val->comp_qa_file_img, 450, 250, '');
                            $logic_image = $val->comp_qa_file_img_thumbnail;
                        } else {
                            $logic_image = '';
                        }
                        $sponsorLogoArry = explode(",", $val->sponsor_logo);
                        if (count($sponsorLogoArry) > 0) {
                            foreach ($sponsorLogoArry as $valueSponor) {
                                if ($valueSponor) {
                                    $sponsorLogomix[] = '' . $valueSponor;
                                }
                            }
                        } else {
                            if ($val->sponsor_logo) {
                                $sponsorLogomix[] = '' . $val->sponsor_logo;
                            }
                        }
                        $sponsorLogo = implode(",", (array)$sponsorLogomix);
                        unset($sponsorLogomix);
                        unset($sponsorLogoArry);
                        $vx[] = array(
                            "slno" => $i,
                            "trending_type" => "comp",
                            "type" => "comp",
                            "con_type" => $val->type,
                            "vendor" => $val->vendor,
                            "color" => ($val->color != '') ? $val->color : '#918c91',
                            "src" => $val->src,
                            "type_id" => $val->type_id,
                            "type" => 'comp',
                            "date" => date(' jS F y', strtotime($val->publish_date)),
                            "question" => html_entity_decode(strip_tags($val->title)),
                            "image" => change_img_src($logic_image),
                            "answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                            "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                            "client_name" => $val->client_name,
                            "client_logo" => change_img_src('' . $val->client_logo),
                            "sponsor_name" => $val->sponsor,
                            "sponsor_logo" => change_img_src($sponsorLogo),
                            //============ integrated for subscription ============//
                            "is_locked" => $key_locked,
                            "price" => $val->price,
                            "user_content_payment" => $val->user_contnet_payment_status,
                            //get_user_content_status($val->type_id, 1, $user_master_id),
                            //============ integrated for subscription ============//
                            "comment_count" => $val->count_comment,
                            "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                            "myrating" => ($val->myrating != '') ? true : false,
                            "vault" => ($val->vault != '') ? $val->vault : 0,
                            "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                        );
                        $i++;
                    }
                    break;
                case 'spq':
                    $key_locked = get_user_package($user_master_id, 'survey');
                    $sqlCompl = "SELECT
                    survey_id
                    FROM
                    survey_user_answer sv
                    WHERE
                    sv.user_master_id = '" . $user_master_id . "'";
                    $queryCompl = $this->db->query($sqlCompl);
                    $resultCompl = $queryCompl->result();
                    $complID = array();
                    foreach ($resultCompl as $valCompl) {
                        $complID[] = $valCompl->survey_id;
                    }
                    //print_r($complID); exit;
                    $sqlInCompl = "SELECT
                    survey_id
                    FROM
                    survey_user_incomplete_answer sv
                    WHERE
                    sv.status = 3
                    and
                    sv.user_master_id = '" . $user_master_id . "'";
                    $queryInCompl = $this->db->query($sqlInCompl);
                    $resultInCompl = $queryInCompl->result();
                    $incomplID = array();
                    foreach ($resultInCompl as $valInCompl) {
                        $incomplID[] = $valInCompl->survey_id;
                    }
                    $arrayFinal = array_unique(array_merge($complID, $incomplID));
                    //print_r($arrayFinal); exit;
                    $complIDStr = implode(",", (array)$arrayFinal);
                    //echo $complIDStr ; exit;
                    if ($complIDStr) {
                        $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
                    } else {
                        $qryStr = '';
                    }
                    $sql = "SELECT
                                sv.survey_id,
                                sv.category,
                                sv.survey_title,
                                sv.survey_description,
                                sv.image,
                                sv.survey_points,
                                sv.survey_time,
                                sv.question_count,
                                sv.publishing_date,
                                sv.client_id,
                                sv.sponsor_ids,
                                sv.deeplink,
                                sv.gl_deeplink,
                                sv.verified,
                                sv.display_in_dashboard,
                                sv.privacy_status,
                                sv.template_id,
                                sv.color,
                                sv.points_on_approval,
                                sv.added_on,
                                sv.added_by,
                                sv.modified_on,
                                sv.modified_by,
                                sv.status,
                                sv.is_available_survey_portal,
                                sv.available_for_live_session,
                                sv.env,
                                sv.is_share,
                                sv.is_like,
                                sv.is_comment,
                                sv.approved_by,
                                sv.img_credits,
                                svd.data,
                                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                                GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                                cln.client_name,
                                cln.client_logo,
                                cTenv.price,
                                uTpyCont.status AS user_contnet_payment_status,
                                GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                                GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                            FROM survey sv
                            LEFT JOIN survey_to_speciality svts ON svts.survey_id = sv.survey_id
                            LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = svts.speciality_id
                            JOIN client_master cln ON cln.client_master_id = sv.client_id
                            LEFT JOIN survey_to_sponsor suvTspon ON suvTspon.survey_id = sv.survey_id
                            LEFT JOIN client_master clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                            LEFT JOIN content_to_env cTenv ON cTenv.type_id = sv.survey_id AND cTenv.type = 6
                            LEFT JOIN payment_user_to_content uTpyCont ON uTpyCont.type_id = sv.survey_id
                                AND uTpyCont.type = 6
                                AND uTpyCont.user_master_id = " . $user_master_id . "
                            JOIN survey_detail svd ON svd.survey_id = sv.survey_id
                            LEFT JOIN survey_user_answer sua ON sua.survey_id = sv.survey_id
                            WHERE
                                sv.status = 3
                                " . $envStatus . "
                                AND sv.category = 'quiz'
                                AND sv.display_in_dashboard = 1
                                AND DATE(sv.publishing_date) <= CURDATE()
                                AND sv.privacy_status = 0
                                " . $qryStr . "
                            GROUP BY
                                sv.survey_id,
                                sv.category,
                                sv.survey_title,
                                sv.survey_description,
                                sv.image,
                                sv.survey_points,
                                sv.survey_time,
                                sv.question_count,
                                sv.publishing_date,
                                sv.client_id,
                                sv.sponsor_ids,
                                sv.deeplink,
                                sv.gl_deeplink,
                                sv.verified,
                                sv.display_in_dashboard,
                                sv.privacy_status,
                                sv.template_id,
                                sv.color,
                                sv.points_on_approval,
                                sv.added_on,
                                sv.added_by,
                                sv.modified_on,
                                sv.modified_by,
                                sv.status,
                                sv.is_available_survey_portal,
                                sv.available_for_live_session,
                                sv.env,
                                sv.is_share,
                                sv.is_like,
                                sv.is_comment,
                                sv.approved_by,
                                sv.img_credits,
                                svd.data,
                                cln.client_name,
                                cln.client_logo,
                                cTenv.price,
                                uTpyCont.status
                            ORDER BY sv.publishing_date DESC
                            LIMIT 0, 3";
                    //echo $sql; exit;
                    //exit;
                    //" . $limit . ";
                    //add child checking in this sql
                    //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
                    //exit;
                    //echo  $sql; exit;
                    $query = $this->db->query($sql);
                    //$this->db->cache_off();
                    $result = $query->result();
                    foreach ($result as $val) {
                        $dataArry = unserialize($val->data);
                        $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                        $str = preg_replace('/\\\"/', "\"", $json);
                        $sponsorLogoArry = explode(",", $val->sponsor_logo);
                        if (count($sponsorLogoArry) > 0) {
                            foreach ($sponsorLogoArry as $valueSponor) {
                                if ($valueSponor) {
                                    $sponsorLogomix[] = '' . $valueSponor;
                                }
                            }
                        } else {
                            if ($val->sponsor_logo) {
                                $sponsorLogomix[] = '' . $val->sponsor_logo;
                            }
                        }
                        $sponsorLogo = implode(",", (array)$sponsorLogomix);
                        unset($sponsorLogomix);
                        unset($sponsorLogoArry);
                        $vx[] = array(
                            "survey_id" => $val->survey_id,
                            "trending_type" => "survey",
                            "type" => "survey",
                            "survey_time" => $val->survey_time,
                            "question_count" => $val->question_count,
                            "category" => $val->category,
                            "point" => $val->survey_points,
                            //============ integrated for subscription ============//
                            "is_locked" => $key_locked,
                            "price" => $val->price,
                            "user_content_payment" => $val->user_contnet_payment_status,
                            //get_user_content_status($val->type_id, 6, $user_master_id),
                            //============ integrated for subscription ============//
                            "json_data" => $str,
                            "survey_title" => $val->survey_title,
                            "deeplink" => $val->deeplink,
                            "survey_description" => substr(strip_tags($val->survey_description), 0, 150),
                            "image" => change_img_src($val->image),
                            "specialities_name" => $val->specialities_name,
                            "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                            "client_name" => $val->client_name,
                            "client_logo" => change_img_src('' . $val->client_logo),
                            "sponsor_name" => $val->sponsor,
                            "sponsor_logo" => change_img_src($sponsorLogo),
                            "publishing_date" => $val->publishing_date,
                        );
                    }
                    break;
                case 'gr':
                    //$this->db->cache_on();
                    $key_locked = get_user_package($user_master_id, 'gr');
                    $sql = "SELECT
                                gr.gr_id AS type_id,
                                gr.gr_title AS title,
                                gr.gr_description AS description,
                                gr.gr_chief_scientific_editor,
                                gr.gr_preview_image,
                                gr.added_on,
                                gr.gr_date_of_publication AS publish_date,
                                gr.deeplink,
                                gr.color,
                                cTenv.price,
                                uTpyCont.status AS user_contnet_payment_status,
                                cln.client_name,
                                cln.client_logo,
                                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                                GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                                GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                                GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id) AS session_doctor_id,
                                GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
                                (SELECT COUNT(rt.rating)
                                FROM knwlg_rating rt
                                WHERE rt.post_id = gr.gr_id AND rt.post_type = 'gr') AS averageRating,
                                rtmy.rating AS myrating,
                                (SELECT COUNT(kcm.knwlg_comment_id)
                                FROM knwlg_comment kcm
                                WHERE kcm.type_id = gr.gr_id AND kcm.type = 'gr') AS count_comment,
                                kv.status AS vault
                            FROM knwlg_gr_register AS gr
                            JOIN gr_to_specialities AS grTs ON grTs.gr_id = gr.gr_id
                            JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = grTs.specialities_id
                            JOIN client_master AS cln ON cln.client_master_id = gr.client_id
                            LEFT JOIN gr_to_sponsor AS grTspon ON grTspon.gr_id = gr.gr_id
                            LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = grTspon.sponsor_id
                            LEFT JOIN content_to_env AS cTenv ON cTenv.type_id = gr.gr_id AND cTenv.type = 5
                            LEFT JOIN payment_user_to_content AS uTpyCont ON uTpyCont.type_id = gr.gr_id
                                AND uTpyCont.type = 5 AND uTpyCont.user_master_id = " . $user_master_id . "
                            LEFT JOIN gr_to_session_doctor AS grTsdoc ON grTsdoc.gr_id = gr.gr_id
                            LEFT JOIN knwlg_rating AS rtmy ON rtmy.post_id = gr.gr_id
                                AND rtmy.post_type = 'gr' AND rtmy.rating != 0 AND rtmy.user_master_id = " . $user_master_id . "
                            LEFT JOIN knwlg_vault AS kv ON kv.post_id = gr.gr_id
                                AND kv.type_text = 'gr' AND kv.user_id = " . $user_master_id . "
                            WHERE
                                gr.status = 3
                                " . $envStatus . "
                                AND gr.gr_id != 10
                                AND gr.display_in_dashboard = 1
                                AND DATE(gr.gr_date_of_publication) <= CURDATE()
                                AND gr.privacy_status = 0
                            GROUP BY
                                gr.gr_id, gr.gr_title, gr.gr_description, gr.gr_chief_scientific_editor,
                                gr.gr_preview_image, gr.added_on, gr.gr_date_of_publication, gr.deeplink,
                                gr.color, cTenv.price, uTpyCont.status, cln.client_name, cln.client_logo, kv.status, rtmy.rating
                            ORDER BY gr.gr_date_of_publication DESC
                            LIMIT 3";
                    //echo $sql; exit;
                    //exit;
                    //add child checking in this sql
                    //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
                    //exit;
                    //echo  $sql; exit;
                    $query = $this->db->query($sql);
                    //$this->db->cache_off();
                    $result = $query->result();
                    //print_r($result); exit;
                    $i = 1;
                    $vx = array();
                    foreach ($result as $val) {
                        $sponsorLogoArry = explode(",", $val->sponsor_logo);
                        if (count($sponsorLogoArry) > 0) {
                            foreach ($sponsorLogoArry as $valueSponor) {
                                if ($valueSponor) {
                                    $sponsorLogomix[] = '' . $valueSponor;
                                }
                            }
                        } else {
                            if ($val->sponsor_logo) {
                                $sponsorLogomix[] = '' . $val->sponsor_logo;
                            }
                        }
                        $sponsorLogo = implode(",", (array)$sponsorLogomix);
                        unset($sponsorLogomix);
                        unset($sponsorLogoArry);
                        $ses_doc_det_array = array();
                        if ($val->session_doctor_id) {
                            $session_doc_array = explode(",", $val->session_doctor_id);
                            $inc_pp = 0;
                            foreach ($session_doc_array as $single_doctor) {
                                $var = session_doc_detail($single_doctor);
                                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                if ($image) {
                                    $logic_image = $var[0]['profile_image'];
                                } else {
                                    // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                    // $logic_image_path = "uploads/docimg/" . $image;
                                    // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                    $logic_image = docimg; //$imgPr;
                                    //$logic_image = $var[0]['profile_image'];
                                }
                                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                                $inc_pp++;
                            }
                        }
                        $vx[] = array(
                            "slno" => $i,
                            "type_id" => $val->type_id,
                            "trending_type" => "gr",
                            "type" => 'gr',
                            "date" => date(' jS F y', strtotime($val->publish_date)),
                            "title" => html_entity_decode(strip_tags($val->title)),
                            "image" => change_img_src($val->gr_preview_image),
                            "color" => ($val->color != '') ? $val->color : '#918c91',
                            "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                            "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                            "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                            "client_name" => $val->client_name,
                            "client_logo" => change_img_src('' . $val->client_logo),
                            //============ integrated for subscription ============//
                            "is_locked" => $key_locked,
                            "price" => $val->price,
                            "user_content_payment" => $val->user_contnet_payment_status,
                            //get_user_content_status($val->type_id, 5, $user_master_id),
                            //============ integrated for subscription ============//
                            "sponsor_name" => $val->sponsor,
                            "sponsor_logo" => change_img_src($sponsorLogo),
                            "session_doctor_entities" => $ses_doc_det_array,
                            "comment_count" => $val->count_comment,
                            "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                            "myrating" => ($val->myrating != '') ? true : false,
                            "vault" => ($val->vault != '') ? $val->vault : 0,
                            "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                        );
                        $i++;
                    }
                    break;
                case 'allOLD':
                    //for survey
                    $sqlCompl = "SELECT
                    survey_id
                    FROM
                    survey_user_answer sv
                    WHERE
                    sv.user_master_id = '" . $user_master_id . "'";
                    $queryCompl = $this->db->query($sqlCompl);
                    $resultCompl = $queryCompl->result();
                    $complID = array();
                    foreach ($resultCompl as $valCompl) {
                        $complID[] = $valCompl->survey_id;
                    }
                    //print_r($complID); exit;
                    $sqlInCompl = "SELECT
                    survey_id
                    FROM
                    survey_user_incomplete_answer sv
                    WHERE
                    sv.status = 3
                    and
                    sv.user_master_id = '" . $user_master_id . "'";
                    $queryInCompl = $this->db->query($sqlInCompl);
                    $resultInCompl = $queryInCompl->result();
                    $incomplID = array();
                    foreach ($resultInCompl as $valInCompl) {
                        $incomplID[] = $valInCompl->survey_id;
                    }
                    $arrayFinal = array_unique(array_merge($complID, $incomplID));
                    //print_r($arrayFinal); exit;
                    $complIDStr = implode(",", (array)$arrayFinal);
                    //echo $complIDStr ; exit;
                    if ($complIDStr) {
                        $qryStr = '';
                    } else {
                        $qryStr = '';
                    }
                    //for survey
                    //fd.publish_date <= CURDATE()
                    //$this->db->cache_on();
                    $sql = "SELECT
                                fd.feed_id,
                                fd.type_id,
                                fd.type,
                                fd.title,
                                fd.description,
                                fd.image,
                                fd.session_doctor_id,
                                fd.added_on,
                                fd.publish_date,
                                fd.end_date,
                                ksc.category_name,
                                ksc.category_logo,
                                ks.session_status,
                                ks.session_doctor_id as session_doctor_id_ss,
                                ks.color as session_color,
                                ks.deeplink as session_deeplink,
                                ks.session_topic as session_topic,
                                cln.client_name,
                                cln.client_logo,
                                fd.publish_date_session,
                                GROUP_CONCAT(DISTINCT msCms.specialities_name) as specialities_name_comp,
                                GROUP_CONCAT(DISTINCT msSvs.specialities_name) as specialities_name_survey,
                                GROUP_CONCAT(DISTINCT msSs.specialities_name) as specialities_name_session,
                                GROUP_CONCAT(DISTINCT msDs.specialities_name) as specialities_name_doctor,
                                GROUP_CONCAT(DISTINCT msVs.specialities_name) as specialities_name_video,
                                GROUP_CONCAT(DISTINCT clintsponCM.client_name) as sponsorCM,
                                GROUP_CONCAT(DISTINCT clintsponCM.client_logo) as sponsor_logoCM,
                                GROUP_CONCAT(DISTINCT clintsponSES.client_name) as sponsorSES,
                                GROUP_CONCAT(DISTINCT clintsponSES.client_logo) as sponsor_logoSES,
                                GROUP_CONCAT(DISTINCT clintsponSUV.client_name) as sponsorSUV,
                                GROUP_CONCAT(DISTINCT clintsponSUV.client_logo) as sponsor_logoSUV,
                                (SELECT COUNT(rt.rating) FROM knwlg_rating rt WHERE rt.post_id = fd.type_id AND rt.post_type = fd.type) as averageRating,
                                rtmy.rating as myrating,
                                sdoc.doctor_name,
                                sdoc.profile_image,
                                (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm WHERE kcm.type_id = fd.type_id AND kcm.type = fd.type) as count_comment,
                                cm.deeplink as cm_deeplink,
                                cm.comp_qa_file_img,
                                cm.comp_qa_file_img_thumbnail,
                                cm.type as con_type,
                                cm.vendor as vendor_cm,
                                cm.src as src_cm,
                                cm.color as medwiki_color,
                                kva.video_archive_file_img,
                                kva.deeplink,
                                kva.vendor,
                                kva.src,
                                kvaTs.session_doctor_id,
                                kvsc.category_name as category_name_video,
                                kvsc.category_logo as category_logo_video,
                                kvaTs.session_id,
                                sv.survey_points,
                                sv.category,
                                sv.deeplink as sur_deeplink,
                                mst.status_name,
                                kv.status as vault
                            FROM feed_V1 as fd
                            LEFT JOIN compendium_to_specialities as cmTs ON (cmTs.comp_qa_id = fd.type_id AND fd.type = 'comp')
                            LEFT JOIN master_specialities_V1 as msCms ON (msCms.master_specialities_id = cmTs.specialities_id)
                            LEFT JOIN survey_to_speciality as svts ON (svts.survey_id = fd.type_id AND fd.type = 'survey')
                            LEFT JOIN master_specialities_V1 as msSvs ON (msSvs.master_specialities_id = svts.speciality_id)
                            LEFT JOIN session_to_specialities as sesTs ON (sesTs.session_id = fd.type_id AND fd.type = 'session')
                            LEFT JOIN master_specialities_V1 as msSs ON (msSs.master_specialities_id = sesTs.specialities_id)
                            LEFT JOIN doctors_to_specialities as docTs ON (docTs.sessions_doctors_id = fd.type_id AND fd.type = 'user')
                            LEFT JOIN master_specialities_V1 as msDs ON (msDs.master_specialities_id = docTs.specialities_id)
                            LEFT JOIN video_archive_to_specialities as vaTs ON (vaTs.video_archive_id = fd.type_id AND fd.type = 'video_archive')
                            LEFT JOIN master_specialities_V1 as msVs ON (msVs.master_specialities_id = vaTs.specialities_id)
                            LEFT JOIN client_master as cln ON cln.client_master_id = fd.client_id
                            LEFT JOIN compendium_to_sponsor as cmTspon ON (cmTspon.comp_qa_id = fd.type_id AND fd.type = 'comp')
                            LEFT JOIN client_master as clintsponCM ON clintsponCM.client_master_id = cmTspon.sponsor_id
                            LEFT JOIN session_to_sponsor as sTspon ON (sTspon.session_id = fd.type_id AND fd.type = 'session')
                            LEFT JOIN client_master as clintsponSES ON clintsponSES.client_master_id = sTspon.sponsor_id
                            LEFT JOIN survey_to_sponsor as suvTspon ON (suvTspon.survey_id = fd.type_id AND fd.type = 'survey')
                            LEFT JOIN client_master as clintsponSUV ON clintsponSUV.client_master_id = suvTspon.sponsor_id
                            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = fd.type_id AND rtmy.post_type = fd.type AND rtmy.rating != 0 AND rtmy.user_master_id = " . $user_master_id . "
                            LEFT JOIN knwlg_vault as kv ON kv.post_id = fd.type_id AND kv.type_text = fd.type AND kv.user_id = " . $user_master_id . "
                            LEFT JOIN knwlg_sessions_doctors as sdoc ON sdoc.sessions_doctors_id = fd.session_doctor_id
                            LEFT JOIN knwlg_sessions_V1 as ks ON (ks.session_id = fd.type_id AND fd.type = 'session')
                            LEFT JOIN master_session_status as mst ON mst.master_session_status_id = ks.session_status
                            LEFT JOIN knwlg_compendium_V1 as cm ON (cm.comp_qa_id = fd.type_id AND fd.type = 'comp')
                            LEFT JOIN knwlg_video_archive as kva ON (kva.video_archive_id = fd.type_id AND fd.type = 'video_archive')
                            LEFT JOIN knwlg_sessions_V1 as kvaTs ON (kvaTs.session_id = kva.video_archive_session_id)
                            LEFT JOIN survey as sv ON (sv.survey_id = fd.type_id AND fd.type = 'survey')
                            LEFT JOIN master_session_category as ksc ON (ksc.mastersession_category_id = ks.category_id)
                            LEFT JOIN master_session_category as kvsc ON (kvsc.mastersession_category_id = kvaTs.category_id)
                            WHERE
                                fd.status = 3
                                AND fd.type != 'user'
                                AND ((fd.publish_date <= CURDATE() AND fd.publish_date_session IS NULL)
                                    OR (fd.publish_date_session > CURDATE() AND fd.publish_date_session IS NOT NULL))
                                AND fd.display_in_dashboard = 1
                                " . $qryStr . "
                            GROUP BY
                                fd.feed_id,
                                fd.type_id,
                                fd.type,
                                fd.title,
                                fd.description,
                                fd.image,
                                fd.session_doctor_id,
                                fd.added_on,
                                fd.publish_date,
                                fd.end_date,
                                ksc.category_name,
                                ksc.category_logo,
                                ks.session_status,
                                ks.session_doctor_id,
                                ks.color,
                                ks.deeplink,
                                ks.session_topic,
                                cln.client_name,
                                cln.client_logo,
                                fd.publish_date_session,
                                rtmy.rating,
                                sdoc.doctor_name,
                                sdoc.profile_image,
                                cm.deeplink,
                                cm.comp_qa_file_img,
                                cm.comp_qa_file_img_thumbnail,
                                cm.type,
                                cm.vendor,
                                cm.src,
                                cm.color,
                                kva.video_archive_file_img,
                                kva.deeplink,
                                kva.vendor,
                                kva.src,
                                kvaTs.session_doctor_id,
                                kvsc.category_name,
                                kvsc.category_logo,
                                kvaTs.session_id,
                                sv.survey_points,
                                sv.category,
                                sv.deeplink,
                                mst.status_name,
                                kv.status
                            ORDER BY fd.publish_date_session ASC, fd.publish_date DESC
                            " . $limit . "";
                    //Index were not used because of the different ORDER BY a GROUP BY clauses. (my discovery)3144
                    //echo $sql;
                    //exit;
                    //and ( (fd.publish_date <= CURDATE() and fd.publish_date_session is null) or (fd
                    //.publish_date_session > CURDATE() and fd.publish_date_session is not null)
                    //)
                    //order by fd.feed_id desc
                    //order by publish_date_session asc,fd.publish_date desc
                    //GROUP_CONCAT(ms.specialities_name) as specialities_name,fd.publish_date desc LEFT JOIN master_specialities_V1 as ms ON (ms.master_specialities_id = cmTs.specialities_id and ms.master_specialities_id = sesTs.specialities_id and ms.master_specialities_id = svts.speciality_id and ms.master_specialities_id = vaTs.specialities_id)
                    //fd.publish_date_session, fd.publish_date DESC
                    //LEFT JOIN video_archive_to_sponsor as vArspon ON (vArspon.video_archive_id = fd.type_id and fd.type = 'video_archive')
                    //LEFT JOIN client_master as clintVAspon ON clintVAspon.client_master_id = vArspon.sponsor_id
                    //GROUP_CONCAT(DISTINCT clintVAspon.client_name) as sponsorVa,
                    // GROUP_CONCAT(DISTINCT clintVAspon.client_logo) as sponsor_logoVA,
                    $query = $this->db->query($sql);
                    //$this->db->cache_on();
                    $result = $query->result();
                    $i = 1;
                    $vx = array();
                    foreach ($result as $val) {
                        switch ($val->type) {
                            case 'comp':
                                if ($val->comp_qa_file_img_thumbnail) {
                                    $img = $val->comp_qa_file_img_thumbnail; //base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
                                } else {
                                    $img = '';
                                }
                                $sponsorCMLogoArry = explode(",", $val->sponsor_logoCM);
                                if (count($sponsorCMLogoArry) > 0) {
                                    foreach ($sponsorCMLogoArry as $valueSponor) {
                                        if ($valueSponor) {
                                            $sponsorCMLogomix[] = '' . $valueSponor;
                                        }
                                    }
                                } else {
                                    if ($val->sponsor_logoCM) {
                                        $sponsorCMLogomix[] = '' . $val->sponsor_logoCM;
                                    }
                                }
                                $sponsorCMLogo = implode(",", (array)$sponsorCMLogomix);
                                unset($sponsorCMLogomix);
                                unset($sponsorCMLogoArry);
                                $string = htmlentities($val->title, null, 'utf-8');
                                //$string = html_entity_decode($string);
                                $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                                $main_description = "";
                                $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
                                $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                                $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                                $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
                                //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
                                $vx[] = array(
                                    "slno" => $i,
                                    "trending_type" => "comp",
                                    "con_type" => $val->con_type,
                                    "vendor" => $val->vendor_cm,
                                    "src" => $val->src_cm,
                                    "sponsor_name" => $val->sponsorCM,
                                    "sponsor_logo" => change_img_src($sponsorCMLogo),
                                    "type_id" => $val->type_id,
                                    "type" => $val->type,
                                    "date" => date(' jS F y', strtotime($val->publish_date)),
                                    "question" => html_entity_decode(strip_tags($string)),
                                    "image" => change_img_src($img),
                                    //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                                    "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                                    "specialities" => ($val->specialities_name_comp != '') ? $val->specialities_name_comp : '',
                                    "color" => ($val->medwiki_color != '') ? $val->medwiki_color : '#918c91',
                                    "client_name" => $val->client_name,
                                    "client_logo" => change_img_src('' . $val->client_logo),
                                    "comment_count" => $val->count_comment,
                                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                                    "myrating" => ($val->myrating != '') ? true : false,
                                    "vault" => ($val->vault != '') ? $val->vault : 0,
                                    "deeplink" => ($val->cm_deeplink != '') ? $val->cm_deeplink : 0,
                                );
                                break;
                            case 'survey':
                                //$dataArry = unserialize($val->data);
                                //$json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                                //$str = preg_replace('/\\\"/', "\"", $json);
                                $sponsorLogoArry = explode(",", $val->sponsor_logoSUV);
                                if (count($sponsorLogoArry) > 0) {
                                    foreach ($sponsorLogoArry as $valueSponor) {
                                        if ($valueSponor) {
                                            $sponsorLogomix[] = '' . $valueSponor;
                                        }
                                    }
                                } else {
                                    if ($val->sponsor_logo) {
                                        $sponsorLogomix[] = '' . $val->sponsor_logoSUV;
                                    }
                                }
                                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                                unset($sponsorLogomix);
                                unset($sponsorLogoArry);
                                $vx[] = array(
                                    "survey_id" => $val->type_id,
                                    "trending_type" => "survey",
                                    "type" => "survey",
                                    "type_id" => $val->type_id,
                                    "category" => $val->category,
                                    "point" => $val->survey_points,
                                    //"json_data" => $str,
                                    "survey_title" => $val->title,
                                    "deeplink" => $val->sur_deeplink,
                                    "survey_description" => substr($val->description, 0, 150),
                                    "image" => change_img_src($val->image),
                                    "specialities_name" => $val->specialities_name_survey,
                                    "client_name" => $val->client_name,
                                    "client_logo" => change_img_src('' . $val->client_logo),
                                    "sponsor_name" => $val->sponsorSUV,
                                    "sponsor_logo" => change_img_src($sponsorLogo),
                                    "publishing_date" => $val->publishing_date,
                                );
                                break;
                            case 'session':
                                if (stripos($val->profile_image, "https://storage.googleapis.com") > -1) {
                                    $logic_image = '' . $val->profile_image;
                                } else {
                                    $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
                                }
                                $start_time = $val->publish_date;
                                $start_time = date("g:i A", strtotime($start_time));
                                $ses_doc_det_array = array();
                                if ($val->session_doctor_id_ss) {
                                    $session_doc_array = explode(",", $val->session_doctor_id_ss);
                                    $inc_pp = 0;
                                    foreach ($session_doc_array as $single_doctor) {
                                        $var = session_doc_detail($single_doctor);
                                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                        /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                                             $logic_image = base_url() . "uploads/docimg/" . $image;
                                         } else {
                                             $logic_image = base_url() . "uploads/docimg/MConsult.png";
                                         }*/
                                        if ($image) {
                                            // $logic_image_path = "uploads/docimg/" . $image;
                                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                            // $logic_image = $imgPr;
                                            /////============================== updated by  ramanath  14-5-21
                                            if (stripos($image, "https://storage.googleapis.com") > -1) {
                                                $logic_image = $image;
                                            } else {
                                                // $logic_image_path = "uploads/docimg/" . $image;
                                                // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                                $logic_image = docimg; //$imgPr;
                                            }
                                            //=======================================
                                        } else {
                                            $logic_image = base_url() . "uploads/docimg/no-image.png";
                                        }
                                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $single_doctor;
                                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                                        $inc_pp++;
                                    }
                                }
                                //$entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
                                $end_time = $val->end_date;
                                $end_time = date("g:i A", strtotime($end_time));
                                /**
                                 * new sponsor logic
                                 */
                                $sponsorSESLogoArry = explode(",", $val->sponsor_logoSES);
                                if (count($sponsorSESLogoArry) > 0) {
                                    foreach ($sponsorSESLogoArry as $valueSponor) {
                                        if ($valueSponor) {
                                            $sponsorSESLogomix[] = '' . $valueSponor;
                                        }
                                    }
                                } else {
                                    if ($val->sponsor_logoSES) {
                                        $sponsorSESLogomix[] = '' . $val->sponsor_logoSES;
                                    }
                                }
                                $sponsorLogoSES = implode(",", (array)$sponsorSESLogomix);
                                unset($sponsorSESLogomix);
                                unset($sponsorSESLogoArry);
                                $vx[] = array(
                                    "slno" => $i,
                                    "trending_type" => "session",
                                    "type_id" => $val->type_id,
                                    "session_id" => $val->type_id,
                                    "type" => "session",
                                    "doctor_name" => $val->doctor_name,
                                    "session_doctor_id" => $val->session_doctor_id_ss,
                                    "date" => date(' jS F y', strtotime($val->publish_date)),
                                    "start_datetime" => date(' jS F y', strtotime($val->publish_date)),
                                    "display_date" => $start_time . "-" . $end_time,
                                    "ms_cat_name" => $val->category_name,
                                    "category_image" => base_url() . "/themes/front/images/session/" . $val->category_logo,
                                    "sponsor_name" => $val->sponsorSES,
                                    "sponsor_logo" => change_img_src($sponsorLogoSES),
                                    "image" => change_img_src($logic_image),
                                    "image_raw_name" => change_img_src($val->profile_image),
                                    "session_status" => $val->session_status,
                                    "status_name" => $val->status_name,
                                    "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),
                                    "session_topic" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                                    "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),
                                    "specialities" => ($val->specialities_name_session != '') ? $val->specialities_name_session : '',
                                    "color" => ($val->session_color != '') ? $val->session_color : '#08cc9e',
                                    "client_name" => $val->client_name,
                                    "client_logo" => change_img_src('' . $val->client_logo),
                                    "comment_count" => $val->count_comment,
                                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                                    "myrating" => ($val->myrating != '') ? true : false,
                                    "vault" => ($val->vault != '') ? $val->vault : 0,
                                    "deeplink" => ($val->session_deeplink != '') ? $val->session_deeplink : 0,
                                    "session_doctor_entities" => $ses_doc_det_array,
                                    "cpddetail" => $this->getcpddetails($val->type_id)
                                );
                                break;
                            case 'user':
                                if (@getimagesize(base_url() . "uploads/docimg/" . $val->image)) {
                                    $logic_image = base_url() . "uploads/docimg/" . $val->image;
                                } else {
                                    $logic_image = base_url() . "uploads/docimg/MConsult.png";
                                }
                                $start_time = $val->publish_date;
                                $start_time = date("g:i A", strtotime($start_time));
                                $end_time = $val->end_date;
                                $end_time = date("g:i A", strtotime($end_time));
                                $vx[] = array(
                                    "slno" => $i,
                                    "trending_type" => "user",
                                    "type_id" => $val->type_id,
                                    "type" => $val->type,
                                    "doctor_name" => $val->title,
                                    "description" => $val->description,
                                    "date" => date(' jS F y', strtotime($val->publish_date)),
                                    "image" => change_img_src($logic_image),
                                    "image_raw_name" => change_img_src($val->image),
                                    "specialities" => ($val->specialities_name_doctor != '') ? $val->specialities_name_doctor : '',
                                );
                                break;
                            case 'video_archive':
                                if ($val->video_archive_file_img) {
                                    $img = $val->video_archive_file_img;
                                } else {
                                    $img = '';
                                }
                                $sponsorLogoArry = explode(",", $val->sponsor_logoVA);
                                if (count($sponsorLogoArry) > 0) {
                                    foreach ($sponsorLogoArry as $valueSponor) {
                                        if ($valueSponor) {
                                            $sponsorLogomix[] = '' . $valueSponor;
                                        }
                                    }
                                } else {
                                    if ($val->sponsor_logo) {
                                        $sponsorLogomix[] = '' . $val->sponsor_logo;
                                    }
                                }
                                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                                unset($sponsorLogomix);
                                unset($sponsorLogoArry);
                                $ses_doc_det_array = array();
                                if ($val->session_doctor_id) {
                                    $session_doc_array = explode(",", $val->session_doctor_id);
                                    $inc_pp = 0;
                                    foreach ($session_doc_array as $single_doctor) {
                                        $var = session_doc_detail($single_doctor);
                                        if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                                            $logic_image = $var[0]['profile_image'];
                                        } else {
                                            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                            if ($image) {
                                                // $logic_image_path = "uploads/docimg/" . $image;
                                                // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                                $logic_image = $image;
                                            } else {
                                                $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                                            }
                                        }
                                        $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                                        $inc_pp++;
                                    }
                                }
                                $string = htmlentities($val->title, null, 'utf-8');
                                $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                                $main_description = "";
                                $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
                                $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                                $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                                $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
                                //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
                                $vx[] = array(
                                    "slno" => $i,
                                    "con_type" => $val->type,
                                    "type_id" => $val->type_id,
                                    "trending_type" => "video_archive",
                                    "type" => "video_archive",
                                    "vendor" => $val->vendor,
                                    "session_id" => $val->session_id,
                                    "src" => $val->src,
                                    "type_id" => $val->type_id,
                                    "date" => date(' jS F y', strtotime($val->publication_date)),
                                    "question" => html_entity_decode(strip_tags($string)),
                                    "image" => change_img_src($img),
                                    //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                                    "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                                    "specialities" => ($val->specialities_name_video != '') ? $val->specialities_name_video : '',
                                    "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                                    "client_name" => $val->client_name,
                                    "client_logo" => change_img_src('' . $val->client_logo),
                                    "category_name" => $val->category_name_video,
                                    "category_logo" => change_img_src($val->category_logo_video),
                                    "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo_video),
                                    "sponsor_name" => $val->sponsor,
                                    "sponsor_logo" => change_img_src($sponsorLogo),
                                    "comment_count" => $val->count_comment,
                                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                                    "myrating" => ($val->myrating != '') ? true : false,
                                    "vault" => ($val->vault != '') ? $val->vault : 0,
                                    "session_doctor_id" => $val->session_doctor_id,
                                    "session_doctor_entities" => $ses_doc_det_array,
                                );
                                break;
                            case 'gr':
                                break;
                        }
                        $i++;
                    }
                    break;
                case 'all':
                    $limitToFn = floor($limitTo / 4);
                    $limitFromFn = floor($limitFrom / 4);
                    //$limitToFn = $limitTo;
                    //$limitFromFn = $limitFrom;
                    //echo $limitToFn.'---'.$limitFromFn;
                    //exit;
                    $compArry = $this->getcomp_v1($user_master_id, $limitFromFn, $limitToFn, $spIds, $type_id); //$this->getcomp($user_master_id,$limitFromFn,$limitToFn,$spIds);
                    $spqArry = $this->getspq_v1($user_master_id, $limitFromFn, $limitToFn, $spIds, $type_id); //$this->getspq($user_master_id,$limitFromFn,$limitToFn,$spIds);
                    $grArry = $this->getgr($user_master_id, $limitFromFn, $limitToFn, $spIds);
                    $sessionpArry = $this->getsession($user_master_id, $limitFromFn, $limitToFn, $spIds);
                    //$sessionpArry = $this->getsession_v1($user_master_id,$limitFromFn,$limitToFn,$spIds,$type_id);//$this->getsession($user_master_id,$limitFromFn,$limitToFn,$spIds);
                    $vidArry = $this->getvid_v1($user_master_id, $limitFromFn, $limitToFn, $spIds, $type_id); //$this->getvid($user_master_id,$limitFromFn,$limitToFn,$spIds);
                    if ($env != 1) { // !IN
                        $finalArry = array_merge_recursive($sessionpArry, $compArry, $spqArry, $vidArry);
                    } else {
                        $finalArry = array_merge_recursive($sessionpArry, $compArry, $spqArry, $grArry, $vidArry);
                    }
                    // $finalArry = array_merge_recursive($sessionpArry,$compArry,$spqArry,$vidArry);
                    // $finalArry = array_merge_recursive($sessionpArry,$compArry,$spqArry,$grArry,$vidArry);
                    //$menuItems = array_slice( $finalArry, $limitFrom, $limitTo );
                    //print_r($menuItems);
                    return $finalArry;
                    break;
                case 'epub':
                    if ($limitFrom != '' and $limitTo != '') {
                        $limit = "limit " . $limitFrom . " , " . $limitTo;
                    } else {
                        $limit = "";
                    }
                    if ($spIds != '') {
                        $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $spIds . ")  and (cm.publication_date <= CURDATE() ) ) )";
                    }
                    if ($val == '' and $spIds == '') {
                        $searchQuery[] = " (cm.publication_date <= CURDATE())";
                    }
                    $searchQueryStr = implode("or", $searchQuery);
                    if (!empty($user_master_id)) {
                        // =============== env_id implementation ===================//
                        $env = get_user_env_id($user_master_id);
                        if ($env) {
                            if ($env != 2) {
                                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                            } else {
                                $envStatus = "AND cTenv.env =" . $env . "";
                            }
                        } else {
                            $envStatus = "";
                        }
                        $key_locked = get_user_package($user_master_id, 'epub');
                        if ($key_locked == '') {
                            return null;
                        }
                        // =============== env_id implementation ===================//
                        $sql = "SELECT
                                    cm.epub_id AS type_id,
                                    cm.epub_description AS description,
                                    cm.epub_title AS title,
                                    cm.epub_img,
                                    cm.epub_img_thumbnail,
                                    cm.epub_file,
                                    cm.author,
                                    cm.added_on,
                                    cm.publication_date AS publish_date,
                                    cln.client_name,
                                    cln.client_logo,
                                    cTenv.price,
                                    uTpyCont.status AS user_contnet_payment_status,
                                    cm.deeplink,
                                    cm.color,
                                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
                                    (SELECT COUNT(rt.rating) FROM knwlg_rating rt WHERE rt.post_id = cm.epub_id AND rt.post_type='epub') AS averageRating,
                                    rtmy.rating AS myrating,
                                    (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm WHERE kcm.type_id = cm.epub_id AND kcm.type = 'epub') AS count_comment,
                                    kv.status AS vault
                                FROM epub_master AS cm
                                JOIN epub_to_specialities AS cmTs ON cmTs.epub_id = cm.epub_id
                                JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                                JOIN client_master AS cln ON cln.client_master_id = cm.client_id
                                LEFT JOIN epub_to_sponsor AS cmTspon ON cmTspon.epub_id = cm.epub_id
                                LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                                LEFT JOIN knwlg_rating AS rtmy
                                    ON rtmy.post_id = cm.epub_id
                                    AND rtmy.post_type='epub'
                                    AND rtmy.rating != 0
                                    AND rtmy.user_master_id = " . $user_master_id . "
                                LEFT JOIN knwlg_vault AS kv
                                    ON kv.post_id = cm.epub_id
                                    AND kv.type_text='epub'
                                    AND kv.user_id = " . $user_master_id . "
                                LEFT JOIN knwlg_rating AS rt
                                    ON rt.post_id = cm.epub_id
                                    AND rt.post_type='epub'
                                LEFT JOIN content_to_env AS cTenv
                                    ON cTenv.type_id = cm.epub_id
                                    AND cTenv.type = 9
                                LEFT JOIN payment_user_to_content AS uTpyCont
                                    ON uTpyCont.type_id = cm.epub_id
                                    AND uTpyCont.type = 9
                                    AND uTpyCont.user_master_id = " . $user_master_id . "
                                WHERE
                                    cm.status = 3
                                    AND cln.client_master_id = 1
                                    AND cm.privacy_status = 0
                                    " . $searchBYdate . "
                                    " . $envStatus . "
                                    AND " . $searchQueryStr . "" . $mobileview . "" . $featured . "
                                GROUP BY
                                    cm.epub_id,
                                    cm.epub_description,
                                    cm.epub_title,
                                    cm.epub_img,
                                    cm.epub_img_thumbnail,
                                    cm.epub_file,
                                    cm.author,
                                    cm.added_on,
                                    cm.publication_date,
                                    cln.client_name,
                                    cln.client_logo,
                                    cTenv.price,
                                    uTpyCont.status,
                                    cm.deeplink,
                                    cm.color,
                                    rtmy.rating,
                                    kv.status
                                ORDER BY cm.publication_date DESC " . $limit . "";
                        //echo $sql; exit;
                        //exit;
                        $query = $this->db->query($sql);
                        $result = $query->result();
                        $i = 1;
                        $vx = array();
                        foreach ($result as $val) {
                            $sponsorLogoArry = explode(",", $val->sponsor_logo);
                            if (count($sponsorLogoArry) > 0) {
                                foreach ($sponsorLogoArry as $valueSponor) {
                                    if ($valueSponor) {
                                        $sponsorLogomix[] = '' . $valueSponor;
                                    }
                                }
                            } else {
                                if ($val->sponsor_logo) {
                                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                                }
                            }
                            $sponsorLogo = implode(",", (array)$sponsorLogomix);
                            unset($sponsorLogomix);
                            unset($sponsorLogoArry);
                            $finalArry[] = array(
                                "slno" => $i,
                                "type_id" => $val->type_id,
                                "type" => 'epub',
                                "date" => date(' jS F y', strtotime($val->publish_date)),
                                "title" => html_entity_decode(strip_tags($val->title)),
                                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                                "description_short" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                                "author" => $val->author,
                                "epub_file" => $val->epub_file,
                                "image" => change_img_src($val->epub_img_thumbnail),
                                "color" => ($val->color != '') ? $val->color : '#918c91',
                                //============ integrated for subscription ============//
                                "is_locked" => $key_locked,
                                "price" => $val->price,
                                "user_content_payment" => get_user_content_status($val->type_id, 9, $user_master_id),
                                //============ integrated for subscription ============//
                                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                                "client_name" => $val->client_name,
                                "client_logo" => change_img_src('' . $val->client_logo),
                                "sponsor_name" => $val->sponsor,
                                "sponsor_logo" => change_img_src($sponsorLogo),
                                "comment_count" => $val->count_comment,
                                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                                "myrating" => ($val->myrating != '') ? true : false,
                                "vault" => ($val->vault != '') ? $val->vault : 0,
                                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                            );
                            $i++;
                        }
                        return $finalArry;
                        break;
                    }
                    // no break
                case 'training':
                    //if (!empty($user_master_id)) {
                    $env = get_user_env_id($user_master_id);
                    $key_locked = get_user_package($user_master_id, 'training');
                    if ($key_locked == '') {
                        return null;
                    }
                    $this->db->select("tm.id,
                        tm.title,
                        tm.description,
                        tm.preview_image,
                        tm.client_id,
                        tm.channel_id,
                        tm.template_id,
                        tm.color,
                        tm.display_in_dashboard,
                        tm.featured_video,
                        tm.deeplink,
                        tm.in_deeplink,
                        tm.gl_deeplink,
                        tm.start_like,
                        tm.start_date,
                        tm.url,
                        tm.max_participants,
                        tm.added_on,
                        tm.added_by,
                        tm.modified_on,
                        tm.modified_by,
                        tm.status,
                        tm.cert_template_id,
                        tm.duration,
                        tm.privacy_status,
                        
                        tm.published_date,
                        tm.env,
                        tm.is_share,
                        tm.is_like,
                        tm.is_comment,
                        tm.enable_maxparticipants,
                        tm.allow_postStart,
                        cTenv.price,
                        uTpyCont.status as user_contnet_payment_status,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id)) AS master_spec_id,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                        MAX(ms.rank) AS maxrank,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                        GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) AS session_doctor_id,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
                        clintspon.client_name,
                        clintspon.client_logo,
                        (SELECT COUNT(user_master_id) FROM `payment_user_to_content` WHERE `type_id` = tm.id) AS `active_users`,
                        COUNT(rt.rating) AS averageRating,
                        COUNT(DISTINCT training_module.id) AS count_module, kv.status as vault");
                    $this->db->from('training_master as tm');
                    $this->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
                    $this->db->join('client_master as clintspon', 'ts.sponsor_id = clintspon.client_master_id', 'left');
                    $this->db->join('training_to_speciality tts', 'tts.training_id = tm.id', 'left');
                    $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
                    $this->db->join('content_to_env as cTenv', 'cTenv.type_id = tm.id and cTenv.type = 4', 'left');
                    $this->db->join('payment_user_to_content as uTpyCont', 'uTpyCont.type_id = tm.id and  uTpyCont.type = 4 and uTpyCont.user_master_id = ' . $user_master_id, 'left');
                    $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
                    $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id AND rt.post_type='training'", 'left');
                    $this->db->join('knwlg_vault as kv', "kv.post_id = tm.id and  kv.type_text='course' and  kv.user_id = " . $user_master_id . "", 'LEFT');
                    $this->db->join('training_module', 'training_module.training_id = tm.id AND training_module.status = 3', 'left');
                    // $this->db->join('training_module_content', 'training_module_content.training_id = tm.id AND training_module_content.status = 3', 'left');
                    if ($spIds != '') {
                        $this->db->where_in('tts.specialities_id', "(" . $spids . ")");
                    }
                    $this->db->where('tm.status', 3);
                    $this->db->where('date(tm.published_date) <=', date('Y-m-d'));
                    $this->db->where('tm.privacy_status', 0);
                    $this->db->where('tm.display_in_dashboard', 1);
                    if ($env != 2) {
                        $this->db->where_in('cTenv.env', array(2, $env));
                    } else {
                        $this->db->where_in('cTenv.env', array($env));
                    }
                    $this->db->group_by([
                        'tm.id',
                        'tm.title',
                        'tm.description',
                        'tm.preview_image',
                        'tm.client_id',
                        'tm.channel_id',
                        'tm.template_id',
                        'tm.color',
                        'tm.display_in_dashboard',
                        'tm.featured_video',
                        'tm.deeplink',
                        'tm.in_deeplink',
                        'tm.gl_deeplink',
                        'tm.start_like',
                        'tm.start_date',
                        'tm.url',
                        'tm.max_participants',
                        'tm.added_on',
                        'tm.added_by',
                        'tm.modified_on',
                        'tm.modified_by',
                        'tm.status',
                        'tm.cert_template_id',
                        'tm.duration',
                        'tm.privacy_status',
                        'tm.img_credits',
                        'tm.published_date',
                        'tm.env',
                        'tm.is_share',
                        'tm.is_like',
                        'tm.is_comment',
                        'tm.enable_maxparticipants',
                        'tm.allow_postStart',
                        'cTenv.price',
                        'uTpyCont.status',
                        'clintspon.client_name',
                        'clintspon.client_logo',
                        'kv.status'
                    ]);
                    $this->db->order_by('tm.published_date', 'desc');
                    if ($limitFrom != '' and $limitTo != '') {
                        $this->db->limit($limitTo, $limitFrom);
                    }
                    $query = $this->db->get();
                    //$this->db->save_queries = TRUE;
                    // $str = $this->db->last_query();
                    // echo $str; exit;
                    if (($query) && ($query->num_rows())) {
                        $res = $query->result();
                    } else {
                        $res = array();
                    }
                    $i = 1;
                    foreach ($res as $key => $val) {
                        $sponsorLogoArry = explode(",", $val->sponsor_logo);
                        if (count($sponsorLogoArry) > 0) {
                            foreach ($sponsorLogoArry as $valueSponor) {
                                if ($valueSponor) {
                                    $sponsorLogomix[] =  change_img_src($valueSponor);
                                }
                            }
                        } else {
                            if ($val->sponsor_logo) {
                                $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                            }
                        }
                        $sponsorLogo = implode(",", (array)$sponsorLogomix);
                        unset($sponsorLogomix);
                        unset($sponsorLogoArry);
                        $currentdatetime = date('Y-m-d H:i:s');
                        if (isset($val->start_date) && $currentdatetime < $val->start_date && $val->max_participants > 0) {
                            if ($val->max_participants > $val->active_users) {
                                $is_registrable = true;
                            } else {
                                $is_registrable = false;
                            }
                        } elseif (isset($val->start_date) && $currentdatetime > $val->start_date) {
                            $is_registrable = false;
                        } elseif (!isset($val->start_date) && $val->max_participants == 0) {
                            $is_registrable = true;
                        } elseif (!isset($val->start_date) && $val->max_participants > 0) {
                            $is_registrable = false;
                        } else {
                            $is_registrable = true;
                        }
                        $temp = array(
                            "slno" => $i,
                            "id" => $val->id,
                            "url" => $val->url,
                            "vault" => ($val->vault != '') ? $val->vault : 0,
                            "is_share" => $val->is_share,
                            "title" => html_entity_decode(strip_tags($val->title)),
                            "image" => change_img_src($val->preview_image),
                            "featured_video" => $val->featured_video,
                            "max_participants" => $val->max_participants,
                            "start_date" => $val->start_date,
                            "active_users" => $val->active_users,
                            "is_registrable" => $is_registrable,
                            "color" => ($val->color != '') ? $val->color : '#eb34e5',
                            "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                            "module_Count" => $val->count_module, //$this->count_module($val->id),
                            "live_session_status" => $this->livestatus($val->id),
                            "specialities" => $this->explode_speciality_string($val->specialities_ids_and_names, 0), //$this->specialityname($val->master_spec_id), #($val->specialities_name != '') ? $val->specialities_name : '',
                            "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                            "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                            "client_name" => $val->client_name,
                            "client_logo" => change_img_src($val->client_logo),
                            "sponsor_name" => $val->sponsor,
                            "sponsor_logo" => $sponsorLogo,
                            "duration" => $val->duration,
                            //============ integrated for subscription ============//
                            "is_locked" => $key_locked,
                            "price" => $val->price,
                            "user_content_payment" => get_user_content_status($val->id, 4, $user_master_id),
                            //============ integrated for subscription ============//
                            "is_completed" => $completestatus, #$this->complete_status($val->id,$user_master_id),
                            "is_certificate" => ($val->cert_template_id != '') ? true : false,
                            "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                            "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                        );
                        $vx[] = $temp;
                        $i++;
                    }
                    // return $temp;
                    return $vx;
                    break;
            }
            return $vx;
        }
    }
    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function list(
        $type,
        $user_master_id,
        $client_ids,
        $limitTo,
        $limitFrom,
        $spIds,
        $category,
        $subtype,
        $is_video
    ) {
        $type = ($type == "video") ? $type = "archived_video" : $type;
        $env = get_user_env($user_master_id);
        $cacheName = "discoverlist" . $type . $subtype . "_" . $env . "_" . $client_ids . $spIds . "_" . $is_video ."_". $category . "_" . $limitFrom . "_" . $limitTo;
        if ($this->myredis->exists($cacheName)) {
            return $this->myredis->get($cacheName);
        } else {
            // $sp_ids = array();
            // if ($spIds != '') {
            //     $sp_ids = explode(',', $spIds);
            // }
            // $sqlInt = "select
            //     specialities_id
            //     from
            //     user_to_interest
            //     where
            //     user_master_id = " . $user_master_id . "";
            // $queryInt = $this->db->query($sqlInt);
            // $resultInt = $queryInt->result_array();
            // $specialities = array();
            // foreach ($resultInt as $val) {
            //     $specialities[] = $val['specialities_id'];
            // }
            if ($type != 'channel') {
                switch ($category) {
                    case 1:
                        $records = $this->gettypeids($type, $spIds, 'most_read', 'trending_data', $env);
                        break;
                    case 3:
                        $records = $this->gettypeids($type, $spIds, 'trending_topic', 'trending_data', $env);
                        break;
                    case 4:
                        $records = $this->gettypeids($type, $spIds, 'most_read', 'mostliked_data', $env);
                        break;
                    case 5:
                        $records = "";
                        break;
                    case 7:
                        // using in web only, for gettting mixing of non featured and featured  data
                        $records = "";
                        break;
                    default:
                        $record_mostread_trending = $this->gettypeids($type, $spIds, 'most_read', 'trending_data', $env);
                        //print_r($record_mostread_trending); exit;
                        $records_trendingtopic = $this->gettypeids($type, $spIds, 'trending_topic', 'trending_data', $env);
                        //print_r($records_trendingtopic); exit;
                        $records_mostread_liked = $this->gettypeids($type, $spIds, 'most_read', 'mostliked_data', $env);
                        //print_r($records_mostread_liked); exit;
                        $records = '';
                        if ($record_mostread_trending != '') {
                            $records = $record_mostread_trending;
                        }
                        if (($records_trendingtopic != '') && ($records != '')) {
                            $records = $records . "," . $records_trendingtopic;
                        }
                        if (($records_mostread_liked != '') && ($records != '')) {
                            $records = $records . "," . $records_mostread_liked;
                        }
                        //print_r($records); exit;
                        break;
                }
            }
            //print_r($records); exit;
            if ($env) {
                if ($env != 'GL') {
                    $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
                } else {
                    $envStatus = "AND cm.env ='" . $env . "'";
                }
            } else {
                $envStatus = "";
            }
            switch ($type) {
                case "comp":
                    $this->load->model('Knwlg_model');
                    switch ($subtype) {
                        case "popular":
                            $compArry = $this->Knwlg_model->all_compendium($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, "", $spIds, $is_video, $from_date, $to_date);
                            break;
                        case "most_commented":
                            $compArry = $this->Knwlg_model->topcomment($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, $val, $is_video, $spIds);
                            break;
                        case "top_rated":
                            $compArry = $this->Knwlg_model->toprated($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, "", $is_video, $spIds);
                            break;
                        case "most_read":
                            $mostReadData = $this->get_most_read_content($env, $spIds, "most_read");
                            $compArry = $this->getMedwiki($mostReadData, $user_master_id, $limitFrom, $limitTo);
                            break;
                        default:
                            $compArry = $this->complist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                    }
                    return $compArry;
                    break;
                case "survey":
                    $this->load->model('Survey_model');
                    switch ($subtype) {
                        case "poll":
                            $spqArry = $this->survey_model->list_data($user_master_id, "poll", $type, $user_master_id, $limitTo, $limitFrom, $spIds);
                            break;
                        case "quiz":
                            $spqArry = $this->survey_model->list_data($user_master_id, "quiz", $type, $user_master_id, $limitTo, $limitFrom, $spIds);
                            break;
                        case "survey":
                            $spqArry = $this->survey_model->list_data($user_master_id, "survey", $type, $user_master_id, $limitTo, $limitFrom, $spIds);
                            break;
                        case "completed":
                            $spqArry = $this->survey_model->user_survey_list($user_master_id, null, $limitFrom, $limitTo, $spIds);
                            break;
                        case "pending":
                            $spqArry = $this->survey_model->list_Incomp_data($user_master_id, null);
                            break;
                        default:
                            $spqArry = $this->spqlist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                            break;
                    }
                    return $spqArry;
                    break;
                    // case "gr":
                    //     $grArry = $this->grlist($envStatus,$category,$records,$limitTo,$limitFrom,$user_master_id,$spIds);
                    //     return $grArry;
                    //     break;
                case "epub":
                    $epubArry = $this->epublist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                    return $epubArry;
                    break;
                case "archived_video":
                    $this->load->model('Knwlg_model');
                    switch ($subtype) {
                        case "recomended":
                            $videoArry = $this->Knwlg_model->all_archiveVideo($user_master_id, $client_ids, $group_ids, $spIds, $limitFrom, $limitTo, $type_id, $type);
                            break;
                        case "watched_videos":
                            $videoArry = $this->Knwlg_model->gethistoryvideoSpeciality($user_master_id, $limitTo, $limitFrom, $spIds);
                            break;
                        default:
                            $videoArry = $this->videolist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                    }
                    return $videoArry;
                    break;
                case "session":
                    $this->load->model('Knwlgmastersessionnew_model');
                    switch ($subtype) {
                        case "upcoming":
                            $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
                            $sessionlist = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details_slider($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, $client_ids, $user_group_ids, null, $spIds);
                            break;
                        case "reserved":
                            $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
                            $sessionlist = $this->Knwlgmastersessionnew_model->all_bookedmastersession_details($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, null, $spIds);
                            break;
                        case "doctors":
                            $sessionlist = $this->Knwlgmastersessionnew_model->sessionsDoctorsListByClientID($user_master_id, $client_ids, $doc_ids_optional, $spIds, $filter_sp_ids, $isRequestBlank, $limitFrom, $limitTo);
                            break;
                        default:
                            $sessionlist = $this->sessionlist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                    }
                    return $sessionlist;
                    break;
                case "training":
                    switch ($subtype) {
                        case "incomplete":
                            $traininglist = $this->training_model->list('incompleted', $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        case "complete":
                            $traininglist = $this->training_model->list('completed', $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        case "my_paid_course":
                            $traininglist = $this->training_model->list_paid(null, $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        case "popular":
                            $traininglist = $this->training_model->list(null, $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        default:
                            $traininglist = $this->traininglist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $sp_ids);
                            break;
                    }
                    return $traininglist;
                    break;
                case "community":
                    $this->load->model('Channelnew_model');
                    switch ($subtype) {
                        case "popular_community":
                            $communitylist = $this->Channelnew_model->featured_channel($user_master_id, $client_ids, $limitTo, $limitFrom);
                            break;
                        case "most_followed":
                            $communitylist = $this->Channelnew_model->mostfollowedchannel($user_master_id, $client_ids, $limitTo, $limitFrom);
                            break;
                        case "followed_channels":
                            $communitylist = $this->Channelnew_model->all_userChannel($user_master_id, $client_ids, $limitTo, $limitFrom);
                            break;
                        default:
                            //$communitylist = array();
                            $communitylist = $this->channellist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);

                            break;
                    }
                    return $communitylist;
                    break;
                case "channel":
                    $channellist = $this->channellist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                    return $channellist;
                    break;
                default:
                    $finalvary = array();
                    $recordsmedwiki = '';
                    if (!empty($records['medwiki'])) {
                        $recordsmedwiki = implode(',', $records['medwiki']);
                    }
                    $compArry = $this->complist($envStatus, $category, $recordsmedwiki, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($compArry)) && (count($compArry) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $compArry);
                    }
                    $recordsspq = '';
                    if (!empty($records['spq'])) {
                        $recordsspq = implode(',', $records['spq']);
                    }
                    $spqArry = $this->spqlist($envStatus, $category, $recordsspq, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($spqArry)) && (count($spqArry) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $spqArry);
                    }
                    $recordsgr = '';
                    if (!empty($records['gr'])) {
                        $recordsgr = implode(',', $records['gr']);
                    }
                    // $grArry = $this->grlist($envStatus,$category,$recordsgr,$limitTo,$limitFrom,$user_master_id,$spIds);
                    // if (count($grArry) > 0) {
                    //     $finalvary = array_merge_recursive($finalvary, $grArry);
                    // }
                    $recordsepub = '';
                    if (!empty($records['epub'])) {
                        $recordsepub = implode(',', $records['epub']);
                    }
                    $epubArry = $this->epublist($envStatus, $category, $recordsepub, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($epubArry)) && (count($epubArry) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $epubArry);
                    }
                    //print_r($epubArry); exit;
                    $recordsvideo = '';
                    if (!empty($records['archived_video'])) {
                        $recordsvideo = implode(',', $records['archived_video']);
                    }
                    $videoArry = $this->videolist($envStatus, $category, $recordsvideo, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($videoArry)) && (count($videoArry) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $videoArry);
                    }
                    //print_r($videoArry); exit;
                    $recordssession = '';
                    if (!empty($records['session'])) {
                        $recordssession = implode(',', $records['session']);
                    }
                    $sessionlist = $this->sessionlist($envStatus, $category, $recordssession, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($sessionlist)) && (count($sessionlist) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $sessionlist);
                    }
                    //print_r($sessionlist); exit;
                    $recordstraining = '';
                    if (!empty($records['training'])) {
                        $recordstraining = implode(',', $records['training']);
                    }
                    $traininglist = array();
                    $traininglist = $this->traininglist($envStatus, $category, $recordstraining, $limitTo, $limitFrom, $user_master_id, $sp_ids);
                    if ((isset($traininglist)) && (count($traininglist) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $traininglist);
                    }
                    // print_R($traininglist); exit;
                    $recordschannel = '';
                    if (!empty($records['channel'])) {
                        $recordschannel = implode(',', $records['channel']);
                    }
                    $channellist = $this->channellist($envStatus, $category, $recordschannel, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                    //print_R($traininglist); exit;
                    if ((isset($channellist)) && (count($channellist) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $channellist);
                    }
                    return $finalvary;
                    break;
            }
        }
    }
    public function webList(
        $type,
        $user_master_id,
        $client_ids,
        $limitTo,
        $limitFrom,
        $spIds,
        $category,
        $subtype,
        $is_video
    ) {

        //echo 'user_master_id-webList:   '.$user_master_id;

        $type = ($type == "video") ? $type = "archived_video" : $type;
        $env = get_user_env($user_master_id);
        $cacheName = "web_discoverlist" . $type . $subtype ."_" . $env . "_" . $client_ids . $spIds . "_" . $is_video ."_". $category . "_" . $limitFrom . "_" . $limitTo;
        if ($this->myredis->exists($cacheName)) {
            return $this->myredis->get($cacheName);
        } else {

            if ($type != 'channel') {
                // switch ($category) {
                //     case 1:
                //         $records = $this->gettypeids($type, $spIds, 'most_read', 'trending_data', $env);
                //         break;
                //     case 3:
                //         $records = $this->gettypeids($type, $spIds, 'trending_topic', 'trending_data', $env);
                //         break;
                //     case 4:
                //         $records = $this->gettypeids($type, $spIds, 'most_read', 'mostliked_data', $env);
                //         break;
                //     case 5:
                //         $records = "";
                //         break;
                //     case 7:
                //         // using in web only, for gettting mixing of non featured and featured  data
                //         $records = "";
                //         break;
                //     default:
                //         $record_mostread_trending = $this->gettypeids($type, $spIds, 'most_read', 'trending_data', $env);
                //         //print_r($record_mostread_trending); exit;
                //         $records_trendingtopic = $this->gettypeids($type, $spIds, 'trending_topic', 'trending_data', $env);
                //         //print_r($records_trendingtopic); exit;
                //         $records_mostread_liked = $this->gettypeids($type, $spIds, 'most_read', 'mostliked_data', $env);
                //         //print_r($records_mostread_liked); exit;
                //         $records = '';
                //         if ($record_mostread_trending != '') {
                //             $records = $record_mostread_trending;
                //         }
                //         if (($records_trendingtopic != '') && ($records != '')) {
                //             $records = $records . "," . $records_trendingtopic;
                //         }
                //         if (($records_mostread_liked != '') && ($records != '')) {
                //             $records = $records . "," . $records_mostread_liked;
                //         }
                //         //print_r($records); exit;
                //         break;
                // }
            }
            if ($env) {
                if ($env != 'GL') {
                    $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
                } else {
                    $envStatus = "AND cm.env ='" . $env . "'";
                }
            } else {
                $envStatus = "";
            }
            switch ($type) {
                case "comp":


                    switch ($subtype) {
                        case "popular":
                            $category = 28;
                            // $compArry = $this->Knwlg_model->all_compendium($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, "", $spIds, $is_video, $from_date, $to_date);
                            $compArry = $this->complist($env, $envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        case "most_commented":
                            $category = 27;
                            // $compArry = $this->Knwlg_model->topcomment($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, $val, $is_video, $spIds);
                            $compArry = $this->complist($env, $envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        case "top_rated":
                            $category = 24;
                            // $compArry = $this->Knwlg_model->toprated($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, "", $is_video, $spIds);
                            $compArry = $this->complist($env, $envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        case "most_read":
                            $category = 29;
                            // $mostReadData = $this->get_most_read_content($env, $spIds, "most_read");
                            // $compArry = $this->getMedwiki($mostReadData, $user_master_id, $limitFrom, $limitTo);
                            $compArry = $this->complist($env, $envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);

                            break;
                        case "all":
                            $category = 25;
                            // $compArry = $this->Knwlg_model->all_compendium($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, '', $spIds, $type, $from_date, $to_date);
                            $compArry = $this->complist($env, $envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        case "recommended":
                            $category = 26;
                            // $compArry = $this->Knwlg_model->all_compendium($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, '', $spIds, 'featured', $from_date, $to_date);
                            $compArry = $this->complist($env, $envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        default:
                            $compArry = $this->complist($env, $envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                    }
                    return $compArry;
                    break;
                case "survey":
                    //$this->load->model('Survey_model');
                    switch ($subtype) {
                        case "poll":
                            $spqArry = $this->survey_model->list_data($user_master_id, "poll", $type, $user_master_id, $limitTo, $limitFrom, $spIds);
                            break;
                        case "quiz":
                            $spqArry = $this->survey_model->list_data($user_master_id, "quiz", $type, $user_master_id, $limitTo, $limitFrom, $spIds);
                            break;
                        case "survey":
                            $spqArry = $this->survey_model->list_data($user_master_id, "survey", $type, $user_master_id, $limitTo, $limitFrom, $spIds);
                            break;
                        case "completed":
                            $spqArry = $this->survey_model->user_survey_list($user_master_id, null, $limitFrom, $limitTo, $spIds);
                            break;
                        case "pending":
                            $spqArry = $this->survey_model->list_Incomp_data($user_master_id, null);
                            break;
                        case "all":
                            $spqArry = $this->survey_model->list_data($user_master_id, '', '', $user_master_id, $limitTo, $limitFrom, $spIds);
                            break;
                        case "recommended":
                            $spqArry = $this->survey_model->list_data($user_master_id, '', 'featured', $user_master_id, $limitTo, $limitFrom, $spIds);
                            break;
                        default:
                            $spqArry = $this->spqlist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                            break;
                    }
                    return $spqArry;
                    break;
                    // case "gr":
                    //     $grArry = $this->grlist($envStatus,$category,$records,$limitTo,$limitFrom,$user_master_id,$spIds);
                    //     return $grArry;
                    //     break;
                case "epub":
                    $this->load->model('Epub_model');
                    switch ($subtype) {
                        case "recent":
                            $category = 45;
                            $epubArry = $this->epublist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        case "recommended":
                            $category = 46;
                            // $epubArry = $this->Epub_model->all_epub($user_master_id, $client_ids, $limitFrom, $limitTo, "", $spIds, '', $from_date, $to_date, '', $convert, 1);
                            $epubArry = $this->epublist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        case "all":
                            $category = 47;
                            // $epubArry = $this->Epub_model->all_epub($user_master_id, $client_ids, $limitFrom, $limitTo, "", $spIds, '', $from_date, $to_date, '', $convert, 0);
                            $epubArry = $this->epublist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        default:
                            $epubArry = $this->epublist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                    }
                    return $epubArry;
                    break;
                case "archived_video":
                    //$this->load->model('Knwlg_model');
                    switch ($subtype) {
                        case "recomended":
                            $category = 35;
                            // $videoArry = $this->Knwlg_model->all_archiveVideo($user_master_id, $client_ids, $group_ids, $spIds, $limitFrom, $limitTo, $type_id, $type);
                            $videoArry = $this->videolist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        case "watched_videos":
                            $category = 37;
                            // $videoArry = $this->Knwlg_model->gethistoryvideoSpeciality($user_master_id, $limitTo, $limitFrom, $spIds);
                            $videoArry = $this->videolist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        case "recommended":
                            $category = 35;
                            // $videoArry = $this->Knwlg_model->all_archiveVideo($user_master_id, $client_ids, $group_ids, $spIds, $limitFrom, $limitTo, $type_id, "featured");
                            $videoArry = $this->videolist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        case "all":
                            $category = 36;
                            // $videoArry = $this->Knwlg_model->all_archiveVideo($user_master_id, $client_ids, $group_ids, $spIds, $limitFrom, $limitTo, $type_id, "");
                            $videoArry = $this->videolist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                        default:
                            $videoArry = $this->videolist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds);
                            break;
                    }
                    return $videoArry;
                    break;
                case "session":
                    switch ($subtype) {
                        case "upcoming":
                            $category = 30;
                            // $user_booked_session_id = "" ;
                            // $sessionlist = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, $client_ids, $user_group_ids, null, $spIds);
                            $sessionlist = $this->sessionlist($envStatus, $category, $records, $limitTo, $limitFrom, $spIds, $user_booked_session_id, $user_master_id, $client_ids, $user_group_ids);

                            break;
                        case "reserved":
                            $category = 31;
                            // $user_booked_session_id = "" ;
                            // // echo 'user_booked_session_id ----- '.$user_booked_session_id;
                            // // print_r($user_booked_session_id);
                            // // exit;
                            // $sessionlist = $this->Knwlgmastersessionnew_model->all_bookedmastersession_details($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, null, $spIds);
                            $sessionlist = $this->sessionlist($envStatus, $category, $records, $limitTo, $limitFrom, $spIds, $user_booked_session_id, $user_master_id, $client_ids, $user_group_ids);
                            break;
                        case "recommended":
                            $category = 33;
                            // $user_booked_session_id =  "";
                            // $sessionlist = $this->Knwlgmastersessionnew_model->all_featuredmastersession_details($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, $client_ids, $user_group_ids, '', $spIds);
                            // echo $user_master_id;
                            // exit;
                            $sessionlist = $this->sessionlist($envStatus, $category, $records, $limitTo, $limitFrom, $spIds, $user_booked_session_id, $user_master_id, $client_ids, $user_group_ids);
                            break;
                        case "all":
                            $category = 32;
                            // $user_booked_session_id = "";
                            // $sessionlist = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details_slider($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, $client_ids, $user_group_ids, '', $spIds);
                            $sessionlist = $this->sessionlist($envStatus, $category, $records, $limitTo, $limitFrom, $spIds, $user_booked_session_id, $user_master_id, $client_ids, $user_group_ids);
                            break;
                        case "doctors":
                            $category = 34;
                            // $sessionlist = $this->Knwlgmastersessionnew_model->sessionsDoctorsListByClientID($user_master_id, $client_ids, $doc_ids_optional, $spIds, $filter_sp_ids, $isRequestBlank, $limitFrom, $limitTo);
                            $sessionlist = $this->sessionlist($envStatus, $category, $records, $limitTo, $limitFrom, $spIds, $user_booked_session_id, $user_master_id, $client_ids, $user_group_ids);
                            break;
                        default:
                            $sessionlist = $this->sessionlist($envStatus, $category, $records, $limitTo, $limitFrom, $spIds, $user_booked_session_id, $user_master_id, $client_ids, $user_group_ids);
                            break;
                    }
                    return $sessionlist;
                    break;
                case "training":
                    switch ($subtype) {
                        case "incomplete":
                            $traininglist = $this->training_model->list('incompleted', $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        case "complete":
                            $traininglist = $this->training_model->list('completed', $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        case "my_paid_course":
                            $traininglist = $this->training_model->list_paid(null, $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        case "popular":
                            $traininglist = $this->training_model->list(null, $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        case "recommended":
                            $traininglist = $this->training_model->list("featured", $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        case "all":
                            $traininglist = $this->training_model->list(null, $user_master_id, $limitTo, $limitFrom, $sp_ids);
                            break;
                        default:
                            $traininglist = $this->traininglist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $sp_ids);
                            break;
                    }
                    return $traininglist;
                    break;
                case "community":
                    $this->load->model('Channelnew_model');
                    switch ($subtype) {
                        case "popular_community":
                            // $communitylist = $this->Channelnew_model->featured_channel($user_master_id, $client_ids, $limitTo, $limitFrom);
                            $category = null;
                            $communitylist = $this->channellistWeb($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                            break;
                        case "most_followed":
                            // $communitylist = $this->Channelnew_model->mostfollowedchannel($user_master_id, $client_ids, $limitTo, $limitFrom);
                            $category = null;
                            $communitylist = $this->channellistWeb($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                            break;
                        case "followed_channels":
                            // $communitylist = $this->Channelnew_model->all_userChannel($user_master_id, $client_ids, $limitTo, $limitFrom);
                            $category = null;
                            $communitylist = $this->channellistWeb($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                            break;
                        case "recommended":
                            // $communitylist = $this->Channelnew_model->featured_channel($user_master_id, $client_ids, $limitTo, $limitFrom);
                            $category = null;
                            $communitylist = $this->channellistWeb($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                            break;
                        case "all":
                            // $communitylist = $this->Channelnew_model->all_channelSuggestion($user_master_id, $client_ids, $limitTo, $limitFrom, $type_id);
                            $category = null;
                            $communitylist = $this->channellistWeb($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                            break;
                        default:
                            return $this->channellistWeb($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                            // $channellist;
                            break;
                    }
                    return $communitylist;
                    break;
                case "channel":
                    return $this->channellist($envStatus, $category, $records, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);

                    break;
                default:
                    $finalvary = array();
                    $recordsmedwiki = '';
                    if (!empty($records['medwiki'])) {
                        $recordsmedwiki = implode(',', $records['medwiki']);
                    }
                    $compArry = $this->complist($envStatus, $category, $recordsmedwiki, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($compArry)) && (count($compArry) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $compArry);
                    }
                    $recordsspq = '';
                    if (!empty($records['spq'])) {
                        $recordsspq = implode(',', $records['spq']);
                    }
                    $spqArry = $this->spqlist($envStatus, $category, $recordsspq, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($spqArry)) && (count($spqArry) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $spqArry);
                    }
                    $recordsgr = '';
                    if (!empty($records['gr'])) {
                        $recordsgr = implode(',', $records['gr']);
                    }
                    // $grArry = $this->grlist($envStatus,$category,$recordsgr,$limitTo,$limitFrom,$user_master_id,$spIds);
                    // if (count($grArry) > 0) {
                    //     $finalvary = array_merge_recursive($finalvary, $grArry);
                    // }
                    $recordsepub = '';
                    if (!empty($records['epub'])) {
                        $recordsepub = implode(',', $records['epub']);
                    }
                    $epubArry = $this->epublist($envStatus, $category, $recordsepub, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($epubArry)) && (count($epubArry) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $epubArry);
                    }
                    //print_r($epubArry); exit;
                    $recordsvideo = '';
                    if (!empty($records['archived_video'])) {
                        $recordsvideo = implode(',', $records['archived_video']);
                    }
                    $videoArry = $this->videolist($envStatus, $category, $recordsvideo, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($videoArry)) && (count($videoArry) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $videoArry);
                    }
                    //print_r($videoArry); exit;
                    $recordssession = '';
                    if (!empty($records['session'])) {
                        $recordssession = implode(',', $records['session']);
                    }
                    $sessionlist = $this->sessionlist($envStatus, $category, $recordssession, $limitTo, $limitFrom, $user_master_id, $spIds);
                    if ((isset($sessionlist)) && (count($sessionlist) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $sessionlist);
                    }
                    //print_r($sessionlist); exit;
                    $recordstraining = '';
                    if (!empty($records['training'])) {
                        $recordstraining = implode(',', $records['training']);
                    }
                    $traininglist = array();
                    $traininglist = $this->traininglist($envStatus, $category, $recordstraining, $limitTo, $limitFrom, $user_master_id, $sp_ids);
                    if ((isset($traininglist)) && (count($traininglist) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $traininglist);
                    }
                    // print_R($traininglist); exit;
                    $recordschannel = '';
                    if (!empty($records['channel'])) {
                        $recordschannel = implode(',', $records['channel']);
                    }
                    $channellist = $this->channellist($envStatus, $category, $recordschannel, $limitTo, $limitFrom, $user_master_id, $spIds, $subtype);
                    //print_R($traininglist); exit;
                    if ((isset($channellist)) && (count($channellist) > 0)) {
                        $finalvary = array_merge_recursive($finalvary, $channellist);
                    }
                    return $finalvary;
                    break;
            }
        }
    }
    public function get_most_read_content($env, $spc, $typ)
    {
        if (!empty($spc) && $spc != '') {
            $specClause = " AND trending_speciality IN({$spc})";
        }
        $sql = "SELECT
                    array_data
                FROM
                    trending_data
                WHERE
                    trending_country = '{$env}'
                    AND trending_type = '{$typ}'
                    {$specClause}
        ";
        $exe_query = $this->db->query($sql);
        return $exe_query->result();

    }
    public function getMedwiki($mostReadData, $user_master_id, $from, $to)
    {
        $this->load->library('service');
        $total_rows = $from + $to;
        foreach ($mostReadData as $key => $content) {
            foreach (json_decode($content->array_data) as $i => $j) {
                if ($i >= $this->payload['offset'] && $i <  $total_rows) {
                    $mod_content_type = $this->set_content_type3($j);
                    // $sigle_data = $this->service->content_by_id_and_content_type($mod_content_type, $j->content_id, $user_master_id, null, $j->user_count);
                    $sigle_data = productdetail($mod_content_type, $j->content_id, $user_master_id, '');
                    if (!empty($sigle_data)) {
                        $my_data_array[] = $sigle_data;
                    }
                }
            }
        }
        return $my_data_array;
    }
    private function set_content_type3($content)
    {
        //echo "<pre>";print_r($content); die;
        if (isset($content->content) && !empty($content)) {
            if ($content->content == 'medwiki') {
                $content_type = 'comp';
            } elseif ($content->content == 'archived_video') {
                $content_type = 'archivevideo';
            } else {
                $content_type = $content;
            }
        }
        //print_r($content_type); //exit;
        return $content_type;
    }
    public function gettypeids(
        $type,
        $spIds,
        $value,
        $table,
        $env
    ) {
        //$this->db->save_queries = TRUE;
        $this->db->select('array_data');
        $this->db->from($table);
        if ($env == 'IN') {
            $this->db->where_in('trending_country', ['IN', 'GL']);
        } else {
            # code...
            $this->db->where('trending_country', $env);
        }
        if ($spIds) {
            $sp_ids = explode(',', $spIds);
            $this->db->where_in('trending_speciality', $sp_ids);
        }
        if ($value) {
            $this->db->where('trending_type', $value);
        }
        $query = $this->db->get();
        //print_r($query); //exit;
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            foreach ($result as $key => $value) {
                $arraydata = json_decode($value->array_data);
                foreach ($arraydata as $k => $v) {
                    // print_r($v);
                    if (($v->content == 'medwiki') && ($type == 'comp')) {
                        $ids[] = $v->content_id;
                    }
                    if (($v->content == 'spq') && ($type == 'survey')) {
                        $ids[] = $v->content_id;
                    }
                    if (($v->content == 'gr') && ($type == 'gr')) {
                        $ids[] = $v->content_id;
                    }
                    if (($v->content == 'epub') && ($type == 'epub')) {
                        $ids[] = $v->content_id;
                    }
                    if (($v->content == 'archived_video') && ($type == 'archived_video')) {
                        $ids[] = $v->content_id;
                    }
                    if (($v->content == 'session') && ($type == 'session')) {
                        $ids[] = $v->content_id;
                    }
                    if (($v->content == 'training') && ($type == 'training')) {
                        $ids[] = $v->content_id;
                    }
                    if (($v->content == 'channel') && ($type == 'channel')) {
                        $ids[] = $v->content_id;
                    }
                    if ($type == 'all') {
                        $ids[$v->content][] = $v->content_id;
                    }
                }
            }
            if ($type != 'all') {
                if (!empty($ids)) {
                    $recordids = implode(',', $ids);
                }
            } else {
                // print_r($ids); exit;
                $recordids = $ids;
            }
        }
        // echo 'hello...'.$recordids;
        // exit;
        // print_r($recordids);
        // exit;
        return $recordids;
    }
    public function complist(
        $env,
        $envStatus,
        $category,
        $record,
        $limitTo,
        $limitFrom,
        $user_master_id,
        $spids
    ) {



        switch ($category) {
            case 24:
                $compArry = $this->Knwlg_model->toprated($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, "", $is_video, $spIds);
                break;
            case 25:
                $compArry = $this->Knwlg_model->all_compendium($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, '', $spIds, $type, $from_date, $to_date);
                break;
            case 26:
                $compArry = $this->Knwlg_model->all_compendium($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, '', $spIds, 'featured', $from_date, $to_date);
                break;
            case 27:
                $compArry = $this->Knwlg_model->topcomment($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, $val, $is_video, $spIds);
                break;
            case 28:
                $compArry = $this->Knwlg_model->all_compendium($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, "", $spIds, $is_video, $from_date, $to_date);
                break;
            case 29:
                $mostReadData = $this->get_most_read_content($env, $spIds, "most_read");
                $compArry = $this->getMedwiki($mostReadData, $user_master_id, $limitFrom, $limitTo);
                break;
            default:
                // $compArry = $this->Knwlg_model->all_compendium($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, '', $spIds, '', $from_date, $to_date);
                $vx = array();
                $key_locked = get_user_package($user_master_id, 'comp');
                $records = implode(",", array_unique(explode(",", $record)));
                $is_video = $this->input->get('is_video');
                if ($is_video) {
                    $is_videoSql = "AND cm.type = 'video'";
                }

                if ($category == 5) {
                    $categorySql = "AND cm.display_in_dashboard = 1";
                } else {
                    $categorySql = "";
                }
                if ($records) {
                    $recordsSql = "AND cm.comp_qa_id IN ($records)";
                } else {
                    $recordsSql = "";
                }

                if ($limitFrom != '' && $limitTo != '') {
                    $limitSql = "LIMIT $limitFrom, $limitTo";
                } else {
                    $limitSql = "LIMIT 0, 5";
                }


                // Using CTEs for better performance and to handle ONLY_FULL_GROUP_BY mode
                $sql = "WITH
                FilteredCompendiums AS (
                    SELECT
                        cm.comp_qa_id
                    FROM
                        knwlg_compendium_V1 as cm
                    WHERE
                        cm.status = 3
                        AND cm.privacy_status = 0
                        AND cm.publication_date <= CURDATE()
                        {$categorySql}
                        {$recordsSql}
                        {$is_videoSql}
                        {$envStatus}
                        order by cm.publication_date DESC
                        {$limitSql}
                ),
                SpecialitiesData AS (
                    SELECT
                        cmTs.comp_qa_id,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names,
                        MAX(ms.rank) as maxrank
                    FROM
                        FilteredCompendiums fc
                    JOIN
                        compendium_to_specialities as cmTs ON cmTs.comp_qa_id = fc.comp_qa_id
                    JOIN
                        master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                    " . (($spids) && ($spids != 0) ? "WHERE cmTs.specialities_id IN (" . $spids . ")" : "") . "
                    GROUP BY
                        cmTs.comp_qa_id
                ),
                SponsorData AS (
                    SELECT
                        cmTspon.comp_qa_id,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                    FROM
                        FilteredCompendiums fc
                    JOIN
                        compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = fc.comp_qa_id
                    JOIN
                        client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                
                    GROUP BY
                        cmTspon.comp_qa_id
                ),
                RatingData AS (
                    SELECT
                        rt.post_id,
                        COUNT(rt.rating) as averageRating
                    FROM
                        FilteredCompendiums fc
                    JOIN
                        knwlg_rating rt ON rt.post_id = fc.comp_qa_id AND rt.post_type = 'comp'
                    GROUP BY
                        rt.post_id
                )
                SELECT
                    cm.comp_qa_id as type_id,
                    cm.comp_qa_question,
                    cm.comp_qa_answer,
                    cm.comp_qa_answer_raw as description,
                    cm.comp_qa_question_raw as title,
                    cm.comp_qa_file_img,
                    cm.comp_qa_file_img_thumbnail,
                    cm.added_on,
                    cm.is_share,
                    cm.publication_date as publish_date,
                    cln.client_name,
                    cln.client_logo,
                    cm.start_like,
                    cm.color,
                    cm.type,
                    cm.vendor,
                    cm.src,
                    cm.deeplink,
                    cTenv.price,
                    uTpyCont.status as user_contnet_payment_status,
                    sd.specialities_name,
                    sd.specialities_ids_and_names,
                    sd.maxrank,
                    spd.sponsor,
                    spd.sponsor_logo,
                    cm.comp_qa_speciality_id,
                    COALESCE(rd.averageRating, 0) as averageRating,
                    rtmy.rating as myrating,
                    kv.status as vault
                FROM
                    FilteredCompendiums fc
                JOIN
                    knwlg_compendium_V1 as cm ON cm.comp_qa_id = fc.comp_qa_id
                JOIN
                    client_master as cln ON cln.client_master_id = cm.client_id
                LEFT JOIN
                    SpecialitiesData sd ON sd.comp_qa_id = cm.comp_qa_id
                LEFT JOIN
                    SponsorData spd ON spd.comp_qa_id = cm.comp_qa_id
                LEFT JOIN
                    RatingData rd ON rd.post_id = cm.comp_qa_id
                LEFT JOIN
                    content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id AND cTenv.type = 1
                LEFT JOIN
                    payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.comp_qa_id AND uTpyCont.type = 1 AND uTpyCont.user_master_id = {$user_master_id}
                LEFT JOIN
                    knwlg_rating as rtmy ON rtmy.post_id = cm.comp_qa_id AND rtmy.post_type = 'comp' AND rtmy.rating != 0 AND rtmy.user_master_id = {$user_master_id}
                LEFT JOIN
                    knwlg_vault as kv ON kv.post_id = cm.comp_qa_id AND kv.type_text = 'comp' AND kv.user_id = {$user_master_id}
                ORDER BY
                    cm.publication_date DESC
                ";
                $query = $this->db->query($sql);
                // echo $sql;
                // exit;
                $i = 1;
                $vx = array();
                if (($query) && ($query->num_rows() > 0)) {
                    $result = $query->result();
                    foreach ($result as $val) {
                        $sponsorLogoArry = explode(",", $val->sponsor_logo);
                        $allsponsor = array();
                        $sponsorname = explode(",", $val->sponsor);
                        $sp = 0;
                        if (count($sponsorLogoArry) > 0) {
                            foreach ($sponsorLogoArry as $valueSponor) {
                                if ($valueSponor) {
                                    $sponsorLogomix[] = $valueSponor;
                                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                                    $sp++;
                                }
                            }
                        } else {
                            if ($val->sponsor_logo) {
                                $sponsorLogomix[] = $val->sponsor_logo;
                                $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                            }
                        }
                        $sponsorLogo = implode(",", (array)$sponsorLogomix);
                        unset($sponsorLogomix);
                        unset($sponsorLogoArry);
                        $vx[] = array(
                            "slno" => $i,
                            "trending_type" => "comp",
                            "type" => "comp",
                            "con_type" => $val->type,
                            "vendor" => $val->vendor,
                            "color" => ($val->color != '') ? $val->color : '#918c91',
                            "src" => $val->src,
                            "is_share" => $val->is_share,
                            "type_id" => $val->type_id,
                            "type" => 'comp',
                            "date" => date('jS F y', strtotime($val->publish_date)),
                            "question" => html_entity_decode(strip_tags($val->title)),
                            "image" => change_img_src($val->comp_qa_file_img_thumbnail),
                            "answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                            "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),

                            "sponsor_name" => $val->sponsor,
                            "sponsor_logo" => change_img_src($sponsorLogo),
                            "all_sponsor" => $allsponsor,
                            "is_locked" => $key_locked,
                            "price" => $val->price,
                            "user_content_payment" => $val->user_contnet_payment_status,
                            "comment_count" => $val->count_comment,
                            "rating" => ($val->averageRating != '') ? $val->averageRating + $val->start_like : $val->start_like,
                            "myrating" => ($val->myrating != '') ? true : false,
                            "vault" => ($val->vault != '') ? $val->vault : 0,
                            "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                        );
                        $i++;
                    }
                }
                $compArry = $vx;
                break;
        }


        return $compArry;

    }



    public function spqlist(
        $envStatus,
        $category,
        $record,
        $limitTo,
        $limitFrom,
        $user_master_id,
        $spids,
        $subtype = ''
    ) {
        $key_locked = get_user_package($user_master_id, 'survey');
        $vx = array();
        $records = implode(",", array_unique(explode(",", $record)));
        $sqlCompl = "SELECT
        survey_id
        FROM
        survey_user_answer sv
        WHERE
        sv.user_master_id = '" . $user_master_id . "'";
        $queryCompl = $this->db->query($sqlCompl);
        $resultCompl = $queryCompl->result();
        $complID = array();
        foreach ($resultCompl as $valCompl) {
            $complID[] = $valCompl->survey_id;
        }
        //print_r($complID); exit;
        $sqlInCompl = "SELECT
        survey_id
        FROM
        survey_user_incomplete_answer sv
        WHERE
        sv.status = 3
        and
        sv.user_master_id = '" . $user_master_id . "'";
        $queryInCompl = $this->db->query($sqlInCompl);
        $resultInCompl = $queryInCompl->result();
        $incomplID = array();
        foreach ($resultInCompl as $valInCompl) {
            $incomplID[] = $valInCompl->survey_id;
        }
        $arrayFinal = array_filter(array_unique(array_merge($complID, $incomplID)));
        //print_r($arrayFinal); exit;
        $complIDStr = implode(",", (array)$arrayFinal);
        //echo $complIDStr ; exit;
        if ($complIDStr) {
            $qryStr = 'and cm.survey_id not in (' . $complIDStr . ')';
        } else {
            $qryStr = '';
        }
        if (!empty($subtype) && $subtype != '') {
            $categoryClause = " AND cm.category = '{$subtype}'";
        } else {
            $categoryClause = " AND cm.category != 'poll'";
        }
        $sql = "SELECT
                        cm.survey_id,
                        cm.category,
                        cm.survey_title,
                        cm.survey_description,
                        cm.image,
                        cm.survey_points,
                        cm.survey_time,
                        cm.question_count,
                        cm.publishing_date,
                        cm.client_id,
                        cm.sponsor_ids,
                        cm.deeplink,
                        cm.gl_deeplink,
                        cm.verified,
                        cm.display_in_dashboard,
                        cm.privacy_status,
                        cm.template_id,
                        cm.color,
                        cm.points_on_approval,
                        cm.added_on,
                        cm.added_by,
                        cm.modified_on,
                        cm.modified_by,
                        cm.status,
                        cm.is_available_survey_portal,
                        cm.available_for_live_session,
                        cm.env,
                        cm.is_share,
                        cm.is_like,
                        cm.is_comment,
                        cm.approved_by,
                        cm.img_credits,
                        svd.data,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                        cln.client_name,
                        cln.client_logo,
                        cTenv.price,
                        uTpyCont.status as user_contnet_payment_status,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                        FROM
                        survey cm
                        left JOIN survey_to_speciality as svts ON svts.survey_id = cm.survey_id
                        left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                        JOIN client_master as cln ON cln.client_master_id = cm.client_id
                        LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = cm.survey_id
                        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                        LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.survey_id and  cTenv.type = 6
                        LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.survey_id and  uTpyCont.type = 6 and 	uTpyCont.user_master_id = " . $user_master_id . "
                        JOIN survey_detail as svd ON svd.survey_id = cm.survey_id
                        left JOIN survey_user_answer as sua ON sua.survey_id = cm.survey_id
                        WHERE
                        cm.status = 3
                        " . $envStatus . "";
        //and  cm.category = 'quiz
        if (($spids) && ($spids != 0)) {
            $sql .= " AND svts.speciality_id IN (" . $spids . ")";
        }
        if ($category == 5) {
            $sql .= " AND cm.display_in_dashboard = 1";
        }
        if ($records != '') {
            $sql .= " AND cm.survey_id IN ($records)";
        }
        $sql .= " and date(cm.publishing_date) <= CURDATE()
                        and cm.privacy_status = 0
                        " . $qryStr . "
                        {$categoryClause}
                        group by
                        cm.survey_id,
                        cm.category,
                        cm.survey_title,
                        cm.survey_description,
                        cm.image,
                        cm.survey_points,
                        cm.survey_time,
                        cm.question_count,
                        cm.publishing_date,
                        cm.client_id,
                        cm.sponsor_ids,
                        cm.deeplink,
                        cm.gl_deeplink,
                        cm.verified,
                        cm.display_in_dashboard,
                        cm.privacy_status,
                        cm.template_id,
                        cm.color,
                        cm.points_on_approval,
                        cm.added_on,
                        cm.added_by,
                        cm.modified_on,
                        cm.modified_by,
                        cm.status,
                        cm.is_available_survey_portal,
                        cm.available_for_live_session,
                        cm.env,
                        cm.is_share,
                        cm.is_like,
                        cm.is_comment,
                        cm.approved_by,
                        cm.img_credits,
                        svd.data,
                        cln.client_name,
                        cln.client_logo,
                        cTenv.price,
                        uTpyCont.status
                        order by cm.publishing_date desc";
        if ($limitFrom == 0 && $limitTo != '') {
            $sql .= " LIMIT $limitFrom, $limitTo";
        } else {
            $sql .= " LIMIT $limitFrom, $limitTo";
        }
        // echo $sql;
        // exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        foreach ($result as $val) {
            $dataArry = unserialize($val->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                    $sp++;
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                    $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $vx[] = array(
                "survey_id" => $val->survey_id,
                "trending_type" => "survey",
                "type" => "survey",
                "survey_time" => $val->survey_time,
                "survey_duration" => timeconversion($val->survey_time),
                "question_count" => $val->question_count,
                "category" => $val->category,
                "point" => $val->survey_points,
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "is_share" => $val->is_share,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                //get_user_content_status($val->type_id, 6, $user_master_id),
                //============ integrated for subscription ============//
                "json_data" => $str,
                "survey_title" => $val->survey_title,
                "question" => $val->survey_title,
                "deeplink" => $val->deeplink,
                "survey_description" => substr(strip_tags($val->survey_description), 0, 150),
                "image" => change_img_src($val->image),
                "specialities_name" => $val->specialities_name,
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                // "client_name" => $val->client_name,
                // "client_logo" => change_img_src('' . $val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "publishing_date" => $val->publishing_date,
            );
        }
        return $vx;
    }
    public function epublist(
        $envStatus,
        $category,
        $record,
        $limitTo,
        $limitFrom,
        $user_master_id,
        $spIds
    ) {
        $key_locked = get_user_package($user_master_id, 'epub');
        //$vx = array();
        if ($key_locked == '') {
            return null;
        }
        $records = implode(",", array_unique(explode(",", $record)));

        $displayInDashboard = "";
        $orderBy = "";
        if ($category === 46) {
            $displayInDashboard = " AND cm.display_in_dashboard = 1";
            $orderBy = "ORDER BY cm.publication_date DESC";
        }

        if ($category === 45) {
            $displayInDashboard = "";
            $orderBy = "ORDER BY cm.publication_date DESC";

        }

        if ($category === 47) {
            $displayInDashboard = "";
            $orderBy = "ORDER BY cm.publication_date DESC";

        }
        if ($category == '') {
            $displayInDashboard = "";
            $orderBy = "ORDER BY cm.publication_date DESC";

        }

        if ($records != '') {
            $recordCondition = " AND cm.epub_id IN ($records)";
        } else {
            $recordCondition = "";
        }

        if (!empty($spIds)) {
            $spIdsCondition = " AND cmTs.specialities_id IN ($spIds)";
        } else {
            $spIdsCondition = "";
        }





        $sql = "WITH epub_data AS (
            SELECT
                cm.epub_id,
                cm.epub_description,
                cm.epub_title,
                cm.epub_img,
                cm.epub_img_thumbnail,
                cm.epub_file,
                cm.author,
                cm.added_on,
                cm.is_share,
                cm.publication_date,
                cm.deeplink,
                cm.color,
                cm.start_like,
                cm.client_id,
                cm.is_converted,
                cm.privacy_status
            FROM 
                epub_master AS cm
            JOIN 
                epub_to_specialities AS cmTs ON cmTs.epub_id = cm.epub_id

            WHERE 
                cm.status = 3
                AND cm.privacy_status = 0
                AND is_converted = 1
                AND cm.publication_date <= CURDATE()
                {$displayInDashboard}
                {$recordCondition}
                {$spIdsCondition}
                {$orderBy}
                LIMIT {$limitFrom}, {$limitTo}
        ),

        specialities_data AS (
            SELECT 
                cmTs.epub_id,
                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name
            FROM 
                epub_to_specialities AS cmTs
            JOIN 
                master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
            /* If spIds condition */
            GROUP BY 
                cmTs.epub_id
        ),

        sponsor_data AS (
            SELECT 
                cmTspon.epub_id,
                GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
            FROM 
                epub_to_sponsor AS cmTspon
            JOIN 
                client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            GROUP BY 
                cmTspon.epub_id
        ),

        rating_data AS (
            SELECT 
                rt.post_id,
                COUNT(rt.rating) AS averageRating
            FROM 
                knwlg_rating AS rt
            WHERE 
                rt.post_type = 'epub'
            GROUP BY 
                rt.post_id
        ),

        comment_data AS (
            SELECT 
                kcm.type_id,
                COUNT(kcm.knwlg_comment_id) AS count_comment
            FROM 
                knwlg_comment AS kcm
            WHERE 
                kcm.type = 'epub'
            GROUP BY 
                kcm.type_id
        )

        SELECT
            ed.epub_id AS type_id,
            ed.epub_description AS description,
            ed.epub_title AS title,
            ed.epub_img,
            ed.epub_img_thumbnail,
            ed.epub_file,
            ed.author,
            ed.added_on,
            ed.is_share,
            ed.publication_date AS publish_date,
            cln.client_name,
            cln.client_logo,
            cTenv.price,
            uTpyCont.status AS user_contnet_payment_status,
            ed.deeplink,
            ed.color,
            ed.start_like,
            ed.is_converted,    
            sd.specialities_name,
            spd.sponsor,
            spd.sponsor_logo,
            COALESCE(rd.averageRating, 0) AS averageRating,
            rtmy.rating AS myrating,
            COALESCE(cd.count_comment, 0) AS count_comment,
            kv.status AS vault
        FROM 
            epub_data AS ed
        LEFT JOIN 
            specialities_data AS sd ON sd.epub_id = ed.epub_id
        LEFT JOIN 
            sponsor_data AS spd ON spd.epub_id = ed.epub_id
        LEFT JOIN 
            rating_data AS rd ON rd.post_id = ed.epub_id
        LEFT JOIN 
            comment_data AS cd ON cd.type_id = ed.epub_id
        JOIN 
            client_master AS cln ON cln.client_master_id = ed.client_id AND cln.client_master_id = 1
        LEFT JOIN 
            knwlg_rating AS rtmy ON rtmy.post_id = ed.epub_id AND rtmy.post_type = 'epub' AND rtmy.rating != 0 AND rtmy.user_master_id = {$user_master_id}
        LEFT JOIN 
            knwlg_vault AS kv ON kv.post_id = ed.epub_id AND kv.type_text = 'epub' AND kv.user_id = {$user_master_id}
        LEFT JOIN 
            content_to_env AS cTenv ON cTenv.type_id = ed.epub_id AND cTenv.type = 9
        LEFT JOIN 
            payment_user_to_content AS uTpyCont ON uTpyCont.type_id = ed.epub_id AND uTpyCont.type = 9 AND uTpyCont.user_master_id = {$user_master_id}
        
        ";

        // echo $sql;
        // exit;
        // exit;
        $query = $this->db->query($sql);

        //echo $this->db->last_query();
        // exit;
        $result = $query->result();




        $i = 1;
        $finalArry = array();
        foreach ($result as $val) {


            // $sponsorLogoArry = explode(",", $val->sponsor_logo);
            // $allsponsor = array();
            // $sponsorname = explode(",", $val->sponsor);
            // $sp = 0;
            // if (count($sponsorLogoArry) > 0) {
            //     foreach ($sponsorLogoArry as $valueSponor) {
            //         if ($valueSponor) {
            //             $sponsorLogomix[] = '' . $valueSponor;
            //             $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
            //             $sp++;
            //         }
            //     }
            // } else {
            //     if ($val->sponsor_logo != '') {
            //         $sponsorLogomix[] = '' . $val->sponsor_logo;
            //         $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
            //     }
            // }
            // $sponsorLogo = implode(",", (array)$sponsorLogomix);
            // unset($sponsorLogomix);
            // unset($sponsorLogoArry);


            $sponsor_data = get_sponsor_data($result->sponsor, $result->sponsor_logo, true);
            $allsponsor = $sponsor_data['all_sponsors'];
            $sponsorLogo = $sponsor_data['sponsor_logo'];




            $finalArry[] = array(
                "slno" => $i,
                "type_id" => $val->type_id,
                "type" => 'epub',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "question" => html_entity_decode(strip_tags($val->title)),
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "description_short" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "author" => $val->author,
                "is_converted" => $val->is_converted,
                "epub_file" => $val->epub_file,
                "image" => change_img_src($val->epub_img_thumbnail),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "is_share" => $val->is_share,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->type_id, 9, $user_master_id),
                //============ integrated for subscription ============//
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                // "client_name" => $val->client_name,
                // "client_logo" => change_img_src('' . $val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "comment_count" => $val->count_comment,
                "rating" => ($val->averageRating != '') ? $val->averageRating + $val->start_like : $val->start_like,
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            $i++;
        }
        //print_r($finalArry); exit;
        return $finalArry;
    }
    public function videolist(
        $envStatus,
        $category,
        $record,
        $limitTo,
        $limitFrom,
        $user_master_id,
        $spIds
    ) {

        switch ($category) {
            case 35:
                $videoArry = $this->Knwlg_model->all_archiveVideo($user_master_id, $client_ids, $group_ids, $spIds, $limitFrom, $limitTo, $type_id, "featured");
                break;
            case 37:
                $videoArry = $this->Knwlg_model->gethistoryvideoSpeciality($user_master_id, $limitTo, $limitFrom, $spIds);
                break;
            case 36:
                $videoArry = $this->Knwlg_model->all_archiveVideo($user_master_id, $client_ids, $group_ids, $spIds, $limitFrom, $limitTo, $type_id, "");
                break;
            default:
                $vx = array();
                $key_locked = get_user_package($user_master_id, 'comp');
                $records = implode(",", array_unique(explode(",", $record)));


                if ($records != '') {
                    $condition = " AND cm.video_archive_id IN ($records)";
                }
                // if ($category == 5) {
                //     $condition = " AND cm.display_in_dashboard = 1";
                // }
                if (($spIds != '') && ($spIds != 0)) {
                    $condition = "  AND cmTs.specialities_id IN (" . $spIds . ")";
                }


                $sql = "WITH video_archive_data AS (
                    SELECT
                        cm.video_archive_id,
                        cm.video_archive_question,
                        cm.video_archive_answer,
                        cm.video_archive_question_raw,
                        cm.video_archive_answer_raw,
                        cm.video_archive_file_img,
                        cm.video_archive_file_img_thumbnail,
                        cm.deeplink,
                        cm.start_like,
                        cm.duration,
                        cm.is_share,
                        cm.added_on,
                        cm.publication_date,
                        cm.type,
                        cm.vendor,
                        cm.src,
                        cm.video_archive_session_id,
                        cm.video_archive_speciality_id,
                        cm.client_id
                    FROM 
                        knwlg_video_archive AS cm
                        LEFT JOIN 
                        video_archive_to_specialities AS cmTs ON cmTs.video_archive_id = cm.video_archive_id
                    WHERE 
                        cm.status = 3
                        AND cm.privacy_status = 0 
                        AND cm.publication_date <= CURDATE()
                        {$condition}
                        ORDER BY 
                        cm.publication_date DESC
                        LIMIT {$limitFrom}, {$limitTo}

                ),
                
                specialities_data AS (
                    SELECT 
                        cmTs.video_archive_id,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                    FROM 
                        video_archive_to_specialities AS cmTs
                    JOIN 
                        master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                    /* If spIds condition */
                    GROUP BY 
                        cmTs.video_archive_id
                ),
                
                sponsor_data AS (
                    SELECT 
                        cmTspon.video_archive_id,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                    FROM 
                        video_archive_to_sponsor AS cmTspon
                    JOIN 
                        client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                    GROUP BY 
                        cmTspon.video_archive_id
                ),
                
                rating_data AS (
                    SELECT 
                        rt.post_id,
                        COUNT(rt.rating) AS averageRating
                    FROM 
                        knwlg_rating AS rt
                    WHERE 
                        rt.post_type = 'video_archive' 
                        AND rt.rating != 0
                    GROUP BY 
                        rt.post_id
                )
                
                SELECT
                    vad.video_archive_id as type_id,
                    vad.video_archive_question,
                    vad.video_archive_answer,
                    vad.video_archive_question_raw,
                    vad.video_archive_answer_raw,
                    vad.video_archive_file_img,
                    vad.video_archive_file_img_thumbnail,
                    vad.deeplink,
                    vad.start_like,
                    cTenv.price,
                    uTpyCont.status as user_contnet_payment_status,
                    kvtd.play_time,
                    vad.duration,
                    vad.is_share,
                    vad.added_on,
                    vad.publication_date,
                    cln.client_name,
                    cln.client_logo,
                    vad.type,
                    vad.vendor,
                    vad.src,
                    ks.session_doctor_id,
                    msct.category_name,
                    msct.category_logo,
                    sd.sponsor,
                    sd.sponsor_logo,
                    spd.specialities_name,
                    spd.specialities_ids_and_names,
                    vad.video_archive_speciality_id,
                    kv.status as vault,
                    COALESCE(rd.averageRating, 0) as averageRating,
                    rtmy.rating as myrating
                FROM 
                    video_archive_data AS vad
                LEFT JOIN 
                    specialities_data AS spd ON spd.video_archive_id = vad.video_archive_id
                LEFT JOIN 
                    sponsor_data AS sd ON sd.video_archive_id = vad.video_archive_id
                LEFT JOIN 
                    knwlg_sessions_V1 AS ks ON ks.session_id = vad.video_archive_session_id
                LEFT JOIN 
                    master_session_category AS msct ON msct.mastersession_category_id = ks.category_id
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = vad.video_archive_id AND cTenv.type = 3
                LEFT JOIN 
                    payment_user_to_content AS uTpyCont ON uTpyCont.type_id = vad.video_archive_id AND uTpyCont.type = 3 AND uTpyCont.user_master_id = {$user_master_id}
                LEFT JOIN 
                    knwlg_video_tracking_data AS kvtd ON kvtd.content_id = vad.video_archive_id AND kvtd.content_type = 'video_archive' AND kvtd.user_master_id = {$user_master_id}
                LEFT JOIN 
                    knwlg_vault AS kv ON kv.post_id = vad.video_archive_id AND kv.type_text = 'video_archive' AND kv.user_id = {$user_master_id}
                LEFT JOIN 
                    knwlg_rating AS rtmy ON rtmy.post_id = vad.video_archive_id AND rtmy.post_type = 'video_archive' AND rtmy.rating != 0 AND rtmy.user_master_id = {$user_master_id}
                LEFT JOIN 
                    rating_data AS rd ON rd.post_id = vad.video_archive_id
                JOIN 
                    client_master AS cln ON cln.client_master_id = vad.client_id
                ";

                $query = $this->db->query($sql);

                //echo $sql; exit;
                $vx = array();
                if (($query) && ($query->num_rows() > 0)) {
                    $result = $query->result();
                    //print_R($result); exit;
                    $i = 1;
                    $vx = array();
                    foreach ($result as $val) {
                        if ($val->video_archive_file_img) {
                            $img = $val->video_archive_file_img;
                        } else {
                            $img = '';
                        }
                        $allsponsor = array();
                        $sponsorname = explode(",", $val->sponsor);
                        $sp = 0;
                        $sponsorLogoArry = explode(",", $val->sponsor_logo);
                        if (count($sponsorLogoArry) > 0) {
                            foreach ($sponsorLogoArry as $valueSponor) {
                                if ($valueSponor) {
                                    // if full path exist
                                    if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                                        $sponsorLogomix[] = $valueSponor;
                                    } else {
                                        $sponsorLogomix[] = '' . $valueSponor;
                                    }
                                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                                    $sp++;
                                }
                            }
                        } else {
                            if ($val->sponsor_logo != '') {
                                /// if full path exist
                                if (stripos($val->sponsor_logo, "https://storage.googleapis.com") > -1) {
                                    $sponsorLogomix[] = $val->sponsor_logo;
                                } else {
                                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                                }
                                $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                            }
                        }
                        $sponsorLogo = implode(",", (array)$sponsorLogomix);
                        unset($sponsorLogomix);
                        unset($sponsorLogoArry);
                        $session_doc_array = explode(",", $val->session_doctor_id);
                        $ses_doc_det_array = array();
                        $inc_pp = 0;
                        foreach ($session_doc_array as $single_doctor) {
                            $var = session_doc_detail($single_doctor);
                            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                             $logic_image = base_url() . "uploads/docimg/" . $image;
                                 } else {
                             $logic_image = base_url() . "uploads/docimg/MConsult.png";
                                 }*/
                            if ($image) {
                                /////============================== updated by  ramanath  14-5-21
                                if (stripos($image, "https://storage.googleapis.com") > -1) {
                                    $logic_image = $image;
                                } else {
                                    $logic_image_path = docimg; //"uploads/docimg/" . $image;
                                    $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                    $logic_image = $imgPr;
                                }
                                //=======================================
                            } else {
                                $logic_image = docimg; //base_url() . "docimgno-image.png";
                            }
                            $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                            $inc_pp++;
                        }
                        $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
                        $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                        $main_description = "";
                        $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
                        $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                        $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                        $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
                        //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
                        $vx[] = array(
                            "slno" => $i,
                            "con_type" => $val->type,
                            "type" => "video",
                            "type_id" => $val->type_id,
                            "type" => "video",
                            "vendor" => $val->vendor,
                            "is_share" => $val->is_share,
                            "src" => $val->src,
                            "date" => date(' jS F y', strtotime($val->publication_date)),
                            "question" => html_entity_decode(strip_tags($string)),
                            "image" => change_img_src($img),
                            //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                            "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                            "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                            "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                            // "client_name" => $val->client_name,
                            // "client_logo" => change_img_src('' . $val->client_logo),
                            "play_time" => $val->play_time,
                            "duration" => $val->duration,
                            "category_name" => $val->category_name,
                            //============ integrated for subscription ============//
                            "is_locked" => $key_locked,
                            "price" => $val->price,
                            "user_content_payment" => get_user_content_status($val->type_id, 3, $user_master_id),
                            //============ integrated for subscription ============//
                            "sponsor_name" => $val->sponsor,
                            "sponsor_id" => $val->sponsor_ids,
                            "sponsor_logo" => change_img_src($sponsorLogo),
                            "all_sponsor" => $allsponsor,
                            "comment_count" => $val->count_comment,
                            // "deeplink" => ($env != 1) ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                            "deeplink" => $val->deeplink , //$val->deeplink,
                            "rating" => ($val->averageRating != '') ? $val->averageRating + $val->start_like : $val->start_like,
                            "myrating" => ($val->myrating != '') ? true : false,
                            "vault" => ($val->vault != '') ? $val->vault : 0,
                            "session_doctor_id" => $val->session_doctor_id,
                            "session_doctor_entities" => $ses_doc_det_array,
                        );
                        $i++;
                    }
                }
                //print_R($vx); exit;
                $videoArry = $vx;
                break;
        }

        return $videoArry;

    }
    public function sessionlist(
        $envStatus,
        $category,
        $record,
        $limitTo,
        $limitFrom,
        $spIds,
        $user_booked_session_id = '',
        $user_master_id,
        $client_ids,
        $user_group_ids
    ) {



        // echo 'user_master_id_seesionlist:   '.$user_master_id;

        switch ($category) {
            case 30:
                $sessionlist = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, $client_ids, $user_group_ids, null, $spIds);
                break;
            case 31:
                $sessionlist = $this->Knwlgmastersessionnew_model->all_bookedmastersession_details($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, null, $spIds);
                break;
            case 33:

                //echo 'hello..';

                $sessionlist = $this->Knwlgmastersessionnew_model->all_featuredmastersession_details($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, $client_ids, $user_group_ids, '', $spIds);


                break;
            case 32:
                $sessionlist = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details_slider($user_booked_session_id, $user_master_id, $limitFrom, $limitTo, $client_ids, $user_group_ids, '', $spIds);
                break;
            case 34:
                $sessionlist = $this->Knwlgmastersessionnew_model->sessionsDoctorsListByClientID($user_master_id, $client_ids, $doc_ids_optional, $spIds, $filter_sp_ids, $isRequestBlank, $limitFrom, $limitTo);
                break;

            default:


                if ($spIds != '') {
                    $spIds = " AND sTs.specialities_id IN (".$spIds.")";
                }

                $sql = "WITH FilteredSessions AS (
                            SELECT
                                ks.session_id,
                                sp.participant_id
                            FROM
                                knwlg_sessions_V1 AS ks
                        LEFT JOIN
                                content_to_env AS cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
                        LEFT JOIN
                                session_to_specialities AS sTs ON sTs.session_id = ks.session_id
                        LEFT JOIN
                                knwlg_sessions_participant sp  ON sp.knwlg_sessions_id = ks.session_id
                        WHERE
                                --    {$global_status}
                                --    {$session_status}
                                --    {$childsessionids}
                                --    {$privacy_status}
                                --    {$session_end_datetime}
                                --    {$session_start_datetime}
                                --    {$bookedIdsql}
                                --    {$optionalQuery}
                                {$envStatus}
                
                                GROUP BY ks.session_id
                                ORDER BY {$orderBydate}
                                LIMIT ?, ?
                
                    ),
                    SpecialitiesAgg AS (
                        
                
                        SELECT
                        sTs.session_id,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                        FROM session_to_specialities sTs
                        LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = sTs.specialities_id
                        GROUP BY sTs.session_id
                    ),
                    SponsorsAgg AS (
                        SELECT
                            fs.session_id,
                            GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                            GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                        FROM
                            FilteredSessions fs
                        JOIN
                            session_to_sponsor sTspon ON sTspon.session_id = fs.session_id
                        JOIN
                            client_master clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                        GROUP BY
                            fs.session_id
                    ),
                    DoctorsAgg AS (
                        -- SELECT
                        --     fs.session_id,
                        --     GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
                        --     GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
                        --     GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') AS speciality,
                        --     GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
                        --     GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images
                        -- FROM
                        --     FilteredSessions fs
                        -- JOIN
                        --     knwlg_sessions_V1 ks ON ks.session_id = fs.session_id
                        -- LEFT JOIN
                        --     knwlg_sessions_doctors sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                        -- GROUP BY
                        --     fs.session_id
                
                            SELECT
                            fs.session_id,
                            GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
                            GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
                            GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') AS speciality,
                            GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
                            GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images
                            FROM
                            FilteredSessions fs
                            JOIN
                            session_to_sessiondoctor s2sd ON s2sd.session_id = fs.session_id
                            JOIN
                            knwlg_sessions_doctors sdoc ON sdoc.sessions_doctors_id = s2sd.sessions_doctors_id
                            GROUP BY
                            fs.session_id
                    )
                    
                    SELECT
                        fs.participant_id,
                        ks.*,
                        
                        cln.client_name,
                        cln.client_logo,
                        msct.category_name,
                        msct.category_logo,
                        cTenv.price,
                        uTpyCont.status AS user_content_payment_status,
                        
                        kv.status AS vault,
                        stci.cover_image1,
                        stci.cover_image2,
                        stci.cover_image3,
                        stci.cover_image4,
                        stci.cover_image5,
                        (ks.total_buffer + ks.total_seats) AS tot_seat,
                        spec.specialities_name,
                        spec.specialities_ids_and_names,
                        spon.sponsor,
                        spon.sponsor_logo,
                        doc.session_doctor_id,
                        doc.doctor_name,
                        doc.speciality,
                        doc.profile,
                        doc.profile_images
                        -- part.PartName,
                        -- part.users,
                        -- part.IS_ATTENDED
                    FROM
                        FilteredSessions fs
                    JOIN
                        knwlg_sessions_V1 ks ON ks.session_id = fs.session_id
                    LEFT JOIN
                        client_master cln ON cln.client_master_id = ks.client_id
                    LEFT JOIN
                        session_to_cover_image stci ON stci.session_id = ks.session_id
                    LEFT JOIN
                        knwlg_vault kv ON kv.type_text = 'session' AND kv.post_id = ks.session_id
                    LEFT JOIN
                        content_to_env cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
                    LEFT JOIN
                        payment_user_to_content uTpyCont ON uTpyCont.type_id = ks.session_id AND uTpyCont.type = 2 AND uTpyCont.user_master_id = ?
                    LEFT JOIN
                        master_session_category msct ON msct.mastersession_category_id = ks.category_id
                    
                    LEFT JOIN
                        SpecialitiesAgg spec ON spec.session_id = ks.session_id
                    LEFT JOIN
                        SponsorsAgg spon ON spon.session_id = ks.session_id
                    LEFT JOIN
                        DoctorsAgg doc ON doc.session_id = ks.session_id ";



                $query = $this->db->query($sql);

                $vx = array();

                if (($query) && ($query->num_rows() > 0)) {
                    $result = $query->result();
                    $i = 0;

                    foreach ($result as $val) {
                        if (@getimagesize(base_url() . "uploads/docimg/" . $val->profile_image)) {
                            $logic_image = '' . $val->profile_image;
                        } else {
                            $logic_image = base_url() . "uploads/docimg/MConsult.png";
                        }
                        $start_time = $val->start_datetime;
                        $start_time = date("g:i A", strtotime($start_time));
                        $ses_doc_det_array = array();
                        if ($val->session_doctor_id != '') {
                            $session_doc_array = explode(",", $val->session_doctor_id);
                        } else {
                            //print_r($getchildsession['session_doctors']);
                            $session_doc_array = explode(",", $getchildsession['session_doctors'][$val->session_id]);
                            //print_r($session_doc_array); exit;
                        }
                        if (!empty($session_doc_array)) {
                            #$session_doc_array = explode(",", $val->session_doctor_id);
                            $inc_pp = 0;
                            $store_total_doctors[$val->session_id] = 0;
                            foreach ($session_doc_array as $single_doctor) {
                                $var = session_doc_detail($single_doctor);
                                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                if ($image) {
                                    $logic_image = $image;
                                } else {
                                    $logic_image = docimg;
                                    //$logic_image = base_url() . "uploads/docimg/no-image.png";
                                }
                                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                                $inc_pp++;
                                $store_total_doctors[$val->session_id] = $inc_pp;
                            }
                        }
                        $k = array_keys((array)$getchildsession['sessioncount']);
                        //print_r(in_array($val->session_id,$k));
                        if (in_array($val->session_id, $k)) {
                            $total_multiday_session = $getchildsession['sessioncount'][$val->session_id];
                            // print_R($total_multiday_session."/n");
                        } else {
                            $total_multiday_session = 0;
                        }
                        $keyvalue_1 = array_keys((array)$getchildsession['doctorcount']);
                        if (in_array($val->session_id, $keyvalue_1)) {
                            $total_doctors = $getchildsession['doctorcount'][$val->session_id];
                        } else {
                            $total_doctors = $store_total_doctors[$val->session_id];
                        }
                        $datetime1 = new DateTime(date('Y-m-d H:i:s'));
                        $datetime2 = new DateTime($val->start_datetime);
                        $difference = $datetime1->diff($datetime2);
                        //$entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
                        $end_time = $val->end_datetime;
                        $end_time = date("g:i A", strtotime($end_time));
                        /**
                         * new sponsor logic
                         */
                        $allsponsor = array();
                        $sponsorname = explode(",", $val->sponsor);
                        $sp = 0;
                        $sponsorSESLogoArry = explode(",", $val->sponsor_logo);
                        if (count($sponsorSESLogoArry) > 0) {
                            foreach ($sponsorSESLogoArry as $valueSponor) {
                                if ($valueSponor) {
                                    $sponsorSESLogomix[] = '' . $valueSponor;
                                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                                    $sp++;
                                }
                            }
                        } else {
                            if ($val->sponsor_logoSES != '') {
                                $sponsorSESLogomix[] = '' . $val->sponsor_logoSES;
                                $allsponsor[] = array('name' => $val->sponsor, "logo" => $val->sponsor_logoSES);
                            }
                        }
                        $sponsorLogoSES = implode(",", (array)$sponsorSESLogomix);
                        unset($sponsorSESLogomix);
                        unset($sponsorSESLogoArry);
                        $coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
                        //$entities[$i]['cover_image'] = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
                        $vx[] = array(
                            "slno" => $i,
                            "trending_type" => "session",
                            "type_id" => $val->session_id,
                            "session_id" => $val->session_id,
                            "type" => 'session',
                            "total_session" => $total_multiday_session,
                            "total_doctors" => $total_doctors,
                            "total_days" => $difference->d,
                            "doctor_name" => $val->doctor_name,
                            "cover_image" => ($val->cover_image != '') ? change_img_src($val->cover_image) : $coverImg,
                            "cover_image1" => ($val->cover_image1 != '') ? change_img_src($val->cover_image1) : $coverImg,
                            "cover_image2" => ($val->cover_image2 != '') ? change_img_src($val->cover_image2) : $coverImg,
                            "cover_image3" => ($val->cover_image3 != '') ? change_img_src($val->cover_image3) : $coverImg,
                            "cover_image4" => ($val->cover_image4 != '') ? change_img_src($val->cover_image4) : $coverImg,
                            "cover_image5" => ($val->cover_image5 != '') ? change_img_src($val->cover_image5) : $coverImg,
                            "session_doctor_id" => $val->session_doctor_id,
                            "date" => date(' jS F y', strtotime($val->start_datetime)),
                            "is_multiday_session" => $val->is_multiday_session,
                            "start_datetime" => $val->start_datetime, //date(' jS F y', strtotime($val->start_datetime)),
                            "display_date" => $start_time . "-" . $end_time,
                            "ms_cat_name" => $val->category_name,
                            "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo),
                            "sponsor_name" => $val->sponsor,
                            "sponsor_logo" => change_img_src($sponsorLogoSES),
                            "all_sponsor" => $allsponsor,
                            "image" => change_img_src($logic_image),
                            "image_raw_name" => change_img_src($val->profile_image),
                            "session_status" => $val->session_status,
                            "status_name" => $val->status_name,
                            "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),
                            "session_topic" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                            "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),
                            "question" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                            "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                            "color" => ($val->color != '') ? $val->color : '#08cc9e',
                            // "client_name" => $val->client_name,
                            // "client_logo" => change_img_src('' . $val->client_logo),
                            //============ integrated for subscription ============//
                            "is_locked" => $key_locked,
                            "is_share" => $val->is_share,
                            "price" => $val->price,
                            "user_content_payment" => $val->user_contnet_payment_status,
                            //get_user_content_status($val->session_id, 2, $user_master_id),
                            //============ integrated for subscription ============//
                            "vault" => ($val->vault != "") ? $val->vault : 0,
                            "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                            "session_doctor_entities" => $ses_doc_det_array,
                        );
                    }
                }

                $sessionlist = $vx;
                break;
        }

        return $sessionlist;

    }
    public function traininglist(
        $envStatus,
        $category,
        $records,
        $limitTo,
        $limitFrom,
        $user_master_id,
        $sp_ids
    ) {

        // echo $category;
        // exit;
        $env = get_user_env_id($user_master_id);
        $vx = array();
        $key_locked = get_user_package($user_master_id, 'training');
        if ($key_locked == '') {
            return null;
        }
        // =============== env_id implementation ===================//
        //$this->db->save_queries = TRUE;

        $sql = "SELECT
            tm.id,
            tm.title,
            tm.description,
            tm.preview_image,
            tm.client_id,
            tm.channel_id,
            tm.template_id,
            tm.color,
            tm.display_in_dashboard,
            tm.featured_video,
            tm.deeplink,
            tm.in_deeplink,
            tm.gl_deeplink,
            tm.start_like,
            tm.start_date,
            tm.url,
            tm.max_participants,
            tm.added_on,
            tm.added_by,
            tm.modified_on,
            tm.modified_by,
            tm.status,
            tm.cert_template_id,
            tm.duration,
            tm.privacy_status,
           
            tm.published_date,
            tm.env,
            tm.is_share,
            tm.is_like,
            tm.is_comment,
            tm.enable_maxparticipants,
            tm.allow_postStart,
            cTenv.price,
            uTpyCont.status as user_contnet_payment_status,
            (SELECT GROUP_CONCAT(DISTINCT ms.specialities_name) 
             FROM training_to_speciality tts 
             LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = tts.specialities_id 
             WHERE tts.training_id = tm.id
             " . ((!empty($sp_ids) && $sp_ids[0] != 0) ? "AND tts.specialities_id IN (" . implode(',', $sp_ids) . ")" : "") . "
            ) AS specialities_name,
            (SELECT GROUP_CONCAT(DISTINCT ms.master_specialities_id) 
             FROM training_to_speciality tts 
             LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = tts.specialities_id 
             WHERE tts.training_id = tm.id
             " . ((!empty($sp_ids) && $sp_ids[0] != 0) ? "AND tts.specialities_id IN (" . implode(',', $sp_ids) . ")" : "") . "
            ) AS master_spec_id,
            (SELECT GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) 
             FROM training_to_speciality tts 
             LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = tts.specialities_id 
             WHERE tts.training_id = tm.id
             " . ((!empty($sp_ids) && $sp_ids[0] != 0) ? "AND tts.specialities_id IN (" . implode(',', $sp_ids) . ")" : "") . "
            ) AS specialities_ids_and_names,
            (SELECT MAX(ms.rank) 
             FROM training_to_speciality tts 
             LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = tts.specialities_id 
             WHERE tts.training_id = tm.id
             " . ((!empty($sp_ids) && $sp_ids[0] != 0) ? "AND tts.specialities_id IN (" . implode(',', $sp_ids) . ")" : "") . "
            ) AS maxrank,
            (SELECT GROUP_CONCAT(DISTINCT clintspon.client_name) 
             FROM training_to_sponsor ts 
             LEFT JOIN client_master clintspon ON ts.sponsor_id = clintspon.client_master_id 
             WHERE ts.training_id = tm.id
            ) AS sponsor,
            (SELECT GROUP_CONCAT(DISTINCT session_doctor_id) 
             FROM training_to_session_doctor 
             WHERE training_id = tm.id
            ) AS session_doctor_id,
            (SELECT GROUP_CONCAT(DISTINCT clintspon.client_logo) 
             FROM training_to_sponsor ts 
             LEFT JOIN client_master clintspon ON ts.sponsor_id = clintspon.client_master_id 
             WHERE ts.training_id = tm.id
            ) AS sponsor_logo,
            (SELECT MAX(clintspon.client_name) 
             FROM training_to_sponsor ts 
             LEFT JOIN client_master clintspon ON ts.sponsor_id = clintspon.client_master_id 
             WHERE ts.training_id = tm.id
            ) AS client_name,
            (SELECT MAX(clintspon.client_logo) 
             FROM training_to_sponsor ts 
             LEFT JOIN client_master clintspon ON ts.sponsor_id = clintspon.client_master_id 
             WHERE ts.training_id = tm.id
            ) AS client_logo,
            (SELECT COUNT(user_master_id) 
             FROM payment_user_to_content 
             WHERE type = 4 AND type_id = tm.id
            ) AS active_users,
            (SELECT COUNT(rating) 
             FROM knwlg_rating 
             WHERE post_type = 'training' AND post_id = tm.id
            ) AS averageRating,
            (SELECT COUNT(DISTINCT id) 
             FROM training_module 
             WHERE status = 3 AND training_id = tm.id
            ) AS count_module,
            kv.status as vault
        FROM training_master as tm
        LEFT JOIN content_to_env as cTenv ON cTenv.type_id = tm.id AND cTenv.type = 4
        LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = tm.id AND uTpyCont.type = 4 AND uTpyCont.user_master_id = {$user_master_id}
        LEFT JOIN knwlg_vault as kv ON kv.post_id = tm.id AND kv.type_text = 'course' AND kv.user_id = {$user_master_id}
        WHERE tm.status = 3
        AND date(tm.published_date) <= '" . date('Y-m-d') . "'
        AND tm.privacy_status = 0
        " . ($category == 5 ? "AND tm.display_in_dashboard = 1" : "") . "
        " . ($records != '' ? "AND tm.id IN (" . implode(',', array_unique(explode(",", $records))) . ")" : "") . "
        " . ($env != 2 ? "AND cTenv.env IN (2, {$env})" : "AND cTenv.env IN ({$env})") . "
        ORDER BY tm.published_date DESC
        " . (($limitFrom != '' && $limitTo != '') ? "LIMIT {$limitFrom}, {$limitTo}" : "");

        // echo $sql;
        // exit;

        $query = $this->db->query($sql);



        // $query = $this->db->get();
        // $this->db->save_queries = true;
        // $str = $this->db->last_query();
        // echo $str;
        // exit;
        //print_r($query); exit;
        if (($query) && ($query->num_rows())) {
            $res = $query->result();
        } else {
            $res = array();
        }
        $i = 1;
        foreach ($res as $key => $val) {
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] =  change_img_src($valueSponor);
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        $sp++;
                    }
                }
            } else {
                if ($val->sponsor_logo != '') {
                    $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                    $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $currentdatetime = date('Y-m-d H:i:s');
            if (isset($val->start_date) && $currentdatetime < $val->start_date && $val->max_participants > 0) {
                if ($val->max_participants > $val->active_users) {
                    $is_registrable = true;
                } else {
                    $is_registrable = false;
                }
            } elseif (isset($val->start_date) && $currentdatetime > $val->start_date) {
                $is_registrable = false;
            } elseif (!isset($val->start_date) && $val->max_participants == 0) {
                $is_registrable = true;
            } elseif (!isset($val->start_date) && $val->max_participants > 0) {
                $is_registrable = false;
            } else {
                $is_registrable = true;
            }
            $temp = array(
                "slno" => $i,
                "id" => $val->id,
                "type" => "training",
                "url" => $val->url,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "is_share" => $val->is_share,
                "title" => html_entity_decode(strip_tags($val->title)),
                "question" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->preview_image),
                "featured_video" => $val->featured_video,
                "max_participants" => $val->max_participants,
                "start_date" => $val->start_date,
                "active_users" => $val->active_users,
                "is_registrable" => $is_registrable,
                "color" => ($val->color != '') ? $val->color : '#eb34e5',
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "module_Count" => $val->count_module, //$this->count_module($val->id),
                "live_session_status" => $this->livestatus($val->id),
                "specialities" => $this->explode_speciality_string($val->specialities_ids_and_names, 0), //$this->specialityname($val->master_spec_id), #($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                // "client_name" => $val->client_name,
                // "client_logo" => change_img_src($val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "all_sponsor" => $allsponsor,
                "duration" => $val->duration,
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->id, 4, $user_master_id),
                //============ integrated for subscription ============//
                "is_completed" => $completestatus, #$this->complete_status($val->id,$user_master_id),
                "is_certificate" => ($val->cert_template_id != '') ? true : false,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            $vx[] = $temp;
            $i++;
        }
        //print_r($vx);
        // return $temp;
        return $vx;
    }
    public function channellist(
        $envStatus,
        $category,
        $records,
        $limitTo,
        $limitFrom,
        $user_master_id,
        $spIds,
        $subtype
    ) {
        $vx = array();

        $key_locked = get_user_package($user_master_id, 'channel');
        // $sql = "SELECT
        //         cm.channel_master_id as type_id,
        //         cm.title as title,
        //         cm.description as description,
        //         cm.privacy_status,
        //         cTus.status as sub_stat,
        //         cm.cover_image,
        //         cm.is_share,
        //         cm.added_on,
        //         cm.deeplink,
        //         ctm.type as category_name,
        //         cTenv.price,
        //         uTpyCont.status as user_contnet_payment_status,
        //         cm.gl_deeplink,
        //         cm.follower_count,
        //         bnd.logo,
        //         cln.client_name,
        //         (select count(DISTINCT(user_master_id)) as total from channel_to_user where status = 3 and channel_master_id = cm.channel_master_id) as total,
        //         cln.client_logo
        //         FROM channel_master as cm
        //         LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = cm.channel_master_id
        //         LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
        //         LEFT JOIN client_master as cln ON cln.client_master_id = cm.client_id
        //         LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = cm.brand_id
        //         LEFT JOIN channel_to_user  as cTus ON ( cTus.channel_master_id = cm.channel_master_id AND cTus.user_master_id=".$user_master_id.")
        //         left join channel_type_master as ctm on ctm.id=cm.type
        //         LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.channel_master_id and  cTenv.type = 11
        //         LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.channel_master_id and  uTpyCont.type = 11 and 	uTpyCont.user_master_id = " . $user_master_id . "
        //         WHERE
        //         cm.status=3 and cm.privacy_status = 0
        //         " . $typeQury . "
        //         " . $channelIds_query . "
        //         " . $envStatus . "";
        // if ($subtype != 0) {
        //     $sql .= "AND cm.type IN (" . $subtype . ")";
        // }
        // if (($spIds != '') && ($spIds != 0)) {
        //     $sql .= "  AND chTs.specialities_id IN (" . $spIds . ")";
        // }
        // if ((isset($category)) && ($category != '')) {
        //     $sql .= " AND cm.type in(" . $category . ")";
        // }
        // if ($records != '') {
        //     $sql .= " AND cm.channel_master_id IN ($records)";
        // }
        // $sql .= " group by cm.channel_master_id,
        //                     cm.title,
        //                     cm.description,
        //                     cm.privacy_status,
        //                     cTus.status,
        //                     cm.cover_image,
        //                     cm.is_share,
        //                     cm.added_on,
        //                     cm.deeplink,
        //                     ctm.type,
        //                     cTenv.price,
        //                     uTpyCont.status,
        //                     cm.gl_deeplink,
        //                     cm.follower_count,
        //                     bnd.logo,
        //                     cln.client_name,
        //                     cln.client_logo
        //         order by  cm.added_on desc ";
        // if ($limitFrom == 0 and $limitTo != '') {
        //     $sql .= "limit " . $limitFrom . " , " . $limitTo;
        // } else {
        //     $sql .= "limit " . $limitFrom . " , " . $limitTo;
        // }


        if ($subtype != 0) {
            $typeQury = "AND cm.type IN (" . $subtype . ")";
        } else {
            $typeQury = "";
        }
        if ($records != '') {
            $channelIds_query = "AND cm.channel_master_id IN ($records)";
        } else {
            $channelIds_query = "";
        }

        if ($spIds != '') {
            $specialitiesQy = "AND chTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        if ($category != '') {
            $categoryQy = "AND cm.type IN (" . $category . ")";
        } else {
            $categoryQy = "";
        }

        $sql = "WITH ChannelData AS (
            SELECT
                cm.channel_master_id,
                cm.title,
                cm.description,
                cm.privacy_status,
                cm.cover_image,
                cm.is_share,
                cm.added_on,
                cm.deeplink,
                cm.gl_deeplink,
                cm.follower_count,
                cm.type,
                cm.client_id,
                cm.brand_id,
                cm.status
            FROM 
                channel_master AS cm
                LEFT JOIN 
                channel_to_specialities AS chTs ON chTs.channel_master_id = cm.channel_master_id
                 
            WHERE
                cm.status = 3 
                AND cm.privacy_status = 0
                {$typeQury}
                {$channelIds_query}
                {$categoryQy}
                {$envStatus}
                {$specialitiesQy}
                -- GROUP BY 
                -- cm.channel_master_id
                ORDER BY 
                cm.added_on desc
                limit {$limitFrom}, {$limitTo}
                
                
        ),
        SpecialitiesData AS (
            SELECT 
                chTs.channel_master_id,
                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name
            FROM 
                channel_to_specialities AS chTs
            JOIN 
                master_specialities_V1 AS ms ON ms.master_specialities_id = chTs.specialities_id
            WHERE 
                chTs.channel_master_id IN (SELECT channel_master_id FROM ChannelData)
           
            GROUP BY 
                chTs.channel_master_id
        ),
        UserFollowData AS (
            SELECT 
                cTus.channel_master_id,
                cTus.status AS sub_stat
            FROM 
                channel_to_user AS cTus
            WHERE 
                cTus.user_master_id = {$user_master_id}
        ),
        FollowerCountData AS (
            SELECT 
                channel_master_id,
                COUNT(DISTINCT(user_master_id)) AS total
            FROM 
                channel_to_user
            WHERE 
                status = 3
            GROUP BY 
                channel_master_id
        )
        
        SELECT
            cd.channel_master_id AS type_id,
            cd.title,
            cd.description,
            cd.privacy_status,
            ufd.sub_stat,
            cd.cover_image,
            cd.is_share,
            cd.added_on,
            cd.deeplink,
            ctm.type AS category_name,
            cTenv.price,
            uTpyCont.status AS user_contnet_payment_status,
            cd.gl_deeplink,
            cd.follower_count,
            bnd.logo,
            cln.client_name,
            fcd.total,
            cln.client_logo
        FROM 
            ChannelData AS cd
        LEFT JOIN 
            SpecialitiesData AS sd ON sd.channel_master_id = cd.channel_master_id
        LEFT JOIN 
            UserFollowData AS ufd ON ufd.channel_master_id = cd.channel_master_id
        LEFT JOIN 
            FollowerCountData AS fcd ON fcd.channel_master_id = cd.channel_master_id
        LEFT JOIN 
            client_master AS cln ON cln.client_master_id = cd.client_id
        LEFT JOIN 
            clirbanner_master_brand AS bnd ON bnd.id = cd.brand_id
        LEFT JOIN 
            channel_type_master AS ctm ON ctm.id = cd.type
        LEFT JOIN 
            content_to_env AS cTenv ON cTenv.type_id = cd.channel_master_id AND cTenv.type = 11
        LEFT JOIN 
            payment_user_to_content AS uTpyCont ON uTpyCont.type_id = cd.channel_master_id AND uTpyCont.type = 11 AND uTpyCont.user_master_id = {$user_master_id}" ;

        // echo $sql;
        // exit;
        $query = $this->db->query($sql);
        $result = $query->result();
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            if ($val->sub_stat == null || $val->sub_stat == '') {
                $fstat = 0;
            } else {
                $fstat = $val->sub_stat;
            }
            $vx[] = array(
                "slno" => $i,
                "type_id" => $val->type_id,
                "is_share" => get_a_content_is_share_status($val->type_id, '11'),
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                //============ integrated for subscription ============//
                "type" => 'channel',
                "category_name" => $val->category_name,
                "privacy_status" => $val->privacy_status,
                "followed_status" => $fstat,
                "added_on" => date(' jS F y', strtotime($val->added_on)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "cover_image" => change_img_src($val->cover_image),
                "logo" => change_img_src($val->client_logo),
                "slug" => str_replace(' ', '-', strip_tags($val->title)), //html_entity_decode(strip_tags($val->title)),
                "description" => html_entity_decode(strip_tags($val->description)),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0) //($val->deeplink != '') ? $val->deeplink : 0,
            );
            $i++;
        }
        return $vx;
    }


    public function channellistWeb(
        $envStatus,
        $category,
        $records,
        $limitTo,
        $limitFrom,
        $user_master_id,
        $spIds,
        $subtype
    ) {
        $vx = array();

        $orderBy = "cm.added_on desc";

        switch ($subtype) {
            case "popular_community":
                $orderBy = "cm.added_on desc";
                //$typeQury = " AND cm.type = 1";
                break;
            case "most_followed":
                $orderBy = "cm.follower_count desc";
                //$typeQury = " AND cm.type = 1";
                break;
            case "followed_channels":
                $orderBy = "cm.added_on desc";
                //$followed = "AND cTus.user_master_id = " . $user_master_id . "";
                //$typeQury = " AND cm.type = 1";
                break;
            case "recommended":
                $orderBy = "cm.added_on desc";
                //$typeQury = " AND cm.type = 1";
                break;
            case "all":
                $orderBy = "cm.added_on desc";
                //$typeQury = " AND cm.type = 1";
                break;
            default:
                //$typeQury = " AND cm.type = 1";
                break;
        }

        $key_locked = get_user_package($user_master_id, 'channel');
        // $sql = "SELECT
        //         cm.channel_master_id as type_id,
        //         cm.title as title,
        //         cm.description as description,
        //         cm.privacy_status,
        //         cTus.status as sub_stat,
        //         cm.cover_image,
        //         cm.is_share,
        //         cm.added_on,
        //         cm.deeplink,
        //         ctm.type as category_name,
        //         cTenv.price,
        //         uTpyCont.status as user_contnet_payment_status,
        //         cm.gl_deeplink,
        //         cm.follower_count,
        //         bnd.logo,
        //         cln.client_name,
        //         (select count(DISTINCT(user_master_id)) as total from channel_to_user where status = 3 and channel_master_id = cm.channel_master_id) as total,
        //         cln.client_logo
        //         FROM channel_master as cm
        //         LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = cm.channel_master_id
        //         LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
        //         LEFT JOIN client_master as cln ON cln.client_master_id = cm.client_id
        //         LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = cm.brand_id
        //         LEFT JOIN channel_to_user  as cTus ON ( cTus.channel_master_id = cm.channel_master_id AND cTus.user_master_id=".$user_master_id.")
        //         left join channel_type_master as ctm on ctm.id=cm.type
        //         LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.channel_master_id and  cTenv.type = 11
        //         LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.channel_master_id and  uTpyCont.type = 11 and 	uTpyCont.user_master_id = " . $user_master_id . "
        //         WHERE
        //         cm.status=3
        //         and
        //         cm.privacy_status = 0
        //         " . $typeQury . "
        //         " . $channelIds_query . "
        //         " . $envStatus . "
        //         " . $followed ." ";
        // // if ($subtype != 0) {
        // //     $sql .= "AND cm.type IN (" . $subtype . ")";
        // // }
        // if (($spIds != '') && ($spIds != 0)) {
        //     $sql .= "  AND chTs.specialities_id IN (" . $spIds . ")";
        // }
        // if ((isset($category)) && ($category != '')) {
        //     $sql .= " AND cm.type in(" . $category . ")";
        // }
        // if ($records != '') {
        //     $sql .= " AND cm.channel_master_id IN ($records)";
        // }
        // $sql .= " group by cm.channel_master_id,
        //     cm.title,
        //     cm.description,
        //     cm.privacy_status,
        //     cTus.status,
        //     cm.cover_image,
        //     cm.is_share,
        //     cm.added_on,
        //     cm.deeplink,
        //     ctm.type,
        //     cTenv.price,
        //     uTpyCont.status,
        //     cm.gl_deeplink,
        //     cm.follower_count,
        //     bnd.logo,
        //     cln.client_name,
        //     cln.client_logo
        //     order by  ".$orderBy." ";
        // if ($limitFrom != '' && $limitTo != '') {
        //     $sql .= "limit " . $limitFrom . " , " . $limitTo;
        // } else {
        //     $sql .= "limit 0,5";
        // }






        // if ($subtype != 0) {
        //     $typeQury = "AND cm.type IN (" . $subtype . ")";
        // } else {
        //     $typeQury = "";
        // }
        if ($records != '') {
            $channelIds_query = "AND cm.channel_master_id IN ($records)";
        } else {
            $channelIds_query = "";
        }

        if ($spIds != '') {
            $specialitiesQy = "AND chTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        if ($category != '') {
            $categoryQy = "AND cm.type IN (" . $category . ")";
        } else {
            $categoryQy = "";
        }




        $sql = "WITH ChannelData AS (
            SELECT
                cm.channel_master_id,
                cm.title,
                cm.description,
                cm.privacy_status,
                cm.cover_image,
                cm.is_share,
                cm.added_on,
                cm.deeplink,
                cm.gl_deeplink,
                cm.follower_count,
                cm.type,
                cm.client_id,
                cm.brand_id,
                cm.status
            FROM 
                channel_master AS cm
                LEFT JOIN 
                channel_to_specialities AS chTs ON chTs.channel_master_id = cm.channel_master_id
                " . ($subtype == "followed_channels" ?
                "JOIN
                 channel_to_user AS ctu ON ctu.channel_master_id = cm.channel_master_id AND ctu.user_master_id = {$user_master_id} AND ctu.status = 3" :
               "LEFT JOIN channel_to_user AS ctu ON ctu.channel_master_id = cm.channel_master_id  AND ctu.status = 3") . "
            WHERE
                cm.status = 3 
                AND cm.privacy_status = 0
                " . ($subtype == "followed_channels" ? "AND ctu.channel_master_id IS NOT NULL" : "") . "
                
                {$channelIds_query}
                {$categoryQy}
                {$envStatus}
                {$specialitiesQy}
                GROUP BY 
                cm.channel_master_id
                ORDER BY 
                {$orderBy}
                limit {$limitFrom}, {$limitTo}
        ),
        SpecialitiesData AS (
            SELECT 
                chTs.channel_master_id,
                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name
            FROM 
                channel_to_specialities AS chTs
            JOIN 
                master_specialities_V1 AS ms ON ms.master_specialities_id = chTs.specialities_id
            WHERE 
                chTs.channel_master_id IN (SELECT channel_master_id FROM ChannelData)
            GROUP BY 
                chTs.channel_master_id
        ),
        UserFollowData AS (
            SELECT 
                cTus.channel_master_id,
                cTus.status AS sub_stat
            FROM 
                channel_to_user AS cTus
            WHERE 
                cTus.user_master_id = {$user_master_id}
        ),
        FollowerCountData AS (
            SELECT 
                channel_master_id,
                COUNT(DISTINCT(user_master_id)) AS total
            FROM 
                channel_to_user
            WHERE 
                status = 3
            GROUP BY 
                channel_master_id
        )
        
        SELECT
            cd.channel_master_id AS type_id,
            cd.title,
            cd.description,
            cd.privacy_status,
            ufd.sub_stat,
            cd.cover_image,
            cd.is_share,
            cd.added_on,
            cd.deeplink,
            ctm.type AS category_name,
            cTenv.price,
            uTpyCont.status AS user_contnet_payment_status,
            cd.gl_deeplink,
            cd.follower_count,
            bnd.logo,
            cln.client_name,
            fcd.total,
            cln.client_logo,
            sd.specialities_name
        FROM 
            ChannelData AS cd
        LEFT JOIN 
            SpecialitiesData AS sd ON sd.channel_master_id = cd.channel_master_id
        LEFT JOIN 
            UserFollowData AS ufd ON ufd.channel_master_id = cd.channel_master_id
        LEFT JOIN 
            FollowerCountData AS fcd ON fcd.channel_master_id = cd.channel_master_id
        LEFT JOIN 
            client_master AS cln ON cln.client_master_id = cd.client_id
        LEFT JOIN 
            clirbanner_master_brand AS bnd ON bnd.id = cd.brand_id
        LEFT JOIN 
            channel_type_master AS ctm ON ctm.id = cd.type
        LEFT JOIN 
            content_to_env AS cTenv ON cTenv.type_id = cd.channel_master_id AND cTenv.type = 11
        LEFT JOIN 
            payment_user_to_content AS uTpyCont ON uTpyCont.type_id = cd.channel_master_id AND uTpyCont.type = 11 AND uTpyCont.user_master_id = {$user_master_id}
        GROUP BY 
            cd.channel_master_id
        " ;

        // echo $sql;
        // exit;

        $query = $this->db->query($sql);
        $result = $query->result();
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            if ($val->sub_stat == null || $val->sub_stat == '') {
                $fstat = 0;
            } else {
                $fstat = $val->sub_stat;
            }
            $vx[] = array(
                "slno" => $i,
                "type_id" => $val->type_id,
                "is_share" => get_a_content_is_share_status($val->type_id, '11'),
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                //============ integrated for subscription ============//
                "type" => 'channel',
                "category_name" => $val->category_name,
                "privacy_status" => $val->privacy_status,
                "followed_status" => $fstat,
                "follower_count" => ($val->total + $val->follower_count),
                "added_on" => date(' jS F y', strtotime($val->added_on)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "cover_image" => change_img_src($val->cover_image),
                "logo" => change_img_src($val->client_logo),
                "slug" => str_replace(' ', '-', strip_tags($val->title)),
                //html_entity_decode(strip_tags($val->title)),
                "description" => html_entity_decode(strip_tags($val->description)),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0)
                //($val->deeplink != '') ? $val->deeplink : 0,
            );
            $i++;
        }
        return $vx;
    }


    /**
     * @param $user_master_id
     * @param $limitFromFn
     * @param $limitToFn
     * @return array
     */
    public function getvid_v1(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $spIds,
        $type_id
    ) {
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        //$this->db->cache_on();
        if ($spIds != '') {
            $specialitiesQy = "  AND cmTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        if ($type_id != '') {
            $removetypeid = "NOT cm.video_archive_id='" . $type_id . "' and ";
        } else {
            $removetypeid = " ";
        }
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'video_archived');
        // ====================== fetching env_id  ======================//
        $sql = "SELECT
        cm.video_archive_id as type_id,
        cm.video_archive_question,
        cm.video_archive_answer,
        cm.video_archive_question_raw,
        cm.video_archive_answer_raw,
        cm.video_archive_file_img,
        cm.video_archive_file_img_thumbnail,
        cm.deeplink,
        cTenv.price,
        uTpyCont.status as user_contnet_payment_status,
        cm.added_on,
        cm.publication_date,
        cln.client_name,
        cln.client_logo,
        cm.type,
        cm.vendor,
        cm.src,
        ks.session_doctor_id,
        msct.category_name,
		msct.category_logo,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        cm.video_archive_speciality_id ,
        kv.status as vault,
        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.video_archive_id and  rt.post_type='video_archive' and rt.rating!=0) as averageRating,
        rtmy.rating  as myrating
        FROM knwlg_video_archive as cm
        JOIN video_archive_to_specialities  as cmTs ON cmTs.video_archive_id = cm.video_archive_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
        LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
        LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = cm.category_id
        LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.video_archive_id and  cTenv.type = 3
        LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.video_archive_id and  uTpyCont.type = 3 and 	uTpyCont.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id and  kv.type_text='video_archive' and  kv.user_id = " . $user_master_id . "
        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id and  rtmy.post_type='video_archive' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id and  rt.post_type='video_archive'
        JOIN client_master as cln ON cln.client_master_id = cm.client_id
        WHERE " . $removetypeid . "
        cm.status=3
        and cm.privacy_status =0 AND cm.publication_date <= CURDATE()
        " . $specialitiesQy . "
        " . $envStatus . "
        GROUP BY cm.video_archive_id,
                    cm.video_archive_question,
                    cm.video_archive_answer,
                    cm.video_archive_question_raw,
                    cm.video_archive_answer_raw,
                    cm.video_archive_file_img,
                    cm.video_archive_file_img_thumbnail,
                    cm.deeplink,
                    cTenv.price,
                    uTpyCont.status,
                    cm.added_on,
                    cm.publication_date,
                    cln.client_name,
                    cln.client_logo,
                    cm.type,
                    cm.vendor,
                    cm.src,
                    ks.session_doctor_id,
                    msct.category_name,
                    msct.category_logo,
                    cm.video_archive_speciality_id,
                    kv.status,
                    rtmy.rating
        order by cm.publication_date DESC
        " . $limit . "";
        //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
        //echo $sql; exit;
        //and
        //cm.publication_date <= CURDATE()
        $query = $this->db->query($sql);
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            if ($val->video_archive_file_img) {
                $img = $val->video_archive_file_img;
            } else {
                $img = '';
            }
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $session_doc_array = explode(",", $val->session_doctor_id);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                    $logic_image = $var[0]['profile_image'];
                } else {
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    if ($image) {
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            //$image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            // $logic_image_path = docimg;//"uploads/docimg/" . $image;
                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = docimg; //$imgPr;
                            //$logic_image = $var[0]['profile_image'];
                        }
                        //$logic_image = $image;
                    } else {
                        $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                    }
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(
                "slno" => $i,
                "trending_type" => "video_archive",
                "type" => "video_archive",
                "con_type" => $val->type,
                "type_id" => $val->type_id,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "type_id" => $val->type_id,
                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "category_name" => $val->category_name,
                "category_logo" => change_img_src($val->category_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->type_id, 3, $user_master_id),
                //============ integrated for subscription ============//
                "comment_count" => $val->count_comment,
                "deeplink" => $val->deeplink,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
            );
            $i++;
        }
        return $vx;
    }
    /**
     * @param $user_master_id
     */
    public function getcomp_v1(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $spIds,
        $type_id
    ) {
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        //$this->db->cache_on();
        if ($type_id != '') {
            $removetypeid = "NOT cm.comp_qa_id='" . $type_id . "' and ";
        } else {
            $removetypeid = " ";
        }
        if ($spIds != '') {
            $specialitiesQy = "  AND cmTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'comp');
        // ====================== fetching env_id  ======================//
        $sql = "SELECT
        cm.comp_qa_id as type_id,
        cm.comp_qa_question ,
        cm.comp_qa_answer ,
        cm.comp_qa_answer_raw as description,
        cm.comp_qa_question_raw as title,
        cm.comp_qa_file_img,
        cm.comp_qa_file_img_thumbnail,
        cm.added_on,
        cm.publication_date as publish_date,
        cln.client_name,
        cln.client_logo,
        cm.color,
        cm.type,
        cm.vendor,
        cm.src,
        cm.deeplink,
        cTenv.price,
        uTpyCont.status as user_contnet_payment_status,
        kv.status as vault,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        max( ms.rank) as maxrank,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsorCM,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logoCM,
        cm.comp_qa_speciality_id
        FROM knwlg_compendium_V1 as cm
        JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        JOIN client_master as cln ON cln.client_master_id = cm.client_id
        LEFT JOIN compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
        LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id and  cTenv.type = 1
        LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.comp_qa_id and  uTpyCont.type = 1 and 	uTpyCont.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and  kv.type_text='comp' and  kv.user_id = " . $user_master_id . "
        WHERE " . $removetypeid . "
        cm.status=3
        " . $specialitiesQy . "
        " . $envStatus . "
        and
        cm.publication_date < NOW() - INTERVAL 3 DAY
        and cm.privacy_status = 0
        group by cm.comp_qa_id,
                cm.comp_qa_question,
                cm.comp_qa_answer,
                cm.comp_qa_answer_raw,
                cm.comp_qa_question_raw,
                cm.comp_qa_file_img,
                cm.comp_qa_file_img_thumbnail,
                cm.added_on,
                cm.publication_date,
                cln.client_name,
                cln.client_logo,
                cm.color,
                cm.type,
                cm.vendor,
                cm.src,
                cm.deeplink,
                cTenv.price,
                uTpyCont.status,
                kv.status,
                cm.comp_qa_speciality_id
        order by  cm.comp_qa_id desc  " . $limit . "";
        #echo $sql; exit;
        //exit; , maxrank DESC
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            if ($val->comp_qa_file_img_thumbnail) {
                $img = $val->comp_qa_file_img_thumbnail; //base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
            } else {
                $img = '';
            }
            $sponsorCMLogoArry = explode(",", $val->sponsor_logoCM);
            if (count($sponsorCMLogoArry) > 0) {
                foreach ($sponsorCMLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorCMLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logoCM) {
                    $sponsorCMLogomix[] = '' . $val->sponsor_logoCM;
                }
            }
            $sponsorCMLogo = implode(",", (array)$sponsorCMLogomix);
            unset($sponsorCMLogomix);
            unset($sponsorCMLogoArry);
            $string = htmlentities($val->title, null, 'utf-8');
            //$string = html_entity_decode($string);
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(
                "slno" => $i,
                "trending_type" => "comp",
                "type" => "comp",
                "con_type" => $val->type,
                "vendor" => $val->vendor,
                "src" => $val->src_cm,
                "sponsor_name" => $val->sponsorCM,
                "sponsor_logo" => change_img_src($sponsorCMLogo),
                "type_id" => $val->type_id,
                //"type" => $val->type,
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                "imageF" => change_img_src($val->comp_qa_file_img_thumbnail),
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->type_id, 1, $user_master_id),
                //============ integrated for subscription ============//
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            $i++;
        }
        return $vx;
    }
    /**
     * @param $user_master_id
     */
    public function getcomp(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $spIds
    ) {
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        //$this->db->cache_on();
        if ($spIds != '') {
            $specialitiesQy = "  AND cmTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        $sql = "SELECT
        cm.comp_qa_id as type_id,
        cm.comp_qa_question ,
        cm.comp_qa_answer ,
        cm.comp_qa_answer_raw as description,
        cm.comp_qa_question_raw as title,
        cm.comp_qa_file_img,
        cm.comp_qa_file_img_thumbnail,
        cm.added_on,
        cm.publication_date as publish_date,
        cln.client_name,
        cln.client_logo,
        cm.color,
        cm.type,
        cm.vendor,
        cm.src,
        cm.deeplink,
        kv.status as vault,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        max( ms.rank) as maxrank,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsorCM,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logoCM,
        cm.comp_qa_speciality_id
        FROM knwlg_compendium_V1 as cm
        JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        JOIN client_master as cln ON cln.client_master_id = cm.client_id
        LEFT JOIN compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and  kv.type_text='comp' and  kv.user_id = " . $user_master_id . "
        WHERE
        cm.status=3
        " . $specialitiesQy . "
        and
        cm.publication_date < NOW() - INTERVAL 3 DAY
        and cm.privacy_status = 0
        group by
                cm.comp_qa_id,
                cm.comp_qa_question,
                cm.comp_qa_answer,
                cm.comp_qa_answer_raw,
                cm.comp_qa_question_raw,
                cm.comp_qa_file_img,
                cm.comp_qa_file_img_thumbnail,
                cm.added_on,
                cm.publication_date,
                cln.client_name,
                cln.client_logo,
                cm.color,
                cm.type,
                cm.vendor,
                cm.src,
                cm.deeplink,
                kv.status,
                cm.comp_qa_speciality_id
        order by  cm.comp_qa_id desc  " . $limit . "";
        #echo $sql; exit;
        //exit; , maxrank DESC
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            if ($val->comp_qa_file_img_thumbnail) {
                $img = $val->comp_qa_file_img_thumbnail; //base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
            } else {
                $img = '';
            }
            $sponsorCMLogoArry = explode(",", $val->sponsor_logoCM);
            if (count($sponsorCMLogoArry) > 0) {
                foreach ($sponsorCMLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorCMLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logoCM) {
                    $sponsorCMLogomix[] = '' . $val->sponsor_logoCM;
                }
            }
            $sponsorCMLogo = implode(",", (array)$sponsorCMLogomix);
            unset($sponsorCMLogomix);
            unset($sponsorCMLogoArry);
            $string = htmlentities($val->title, null, 'utf-8');
            //$string = html_entity_decode($string);
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(
                "slno" => $i,
                "trending_type" => "comp",
                "type" => "comp",
                "con_type" => $val->type,
                "vendor" => $val->vendor,
                "src" => $val->src_cm,
                "sponsor_name" => $val->sponsorCM,
                "sponsor_logo" => change_img_src($sponsorCMLogo),
                "type_id" => $val->type_id,
                //"type" => $val->type,
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                "imageF" => change_img_src($val->comp_qa_file_img_thumbnail),
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            $i++;
        }
        return $vx;
    }
    /**
     * @param $user_master_id
     * @param $limitFromFn
     * @param $limitToFn
     * @return array
     */
    public function getvid(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $spIds
    ) {
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        //$this->db->cache_on();
        if ($spIds != '') {
            $specialitiesQy = "  AND cmTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        $sql = "SELECT
        cm.video_archive_id as type_id,
        cm.video_archive_question,
        cm.video_archive_answer,
        cm.video_archive_question_raw,
        cm.video_archive_answer_raw,
        cm.video_archive_file_img,
        cm.video_archive_file_img_thumbnail,
        cm.deeplink,
        cm.added_on,
        cm.publication_date,
        cln.client_name,
        cln.client_logo,
        cm.type,
        cm.vendor,
        cm.src,
        ks.session_doctor_id,
        msct.category_name,
		msct.category_logo,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        cm.video_archive_speciality_id ,
        kv.status as vault,
        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.video_archive_id and  rt.post_type='video_archive' and rt.rating!=0) as averageRating,
        rtmy.rating  as myrating
        FROM knwlg_video_archive as cm
        JOIN video_archive_to_specialities  as cmTs ON cmTs.video_archive_id = cm.video_archive_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
        LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
        LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id and  kv.type_text='video_archive' and  kv.user_id = " . $user_master_id . "
        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id and  rtmy.post_type='video_archive' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id and  rt.post_type='video_archive'
        JOIN client_master as cln ON cln.client_master_id = cm.client_id
        WHERE
        cm.status=3
        and cm.privacy_status =0
        " . $specialitiesQy . "
        GROUP BY cm.video_archive_id,
         cm.video_archive_question,
         cm.video_archive_answer,
         cm.video_archive_question_raw,
         cm.video_archive_answer_raw,
         cm.video_archive_file_img,
         cm.video_archive_file_img_thumbnail,
         cm.deeplink,
         cm.added_on,
         cm.publication_date,
         cln.client_name,
         cln.client_logo,
         cm.type,
         cm.vendor,
         cm.src,
         ks.session_doctor_id,
         msct.category_name,
         msct.category_logo,
         cm.video_archive_speciality_id,
         kv.status,
         rtmy.rating
        order by cm.publication_date DESC
        " . $limit . "";
        //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
        #echo $sql; exit;
        //and
        //cm.publication_date <= CURDATE()
        $query = $this->db->query($sql);
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            if ($val->video_archive_file_img) {
                $img = $val->video_archive_file_img;
            } else {
                $img = '';
            }
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $session_doc_array = explode(",", $val->session_doctor_id);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                    $logic_image = $var[0]['profile_image'];
                } else {
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    if ($image) {
                        if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                            $logic_image = $var[0]['profile_image'];
                        } else {
                            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            // $logic_image_path = docimg;//"uploads/docimg/" . $image;
                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = docimg; //$imgPr;
                            //$logic_image = $var[0]['profile_image'];
                        }
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        // $logic_image = $imgPr;
                    } else {
                        $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                    }
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(
                "slno" => $i,
                "trending_type" => "video_archive",
                "type" => "video_archive",
                "con_type" => $val->type,
                "type_id" => $val->type_id,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "type_id" => $val->type_id,
                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "category_name" => $val->category_name,
                "category_logo" => change_img_src($val->category_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "comment_count" => $val->count_comment,
                "deeplink" => $val->deeplink,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
            );
            $i++;
        }
        return $vx;
    }
    /**
     * @param $user_master_id
     */
    public function getspq(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $spIds
    ) {
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        if ($spIds != '') {
            $specialitiesQy = "  AND svts.speciality_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        $sqlCompl = "SELECT
        sv.*
        FROM
        survey_user_answer sv
        WHERE
        sv.user_master_id = '" . $user_master_id . "'";
        $queryCompl = $this->db->query($sqlCompl);
        $resultCompl = $queryCompl->result();
        $complID = array();
        foreach ($resultCompl as $valCompl) {
            $complID[] = $valCompl->survey_id;
        }
        //print_r($complID); exit;
        $sqlInCompl = "SELECT
        sv.*
        FROM
        survey_user_incomplete_answer sv
        WHERE
        sv.status = 3
        and
        sv.user_master_id = '" . $user_master_id . "'";
        $queryInCompl = $this->db->query($sqlInCompl);
        $resultInCompl = $queryInCompl->result();
        $incomplID = array();
        foreach ($resultInCompl as $valInCompl) {
            $incomplID[] = $valInCompl->survey_id;
        }
        $arrayFinal = array_unique(array_merge($complID, $incomplID));
        //print_r($arrayFinal); exit;
        $complIDStr = implode(",", (array)$arrayFinal);
        //echo $complIDStr ; exit;
        if ($complIDStr) {
            $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
        } else {
            $qryStr = '';
        }
        //$this->db->cache_on();
        $sql = "SELECT
                sv.survey_id,
                sv.category,
                sv.survey_title,
                sv.survey_description,
                sv.image,
                sv.survey_points,
                sv.survey_time,
                sv.question_count,
                sv.publishing_date,
                sv.client_id,
                sv.sponsor_ids,
                sv.deeplink,
                sv.gl_deeplink,
                sv.verified,
                sv.display_in_dashboard,
                sv.privacy_status,
                sv.template_id,
                sv.color,
                sv.points_on_approval,
                sv.added_on,
                sv.added_by,
                sv.modified_on,
                sv.modified_by,
                sv.status,
                sv.is_available_survey_portal,
                sv.available_for_live_session,
                sv.env,
                sv.is_share,
                sv.is_like,
                sv.is_comment,
                sv.approved_by,
                sv.img_credits,
                svd.data,
                cln.client_name,
                cln.client_logo,
                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
            FROM survey sv
            LEFT JOIN survey_to_speciality svts ON svts.survey_id = sv.survey_id
            LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = svts.speciality_id
            JOIN client_master cln ON cln.client_master_id = sv.client_id
            LEFT JOIN survey_to_sponsor suvTspon ON suvTspon.survey_id = sv.survey_id
            LEFT JOIN client_master clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
            JOIN survey_detail svd ON svd.survey_id = sv.survey_id
            LEFT JOIN survey_user_answer sua ON sua.survey_id = sv.survey_id
            WHERE
                sv.status = 3
                " . $specialitiesQy . "
                AND sv.category = 'quiz'
                AND sv.display_in_dashboard = 1
                AND sv.privacy_status = 0
                " . $qryStr . "
            GROUP BY
                sv.survey_id,
                sv.category,
                sv.survey_title,
                sv.survey_description,
                sv.image,
                sv.survey_points,
                sv.survey_time,
                sv.question_count,
                sv.publishing_date,
                sv.client_id,
                sv.sponsor_ids,
                sv.deeplink,
                sv.gl_deeplink,
                sv.verified,
                sv.display_in_dashboard,
                sv.privacy_status,
                sv.template_id,
                sv.color,
                sv.points_on_approval,
                sv.added_on,
                sv.added_by,
                sv.modified_on,
                sv.modified_by,
                sv.status,
                sv.is_available_survey_portal,
                sv.available_for_live_session,
                sv.env,
                sv.is_share,
                sv.is_like,
                sv.is_comment,
                sv.approved_by,
                sv.img_credits,
                svd.data,
                cln.client_name,
                cln.client_logo
            ORDER BY sv.publishing_date DESC " . $limit . "";
        #echo $sql; exit;
        //exit;
        //" . $limit . ";
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;1
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        $vx = array();
        foreach ($result as $val) {
            $dataArry = unserialize($val->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $vx[] = array(
                "survey_id" => $val->survey_id,
                "type_id" => $val->survey_id,
                "trending_type" => "survey",
                "type" => "survey",
                "survey_time" => $val->survey_time,
                "question_count" => $val->question_count,
                "category" => $val->category,
                "point" => $val->survey_points,
                "json_data" => $str,
                "survey_title" => $val->survey_title,
                "deeplink" => $val->deeplink,
                "survey_description" => substr($val->survey_description, 0, 150),
                "image" => change_img_src($val->image),
                "specialities_name" => $val->specialities_name,
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "publishing_date" => $val->publishing_date,
            );
        }
        return $vx;
    }
    /**
     * @param $user_master_id
     */
    public function getspq_v1(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $spIds,
        $type_id
    ) {
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        if ($type_id != '') {
            $removetypeid = "NOT sv.survey_id='" . $type_id . "' and ";
        } else {
            $removetypeid = " ";
        }
        if ($spIds != '') {
            $specialitiesQy = "  AND svts.speciality_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'survey');
        // ====================== fetching env_id  ======================//
        $sqlCompl = "SELECT
            sv.*
            FROM
            survey_user_answer sv
            WHERE
            sv.user_master_id = '" . $user_master_id . "'";
        $queryCompl = $this->db->query($sqlCompl);
        $resultCompl = $queryCompl->result();
        $complID = array();
        foreach ($resultCompl as $valCompl) {
            $complID[] = $valCompl->survey_id;
        }
        //print_r($complID); exit;
        $sqlInCompl = "SELECT
            sv.*
            FROM
            survey_user_incomplete_answer sv
            WHERE
            sv.status = 3
            and
            sv.user_master_id = '" . $user_master_id . "'";
        $queryInCompl = $this->db->query($sqlInCompl);
        $resultInCompl = $queryInCompl->result();
        $incomplID = array();
        foreach ($resultInCompl as $valInCompl) {
            $incomplID[] = $valInCompl->survey_id;
        }
        $arrayFinal = array_unique(array_merge(array_filter($complID), array_filter($incomplID)));
        // $arrayFinal = array_unique(array_merge($complID, $incomplID));
        //print_r($arrayFinal); exit;
        $complIDStr = implode(",", (array)$arrayFinal);
        //echo $complIDStr ; exit;
        if ($complIDStr) {
            $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
        } else {
            $qryStr = '';
        }
        //$this->db->cache_on();
        $sql = "SELECT
                sv.survey_id,
                sv.category,
                sv.survey_title,
                sv.survey_description,
                sv.image,
                sv.survey_points,
                sv.survey_time,
                sv.question_count,
                sv.publishing_date,
                sv.client_id,
                sv.sponsor_ids,
                sv.deeplink,
                sv.gl_deeplink,
                sv.verified,
                sv.display_in_dashboard,
                sv.privacy_status,
                sv.template_id,
                sv.color,
                sv.points_on_approval,
                sv.added_on,
                sv.added_by,
                sv.modified_on,
                sv.modified_by,
                sv.status,
                sv.is_available_survey_portal,
                sv.available_for_live_session,
                sv.env,
                sv.is_share,
                sv.is_like,
                sv.is_comment,
                sv.approved_by,
                sv.img_credits,
                svd.data,
                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                cln.client_name,
                cln.client_logo,
                cTenv.price,
                uTpyCont.status AS user_contnet_payment_status,
                GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
            FROM survey sv
            LEFT JOIN survey_to_speciality AS svts ON svts.survey_id = sv.survey_id
            LEFT JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = svts.speciality_id
            JOIN client_master AS cln ON cln.client_master_id = sv.client_id
            LEFT JOIN survey_to_sponsor AS suvTspon ON suvTspon.survey_id = sv.survey_id
            LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
            LEFT JOIN content_to_env AS cTenv ON cTenv.type_id = sv.survey_id AND cTenv.type = 6
            LEFT JOIN payment_user_to_content AS uTpyCont ON uTpyCont.type_id = sv.survey_id
                AND uTpyCont.type = 6
                AND uTpyCont.user_master_id = " . $user_master_id . "
            JOIN survey_detail AS svd ON svd.survey_id = sv.survey_id
            LEFT JOIN survey_user_answer AS sua ON sua.survey_id = sv.survey_id
            WHERE " . $removetypeid . "
                sv.status = 3
                " . $specialitiesQy . "
                " . $envStatus . "
                AND sv.category = 'quiz'
                AND DATE(sv.publishing_date) <= CURDATE()
                AND sv.display_in_dashboard = 1
                AND sv.privacy_status = 0
                " . $qryStr . "
            GROUP BY
                sv.survey_id,
                sv.category,
                sv.survey_title,
                sv.survey_description,
                sv.image,
                sv.survey_points,
                sv.survey_time,
                sv.question_count,
                sv.publishing_date,
                sv.client_id,
                sv.sponsor_ids,
                sv.deeplink,
                sv.gl_deeplink,
                sv.verified,
                sv.display_in_dashboard,
                sv.privacy_status,
                sv.template_id,
                sv.color,
                sv.points_on_approval,
                sv.added_on,
                sv.added_by,
                sv.modified_on,
                sv.modified_by,
                sv.status,
                sv.is_available_survey_portal,
                sv.available_for_live_session,
                sv.env,
                sv.is_share,
                sv.is_like,
                sv.is_comment,
                sv.approved_by,
                sv.img_credits,
                svd.data,
                cln.client_name,
                cln.client_logo,
                cTenv.price,
                uTpyCont.status
            " . $limit . "";
        #echo $sql; exit;
        //exit;
        //" . $limit . ";
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;1
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        $vx = array();
        foreach ($result as $val) {
            $dataArry = unserialize($val->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $vx[] = array(
                "survey_id" => $val->survey_id,
                "type_id" => $val->survey_id,
                "trending_type" => "survey",
                "type" => "survey",
                "survey_time" => $val->survey_time,
                "question_count" => $val->question_count,
                "category" => $val->category,
                "point" => $val->survey_points,
                "json_data" => $str,
                "survey_title" => $val->survey_title,
                "deeplink" => $val->deeplink,
                "survey_description" => substr($val->survey_description, 0, 150),
                "image" => change_img_src($val->image),
                "specialities_name" => $val->specialities_name,
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                //get_user_content_status($val->type_id, 6, $user_master_id),
                //============ integrated for subscription ============//
                "publishing_date" => $val->publishing_date,
            );
        }
        return $vx;
    }
    /**
     * @param $user_master_id
     */
    public function getgr(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $spIds
    ) {
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        if ($spIds != '') {
            $specialitiesQy = "  AND grTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'gr');
        // ====================== fetching env_id  ======================//
        $sql = "SELECT
                    gr.gr_id as type_id,
                    gr.gr_title as title,
                    gr.gr_description as description,
                    gr.gr_chief_scientific_editor,
                    gr.gr_preview_image,
                    gr.added_on,
                    gr.gr_date_of_publication as publish_date,
                    gr.deeplink,
                    gr.color,
                    cTenv.price,
                    uTpyCont.status as user_contnet_payment_status,
                    cln.client_name,
                    cln.client_logo,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id) as session_doctor_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM knwlg_gr_register as gr
                JOIN gr_to_specialities as grTs ON grTs.gr_id = gr.gr_id
                JOIN master_specialities_V1 as ms ON ms.master_specialities_id = grTs.specialities_id
                JOIN client_master as cln ON cln.client_master_id = gr.client_id
                LEFT JOIN gr_to_sponsor as grTspon ON grTspon.gr_id = gr.gr_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = grTspon.sponsor_id
                LEFT JOIN gr_to_session_doctor as grTsdoc ON grTsdoc.gr_id = gr.gr_id
                LEFT JOIN content_to_env as cTenv ON cTenv.type_id = gr.gr_id AND cTenv.type = 5
                LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = gr.gr_id AND uTpyCont.type = 5 AND uTpyCont.user_master_id = " . $user_master_id . "
                WHERE
                    gr.status = 3
                    AND DATE(gr.gr_date_of_publication) <= CURDATE()
                    " . $specialitiesQy . "
                    " . $envStatus . "
                    AND gr.privacy_status = 0
                GROUP BY
                    gr.gr_id,
                    gr.gr_title,
                    gr.gr_description,
                    gr.gr_chief_scientific_editor,
                    gr.gr_preview_image,
                    gr.added_on,
                    gr.gr_date_of_publication,
                    gr.deeplink,
                    gr.color,
                    cTenv.price,
                    uTpyCont.status,
                    cln.client_name,
                    cln.client_logo
                ORDER BY
                    gr.gr_date_of_publication DESC
                    " . $limit . "";
        #echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {
                    $var = session_doc_detail($single_doctor);
                    //$image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                        $logic_image = base_url() . "uploads/docimg/" . $image;
                    } else {
                        $logic_image = base_url() . "uploads/docimg/MConsult.png";
                    }*/
                    if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                        $logic_image = $var[0]['profile_image'];
                    } else {
                        // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = docimg; //$imgPr;
                        //$logic_image = $var[0]['profile_image'];
                    }
                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }
            $vx[] = array(
                "slno" => $i,
                "type_id" => $val->type_id,
                "trending_type" => "gr",
                "type" => 'gr',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->gr_preview_image),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                //get_user_content_status($val->type_id, 5, $user_master_id),
                //============ integrated for subscription ============//
                "session_doctor_entities" => $ses_doc_det_array,
                "comment_count" => $val->count_comment,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            $i++;
        }
        return $vx;
    }
    /**
     * @param string $booked_id
     * @param string $user_master_id
     * @return array
     */
    public function getsession(
        $user_master_id = '',
        $limitFromFn,
        $limitToFn,
        $spIds
    ) {
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        $booked_id = $this->all_bookedmastersession($user_master_id);
        $new_arr = array();
        foreach ($booked_id as $sing) {
            $new_arr[] = $sing->knwlg_sessions_id;
        }
        $res_arr = implode(',', $new_arr);
        if ($res_arr) {
            $sqlStr = "and ( (cm.session_id  NOT IN(" . $res_arr . ") AND cm.start_datetime >= '" . date("Y-m-d H:i:s") . "') or cm.start_datetime >= '" . date("Y-m-d H:i:s") . "') ";
        } else {
            $sqlStr = "and (cm.start_datetime >= '" . date("Y-m-d H:i:s") . "')";
        }
        //echo $res_arr; exit;
        $getchildsession = $this->get_all_childsession();
        if (!empty($getchildsession)) {
            $cids = implode(",", (array)$getchildsession['sessions']);
            $childsessionids = " and cm.session_id NOT IN (" . $cids . ")";
        } else {
            $childsessionids = "";
        }
        if ($spIds != '') {
            $specialitiesQy = "  AND sTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        // ====================== fetching env_id  ======================//
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'session');
        // ====================== fetching env_id  ======================//
        //$this->db->cache_on(); session_to_specialities
        //LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
        $sql = "SELECT
                ksp.participant_id,
                ks.session_id AS ks_session_id,
                ks.session_doctor_id,
                ks.session_topic,
                ks.session_description,
                ks.sessions_question,
                ks.master_tag_ids,
                ks.client_id,
                ks.sponsor_id,
                ks.user_group_id,
                ks.category_id,
                ks.start_datetime,
                ks.end_datetime,
                ks.speciality_id,
                ks.total_seats,
                ks.total_buffer,
                ks.add_question_buffer_days,
                ks.session_link,
                ks.master_conf_provider_id,
                ks.session_access_code,
                ks.deeplink,
                ks.in_deeplink,
                ks.gl_deeplink,
                ks.template_id,
                ks.cert_template_id,
                ks.display_in_dashboard,
                ks.conf_phone_no,
                ks.privacy_status,
                ks.color,
                ks.added_on,
                ks.added_by,
                ks.session_status,
                ks.cover_image,
                ks.modified_on,
                ks.modified_by,
                ks.is_recommended,
                ks.is_multiday_session,
                ks.break_json,
                ks.status,
                ks.is_featured,
                ks.rating_flag,
                ks.remarks,
                ks.crm_id,
                ks.img_credits,
                ks.session_json,
                ks.certified,
                ks.env,
                ks.notification_template,
                ks.shortlink,
                ks.invitefile,
                ks.exitroute,
                ks.is_share,
                ks.is_like,
                ks.is_comment,
                sd.knwlg_sessions_docs_id,
                sd.knwlg_sessions_id,
                sd.added_on,
                sd.added_by,
                sd.modified_on,
                sd.modified_by,
                sd.updated_at,
                sd.updated_by,
                sd.status,
                cln.client_name,
                cln.client_logo,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names,
                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                msct.category_name,
                msct.category_logo,
                stci.id,
                stci.session_id,
                stci.cover_image1,
                stci.cover_image2,
                stci.cover_image3,
                stci.cover_image4,
                stci.cover_image5,
                stci.created_at,
                stci.updated_at,
                stci.created_by,
                stci.updated_by,
                sd.document_path,
                sd.comment,
                ksd.knwlg_sessions_docs_id,
                ksd.document_path,
                ksd.comment,
                GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_doctor_id,
                GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
                GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') as speciality,
                GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as profile,
                GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as profile_images,
                GROUP_CONCAT(ksp.participant_id) as PartName,
                GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED,
                (ks.total_buffer + ks.total_seats) as tot_seat
            FROM knwlg_sessions_V1 as ks
            JOIN session_to_specialities as sTs ON sTs.session_id = ks.session_id
            JOIN master_specialities_V1 as ms ON ms.master_specialities_id = sTs.session_id
            LEFT JOIN session_to_cover_image as stci ON stci.session_id = ks.session_id
            LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id
            LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = ks.session_id AND uTpyCont.type = 2 AND uTpyCont.user_master_id = " . $user_master_id . "
            LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
            LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
            LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
            LEFT JOIN knwlg_sessions_participant as ksp ON ksp.knwlg_sessions_id = ks.session_id
            LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id
            WHERE
                ks.status = 3 " . $childsessionids . "
                " . $sqlStr . "
                " . $specialitiesQy . "
                " . $envStatus . "
                AND (ks.session_status=1 OR ks.session_status=4)
                AND ks.start_datetime > (CURDATE() + INTERVAL 3 DAY)
                AND ks.privacy_status = 0
            GROUP BY
                ks.session_id,
                ksp.participant_id,
                ks.session_doctor_id,
                ks.session_topic,
                ks.session_description,
                ks.sessions_question,
                ks.master_tag_ids,
                ks.client_id,
                ks.sponsor_id,
                ks.user_group_id,
                ks.category_id,
                ks.start_datetime,
                ks.end_datetime,
                ks.speciality_id,
                ks.total_seats,
                ks.total_buffer,
                ks.add_question_buffer_days,
                ks.session_link,
                ks.master_conf_provider_id,
                ks.session_access_code,
                ks.deeplink,
                ks.in_deeplink,
                ks.gl_deeplink,
                ks.template_id,
                ks.cert_template_id,
                ks.display_in_dashboard,
                ks.conf_phone_no,
                ks.privacy_status,
                ks.color,
                ks.added_on,
                ks.added_by,
                ks.session_status,
                ks.cover_image,
                ks.modified_on,
                ks.modified_by,
                ks.is_recommended,
                ks.is_multiday_session,
                ks.break_json,
                ks.status,
                ks.is_featured,
                ks.rating_flag,
                ks.remarks,
                ks.crm_id,
                ks.img_credits,
                ks.session_json,
                ks.certified,
                ks.env,
                ks.notification_template,
                ks.shortlink,
                ks.invitefile,
                ks.exitroute,
                ks.is_share,
                ks.is_like,
                ks.is_comment,
                sd.knwlg_sessions_docs_id,
                sd.knwlg_sessions_id,
                sd.added_on,
                sd.added_by,
                sd.modified_on,
                sd.modified_by,
                sd.updated_at,
                sd.updated_by,
                sd.status,
                cln.client_name,
                cln.client_logo,
                cTenv.price,
                uTpyCont.status,
                msct.category_name,
                msct.category_logo,
                stci.id,
                stci.session_id,
                stci.cover_image1,
                stci.cover_image2,
                stci.cover_image3,
                stci.cover_image4,
                stci.cover_image5,
                stci.created_at,
                stci.updated_at,
                stci.created_by,
                stci.updated_by,
                sd.document_path,
                sd.comment,
                ksd.knwlg_sessions_docs_id,
                ksd.document_path,
                ksd.comment
            ORDER BY ks.start_datetime ASC " . $limit . "";
        //echo $sql; exit();
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        //
        $result = $query->result();
        //print_r($result); exit();
        //ks.session_status
        $i = 0;
        $vx = array();
        foreach ($result as $val) {
            if (@getimagesize(base_url() . "uploads/docimg/" . $val->profile_image)) {
                $logic_image = '' . $val->profile_image;
            } else {
                $logic_image = base_url() . "uploads/docimg/MConsult.png";
            }
            $start_time = $val->start_datetime;
            $start_time = date("g:i A", strtotime($start_time));
            $ses_doc_det_array = array();
            if ($val->session_doctor_id != '') {
                $session_doc_array = explode(",", $val->session_doctor_id);
            } else {
                //print_r($getchildsession['session_doctors']);
                $session_doc_array = explode(",", $getchildsession['session_doctors'][$val->session_id]);
                //print_r($session_doc_array); exit;
            }
            if (!empty($session_doc_array)) {
                #$session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;
                $store_total_doctors[$val->session_id] = 0;
                foreach ($session_doc_array as $single_doctor) {
                    $var = session_doc_detail($single_doctor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    if ($image) {
                        $logic_image = $image;
                    } else {
                        $logic_image = docimg;
                        //$logic_image = base_url() . "uploads/docimg/no-image.png";
                    }
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                    $store_total_doctors[$val->session_id] = $inc_pp;
                }
            }
            $k = array_keys((array)$getchildsession['sessioncount']);
            //print_r(in_array($val->session_id,$k));
            if (in_array($val->session_id, $k)) {
                $total_multiday_session = $getchildsession['sessioncount'][$val->session_id];
                // print_R($total_multiday_session."/n");
            } else {
                $total_multiday_session = 0;
            }
            $keyvalue_1 = array_keys((array)$getchildsession['doctorcount']);
            if (in_array($val->session_id, $keyvalue_1)) {
                $total_doctors = $getchildsession['doctorcount'][$val->session_id];
            } else {
                $total_doctors = $store_total_doctors[$val->session_id];
            }
            $datetime1 = new DateTime(date('Y-m-d H:i:s'));
            $datetime2 = new DateTime($val->start_datetime);
            $difference = $datetime1->diff($datetime2);
            //$entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $end_time = $val->end_datetime;
            $end_time = date("g:i A", strtotime($end_time));
            /**
             * new sponsor logic
             */
            $sponsorSESLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorSESLogoArry) > 0) {
                foreach ($sponsorSESLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorSESLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logoSES) {
                    $sponsorSESLogomix[] = '' . $val->sponsor_logoSES;
                }
            }
            $sponsorLogoSES = implode(",", (array)$sponsorSESLogomix);
            unset($sponsorSESLogomix);
            unset($sponsorSESLogoArry);
            $coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
            //$entities[$i]['cover_image'] = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
            $vx[] = array(
                "slno" => $i,
                "trending_type" => "session",
                "type_id" => $val->session_id,
                "session_id" => $val->session_id,
                "type" => 'session',
                "total_session" => $total_multiday_session,
                "total_doctors" => $total_doctors,
                "total_days" => $difference->d,
                "doctor_name" => $val->doctor_name,
                "cover_image" => ($val->cover_image != '') ? change_img_src($val->cover_image) : $coverImg,
                "cover_image1" => ($val->cover_image1 != '') ? change_img_src($val->cover_image1) : $coverImg,
                "cover_image2" => ($val->cover_image2 != '') ? change_img_src($val->cover_image2) : $coverImg,
                "cover_image3" => ($val->cover_image3 != '') ? change_img_src($val->cover_image3) : $coverImg,
                "cover_image4" => ($val->cover_image4 != '') ? change_img_src($val->cover_image4) : $coverImg,
                "cover_image5" => ($val->cover_image5 != '') ? change_img_src($val->cover_image5) : $coverImg,
                "session_doctor_id" => $val->session_doctor_id,
                "date" => date(' jS F y', strtotime($val->start_datetime)),
                "is_multiday_session" => $val->is_multiday_session,
                "start_datetime" => $val->start_datetime, //date(' jS F y', strtotime($val->start_datetime)),
                "display_date" => $start_time . "-" . $end_time,
                "ms_cat_name" => $val->category_name,
                "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogoSES),
                "image" => change_img_src($logic_image),
                "image_raw_name" => change_img_src($val->profile_image),
                "session_status" => $val->session_status,
                "status_name" => $val->status_name,
                "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),
                "session_topic" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                "color" => ($val->color != '') ? $val->color : '#08cc9e',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                //get_user_content_status($val->session_id, 2, $user_master_id),
                //============ integrated for subscription ============//
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                "session_doctor_entities" => $ses_doc_det_array,
            );
        }
        return $vx;
        //print_r($entities); exit();
    }
    /**
     * @param string $booked_id
     * @param string $user_master_id
     * @return array
     */
    public function getsession_v1(
        $user_master_id = '',
        $limitFromFn,
        $limitToFn,
        $spIds,
        $type_id
    ) {
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        if ($type_id != '') {
            $removetypeid = "NOT ks.session_id='" . $type_id . "' and ";
        } else {
            $removetypeid = " ";
        }
        $booked_id = $this->all_bookedmastersession($user_master_id);
        $new_arr = array();
        foreach ($booked_id as $sing) {
            $new_arr[] = $sing->knwlg_sessions_id;
        }
        $res_arr = implode(',', $new_arr);
        if ($res_arr) {
            $sqlStr = "and ( (ks.session_id  NOT IN(" . $res_arr . ") AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "') or ks.start_datetime >= '" . date("Y-m-d H:i:s") . "') ";
        } else {
            $sqlStr = "and (ks.start_datetime >= '" . date("Y-m-d H:i:s") . "')";
        }
        //echo $res_arr; exit;
        if ($spIds != '') {
            $specialitiesQy = "  AND sTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialitiesQy = "";
        }
        //$this->db->cache_on(); session_to_specialities
        //LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
        $sql = "SELECT
                        ksp.participant_id,
                        ks.session_id AS ks_session_id,
                        ks.session_doctor_id,
                        ks.session_topic,
                        ks.session_description,
                        ks.sessions_question,
                        ks.master_tag_ids,
                        ks.client_id,
                        ks.sponsor_id,
                        ks.user_group_id,
                        ks.category_id,
                        ks.start_datetime,
                        ks.end_datetime,
                        ks.speciality_id,
                        ks.total_seats,
                        ks.total_buffer,
                        ks.add_question_buffer_days,
                        ks.session_link,
                        ks.master_conf_provider_id,
                        ks.session_access_code,
                        ks.deeplink,
                        ks.in_deeplink,
                        ks.gl_deeplink,
                        ks.template_id,
                        ks.cert_template_id,
                        ks.display_in_dashboard,
                        ks.conf_phone_no,
                        ks.privacy_status,
                        ks.color,
                        ks.added_on,
                        ks.added_by,
                        ks.session_status,
                        ks.cover_image,
                        ks.modified_on,
                        ks.modified_by,
                        ks.is_recommended,
                        ks.is_multiday_session,
                        ks.break_json,
                        ks.status,
                        ks.is_featured,
                        ks.rating_flag,
                        ks.remarks,
                        ks.crm_id,
                        ks.img_credits,
                        ks.session_json,
                        ks.certified,
                        ks.env,
                        ks.notification_template,
                        ks.shortlink,
                        ks.invitefile,
                        ks.exitroute,
                        ks.is_share,
                        ks.is_like,
                        ks.is_comment,
                        sd.knwlg_sessions_docs_id,
                        sd.knwlg_sessions_id,
                        sd.added_on AS sd_added_on,
                        sd.added_by AS sd_added_by,
                        sd.modified_on AS sd_modified_on,
                        sd.modified_by AS sd_modified_by,
                        sd.updated_at AS sd_updated_at,
                        sd.updated_by AS sd_updated_by,
                        sd.status AS sd_status,
                        cln.client_name,
                        cln.client_logo,
                        msct.category_name,
                        msct.category_logo,
                        sd.document_path,
                        sd.comment,
                        ksd.knwlg_sessions_docs_id AS ksd_knwlg_sessions_docs_id,
                        ksd.document_path AS ksd_document_path,
                        ksd.comment AS ksd_comment,
                        (ks.total_buffer + ks.total_seats) AS tot_seat,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
                        GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
                        GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
                        GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') AS speciality,
                        GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
                        GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images,
                        GROUP_CONCAT(ksp.participant_id) AS PartName,
                        GROUP_CONCAT(ksp.is_attended) AS IS_ATTENDED
                    FROM knwlg_sessions_V1 AS ks
                    JOIN session_to_specialities AS sTs ON sTs.session_id = ks.session_id
                    JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = sTs.specialities_id
                    LEFT JOIN client_master AS cln ON cln.client_master_id = ks.client_id
                    LEFT JOIN session_to_sponsor AS sTspon ON sTspon.session_id = ks.session_id
                    LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                    LEFT JOIN master_session_category AS msct ON msct.mastersession_category_id = ks.category_id
                    LEFT JOIN knwlg_sessions_doctors AS sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                    LEFT JOIN knwlg_sessions_documents AS sd ON sd.knwlg_sessions_id = ks.session_id
                    LEFT JOIN knwlg_sessions_participant AS ksp ON ksp.knwlg_sessions_id = ks.session_id
                    LEFT JOIN knwlg_sessions_documents AS ksd ON ksd.knwlg_sessions_id = ks.session_id
                    WHERE " . $removetypeid . "
                    AND ks.status = 3
                    " . $sqlStr . "
                    " . $specialitiesQy . "
                    AND (ks.session_status = 1 OR ks.session_status = 4)
                    AND ks.start_datetime > (CURDATE() + INTERVAL 3 DAY)
                    AND ks.privacy_status = 0
                    GROUP BY
                        ks.session_id,
                        ks.session_doctor_id,
                        ks.session_topic,
                        ks.session_description,
                        ks.sessions_question,
                        ks.master_tag_ids,
                        ks.client_id,
                        ks.sponsor_id,
                        ks.user_group_id,
                        ks.category_id,
                        ks.start_datetime,
                        ks.end_datetime,
                        ks.speciality_id,
                        ks.total_seats,
                        ks.total_buffer,
                        ks.add_question_buffer_days,
                        ks.session_link,
                        ks.master_conf_provider_id,
                        ks.session_access_code,
                        ks.deeplink,
                        ks.in_deeplink,
                        ks.gl_deeplink,
                        ks.template_id,
                        ks.cert_template_id,
                        ks.display_in_dashboard,
                        ks.conf_phone_no,
                        ks.privacy_status,
                        ks.color,
                        ks.added_on,
                        ks.added_by,
                        ks.session_status,
                        ks.cover_image,
                        ks.modified_on,
                        ks.modified_by,
                        ks.is_recommended,
                        ks.is_multiday_session,
                        ks.break_json,
                        ks.status,
                        ks.is_featured,
                        ks.rating_flag,
                        ks.remarks,
                        ks.crm_id,
                        ks.img_credits,
                        ks.session_json,
                        ks.certified,
                        ks.env,
                        ks.notification_template,
                        ks.shortlink,
                        ks.invitefile,
                        ks.exitroute,
                        ks.is_share,
                        ks.is_like,
                        ks.is_comment,
                        sd.knwlg_sessions_docs_id,
                        sd.knwlg_sessions_id,
                        sd.added_on,
                        sd.added_by,
                        sd.modified_on,
                        sd.modified_by,
                        sd.updated_at,
                        sd.updated_by,
                        sd.status,
                        cln.client_name,
                        cln.client_logo,
                        msct.category_name,
                        msct.category_logo,
                        sd.document_path,
                        sd.comment,
                        ksd.knwlg_sessions_docs_id,
                        ksd.document_path,
                        ksd.comment
                    ORDER BY ks.start_datetime ASC
                    " . $limit . "";
        #echo $sql; exit();
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        //
        $result = $query->result();
        //print_r($result); exit();
        //ks.session_status
        $i = 0;
        $vx = array();
        foreach ($result as $val) {
            if (stripos($val->profile_image, "https://storage.googleapis.com") > -1) {
                $logic_image = '' . $val->profile_image;
            } else {
                $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
            }
            $start_time = $val->start_datetime;
            $start_time = date("g:i A", strtotime($start_time));
            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {
                    $var = session_doc_detail($single_doctor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                        $logic_image = base_url() . "uploads/docimg/" . $image;
                    } else {
                        $logic_image = base_url() . "uploads/docimg/MConsult.png";
                    }*/
                    if ($image) {
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        // $logic_image = $imgPr;
                        /////============================== updated by  ramanath  14-5-21
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            // $logic_image_path = "uploads/docimg/" . $image;
                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = docimg; //$imgPr;
                        }
                        //=======================================
                    } else {
                        $logic_image = docimg; // base_url() . "uploads/docimg/no-image.png";
                    }
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }
            //$entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $end_time = $val->end_datetime;
            $end_time = date("g:i A", strtotime($end_time));
            /**
             * new sponsor logic
             */
            $sponsorSESLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorSESLogoArry) > 0) {
                foreach ($sponsorSESLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorSESLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logoSES) {
                    $sponsorSESLogomix[] = '' . $val->sponsor_logoSES;
                }
            }
            $sponsorLogoSES = implode(",", (array)$sponsorSESLogomix);
            unset($sponsorSESLogomix);
            unset($sponsorSESLogoArry);
            $coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
            //$entities[$i]['cover_image'] = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
            $vx[] = array(
                "slno" => $i,
                "trending_type" => "session",
                "type_id" => $val->session_id,
                "session_id" => $val->session_id,
                "type" => 'session',
                "doctor_name" => $val->doctor_name,
                "cover_image" => ($val->cover_image != '') ? change_img_src($val->cover_image) : $coverImg,
                "session_doctor_id" => $val->session_doctor_id,
                "date" => date(' jS F y', strtotime($val->start_datetime)),
                "start_datetime" => $val->start_datetime, //date(' jS F y', strtotime($val->start_datetime)),
                "display_date" => $start_time . "-" . $end_time,
                "ms_cat_name" => $val->category_name,
                "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogoSES),
                "image" => change_img_src($logic_image),
                "image_raw_name" => change_img_src($val->profile_image),
                "session_status" => $val->session_status,
                "status_name" => $val->status_name,
                "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),
                "session_topic" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                "color" => ($val->color != '') ? $val->color : '#08cc9e',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                "session_doctor_entities" => $ses_doc_det_array,
            );
        }
        return $vx;
        //print_r($entities); exit();
    }
    public function explode_speciality_string($string)
    {
        $final = array();
        if (!empty($string)) {
            $temp_sp_array = explode(",", $string);
            foreach ($temp_sp_array as $ky => $sp_id_name) {
                $sp_id_name_array = explode("#", $sp_id_name);
                $final[$ky] = array();
                $final[$ky]['id'] = $sp_id_name_array[0];
                $final[$ky]['name'] = $sp_id_name_array[1];
            }
        }
        return $final;
    }
    public function getcpddetails($session_id)
    {
        $this->db->select('id');
        $this->db->from('Master_service');
        $this->db->where('name', 'session');
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            $this->db->select('ctc.id,ctc.points,c.name,c.short_name');
            $this->db->from('content_to_cpd as ctc');
            $this->db->join('council as c', 'c.id=ctc.mc_id');
            $this->db->where(array('ctc.type_id' => $session_id, 'ctc.status' => 3, 'ctc.type' => $result[0]->id));
            $query = $this->db->get();
            #print_r($this->db->last_query()); exit;
            if (($query) && ($query->num_rows())) {
                return $query->result();
            } else {
                return array();
            }
        } else {
            return array();
        }
    }
    public function get_all_childsession()
    {
        $ids = array();
        $this->db->select("kstc.multidaysession_id,GROUP_CONCAT(DISTINCT kstc.childsession_id SEPARATOR ',') as childsession_id,GROUP_CONCAT(DISTINCT sts.sessions_doctors_id SEPARATOR ',') as session_soctor_id");
        $this->db->from('knwlg_session_to_child as kstc');
        $this->db->join('session_to_sessiondoctor as sts', 'sts.session_id = kstc.childsession_id', 'left');
        $this->db->group_by('kstc.multidaysession_id');
        $query = $this->db->get();
        //print_r($this->db->last_query()); exit;
        $ids = array();
        if (($query) && ($query->num_rows() > 0)) {
            $session_ids = $query->result();
            foreach ($session_ids as $key => $value) {
                // print_R($value);
                $totalids = explode(',', $value->childsession_id);
                $getids[$value->multidaysession_id] = count(explode(',', $value->childsession_id)); //$value->childsession_id;
                $getdoctorcount[$value->multidaysession_id] = count(explode(',', $value->session_soctor_id));
                $ids = array_merge($ids, $totalids); //$value->childsession_id;
                // $countmultidayminisession[$value->multidaysession_id] = sizeof($getids[$value->multidaysession_id]);
            }
        }
        $response['doctorcount'] = $getdoctorcount;
        $response['sessioncount'] = $getids; //$countmultidayminisession;
        $response['sessions'] = $ids;
        //print_R($response); exit;
        return $response;
    }

    /**
     * Summary of getGrLinkedContent
     * @param mixed $typeId
     * @param mixed $limitFromFn
     * @param mixed $limitToFn
     * @return array
     */
    private function getGrLinkedContent(
        $typeId,
        $limitFromFn,
        $limitToFn,
        $user_master_id
    ) {
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'gr');
        $limit = "";
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        $AND_WHERE = "";
        if ($typeId != '') {
            $AND_WHERE = " AND gc.comp_qa_id = " . $typeId . "";
        }
        $sql = "SELECT
                        gr.gr_id AS type_id,
                        gr.gr_title AS title,
                        gr.gr_description AS description,
                        gr.gr_chief_scientific_editor,
                        gr.gr_preview_image,
                        gr.added_on,
                        gr.gr_date_of_publication AS publish_date,
                        gr.deeplink,
                        gr.color,
                        cln.client_name,
                        cln.client_logo,
                        cTenv.price,
                        uTpyCont.status AS user_content_payment_status,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                        MAX(ms.rank) AS maxrank,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                        GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id) AS session_doctor_id,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                    FROM knwlg_gr_register AS gr
                    LEFT JOIN gr_to_compendium AS gc ON gc.gr_id = gr.gr_id
                    LEFT JOIN gr_to_specialities AS grTs ON grTs.gr_id = gr.gr_id
                    JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = grTs.specialities_id
                    JOIN client_master AS cln ON cln.client_master_id = gr.client_id
                    LEFT JOIN gr_to_sponsor AS grTspon ON grTspon.gr_id = gr.gr_id
                    LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = grTspon.sponsor_id
                    LEFT JOIN gr_to_session_doctor AS grTsdoc ON grTsdoc.gr_id = gr.gr_id
                    LEFT JOIN content_to_env AS cTenv ON cTenv.type_id = gr.gr_id AND cTenv.type = 5
                    LEFT JOIN payment_user_to_content AS uTpyCont ON uTpyCont.type_id = gr.gr_id AND uTpyCont.type = 5 AND uTpyCont.user_master_id = " . $user_master_id . "
                    WHERE
                        gr.status = 3
                        " . $envStatus . "
                        " . $AND_WHERE . "
                        AND gr.privacy_status = 0
                    GROUP BY
                        gr.gr_id,
                        gr.gr_title,
                        gr.gr_description,
                        gr.gr_chief_scientific_editor,
                        gr.gr_preview_image,
                        gr.added_on,
                        gr.gr_date_of_publication,
                        gr.deeplink,
                        gr.color,
                        cln.client_name,
                        cln.client_logo,
                        cTenv.price,
                        uTpyCont.status
                    ORDER BY
                        gr.gr_date_of_publication DESC,
                        maxrank DESC
                    " . $limit . "";
        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {
                    $var = session_doc_detail($single_doctor);
                    //$image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                        $logic_image = base_url() . "uploads/docimg/" . $image;
                    } else {
                        $logic_image = base_url() . "uploads/docimg/MConsult.png";
                    }*/
                    if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                        $logic_image = $var[0]['profile_image'];
                    } else {
                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        $logic_image_path = "uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                        //$logic_image = $var[0]['profile_image'];
                    }
                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }
            $vx[] = array(
                "slno" => $i,
                "type_id" => $val->type_id,
                "trending_type" => "gr",
                "type" => 'gr',
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->gr_preview_image),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src('' . $val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "session_doctor_entities" => $ses_doc_det_array,
                //   "comment_count" => $val->count_comment,
                //  "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                //  "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            $i++;
        }
        return $vx;
    }
    /**
     * Summary of getTrainingLinkedContent
     * @param mixed $typeId
     * @param mixed $limitFromFn
     * @param mixed $limitToFn
     * @return array
     */
    private function getTrainingLinkedContent(
        $typeId,
        $limitFromFn = null,
        $limitToFn = null,
        $user_master_id
    ) {
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'training');
        $limit = "";
        if ($limitFromFn == 0 and $limitToFn != '') {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        $AND_WHERE = "";
        if ($typeId != '') {
            $AND_WHERE = " AND tmc.type_id = " . $typeId . "";
        }
        $sql = "SELECT
                tm.id AS type_id,
                tm.title AS title,
                tm.description AS description,
                tm.preview_image,
                tm.added_on,
                tm.published_date,
                tm.deeplink,
                tm.gl_deeplink,
                tm.start_like,
                tm.color,
                tm.display_in_dashboard,
                tm.featured_video,
                tm.cert_template_id,
                tm.duration,
                tm.privacy_status,
                cTenv.price,
                uTpyCont.status AS user_content_payment_status,
                cln.client_name,
                cln.client_logo
            FROM training_master AS tm
            LEFT JOIN training_module_content AS tmc ON tmc.training_id = tm.id
            LEFT JOIN client_master AS cln ON cln.client_master_id = tm.client_id
            LEFT JOIN content_to_env AS cTenv ON cTenv.type_id = tm.id AND cTenv.type = 4
            LEFT JOIN payment_user_to_content AS uTpyCont ON uTpyCont.type_id = tm.id
                AND uTpyCont.type = 4
                AND uTpyCont.user_master_id = " . $user_master_id . "
            WHERE
                tm.status = 3
                " . $envStatus . "
                {$AND_WHERE}
                AND tm.privacy_status = 0
            GROUP BY
                tm.id,
                tm.title,
                tm.description,
                tm.preview_image,
                tm.added_on,
                tm.published_date,
                tm.deeplink,
                tm.gl_deeplink,
                tm.start_like,
                tm.color,
                tm.display_in_dashboard,
                tm.featured_video,
                tm.cert_template_id,
                tm.duration,
                tm.privacy_status,
                cTenv.price,
                uTpyCont.status,
                cln.client_name,
                cln.client_logo
            ORDER BY tm.published_date DESC {$limit}";
        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {
            $ses_doc_det_array = array();
            $vx[] = array(
                "slno" => $i,
                "type_id" => $val->type_id,
                "trending_type" => "training",
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                "type" => 'training',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->preview_image),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "client_name" => $val->client_name,
                "client_logo" => '' . change_img_src($val->client_logo),
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            $i++;
        }
        return $vx;
    }
    /**
     * Summary of linked_content
     * @param mixed $type_id
     * @param mixed $limitFrom
     * @param mixed $limitTo
     * @return mixed
     */
    public function linked_content(
        $type_id,
        $limitFrom,
        $limitTo,
        $user_master_id
    ) {
        $limitToFn = floor($limitTo / 4);
        $limitFromFn = floor($limitFrom / 4);
        $env = get_user_env($user_master_id);
        $cacheName = "related_link_content_" . $type_id . "_" . $env . "_" . $limitFromFn . "_" . $limitToFn;
        if ($this->myredis->exists($cacheName)) {
            return $this->myredis->get($cacheName);
        } else {
            $grArry = $this->getGrLinkedContent($type_id, $limitFromFn, $limitToFn, $user_master_id);
            $trainingArry = $this->getTrainingLinkedContent($type_id, $limitFromFn, $limitToFn, $user_master_id);
            if ($env) {
                if ($env != 'GL') {
                    $finalArry = array_merge_recursive($grArry, $trainingArry);
                } else {
                    $finalArry = $trainingArry;
                }
            }
            $this->myredis->set($cacheName, $finalArry);
            return $finalArry;
        }
    }

    /**
     * Summary of topics
     * @param mixed $type_id
     * @param mixed $service_id
     * @return mixed
     */
    public function topics(
        $type_id,
        $service_id
    ) {
        // set cache key ane check it already exists in the cache and return
        $this->cacheName = "related_topics_" . $type_id;
        if ($this->myredis->exists($this->cacheName)) {
            return $this->myredis->get($this->cacheName);
        }
        $sql = "SELECT
                    mm.mesh_id,
                    mm.name,
                    cm.content_id,
                    cm.content_type,
                    cm.score
                FROM master_mesh mm
                JOIN content_to_mesh_with_tag_status cm
                    ON cm.mesh_id = mm.mesh_id
                JOIN Master_service ms
                    ON ms.id = cm.content_type
                WHERE cm.content_type = {$service_id}
                    AND cm.content_id = {$type_id}
                GROUP BY
                    mm.mesh_id,
                    mm.name,
                    cm.content_id,
                    cm.content_type,
                    cm.score
                ORDER BY cm.score ASC";
        $topics = array_map(function ($item) {
            return [
                'tag_id' => $item['mesh_id'],
                'value' => $item['name'],
            ];
        }, $this->db->query($sql)->result_array());
        // set cache
        $this->myredis->set($this->cacheName, $topics);
        // print_r($topics);
        // exit;
        return $topics;
    }
    /**
     * Summary of getCompendiumByContentId
     * @param mixed $type_id
     * @param mixed $user_master_id
     * @param mixed $spIds
     * @return array
     */
    private function getCompendiumByContentId(
        $contentId,
        $user_master_id = 0
    ) {
        return $this->ci->knwlg_model->detail_for_related($contentId, 'comp', $user_master_id);
    }
    /**
     * Summary of getClinicalVideoByContentId
     * @param mixed $contentId
     * @param mixed $user_master_id
     * @return mixed
     */
    private function getClinicalVideoByContentId(
        $contentId,
        $user_master_id = 0
    ) {
        return $this->ci->knwlg_model->detail_archiveVideo($contentId, $user_master_id);
    }
    /**
     * Summary of getGrByContentId
     * @param mixed $contentId
     * @param mixed $user_master_id
     * @return mixed
     */
    private function getGrByContentId(
        $contentId,
        $user_master_id = 0
    ) {
        return $this->ci->gr_model->detail_for_related($user_master_id, 0, $contentId);
    }
    /**
     * Summary of getSpqByContentId
     * @param mixed $type_id
     * @param mixed $user_master_id
     * @param mixed $limitFromFn
     * @param mixed $limitToFn
     * @param mixed $spIds
     * @return array
     */
    private function getSpqByContentId(
        $contentId,
        $user_master_id = 0
    ) {
        return $this->ci->survey_model->detail_for_related($contentId, $user_master_id);
    }
    /**
     * Summary of getEbookByContentId
     * @param mixed $contentId
     * @param mixed $user_master_id
     * @return mixed
     */
    private function getEbookByContentId(
        $contentId,
        $user_master_id = 0
    ) {
        return $this->ci->epub_model->detail_for_related($contentId, $user_master_id);
    }
    /**
     * Summary of getCourseByContentId
     * @param mixed $contentId
     * @param mixed $user_master_id
     * @return mixed
     */
    private function getCourseByContentId(
        $contentId,
        $user_master_id = 0
    ) {
        return $this->ci->training_model->detail_for_related($contentId, $user_master_id);
    }

    /**
     * code end here
     */
    public function similar_content(
        $service_id,
        $type_id,
        $limitFrom,
        $limitTo,
        $user_master_id
    ) {
        // $limitToFn = floor($limitTo / 4);
        // $limitFromFn = floor($limitFrom / 4);
        $limit = "";
        if ($limitFrom && $limitTo) {
            $limit = "limit " . $limitFrom . " , " . $limitTo;
        }
        $env = get_user_env($user_master_id);
        // set cache key ane check it already exists in the cache and return
        $this->cacheName = "_related_similar_content_" . $type_id . $service_id . "_" . $limitFrom . "_" . $limitTo . "_" . $env;
        if ($this->myredis->exists($this->cacheName)) {
            return $this->myredis->get($this->cacheName);
        }
        $sql = "SELECT *
        FROM similar_content
        WHERE content_type_id = {$service_id}
        AND content_id = {$type_id}
        AND similar_content_id != {$type_id}
        ORDER by priority ASC {$limit}";
        // echo $env;
        // exit;
        $similarContents = [];
        //echo $sql; exit;
        // print_r($this->db->query($sql)->result_array());
        // exit;
        foreach ($this->db->query($sql)->result_array() as $content) {
            if ($content['similar_content_type_id']) {
                switch ($content['similar_content_type_id']) {
                    // compendium
                    case 1:
                        $data = $this->getCompendiumByContentId($content['similar_content_id'], $user_master_id);
                        if (!empty($data) && isset($data)) {
                            $similarContents[] = $data;
                        }
                        break;
                        // clinical video
                    case 2:
                        $data = $this->getClinicalVideoByContentId($content['similar_content_id'], $user_master_id);
                        if (!empty($data) && isset($data)) {
                            $similarContents[] = $data;
                        }
                        break;
                        // session
                    case 3:
                        // $data = $this->getSessionByContentId($content['similar_content_id']);
                        // if (!empty($data) && isset($data)) {
                        //     $similarContents[] = $data;
                        // }
                        break;
                        // grand round
                    case 4:
                        if ($env != 'GL') {
                            $data = $this->getGrByContentId($content['similar_content_id'], $user_master_id);
                            if (!empty($data) && isset($data)) {
                                $similarContents[] = $data;
                            }
                        }
                        break;
                        // sqp
                    case 5:
                        $data = $this->getSpqByContentId($content['similar_content_id'], $user_master_id);
                        // echo $content['similar_content_id'];
                        // exit;
                        if (!empty($data) && isset($data)) {
                            $similarContents[] = $data;
                        }
                        break;
                        // ebook
                    case 6:
                        if ($env != 'GL') {
                            $data = $this->getEbookByContentId($content['similar_content_id'], $user_master_id);
                            if (!empty($data) && isset($data)) {
                                // previously this⏬
                                // $similarContents[] = $content['similar_content_id'];
                                // after chages against "0" : 72 issue ⏬
                                // $similarContents_un_filtered[] = $data;
                                $similarContents[] = $data;
                            }
                        }
                        // if(!empty($similarContents_un_filtered)){
                        //     foreach($similarContents_un_filtered as $i) {
                        //         if($i->type_id != '' || $i->type_id != NULL){
                        //             $similarContents[] = $i;
                        //         }
                        //     }
                        // }
                        break;
                        // courses
                    case 7:
                        $data = $this->getCourseByContentId($content['similar_content_id'], $user_master_id);
                        if (!empty($data) && isset($data)) {
                            $similarContents[] = $data;
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        $i = 0;
        foreach ($similarContents as $index => $similarContent) {
            if ($similarContent['type_id'] != '' || $similarContent['type_id'] != null) {
                $contents[$i] = $similarContent;
                $contents[$i]['slno'] = $i + 1;
                $i++;
            }
        }
        // set cache
        $this->myredis->set($this->cacheName, $contents);
        return $contents;
    }

    public function livestatus($id)
    {
        $currentdatetime = date('Y-m-d H:i:s');
        $status = 0;
        if ($id != '') {
            $this->db->select('tmc.id,tmc.type_id');
            $this->db->from('training_module_content as tmc');
            $this->db->join('knwlg_sessions_V1 as ks', 'ks.session_id = tmc.type_id');
            $this->db->where(array('tmc.type' => 'session', 'tmc.training_id' => $id, 'ks.session_status' => 2, "tmc.status" => 3));
            $this->db->where("'" . $currentdatetime . "' BETWEEN ks.start_datetime and ks.end_datetime");
            $query = $this->db->get();
            if (($query) && ($query->num_rows() > 0)) {
                $status = 1;
            } else {
                $this->db->select('id');
                $this->db->from('training_module_content');
                $this->db->where(array('type' => 'live_video', 'training_id' => $id));
                $this->db->where("'" . $currentdatetime . "' BETWEEN start_datetime and end_datetime");
                $querylivevideo = $this->db->get();
                if (($querylivevideo) && ($querylivevideo->num_rows() > 0)) {
                    $status = 1;
                }
            }
        }
        // print_r($status);
        // die;
        return $status;
    }
    public function communitycategory($mediaType = 'app')
    {
        $sql = "SELECT
                    id,
                    type,
                    sub_type,
                    featured,
                    default_active
                FROM
                    channel_type_master
                WHERE
                    status = 3
                    AND
                    media = '" . $mediaType . "'
                    order by short desc";
        // echo $sql;
        // exit;
        $query = $this->db->query($sql);
        if (($query) && ($query->num_rows() > 0)) {
            return  $query->result_array();
        } else {
            return array();
        }
    }

    public function compendiumcategory($mediaType)
    {

        $sql = "SELECT
        id,
        type,
        sub_type,
        featured,
        default_active
        FROM
        channel_type_master
        WHERE
        status = 3
        AND
        media = '" . $mediaType . "'
        order by short desc";
        // echo $sql;
        // exit;
        $query = $this->db->query($sql);
        if (($query) && ($query->num_rows() > 0)) {
            return $query->result_array();
        } else {
            return array();
        }
    }

    public function categoryData($mediaType, $type)
    {


        //echo 'xxxxxxxxxxxxxxxxxxxxx';
        $sqlStringMedia = "AND media = 'app'";
        $sqlStringType = "AND module = 'community'";

        if ($mediaType) {
            $sqlStringMedia = "AND media = '" . $mediaType . "'";
        }
        if ($type) {
            $sqlStringType = "AND module = '" . $type . "'";
        }
        // $sql = "SELECT
        // id,
        // category,
        // type,
        // sub_type,
        // featured,
        // default_active
        // FROM
        // channel_type_master
        // WHERE
        // status = 3
        // {$sqlStringMedia}
        // {$sqlStringType}
        // order by short desc";


        $sql = "SELECT
        id,
        category,
        type,
        sub_type,
        featured,
        default_active
        FROM
        discover_type_master
        WHERE
        status = 3
        {$sqlStringMedia}
        {$sqlStringType}
        order by short desc";

        // echo $sql;
        // exit;
        $query = $this->db->query($sql);
        if (($query) && ($query->num_rows() > 0)) {
            $data =  $query->result_array();
            // print_r($data);
            // exit;
            $i = 0;
            $result = array();

            foreach ($data as $value) {

                $result[$i]["id"] = $value['category'];
                $result[$i]["primary_id"] = $value['id'];
                $result[$i]["type"] = $value['type'];
                $result[$i]["sub_type"] = $value['sub_type'];
                if ($value['featured'] == 1) {
                    $result[$i]["featured"] = true;
                } else {
                    $result[$i]["featured"] = false;
                }
                if ($value['default_active'] == 1) {
                    $result[$i]["default_active"] = true;
                } else {
                    $result[$i]["default_active"] = false;
                }
                $i++;

            }
            return  $result;
        } else {
            return array();
        }
    }

    public function categoryDataWeb($mediaType, $type)
    {
        $sql = "SELECT
                    id,
                    category,
                    type,
                    sub_type,
                    featured,
                    default_active
                FROM
                    discover_type_master
                WHERE
                    status = 3
                    AND
                    media = '" . $mediaType . "'
                    AND
                    module = '" . $type . "'
                    order by short desc";


        // echo $sql;
        // exit;
        $query = $this->db->query($sql);
        if (($query) && ($query->num_rows() > 0)) {
            $data =  $query->result_array();

            // print_r($data);
            // exit;


            $i = 0;
            $result = array();

            foreach ($data as $value) {

                $result[$i]["id"] = $value['category'];
                $result[$i]["primary_id"] = $value['id'];
                $result[$i]["type"] = $value['type'];
                $result[$i]["sub_type"] = $value['sub_type'];
                if ($value['featured'] == 1) {
                    $result[$i]["featured"] = true;
                } else {
                    $result[$i]["featured"] = false;
                }
                if ($value['default_active'] == 1) {
                    $result[$i]["default_active"] = true;
                } else {
                    $result[$i]["default_active"] = false;
                }
                $i++;

            }
            return  $result;
        } else {
            return array();
        }
    }
}
