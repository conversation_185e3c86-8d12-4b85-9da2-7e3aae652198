<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Search_model extends CI_Model
{
    /**
     * @param string $user_master_id
     * @param string $client_ids
     * @param string $group_ids
     * @param string $limitFrom
     * @param string $limitTo
     * @param string $val
     * @param string $type
     * @param string $specialities
     * @return array
     */
    public function all_data(
        $user_master_id,
        $client_ids,
        $group_ids,
        $limitFrom,
        $limitTo,
        $val,
        $type,
        $specialities
    ) {

        //echo $val; exit;
        if ($limitFrom != '' && $limitTo != '') {

            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {
            $limit = "";
        }
        $stopWords = [
            '/ is /',
            '/ are /',
            '/ has /',
            '/ may /',
            '/ have /',
            '/ the /',
            '/ what /',
            '/ where /',
            '/ when /',
            '/ we /',
            '/ how /',
            '/ to /',
            '/ from /',
            '/ who /',
            '/ are /',
            '/ should /',
            '/ would /',
            '/ am /',
            '/ be /',
        ];
        if ($client_ids) {


            $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                return "FIND_IN_SET('$x', fd.client_id)";
            }, explode(',', $client_ids))) . ')';
        }
        if ($type != '') {
            //echo $type;
            switch ($type) {

                case 'comp':

                    //word processing start
                    if ($val != '') {

                        $valX = preg_replace('/[-?]/', '', $val);

                        $valFilter = preg_replace($stopWords, ' ', $valX);
                        $laString = explode(" ", $valFilter);
                        if (count($laString) > 0) {

                            foreach ($laString as $value) {

                                if ($value != '') {

                                    $sqlTagLikeArray[] = " tag_name like '%" . $value . "%'";
                                    $sqlSepLikeArray[] = " specialities_name like '%" . $value . "%'";
                                }
                            }

                            $sqlTagLike = implode(" or ", $sqlTagLikeArray);
                            $sqlSepLike = implode(" or ", $sqlSepLikeArray);
                        } else {


                            $sqlTagLike = " tag_name like '%" . $val . "%'";
                            $sqlSepLike = " specialities_name like '%" . $val . "%'";
                        }

                        $sqlTag = "select
                        *
                        from master_tags
                        WHERE " . $sqlTagLike . "";
                        $query = $this->db->query($sqlTag);
                        $result = $query->result();

                        $sqlSpec = "select
                        master_specialities_id
                        from master_specialities_V1
                        WHERE " . $sqlSepLike . "";
                        //exit;

                        $query = $this->db->query($sqlSpec);
                        $resultSpeTag = $query->result_array();


                        foreach ($resultSpeTag as $valSpec) {

                            $specIdArray[] = $valSpec['master_specialities_id'];
                        }

                        if (count($specIdArray) > 0) {

                            $specIdStr = implode(",", (array)$specIdArray);
                        } else {

                            $specIdStr = $valSpec['master_specialities_id'];
                        }
                    }
                    //word processing end


                    //if user select only val
                    if ($val != '' and $specialities == '') {
                        //echo $val . '<br>';
                        if ($valFilter) {

                            $inputWords = $valFilter; // filtered words
                            $fullTxt = '"' . $val . '"'; //original words

                            $words = explode(" ", $inputWords);
                            $string = array_map(function (&$word) {
                                return "+" . $word . "*";
                            }, $words);

                            $words = implode(" ", $string);
                            //echo $words; exit;
                            //echo $val; exit;
                            //$searchQuery[] = " ( (fd.title like '%" . $valFilter . "%' or fd.description like '%" . $valFilter . "%' ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";

                            //for medwiki
                            $searchQuery[] = " ( (MATCH(fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $val . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            //for medwiki


                        }
                        if ($specIdStr) {
                            //echo $val; exit;
                            $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";
                        }
                    }

                    //if user select only speciality
                    if ($specialities != '' and $val == '') {

                        $searchQuerySpecialy[] = "  ( (cmTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";
                    }

                    //if user select both val and speciality
                    if ($specialities != '' and $val != '') {


                        if ($valFilter) {

                            $inputWords = $valFilter; // filtered words
                            $fullTxt = '"' . $val . '"'; //original words

                            $words = explode(" ", $inputWords);
                            $string = array_map(function (&$word) {
                                return "+" . $word . "*";
                            }, $words);

                            $words = implode(" ", $string);
                            //echo $words; exit;
                            //echo $val; exit;
                            //$searchQuery[] = " ( (fd.title like '%" . $valFilter . "%' or fd.description like '%" . $valFilter . "%' ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";

                            //for medwiki
                            $searchQuery[] = " ( (MATCH(fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $val . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                        }
                        if ($specIdStr) {
                            //echo $val; exit;

                            $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";
                        }

                        $searchQuerySpecialy[] = "  ( (cmTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";
                    }

                    //if user select nothing
                    if ($val == '' and $specialities == '') {

                        $searchQuery[] = " (  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ")  )";
                    }

                    $searchQueryStrX = @implode("or", $searchQuery);
                    if ($searchQueryStrX) {
                        $searchQueryStr = ' and ( ' . $searchQueryStrX . " ) ";
                    }

                    //echo $specialities;
                    // print_r($searchQuerySpecialy);
                    //exit;

                    $searchQueryStrSpecialityX = @implode("or", $searchQuerySpecialy);
                    if ($searchQueryStrSpecialityX) {
                        $searchQueryStrSpeciality = ' and ( ' . $searchQueryStrSpecialityX . " ) ";
                    }

                    $typeQuery = "  and fd.type = '" . $type . "'";

                    $order_by = ' order by  relevance2 DESC,relevance1 DESC,fd.publish_date DESC';

                    break;

                case 'session':

                    //word processing start
                    if ($val != '') {

                        $valX = preg_replace('/[-?]/', '', $val);

                        $valFilter = preg_replace($stopWords, ' ', $valX);
                        $laString = explode(" ", $valFilter);
                        if (count($laString) > 0) {

                            foreach ($laString as $value) {

                                if ($value != '') {

                                    $sqlTagLikeArray[] = " tag_name like '%" . $value . "%'";
                                    $sqlSepLikeArray[] = " specialities_name like '%" . $value . "%'";
                                }
                            }

                            $sqlTagLike = implode(" or ", $sqlTagLikeArray);
                            $sqlSepLike = implode(" or ", $sqlSepLikeArray);
                        } else {


                            $sqlTagLike = " tag_name like '%" . $val . "%'";
                            $sqlSepLike = " specialities_name like '%" . $val . "%'";
                        }
                        $sqlTag = "select
                *
                from master_tags
                WHERE " . $sqlTagLike . "";
                        $query = $this->db->query($sqlTag);
                        $result = $query->result();

                        $sqlSpec = "select
                master_specialities_id
                from master_specialities_V1
                WHERE " . $sqlSepLike . "";
                        //exit;

                        $query = $this->db->query($sqlSpec);
                        $resultSpeTag = $query->result_array();


                        foreach ($resultSpeTag as $valSpec) {

                            $specIdArray[] = $valSpec['master_specialities_id'];
                        }

                        if (count($specIdArray) > 0) {

                            $specIdStr = implode(",", (array)$specIdArray);
                        } else {

                            $specIdStr = $valSpec['master_specialities_id'];
                        }
                    }
                    //word processing end


                    //if user select only val
                    if ($val != '' and $specialities == '') {
                        //echo $val . '<br>';
                        if ($valFilter) {

                            $inputWords = $valFilter; // filtered words
                            $fullTxt = '"' . $val . '"'; //original words

                            $words = explode(" ", $inputWords);
                            $string = array_map(function (&$word) {
                                return "+" . $word . "*";
                            }, $words);

                            $words = implode(" ", $string);
                            //echo $words; exit;
                            //echo $val; exit;

                            //for session
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                            //for session


                        }
                        if ($specIdStr) {
                            //echo $val; exit;
                            $searchQuery[] = "  ( (sesTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";
                        }
                    }

                    //if user select only speciality
                    if ($specialities != '' and $val == '') {

                        $searchQuerySpecialy[] = "  ( (sesTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";
                    }

                    //if user select both val and speciality
                    if ($specialities != '' and $val != '') {

                        if ($valFilter) {

                            $inputWords = $valFilter; // filtered words
                            $fullTxt = '"' . $val . '"'; //original words

                            $words = explode(" ", $inputWords);
                            $string = array_map(function (&$word) {
                                return "+" . $word . "*";
                            }, $words);

                            $words = implode(" ", $string);
                            //echo $words; exit;
                            //echo $val; exit;
                            //$searchQuery[] = " ( (fd.title like '%" . $valFilter . "%' or fd.description like '%" . $valFilter . "%' ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";


                            //for session
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                        }
                        if ($specIdStr) {
                            //echo $val; exit;

                            $searchQuery[] = "  ( (sesTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";
                        }

                        $searchQuerySpecialy[] = "  ( (sesTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";
                    }

                    //if user select nothing
                    if ($val == '' and $specialities == '') {
                        $searchQuery[] = " (  (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")   )";
                    }

                    $searchQueryStrX = @implode("or", $searchQuery);
                    if ($searchQueryStrX) {
                        $searchQueryStr = ' and ( ' . $searchQueryStrX . " ) ";
                    }


                    $searchQueryStrSpecialityX = @implode("or", $searchQuerySpecialy);
                    if ($searchQueryStrSpecialityX) {
                        $searchQueryStrSpeciality = ' and ( ' . $searchQueryStrSpecialityX . " ) ";
                    }

                    $typeQuery = "  and fd.type = '" . $type . "'";
                    $order_by = ' order by fd.publish_date_session ASC ';

                    break;

                case 'user':

                    if ($client_ids) {


                        $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                            return "FIND_IN_SET('$x', fd.client_id)";
                        }, explode(',', $client_ids))) . ')';
                    }

                    if ($val != '') {


                        $valX = preg_replace('/[-?]/', '', $val);


                        $stopWords = [
                            '/is /',
                            '/the /',
                            '/what /',
                            '/where /',
                            '/when /',
                            '/we /',
                            '/how /',
                            '/to /',
                            '/who /',
                            '/are /',
                        ];

                        $valFilter = preg_replace($stopWords, '', $valX);

                        $laString = explode(" ", $valFilter);

                        if (count($laString) > 0) {

                            foreach ($laString as $value) {

                                if ($value != '') {

                                    $sqlTagLikeArray[] = " tag_name like '%" . $value . "%'";
                                    $sqlSepLikeArray[] = " specialities_name like '%" . $value . "%'";
                                }
                            }

                            $sqlTagLike = implode(" or ", $sqlTagLikeArray);
                            $sqlSepLike = implode(" or ", $sqlSepLikeArray);
                        } else {


                            $sqlTagLike = " tag_name like '%" . $val . "%'";
                            $sqlSepLike = " specialities_name like '%" . $val . "%'";
                        }

                        /*$sqlTag ="select
                        *
                        from master_tags
                        WHERE " . $sqlTagLike . "";
                        $query = $this->db->query($sqlTag);
                        $result = $query->result();*/

                        $sqlSpec = "select
                        master_specialities_id
                        from master_specialities_V1
                        WHERE " . $sqlSepLike . "";


                        $query = $this->db->query($sqlSpec);
                        $resultSpeTag = $query->result_array();


                        foreach ($resultSpeTag as $valSpec) {

                            $specIdArray[] = $valSpec['master_specialities_id'];
                        }


                        if (count($specIdArray) > 0) {

                            $specIdStr = implode(",", (array)$specIdArray);
                        } else {

                            $specIdStr = $valSpec['master_specialities_id'];
                        }


                        //echo $val; exit;
                        $searchQuery = " AND (fd.title like '%" . $valFilter . "%' or fd.description like '%" . $valFilter . "%') or ( docTs.specialities_id IN (" . $specIdStr . "))";
                    } else {
                        $searchQuery = "";
                    }

                    if ($specialities != '') {

                        $specialities = "  AND (  docTs.specialities_id IN (" . $specialities . "))";
                    } else {
                        $specialities = "";
                    }

                    $typeQuery = " ";

                    break;

                case 'all':

                    if ($val != '') {

                        $valX = preg_replace('/[-?]/', '', $val);

                        $valFilter = preg_replace($stopWords, ' ', $valX);
                        $laString = explode(" ", $valFilter);
                        if (count($laString) > 0) {

                            foreach ($laString as $value) {

                                if ($value != '') {

                                    $sqlTagLikeArray[] = " tag_name like '%" . $value . "%'";
                                    $sqlSepLikeArray[] = " specialities_name like '%" . $value . "%'";
                                }
                            }

                            $sqlTagLike = implode(" or ", $sqlTagLikeArray);
                            $sqlSepLike = implode(" or ", $sqlSepLikeArray);
                        } else {


                            $sqlTagLike = " tag_name like '%" . $val . "%'";
                            $sqlSepLike = " specialities_name like '%" . $val . "%'";
                        }
                        $sqlTag = "select
                *
                from master_tags
                WHERE " . $sqlTagLike . "";
                        $query = $this->db->query($sqlTag);
                        $result = $query->result();

                        $sqlSpec = "select
                master_specialities_id
                from master_specialities_V1
                WHERE " . $sqlSepLike . "";
                        //exit;

                        $query = $this->db->query($sqlSpec);
                        $resultSpeTag = $query->result_array();


                        foreach ($resultSpeTag as $valSpec) {

                            $specIdArray[] = $valSpec['master_specialities_id'];
                        }

                        if (count($specIdArray) > 0) {

                            $specIdStr = implode(",", (array)$specIdArray);
                        } else {

                            $specIdStr = $valSpec['master_specialities_id'];
                        }
                    }
                    //word processing end


                    //if user select only val
                    if ($val != '' and $specialities == '') {
                        //echo $val . '<br>';
                        if ($valFilter) {

                            $inputWords = $valFilter; // filtered words
                            $fullTxt = '"' . $val . '"'; //original words

                            $words = explode(" ", $inputWords);
                            $string = array_map(function (&$word) {
                                return "+" . $word . "*";
                            }, $words);

                            $words = implode(" ", $string);
                            //echo $words; exit;
                            //echo $val; exit;
                            //$searchQuery[] = " ( (fd.title like '%" . $valFilter . "%' or fd.description like '%" . $valFilter . "%' ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";

                            //for medwiki
                            $searchQuery[] = " ( (MATCH(fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $val . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            //for medwiki


                            //for session
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                            //for session

                            //for user
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and  ( fd.type='user' ) )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and  ( fd.type='user' ) )";
                        }
                        if ($specIdStr) {
                            //echo $val; exit;

                            $searchQuery[] = "  ( (sesTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";

                            $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";

                            $searchQuery[] = "  ( (docTs.specialities_id IN (" . $specIdStr . ") and fd.type='user' ) )";
                        }
                    }

                    //if user select only speciality
                    if ($specialities != '' and $val == '') {

                        $searchQuerySpeciality[] = "  ( (sesTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";

                        $searchQuerySpeciality[] = "  ( (cmTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";

                        $searchQuerySpeciality[] = "  ( (docTs.specialities_id IN (" . $specialities . ") and fd.type='user' ) )";
                    }

                    //if user select both val and speciality
                    if ($specialities != '' and $val != '') {


                        if ($valFilter) {

                            $inputWords = $valFilter; // filtered words
                            $fullTxt = '"' . $val . '"'; //original words

                            $words = explode(" ", $inputWords);
                            $string = array_map(function (&$word) {
                                return "+" . $word . "*";
                            }, $words);

                            $words = implode(" ", $string);
                            //echo $words; exit;
                            //echo $val; exit;
                            //$searchQuery[] = " ( (fd.title like '%" . $valFilter . "%' or fd.description like '%" . $valFilter . "%' ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";

                            //for medwiki
                            $searchQuery[] = " ( (MATCH(fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $val . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";

                            //for session
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";


                            //for user
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and  ( fd.type='user' ) )";
                            $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and  ( fd.type='user' ) )";
                        }
                        if ($specIdStr) {
                            //echo $val; exit;

                            $searchQuery[] = "  ( (sesTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";

                            $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";

                            $searchQuery[] = "  ( (docTs.specialities_id IN (" . $specIdStr . ") and fd.type='user' ) )";
                        }

                        $searchQuerySpecialy[] = "  ( (sesTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";

                        $searchQuerySpecialy[] = "  ( (cmTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";

                        $searchQuerySpecialy[] = "  ( (docTs.specialities_id IN (" . $specialities . ") and fd.type='user' ) )";
                    }

                    //if user select nothing
                    if ($val == '' and $specialities == '') {
                        $searchQuery[] = " (  (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")   )";
                        $searchQuery[] = " (  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ")  )";
                        $searchQuery[] = " (  fd.type = 'user' )";
                    }

                    $searchQueryStrX = @implode("or", $searchQuery);
                    if ($searchQueryStrX) {
                        $searchQueryStr = ' and  ' . $searchQueryStrX . ' ';
                    }

                    $searchQueryStrSpecialityX = @implode("or", $searchQuerySpecialy);
                    if ($searchQueryStrSpecialityX) {
                        $searchQueryStrSpeciality = ' and  ' . $searchQueryStrSpecialityX . " ";
                    }


                    $order_by = ' order by  relevance2 DESC,relevance1 DESC';

                    break;
            }
        } else {

            //word processing start
            if ($val != '') {

                $valX = preg_replace('/[-?]/', '', $val);

                $valFilter = preg_replace($stopWords, ' ', $valX);
                $laString = explode(" ", $valFilter);
                if (count($laString) > 0) {

                    foreach ($laString as $value) {

                        if ($value != '') {

                            $sqlTagLikeArray[] = " tag_name like '%" . $value . "%'";
                            $sqlSepLikeArray[] = " specialities_name like '%" . $value . "%'";
                        }
                    }

                    $sqlTagLike = implode(" or ", $sqlTagLikeArray);
                    $sqlSepLike = implode(" or ", $sqlSepLikeArray);
                } else {


                    $sqlTagLike = " tag_name like '%" . $val . "%'";
                    $sqlSepLike = " specialities_name like '%" . $val . "%'";
                }
                $sqlTag = "select
                *
                from master_tags
                WHERE " . $sqlTagLike . "";
                $query = $this->db->query($sqlTag);
                $result = $query->result();

                $sqlSpec = "select
                master_specialities_id
                from master_specialities_V1
                WHERE " . $sqlSepLike . "";
                //exit;

                $query = $this->db->query($sqlSpec);
                $resultSpeTag = $query->result_array();


                foreach ($resultSpeTag as $valSpec) {

                    $specIdArray[] = $valSpec['master_specialities_id'];
                }

                if (count($specIdArray) > 0) {

                    $specIdStr = implode(",", (array)$specIdArray);
                } else {

                    $specIdStr = $valSpec['master_specialities_id'];
                }
            }
            //word processing end


            //if user select only val
            if ($val != '' and $specialities == '') {
                //echo $val . '<br>';
                if ($valFilter) {

                    $inputWords = $valFilter; // filtered words
                    $fullTxt = '"' . $val . '"'; //original words

                    $words = explode(" ", $inputWords);
                    $string = array_map(function (&$word) {
                        return "+" . $word . "*";
                    }, $words);

                    $words = implode(" ", $string);
                    //echo $words; exit;
                    //echo $val; exit;
                    //$searchQuery[] = " ( (fd.title like '%" . $valFilter . "%' or fd.description like '%" . $valFilter . "%' ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";

                    //for medwiki
                    $searchQuery[] = " ( (MATCH(fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                    $searchQuery[] = " ( (MATCH(fd.title) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $val . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                    //for medwiki


                    //for session
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                    //for session

                    //for user
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and  ( fd.type='user' ) )";
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and  ( fd.type='user' ) )";
                }
                if ($specIdStr) {
                    //echo $val; exit;

                    $searchQuery[] = "  ( (sesTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";

                    $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";

                    $searchQuery[] = "  ( (docTs.specialities_id IN (" . $specIdStr . ") and fd.type='user' ) )";
                }
            }

            //if user select only speciality
            if ($specialities != '' and $val == '') {

                //echo 'hello';

                $searchQuerySpecialy[] = "  ( (sesTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";

                $searchQuerySpecialy[] = "  ( (cmTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";

                $searchQuerySpecialy[] = "  ( (docTs.specialities_id IN (" . $specialities . ") and fd.type='user' ) )";
            }

            //if user select both val and speciality
            if ($specialities != '' and $val != '') {


                if ($valFilter) {

                    $inputWords = $valFilter; // filtered words
                    $fullTxt = '"' . $val . '"'; //original words

                    $words = explode(" ", $inputWords);
                    $string = array_map(function (&$word) {
                        return "+" . $word . "*";
                    }, $words);

                    $words = implode(" ", $string);
                    //echo $words; exit;
                    //echo $val; exit;
                    //$searchQuery[] = " ( (fd.title like '%" . $valFilter . "%' or fd.description like '%" . $valFilter . "%' ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";

                    //for medwiki
                    $searchQuery[] = " ( (MATCH(fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                    $searchQuery[] = " ( (MATCH(fd.title) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $val . "' IN BOOLEAN MODE) ) and  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") )";

                    //for session
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  )";


                    //for user
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $fullTxt . "' IN BOOLEAN MODE) )  and  ( fd.type='user' ) )";
                    $searchQuery[] = " ( (MATCH(fd.title, fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) )  and  ( fd.type='user' ) )";
                }
                if ($specIdStr) {
                    //echo $val; exit;

                    $searchQuery[] = "  ( (sesTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";

                    $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $specIdStr . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";

                    $searchQuery[] = "  ( (docTs.specialities_id IN (" . $specIdStr . ") and fd.type='user' ) )";
                }

                $searchQuerySpecialy[] = "  ( (sesTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 or ks.session_status = 4) " . $client_list . ")  ) )";

                $searchQuerySpecialy[] = "  ( (cmTs.specialities_id IN (" . $specialities . ")  and (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ") ) )";

                $searchQuerySpecialy[] = "  ( (docTs.specialities_id IN (" . $specialities . ") and fd.type='user' ) )";
            }

            //if user select nothing
            if ($val == '' and $specialities == '') {
                $searchQuery[] = " (  (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY) and fd.type = 'session' and (ks.session_status = 1 ) " . $client_list . ")   )";
                $searchQuery[] = " (  (fd.publish_date <= CURDATE() AND fd.type='comp' " . $client_list . ")  )";
                $searchQuery[] = " (  fd.type = 'user' )";
            }

            $searchQueryStrX = @implode("or", $searchQuery);
            if ($searchQueryStrX) {
                $searchQueryStr = ' and ' . $searchQueryStrX;
            }

            $searchQueryStrSpecialityX = @implode("or", $searchQuerySpecialy);
            if ($searchQueryStrSpecialityX) {
                $searchQueryStrSpeciality = ' and ' . $searchQueryStrSpecialityX;
            }


            $order_by = ' order by  relevance2 DESC,relevance1 DESC';
            //,fd.publish_date  DESC, rand()


        }

        $sql = "SELECT
        fd.type_id,
        fd.type,
        fd.title,
        fd.description,
        fd.image,
        fd.session_doctor_id,
        fd.added_on,
        fd.publish_date,
        fd.end_date,

        ksc.category_name,
        ksc.category_logo,

        ks.session_status,
        ks.session_topic,
        ks.session_doctor_id as session_doctor_id_ss,
        ks.color as session_color,
        ks.deeplink as deeplinkKS,

        cln.client_name,
        cln.client_logo,

        fd.publish_date_session,

        GROUP_CONCAT(DISTINCT msCms.specialities_name) as specialities_name_comp,
        GROUP_CONCAT(DISTINCT msSvs.specialities_name) as specialities_name_survey,
        GROUP_CONCAT(DISTINCT msSs.specialities_name) as specialities_name_session,
        GROUP_CONCAT(DISTINCT msDs.specialities_name) as specialities_name_doctor,
        GROUP_CONCAT(DISTINCT msVs.specialities_name) as specialities_name_video,



        GROUP_CONCAT(DISTINCT clintsponCM.client_name) as sponsorCM,
        GROUP_CONCAT(DISTINCT clintsponCM.client_logo) as sponsor_logoCM,


        GROUP_CONCAT(DISTINCT clintsponSES.client_name) as sponsorSES,
        GROUP_CONCAT(DISTINCT clintsponSES.client_logo) as sponsor_logoSES,


        GROUP_CONCAT(DISTINCT clintsponSUV.client_name) as sponsorSUV,
        GROUP_CONCAT(DISTINCT clintsponSUV.client_logo) as sponsor_logoSUV,




        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = fd.type_id and  rt.post_type=fd.type)as averageRating,
        rtmy.rating  as myrating,

        sdoc.doctor_name,
        sdoc.profile_image,
        sdoc.status as session_doc_status,

        (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = fd.type_id and kcm.type = fd.type)as count_comment,


        cm.deeplink as deeplinkCM,
        cm.comp_qa_file_img,
        cm.comp_qa_file_img_thumbnail,
        cm.type as con_type,
        cm.vendor as cm_vendor,
        cm.src as cm_src,
        cm.color as medwiki_color,

        kva.video_archive_file_img,
        kva.deeplink as deeplinKVA,
        kva.vendor,
        kva.src,
        kvaTs.session_doctor_id,

        kvsc.category_name as category_name_video,
        kvsc.category_logo as category_logo_video,

        kvaTs.session_id,

        sv.survey_points,
        sv.category,
        sv.deeplink as deeplinkSV,

        mst.status_name,

        kv.status as vault,

        MATCH(fd.title,fd.description) AGAINST ('" . $words . "' IN BOOLEAN MODE) as relevance1,
        MATCH(fd.title,fd.description) AGAINST ('" . $fullTxt . "' ) as relevance2


        FROM feed_V1 as fd

        LEFT JOIN compendium_to_specialities as cmTs ON (cmTs.comp_qa_id = fd.type_id and fd.type = 'comp')
        LEFT JOIN master_specialities_V1 as msCms ON (msCms.master_specialities_id = cmTs.specialities_id )

        LEFT JOIN survey_to_speciality as svts ON (svts.survey_id = fd.type_id and fd.type = 'survey')
        LEFT JOIN master_specialities_V1 as msSvs ON (msSvs.master_specialities_id = svts.speciality_id )


        LEFT JOIN session_to_specialities as sesTs ON (sesTs.session_id = fd.type_id and fd.type = 'session')
        LEFT JOIN master_specialities_V1 as msSs ON (msSs.master_specialities_id = sesTs.specialities_id )

        LEFT JOIN doctors_to_specialities as docTs ON (docTs.sessions_doctors_id = fd.type_id and fd.type = 'user')
        LEFT JOIN master_specialities_V1 as msDs ON (msDs.master_specialities_id = docTs.specialities_id )

        LEFT JOIN video_archive_to_specialities as vaTs ON (vaTs.video_archive_id = fd.type_id and fd.type = 'video_archive')
        LEFT JOIN master_specialities_V1 as msVs ON (msVs.master_specialities_id = vaTs.specialities_id)


        LEFT JOIN client_master as cln ON cln.client_master_id = fd.client_id



        LEFT JOIN compendium_to_sponsor as cmTspon ON (cmTspon.comp_qa_id = fd.type_id and fd.type = 'comp')
        LEFT JOIN client_master as clintsponCM ON clintsponCM.client_master_id = cmTspon.sponsor_id

        LEFT JOIN session_to_sponsor as sTspon ON (sTspon.session_id = fd.type_id and fd.type = 'session')
        LEFT JOIN client_master as clintsponSES ON clintsponSES.client_master_id = sTspon.sponsor_id

        LEFT JOIN survey_to_sponsor as suvTspon ON (suvTspon.survey_id = fd.type_id and fd.type = 'survey')
        LEFT JOIN client_master as clintsponSUV ON clintsponSUV.client_master_id = suvTspon.sponsor_id

        LEFT JOIN video_archive_to_sponsor as vArspon ON (vArspon.video_archive_id = fd.type_id and fd.type = 'video_archive')
        LEFT JOIN client_master as clintVAspon ON clintVAspon.client_master_id = vArspon.sponsor_id


        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = fd.type_id and  rtmy.post_type=fd.type and rtmy.rating!=0 and  rtmy.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_vault as kv ON kv.post_id = fd.type_id and  kv.type_text=fd.type and  kv.user_id = " . $user_master_id . "
        LEFT JOIN knwlg_sessions_doctors as sdoc ON sdoc.sessions_doctors_id = fd.session_doctor_id
        LEFT JOIN knwlg_sessions_V1 as ks ON (ks.session_id = fd.type_id and fd.type = 'session')
        LEFT JOIN master_session_status as mst ON mst.master_session_status_id = ks.session_status


        LEFT JOIN knwlg_compendium_V1 as cm ON (cm.comp_qa_id=fd.type_id and fd.type = 'comp')
        LEFT JOIN knwlg_video_archive as kva ON (kva.video_archive_id=fd.type_id and fd.type = 'video_archive')

        LEFT JOIN knwlg_sessions_V1 as kvaTs ON (kvaTs.session_id = kva.video_archive_session_id)

        LEFT JOIN survey as sv ON (sv.survey_id=fd.type_id and fd.type = 'survey')
        LEFT JOIN master_session_category as ksc ON(ksc.mastersession_category_id=ks.category_id)
        LEFT JOIN master_session_category as kvsc ON(kvsc.mastersession_category_id=kvaTs.category_id)

        where
        fd.status = 3

        " . $typeQuery . "
        " . $searchQueryStr . "
        " . $searchQueryStrSpeciality . "
        GROUP BY fd.feed_id
        " . $order_by . "
        " . $limit . "";

        //Index were not used because of the different ORDER BY a GROUP BY clauses. (my discovery)3144
        //order by relevance2 DESC

        //echo $sql;
        //exit;
        //" . $order_by . "
        //GROUP_CONCAT(ms.specialities_name) as specialities_name,fd.publish_date desc
        //fd.publish_date_session, fd.publish_date DESC
        $query = $this->db->query($sql);
        $result = $query->result();

        //print_r($result);

        //$price = array_column($result, 'relevance2');
        //array_multisort($price, SORT_DESC, $result);

        $i = 1;
        $vx = array();
        //print_r($result);
        foreach ($result as $val) {

            switch ($val->type) {

                case 'comp':


                    if ($val->comp_qa_file_img_thumbnail) {

                        $img = $val->comp_qa_file_img_thumbnail; //base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
                    } else {

                        $img = '';
                    }


                    $sponsorLogoArry = explode(",", $val->sponsor_logoCM);

                    if (count($sponsorLogoArry) > 0) {

                        foreach ($sponsorLogoArry as $valueSponor) {

                            if ($valueSponor) {
                                $sponsorLogomix[] = change_img_src($valueSponor);
                            }
                        }
                    } else {

                        if ($val->sponsor_logoCM) {
                            $sponsorLogomix[] = change_img_src($val->sponsor_logoCM);
                        }
                    }

                    $sponsorLogo = implode(",", (array)$sponsorLogomix);

                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);


                    $string = htmlentities($val->title, null, 'utf-8');
                    //$string = html_entity_decode($string);
                    $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                    $main_description = "";
                    $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
                    $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                    $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                    $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
                    //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";

                    $vx[] = array(

                        "slno" => $i,
                        "trending_type" => "comp",
                        "con_type" => $val->con_type,
                        "vendor" => $val->cm_vendor,
                        "src" => $val->cm_src,
                        "sponsor_name" => $val->sponsorCM,
                        "sponsor_logo" => $sponsorLogo,

                        "type_id" => $val->type_id,
                        "type" => $val->type,
                        "date" => date(' jS F y', strtotime($val->publish_date)),
                        "question" => html_entity_decode(strip_tags($string)),
                        "image" => change_img_src($img),
                        //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                        "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),

                        "specialities" => ($val->specialities_name_comp != '') ? $val->specialities_name_comp : '',
                        "color" => ($val->medwiki_color != '') ? $val->medwiki_color : '#eb34e5',
                        "client_name" => $val->client_name,
                        "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                        "comment_count" => $val->count_comment,
                        "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                        "myrating" => ($val->myrating != '') ? true : false,
                        "vault" => ($val->vault != '') ? $val->vault : 0,
                        "deeplink" => ($val->deeplinkCM != '') ? $val->deeplinkCM : 0,

                    );
                    break;
                case 'survey':

                    //$dataArry = unserialize($val->data);
                    //$json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                    //$str = preg_replace('/\\\"/', "\"", $json);

                    $sponsorLogoArry = explode(",", $val->sponsor_logoSUV);

                    if (count($sponsorLogoArry) > 0) {

                        foreach ($sponsorLogoArry as $valueSponor) {

                            if ($valueSponor) {
                                $sponsorLogomix[] = base_url('uploads/logo/') . $valueSponor;
                            }
                        }
                    } else {

                        if ($val->sponsor_logo) {
                            $sponsorLogomix[] = base_url('uploads/logo/') . $val->sponsor_logoSUV;
                        }
                    }

                    $sponsorLogo = implode(",", (array)$sponsorLogomix);

                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);


                    $vx[] = array(

                        "survey_id" => $val->type_id,
                        "type_id" => $val->type_id,
                        "type" => "survey",
                        "trending_type" => "survey",
                        "type" => "survey",
                        "category" => $val->category,
                        "point" => $val->survey_points,
                        //"json_data" => $str,
                        "survey_title" => $val->title,
                        "deeplink" => $val->deeplinkSV,
                        "survey_description" => substr($val->description, 0, 150),
                        "image" => change_img_src($val->image),
                        "specialities_name" => $val->specialities_name_survey,
                        "client_name" => $val->client_name,
                        "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),

                        "sponsor_name" => $val->sponsorSUV,
                        "sponsor_logo" => change_img_src($sponsorLogo),


                        "publishing_date" => $val->publishing_date,

                    );


                    break;
                case 'session':


                    $start_time = $val->publish_date;
                    $start_time = date("g:i A", strtotime($start_time));
                    $ses_doc_det_array = array();
                    if ($val->session_doctor_id_ss) {

                        $session_doc_array = explode(",", $val->session_doctor_id_ss);
                        $inc_pp = 0;
                        foreach ($session_doc_array as $single_doctor) {

                            $var = session_doc_detail($single_doctor);
                            $image = $var[0]['profile_image'];
                            /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                                 $logic_image = base_url() . "uploads/docimg/" . $image;
                             } else {

                                 $logic_image = base_url() . "uploads/docimg/MConsult.png";
                             }*/
                            if ($image) {
                                // if (stripos($image, "https://") > -1) {
                                //     $logic_image = $image;
                                // } else {
                                //     $logic_image_path = "uploads/docimg/" . $image;
                                //     $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                //     $logic_image = $imgPr;

                                // }
                                if (stripos($image, "https://storage.googleapis.com") > -1) {
                                    $logic_image = $image;
                                } else {
                                    // $logic_image_path = "uploads/docimg/" . $image;
                                    // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                    $logic_image = docimg; //$imgPr;

                                }
                                //=======================================
                            } else {
                                $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                            }
                            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                            $inc_pp++;
                        }
                    }

                    //$entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
                    $end_time = $val->end_date;
                    $end_time = date("g:i A", strtotime($end_time));


                    /**
                     * new sponsor logic
                     */
                    $sponsorLogoArry = explode(",", $val->sponsor_logoSES);
                    if (count($sponsorLogoArry) > 0) {

                        foreach ($sponsorLogoArry as $valueSponor) {
                            if ($valueSponor) {
                                $sponsorLogomix[] = '' . $valueSponor;
                            }
                        }
                    } else {

                        if ($val->sponsor_logoSES) {
                            $sponsorLogomix[] = '' . $val->sponsor_logoSES;
                        }
                    }
                    $sponsorLogoSES = implode(",", change_img_src($sponsorLogomix));

                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);

                    $vx[] = array(

                        "slno" => $i,
                        "trending_type" => "session",
                        "type_id" => $val->type_id,
                        "session_id" => $val->type_id,
                        "type" => $val->type,
                        "doctor_name" => $val->doctor_name,
                        "session_doctor_id" => $val->session_doctor_id_ss,
                        "date" => date(' jS F y', strtotime($val->publish_date)),

                        "start_datetime" => date(' jS F y', strtotime($val->publish_date)),
                        "display_date" => $start_time . "-" . $end_time,
                        "ms_cat_name" => $val->category_name,
                        "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo),


                        "sponsor_name" => $val->sponsorSES,
                        "sponsor_logo" => change_img_src($sponsorLogoSES),


                        "image" => change_img_src($logic_image),
                        "image_raw_name" => change_img_src($val->profile_image),
                        "session_status" => $val->session_status,
                        "status_name" => $val->status_name,
                        "session_topic" => $val->session_topic,
                        "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),
                        "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),
                        "specialities" => ($val->specialities_name_session != '') ? $val->specialities_name_session : '',
                        "color" => ($val->session_color != '') ? $val->session_color : '#08cc9e',
                        "client_name" => $val->client_name,
                        "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                        "comment_count" => $val->count_comment,
                        "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                        "myrating" => ($val->myrating != '') ? true : false,
                        "vault" => ($val->vault != '') ? $val->vault : 0,
                        "deeplink" => ($val->deeplinkKS != '') ? $val->deeplinkKS : 0,
                        "session_doctor_entities" => $ses_doc_det_array,


                    );

                    break;
                case 'user':


                    if ($val->session_doc_status == 3) {

                        if ($val->profile_image) {

                            if (stripos($val->profile_image, "htt") > -1) {
                                $logic_image = $val->profile_image;
                            } else {

                                // $logic_image_path = "uploads/docimg/" . $val->profile_image;
                                // $imgPr = image_thumb_url($logic_image_path, $val->profile_image, 75, 75, '');
                                $logic_image = docimg; //$imgPr;


                            }
                        } else {

                            $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
                        }
                        /*if (@getimagesize(base_url() . "uploads/docimg/" . $val->image)) {
                            $logic_image = base_url() . "uploads/docimg/" . $val->image;
                        } else {

                            $logic_image = base_url() . "uploads/docimg/MConsult.png";
                        }*/
                        $start_time = $val->publish_date;
                        $start_time = date("g:i A", strtotime($start_time));
                        $end_time = $val->end_date;
                        $end_time = date("g:i A", strtotime($end_time));
                        $var = session_doc_detail($val->type_id);

                        /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                             $logic_image = base_url() . "uploads/docimg/" . $image;
                         } else {

                             $logic_image = base_url() . "uploads/docimg/MConsult.png";
                         }*/
                        $vx[] = array(

                            "slno" => $i,
                            "trending_type" => "user",
                            "type_id" => $val->type_id,
                            "session_doc_status" => $val->session_doc_status,
                            "type" => $val->type,
                            "doctor_name" => $val->title,
                            "description" => $val->description,
                            "date" => date(' jS F y', strtotime($val->publish_date)),
                            "image" => change_img_src($logic_image),
                            "image_raw_name" => $val->image,
                            "specialities" => ($val->specialities_name_doctor != '') ? $val->specialities_name_doctor : '',


                            "session_doctor_name" => $var[0]['doctor_name'],
                            "session_doctor_image" => change_img_src($logic_image),
                            "DepartmentName" => $var[0]['DepartmentName'],
                            "profile" => change_img_src($var[0]['profile']),
                            "description" => $var[0]['description'],
                            "subtitle" => $var[0]['subtitle']


                        );
                    }


                    break;
                case 'video_archive':

                    if ($val->video_archive_file_img) {

                        $img = $val->video_archive_file_img;
                    } else {

                        $img = '';
                    }

                    $sponsorLogoArry = explode(",", $val->sponsor_logoVA);
                    if (count($sponsorLogoArry) > 0) {

                        foreach ($sponsorLogoArry as $valueSponor) {

                            if ($valueSponor) {
                                $sponsorLogomix[] = change_img_src(base_url('uploads/logo/') . $valueSponor);
                            }
                        }
                    } else {

                        if ($val->sponsor_logo) {
                            $sponsorLogomix[] = change_img_src(base_url('uploads/logo/') . $val->sponsor_logo);
                        }
                    }
                    $sponsorLogo = implode(",", (array)$sponsorLogomix);

                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);


                    $ses_doc_det_array = array();

                    if ($val->session_doctor_id) {
                        $session_doc_array = explode(",", $val->session_doctor_id);
                        $inc_pp = 0;
                        foreach ($session_doc_array as $single_doctor) {

                            $var = session_doc_detail($single_doctor);
                            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            if ($image) {
                                // $logic_image_path = "uploads/docimg/" . $image;
                                // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                // $logic_image = $imgPr;
                                if (stripos($image, "https://storage.googleapis.com") > -1) {
                                    $logic_image = $image;
                                    // } else {
                                    //     $logic_image_path = "uploads/docimg/" . $image;
                                    //     $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                    $logic_image = docimg; // $imgPr;

                                }
                            } else {

                                $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                            }
                            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                            $inc_pp++;
                        }
                    }


                    $string = htmlentities($val->title, null, 'utf-8');
                    $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                    $main_description = "";
                    $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
                    $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                    $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                    $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

                    //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
                    $vx[] = array(
                        "slno" => $i,
                        "con_type" => $val->type,
                        "type_id" => $val->type_id,
                        "trending_type" => "video_archive",
                        "vendor" => $val->vendor,
                        "session_id" => $val->session_id,
                        "src" => $val->src,
                        "type_id" => $val->type_id,
                        "date" => date(' jS F y', strtotime($val->publication_date)),
                        "question" => html_entity_decode(strip_tags($string)),
                        "image" => change_img_src($img),
                        //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                        "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                        "specialities" => ($val->specialities_name_video != '') ? $val->specialities_name_video : '',
                        "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                        "client_name" => $val->client_name,
                        "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),

                        "category_name" => $val->category_name_video,
                        "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo_video),

                        "sponsor_name" => $val->sponsor,
                        "sponsor_logo" => $sponsorLogo,

                        "comment_count" => $val->count_comment,
                        "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                        "myrating" => ($val->myrating != '') ? true : false,
                        "vault" => ($val->vault != '') ? $val->vault : 0,
                        "vault" => ($val->deeplinkKVA != '') ? $val->deeplinkKVA : 0,

                        "session_doctor_id" => $val->session_doctor_id,
                        "session_doctor_entities" => $ses_doc_det_array,

                    );
                    break;
                case 'gr':


                    break;
            }
            $i++;
        }
        return $vx;
    }

    /**
     * @param string $user_master_id
     * @param string $client_ids
     * @param string $group_ids
     * @param string $limitFrom
     * @param string $limitTo
     * @param string $val
     * @param string $type
     * @param string $specialities
     * @return array
     */
    public function all_dataNew(
        $user_master_id,
        $client_ids,
        $group_ids,
        $limitFrom,
        $limitTo,
        $val,
        $type,
        $specialities = ''
    ) {

        //ini_set('display_errors', 1);

        $limitToFn = floor($limitTo / 4);
        $limitFromFn = floor($limitFrom / 4);

        //$limitToFn = $limitTo;
        //$limitFromFn = $limitFrom;


        // echo $limitToFn.'---'.$limitFromFn;
        // exit;
        $compArry = $this->getcomp($user_master_id, $limitFromFn, $limitToFn, $val, $specialities);

        $spqArry = $this->getspq($user_master_id, $limitFromFn, $limitToFn, $val, $specialities);

        //$grArry = $this->getgr($user_master_id,$limitFromFn,$limitToFn,$val);
        $sessionpArry = $this->getsession($user_master_id, $limitFromFn, $limitToFn, $val, $specialities);
        $vidArry = $this->getvid($user_master_id, $limitFromFn, $limitToFn, $val, $specialities);
        $trainingArry = $this->gettraining($user_master_id, $limitFromFn, $limitToFn, $val, $specialities);
        $doctorprofileArry = $this->getdoctorprofile($user_master_id, $limitFromFn, $limitToFn, $val, $specialities);
        #exit;
        $response = array();
        $finalArry = array();
        if ($compArry != '') {
            $finalArry = array_merge_recursive($response, $compArry); #,$spqArry,$sessionpArry,$vidArry,$trainingArry,$doctorprofileArry);
        }

        if ($spqArry != '') {
            $finalArry = array_merge_recursive($finalArry, $spqArry); #,$sessionpArry,$vidArry,$trainingArry,$doctorprofileArry);
        }

        if ($sessionpArry != '') {
            $finalArry = array_merge_recursive($finalArry, $sessionpArry); //$vidArry,$trainingArry,$doctorprofileArry);
        }

        if ($vidArry != '') {
            $finalArry = array_merge_recursive($finalArry, $vidArry); //,$trainingArry,$doctorprofileArry);
        }

        if ($trainingArry != '') {
            $finalArry = array_merge_recursive($finalArry, $trainingArry); //,$doctorprofileArry);
        }

        if ($doctorprofileArry != '') {
            $finalArry = array_merge_recursive($finalArry, $doctorprofileArry);
        }
        #$finalArry = array_merge_recursive($compArry,$spqArry,$sessionpArry,$vidArry,$trainingArry,$doctorprofileArry);
        //print_r($finalArry); exit;
        return $finalArry;
    }

    /**
     * @param string $val
     * @return array
     */
    public function autocomplete_dataNew($val = '')
    {

        //ini_set('display_errors', 1);

        $limitToFn = 4;
        $limitFromFn = 0;

        //$limitToFn = $limitTo;
        //$limitFromFn = $limitFrom;
        // echo $limitToFn.'---'.$limitFromFn;
        // exit;


        $compArry = $this->getcompAutocomplete($limitFromFn, $limitToFn, $val);
        $spqArry = $this->getspqAutocomplete($limitFromFn, $limitToFn, $val);
        //$grArry = $this->getgr($user_master_id,$limitFromFn,$limitToFn,$val);
        $sessionpArry = $this->getsessionAutocomplete($limitFromFn, $limitToFn, $val);
        $vidArry = $this->getvidAutocomplete($limitFromFn, $limitToFn, $val);
        $trainingArry = $this->gettrainingAutocomplete($limitFromFn, $limitToFn, $val);
        $doctorprofileArry = $this->getdoctorprofileAutocomplete($limitFromFn, $limitToFn, $val);


        #exit;
        /*$response = array();
        $finalArry =array();
        if($compArry !=''){
            $finalArry = array_merge_recursive($response,$compArry); #,$spqArry,$sessionpArry,$vidArry,$trainingArry,$doctorprofileArry);
        }

        if($spqArry != ''){
            $finalArry = array_merge_recursive( $finalArry,$spqArry); #,$sessionpArry,$vidArry,$trainingArry,$doctorprofileArry);
        }

        if($sessionpArry != ''){
            $finalArry = array_merge_recursive( $finalArry,$sessionpArry); //$vidArry,$trainingArry,$doctorprofileArry);
        }

        if($vidArry != ''){
            $finalArry = array_merge_recursive($finalArry,$vidArry); //,$trainingArry,$doctorprofileArry);
        }

        if($trainingArry != ''){
            $finalArry = array_merge_recursive( $finalArry,$trainingArry); //,$doctorprofileArry);
        }

        if($doctorprofileArry != ''){
            $finalArry = array_merge_recursive($finalArry,$doctorprofileArry);
        }*/

        //print_r($doctorprofileArry); exit;

        $finalArry = array_merge_recursive($compArry, $spqArry, $sessionpArry, $vidArry, $trainingArry, $doctorprofileArry);

        //print_r($finalArry); exit;
        //
        return $finalArry;
    }


    /**
     * @param $user_master_id
     */
    public function getcompAutocomplete(
        $limitFromFn,
        $limitToFn,
        $val
    ) {


        if ($limitFromFn == 0 && $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }

        //echo $val ; exit;
        //$this->db->cache_on();

        $sql = "SELECT
        cm.comp_qa_id as type_id,
        cm.comp_qa_question ,
        cm.comp_qa_answer ,
        cm.comp_qa_answer_raw as description,
        cm.comp_qa_question_raw as title,
        cm.comp_qa_file_img,
        cm.comp_qa_file_img_thumbnail,
        cm.added_on,
        cm.publication_date as publish_date,

        cm.color,
        cm.type,
        cm.vendor,
        cm.src,
        cm.deeplink

        FROM knwlg_compendium_V1 as cm

        WHERE
        cm.status=3
        and cm.privacy_status =0
        and
        cm.publication_date < NOW() - INTERVAL 3 DAY
        and
        cm.comp_qa_question_raw like '" . $val . "%'
        order by  cm.comp_qa_id desc  " . $limit . "";
        //echo $sql; exit;
        //exit; , maxrank DESC
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {

            if ($val->comp_qa_file_img_thumbnail) {

                $img = $val->comp_qa_file_img_thumbnail; //base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
            } else {

                $img = '';
            }


            $string = htmlentities($val->title, null, 'utf-8');
            //$string = html_entity_decode($string);
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(

                "slno" => $i,
                "trending_type" => "comp",
                "type" => "comp",
                "con_type" => $val->type,
                "vendor" => $val->vendor,
                // "src" => $val->src_cm,

                "type_id" => $val->type_id,
                //"type" => $val->type,
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                "imageF" => change_img_src($val->comp_qa_file_img_thumbnail),
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),

            );


            $i++;
        }


        // print_r($vx); exit;


        return $vx;
    }

    /**
     * @param $user_master_id
     * @param $limitFromFn
     * @param $limitToFn
     * @return array
     */
    public function getvidAutocomplete(
        $limitFromFn,
        $limitToFn,
        $val
    ) {


        if ($limitFromFn == 0 && $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        //$this->db->cache_on();


        $sql = "SELECT
        cm.video_archive_id as type_id,
        cm.video_archive_question,
        cm.video_archive_answer,
        cm.video_archive_question_raw,
        cm.video_archive_answer_raw,
        cm.video_archive_file_img,
        cm.video_archive_file_img_thumbnail,
        cm.deeplink,

        cm.added_on,
        cm.publication_date,

        cm.duration,
        cm.type,
        cm.vendor,
        cm.src

        FROM knwlg_video_archive as cm

        WHERE
        cm.status=3
        and
        cm.privacy_status = 0

        and
        cm.video_archive_question_raw like '" . $val . "%'

        GROUP BY cm.video_archive_id
        order by cm.publication_date DESC
        " . $limit . "";

        //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
        // echo $sql; exit;
        //and
        //cm.publication_date <= CURDATE()

        $query = $this->db->query($sql);
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {

            if ($val->video_archive_file_img) {
                $img = $val->video_archive_file_img;
            } else {
                $img = '';
            }


            $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(
                "slno" => $i,
                "trending_type" => "video_archive",
                "type" => "video_archive",
                "con_type" => $val->type,
                "type_id" => $val->type_id,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "type_id" => $val->type_id,
                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),


            );
            $i++;
        }

        return $vx;
    }

    /**
     * @param $user_master_id
     */
    public function getspqAutocomplete(
        $limitFromFn,
        $limitToFn,
        $val
    ) {


        if ($limitFromFn == 0 && $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }

        //$this->db->cache_on();
        $sql = "SELECT
        sv.* ,
        svd.data
        FROM
        survey sv

        JOIN survey_detail as svd ON svd.survey_id = sv.survey_id

        WHERE
        sv.status = 3
        and
        sv.category = 'quiz'

        and
        sv.privacy_status = 0
        and
        sv.survey_title like '" . $val . "%'

        group by sv.survey_id  " . $limit . "";
        //echo $sql; exit;
        //exit;
        //" . $limit . ";
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;1
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();

        $vx = array();

        foreach ($result as $val) {

            $vx[] = array(

                "survey_id" => $val->survey_id,
                "trending_type" => "survey",
                "type" => "survey",
                "type_id" => $val->survey_id,
                "type" => "survey",
                "category" => $val->category,
                "point" => $val->survey_points,
                "survey_title" => $val->survey_title,
                "publishing_date" => $val->publishing_date,

            );
        }

        return $vx;
    }

    /**
     * @param $user_master_id
     */
    public function getgrAutocomplete(
        $limitFromFn,
        $limitToFn,
        $val
    ) {

        if ($limitFromFn == 0 && $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }

        $sql = "SELECT
                    gr.gr_id as type_id,
                    gr.gr_title as title,
                    gr.gr_description as description,
                    gr.gr_chief_scientific_editor ,
                    gr.gr_preview_image,
                    gr.added_on,
                    gr.gr_date_of_publication as publish_date,
                    gr.deeplink,
                    gr.color,

                    cln.client_name,
                    cln.client_logo,

                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                    max( ms.rank) as maxrank,


                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id) as session_doctor_id,


                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,

                    (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = gr.gr_id and  rt.post_type='gr')as averageRating,
                    rtmy.rating  as myrating,

                    (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = gr.gr_id and kcm.type = 'gr')as count_comment,
                    kv.status as vault

                    FROM knwlg_gr_register as gr

                    JOIN gr_to_specialities  as grTs ON grTs.gr_id = gr.gr_id
                    JOIN master_specialities_V1 as ms ON ms.master_specialities_id = grTs.specialities_id
                    JOIN client_master as cln ON cln.client_master_id = gr.client_id

                    LEFT JOIN gr_to_sponsor as grTspon ON grTspon.gr_id = gr.gr_id
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = grTspon.sponsor_id


                    LEFT JOIN gr_to_session_doctor as grTsdoc ON grTsdoc.gr_id = gr.gr_id

                    LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = gr.gr_id and  rtmy.post_type='gr' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                    LEFT JOIN knwlg_vault as kv ON kv.post_id = gr.gr_id and  kv.type_text='gr' and  kv.user_id = " . $user_master_id . "
                    LEFT JOIN knwlg_rating as rt ON rt.post_id = gr.gr_id and  rt.post_type='gr'

                    WHERE
                    gr.status=3
                    and
                    gr.gr_id != 10


                    group by gr.gr_id
                    order by  gr.gr_date_of_publication desc, maxrank DESC  " . $limit . "";


        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {

            $sponsorLogoArry = explode(",", $val->sponsor_logo);

            if (count($sponsorLogoArry) > 0) {

                foreach ($sponsorLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . change_img_src($valueSponor);
                    }
                }
            } else {

                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . change_img_src($val->sponsor_logo);
                }
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);

            unset($sponsorLogomix);
            unset($sponsorLogoArry);

            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {

                    $var = session_doc_detail($single_doctor);
                    //$image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                         $logic_image = base_url() . "uploads/docimg/" . $image;
                     } else {

                         $logic_image = base_url() . "uploads/docimg/MConsult.png";
                     }*/

                    if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {

                        $logic_image = $var[0]['profile_image'];
                    } else {
                        // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = docimg; //$imgPr;
                        //$logic_image = $var[0]['profile_image'];
                    }
                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }

            $vx[] = array(

                "slno" => $i,
                "type_id" => $val->type_id,
                "type" => 'gr',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->gr_preview_image),

                "color" => ($val->color != '') ? $val->color : '#918c91',

                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',

                "client_name" => $val->client_name,
                "client_logo" => '' . change_img_src($val->client_logo),

                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,

                "session_doctor_entities" => $ses_doc_det_array,

                "comment_count" => $val->count_comment,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

            );


            $i++;
        }

        return $vx;
    }

    /**
     * @param string $booked_id
     * @param string $user_master_id
     * @return array
     */
    public function getsessionAutocomplete(
        $limitFromFn,
        $limitToFn,
        $val
    ) {

        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }


        //$this->db->cache_on();
        $sql = "SELECT

        ks.session_id,
		ks.*

		FROM knwlg_sessions_V1 as ks

        WHERE
        ks.status = 3
        and
        ks.privacy_status = 0
        and
        ks.session_topic like '" . $val . "%'
        AND (ks.session_status=1 OR ks.session_status=4)
        and ks.start_datetime > (CURDATE() + INTERVAL 3 DAY)
        GROUP BY ks.session_id
        ORDER BY ks.start_datetime ASC  " . $limit . "";

        #echo $sql; exit();
        $query = $this->db->query($sql);
        //$this->db->cache_off();

        //
        $result = $query->result();
        //print_r($result); exit();
        //ks.session_status
        $i = 0;
        $vx = array();
        foreach ($result as $val) {


            //$entities[$i]['cover_image'] = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;

            $vx[] = array(

                "slno" => $i,
                "trending_type" => "session",
                "type_id" => $val->session_id,
                "session_id" => $val->session_id,
                "type" => 'session',

                "date" => date(' jS F y', strtotime($val->start_datetime)),


                "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),
                "session_topic" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),


            );
        }

        return $vx;
        //print_r($entities); exit();
    }

    /**
     * @param $user_master_id
     * @param $limitFromFn
     * @param $limitToFn
     * @param $val
     * @param $specialities
     * @return array
     */
    public function gettrainingAutocomplete(
        $limitFromFn,
        $limitToFn,
        $val
    ) {


        $this->db->select("tm.*,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id) )as master_spec_id,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,

        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,(select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = tm.id and  rt.post_type='training')as averageRating");

        $this->db->from('training_master as tm');
        $this->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
        $this->db->join('client_master as clintspon', 'ts.sponsor_id=clintspon.client_master_id', 'left');
        $this->db->join('training_to_speciality tts', 'tts.training_id=tm.id', 'left');
        $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');

        $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
        $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id and  rt.post_type='training'", "left");


        if ($val != '') {
            $this->db->like('tm.title', $val);
        }
        $this->db->where('tm.status', 3);


        $this->db->where('date(tm.published_date)<=', date('Y-m-d'));
        $this->db->where('tm.privacy_status', 0);
        $this->db->group_by('tm.id');
        $this->db->order_by('tm.published_date', 'desc');
        if (($limitFromFn != '') && ($limitToFn != '')) {
            $this->db->limit($limitFromFn, $limitToFn);
        }

        $query = $this->db->get();

        //echo $this->db->last_query(); exit;


        $i = 1;
        $vx = array();
        if (($query) && ($query->num_rows())) {
            foreach ($query->result() as $key => $val) {


                $temp = array(
                    "slno" => $i,
                    "trending_type" => "training",
                    "type" => "training",
                    "type_id" => $val->id,
                    "id" => $val->id,
                    "title" => html_entity_decode(strip_tags($val->title)),


                );

                $vx[] = $temp;

                $i++;
            }
        }
        # die;
        # print_r($vx); die;
        return $vx;
    }

    /**
     * @param $user_master_id
     * @param $limitFromFn
     * @param $limitToFn
     * @param $val
     * @param $specialities
     * @return array
     */
    public function getdoctorprofileAutocomplete(
        $limitFromFn,
        $limitToFn,
        $val
    ) {
        $this->db->select('ksd.sessions_doctors_id,ksd.doctor_name,ksd.profile,ksd.profile_image,ksd.subtitle');
        $this->db->from('knwlg_sessions_doctors as ksd');


        $this->db->where('ksd.status', 3);
        if ($val != '') {
            $this->db->like('ksd.doctor_name', $val);
        }

        if (($limitFromFn != '') && ($limitToFn != '')) {

            $this->db->limit($limitFromFn, $limitToFn);
        }

        $query = $this->db->get();
        //print_r($this->db->last_query()); exit();
        $response = array();
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            foreach ($result as $key => $value) {
                $response[] = array(
                    "trending_type" => "profile_doctor",
                    "type" => "profile_doctor",
                    "type_id" => $value->sessions_doctors_id,
                    "doctor_id" => $value->sessions_doctors_id,
                    "doctor_name" => $value->doctor_name,
                    "profile" => $value->profile,
                    "profile_image" => change_img_src($value->profile_image),
                    "subtitle" => $value->subtitle
                );
            }
        }
        return $response;
    }


    /**
     * @param $user_master_id
     */
    public function getcomp(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $val,
        $specialities
    ) {


        if ($specialities) {


            $searchQuerySpecialy = "  and ( (cmTs.specialities_id IN (" . $specialities . ")   ) )";
        }


        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }


        $env = get_user_env($user_master_id);
        if ($env) {

            if ($env != 'GL') {
                $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
            } else {
                $envStatus = "AND cm.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }

        // echo "<pre>";print_r($env);die;
        //$this->db->cache_on();
        #deactivate cache as some null value is fetched
        $cachename = "search_listing_comp_" . $limitFromFn . "_" . $limitToFn . "_" . $specialities . "_" . $envStatus . "_" . $val;
        // print_r($CI->myredis->get($cachename));
        // exit;
        if ($this->myredis->exists($cachename)) {
            $result = $this->myredis->get($cachename);
            //print_r($result); exit;
        } else {

            $sql = "SELECT
        cm.comp_qa_id as type_id,
        cm.comp_qa_question ,
        cm.comp_qa_answer ,
        cm.comp_qa_answer_raw as description,
        cm.comp_qa_question_raw as title,
        cm.comp_qa_file_img,
        cm.comp_qa_file_img_thumbnail,
        cm.added_on,
        cm.publication_date as publish_date,
        cln.client_name,
        cln.client_logo,
        cm.color,
        cm.type,
        cm.vendor,
        cm.src,
        cm.deeplink,
        kv.status as vault,

        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        max( ms.rank) as maxrank,


        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsorCM,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logoCM,

        cm.comp_qa_speciality_id


        FROM knwlg_compendium_V1 as cm

        JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        JOIN client_master as cln ON cln.client_master_id = cm.client_id

        LEFT JOIN compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id

        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and  kv.type_text='comp' and  kv.user_id = " . $user_master_id . "

        WHERE
        cm.status=3
        " . $envStatus . "
        and cm.privacy_status =0

        and
        cm.publication_date < NOW() - INTERVAL 3 DAY

        and

        cm.comp_qa_answer_raw like '%" . $val . "%'

        and
        cm.comp_qa_question_raw like '%" . $val . "%'

        " . $searchQuerySpecialy . "

        group by cm.comp_qa_id
        order by  cm.comp_qa_id desc  " . $limit . "";
            #echo $sql; exit;
            //exit; , maxrank DESC
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;
            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();
            $this->myredis->set($cachename, $result);
        }
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {

            if ($val->comp_qa_file_img_thumbnail) {

                $img = $val->comp_qa_file_img_thumbnail; //base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
            } else {

                $img = '';
            }
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsorCM);
            $sp = 0;
            $sponsorCMLogoArry = explode(",", $val->sponsor_logoCM);
            if (($val->sponsor_logoCM != '') && (count($sponsorCMLogoArry) > 0)) {

                foreach ($sponsorCMLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorCMLogomix[] = '' . change_img_src($valueSponor);
                    }
                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                    $sp++;
                }
            } else {

                if ($val->sponsor_logoCM  != '') {
                    $sponsorCMLogomix[] = '' . change_img_src($val->sponsor_logoCM);
                    $allsponsor[] = array('name' => $val->sponsorCM, "logo" => $sponsorCMLogomix[0]);
                }
            }
            $sponsorCMLogo = implode(",", (array)$sponsorCMLogomix);
            unset($sponsorCMLogomix);
            unset($sponsorCMLogoArry);


            $string = htmlentities($val->title, null, 'utf-8');
            //$string = html_entity_decode($string);
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(

                "slno" => $i,
                "trending_type" => "comp",
                "type" => "comp",
                "con_type" => $val->type,
                "vendor" => $val->vendor,
                "src" => $val->src_cm,
                "sponsor_name" => $val->sponsorCM,
                "sponsor_logo" => change_img_src($sponsorCMLogo),
                "all_sponsor" => $allsponsor,
                "type_id" => $val->type_id,
                //"type" => $val->type,
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                "imageF" => change_img_src($val->comp_qa_file_img_thumbnail),
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),

                "vault" => ($val->vault != '') ? $val->vault : 0,

                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                // "client_name" => $val->client_name,
                // "client_logo" => '' . change_img_src($val->client_logo),
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

            );


            $i++;
        }


        return $vx;
    }

    /**
     * @param $user_master_id
     * @param $limitFromFn
     * @param $limitToFn
     * @return array
     */
    public function getvid(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $val,
        $specialities
    ) {


        if ($specialities) {


            $searchQuerySpecialy = "  and ( (cmTs.specialities_id IN (" . $specialities . ")   ) )";
        }


        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }


        $env = get_user_env($user_master_id);
        if ($env) {

            if ($env != 'GL') {
                $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
            } else {
                $envStatus = "AND cm.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        //$this->db->cache_on();

        $cachename = "search_listing_vid_" . $limitFromFn . "_" . $limitToFn . "_" . $specialities . "_" . $envStatus . "_" . $val;
        ;
        // // print_r($CI->myredis->get($cachename));
        // // exit;
        if ($this->myredis->exists($cachename)) {
            $result = $this->myredis->get($cachename);
            //print_r($result); exit;
        } else {

            $sql = "SELECT
        cm.video_archive_id as type_id,
        cm.video_archive_question,
        cm.video_archive_answer,
        cm.video_archive_question_raw,
        cm.video_archive_answer_raw,
        cm.video_archive_file_img,
        cm.video_archive_file_img_thumbnail,
        cm.deeplink,

        cm.added_on,
        cm.publication_date,
        cln.client_name,
        cln.client_logo,
        cm.duration,
        cm.type,
        cm.vendor,
        cm.src,
        ks.session_doctor_id,
        msct.category_name,
		msct.category_logo,

        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,


        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        cm.video_archive_speciality_id ,
        kv.status as vault,

        (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.video_archive_id and  rt.post_type='video_archive' and rt.rating!=0) as averageRating,
        rtmy.rating  as myrating


        FROM knwlg_video_archive as cm
        JOIN video_archive_to_specialities  as cmTs ON cmTs.video_archive_id = cm.video_archive_id
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id


        LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id

        LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
        LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id

        LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id and  kv.type_text='video_archive' and  kv.user_id = " . $user_master_id . "

        LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id and  rtmy.post_type='video_archive' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
        LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id and  rt.post_type='video_archive'

        JOIN client_master as cln ON cln.client_master_id = cm.client_id
        WHERE
        cm.status=3

         and cm.publication_date <= CURDATE()
        and
        cm.privacy_status = 0
        " . $envStatus . "
        and

        cm.video_archive_answer_raw like '%" . $val . "%'

        and
        cm.video_archive_question_raw like '%" . $val . "%'

        " . $searchQuerySpecialy . "

        GROUP BY cm.video_archive_id
        order by cm.publication_date DESC
        " . $limit . "";

            //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
            // echo $sql; exit;
            //and
            //cm.publication_date <= CURDATE()

            $query = $this->db->query($sql);
            $result = $query->result();
            $this->myredis->set($cachename, $result);
        }
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {

            if ($val->video_archive_file_img) {
                $img = change_img_src($val->video_archive_file_img);
            } else {
                $img = '';
            }
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $val->sponsor_logo);

            if (($val->sponsor_logo != '') && (count($sponsorLogoArry) > 0)) {

                foreach ($sponsorLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . change_img_src($valueSponor);
                    }
                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                    $sp++;
                }
            } else {

                if ($val->sponsor_logo != '') {
                    $sponsorLogomix[] = '' . change_img_src($val->sponsor_logo);
                    $allsponsor[] = array('name' => $val->sponsor, "logo" => $val->sponsor_logo);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $session_doc_array = explode(",", $val->session_doctor_id);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {

                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if ($image) {
                    // $logic_image_path = "uploads/docimg/" . $image;
                    // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    // $logic_image = $imgPr;
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = docimg; //$imgPr;

                    }
                } else {

                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }


            $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            $vx[] = array(
                "slno" => $i,
                "trending_type" => "video_archive",
                "con_type" => $val->type,
                "type" => $val->type,
                "type_id" => $val->type_id,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "type_id" => $val->type_id,
                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                // "client_name" => $val->client_name,
                // "client_logo" => '' . change_img_src($val->client_logo),

                "category_name" => $val->category_name,

                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "all_sponsor" => $allsponsor,
                "duration" => $val->duration,
                "comment_count" => $val->count_comment,
                "deeplink" => $val->deeplink,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,

                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,

            );
            $i++;
        }

        return $vx;
    }

    /**
     * @param $user_master_id
     */
    public function getspq(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $val,
        $specialities
    ) {


        if ($specialities) {


            $searchQuerySpecialy = "  and ( (svts.speciality_id IN (" . $specialities . ")   ) )";
        }

        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }


        $env = get_user_env($user_master_id);
        if ($env) {

            if ($env != 'GL') {
                $envStatus = "AND (sv.env ='GL' or sv.env ='" . $env . "')";
            } else {
                $envStatus = "AND sv.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }

        $cachename = "search_listing_spq_" . $limitFromFn . "_" . $limitToFn . "_" . $specialities . "_" . $envStatus . "_" . $val;
        ;

        if ($this->myredis->exists($cachename)) {
            $result = $this->myredis->get($cachename);
        } else {

            $sql = "SELECT
        sv.* ,
        svd.data,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        cln.client_name,
        cln.client_logo,

        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo

        FROM
        survey sv
        left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
        left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
        JOIN client_master as cln ON cln.client_master_id = sv.client_id



        LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id



        JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
        left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
        WHERE
        sv.status = 3
        " . $envStatus . "
        and
        sv.category = 'quiz'
        and
        sv.display_in_dashboard = 1

        and
        sv.privacy_status = 0

        and

        sv.survey_title like '%" . $val . "%'

        and
        sv.survey_description like '%" . $val . "%'

        {$searchQuerySpecialy}

        and sv.survey_id NOT IN (
            SELECT
                survey_id

            FROM
                survey_user_answer
            WHERE
                user_master_id = {$user_master_id}
        )

        and sv.survey_id NOT IN (
            SELECT
                survey_id
            FROM
                survey_user_incomplete_answer
            WHERE
                status = 3
                and user_master_id = {$user_master_id}
        )

        group by sv.survey_id  " . $limit . "";
            // echo $sql; exit;
            // exit;
            //" . $limit . ";
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;1
            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();
            $this->myredis->set($cachename, $result);
        }
        $vx = array();

        foreach ($result as $val) {


            $dataArry = unserialize($val->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);


            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsor);
            $sp = 0;
            if (($val->sponsor_logo != '') && (count($sponsorLogoArry) > 0)) {

                foreach ($sponsorLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . change_img_src($valueSponor);
                    }
                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                    $sp++;
                }
            } else {

                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . change_img_src($val->sponsor_logo);
                }
                $allsponsor[] = array('name' => $val->sponsor, "logo" => $val->sponsor_logo);
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);

            unset($sponsorLogomix);
            unset($sponsorLogoArry);


            $vx[] = array(

                "survey_id" => $val->survey_id,
                "trending_type" => "survey",

                "type_id" => $val->survey_id,
                "type" => "survey",

                "category" => $val->category,
                "point" => $val->survey_points,
                "json_data" => $str,
                "survey_title" => $val->survey_title,
                "deeplink" => $val->deeplink,
                "survey_description" => substr($val->survey_description, 0, 150),
                "image" => change_img_src($val->image),
                "specialities_name" => $val->specialities_name,
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                // "client_name" => $val->client_name,
                // "client_logo" => '' . change_img_src($val->client_logo),

                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "all_sponsor" => $allsponsor,

                "publishing_date" => $val->publishing_date,

            );
        }

        return $vx;
    }

    /**
     * @param $user_master_id
     */
    public function getgr(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $val,
        $specialities
    ) {

        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }

        $sql = "SELECT
                    gr.gr_id as type_id,
                    gr.gr_title as title,
                    gr.gr_description as description,
                    gr.gr_chief_scientific_editor ,
                    gr.gr_preview_image,
                    gr.added_on,
                    gr.gr_date_of_publication as publish_date,
                    gr.deeplink,
                    gr.color,

                    cln.client_name,
                    cln.client_logo,

                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                    max( ms.rank) as maxrank,


                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id) as session_doctor_id,


                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,

                    (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = gr.gr_id and  rt.post_type='gr')as averageRating,
                    rtmy.rating  as myrating,

                    (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = gr.gr_id and kcm.type = 'gr')as count_comment,
                    kv.status as vault

                    FROM knwlg_gr_register as gr

                    JOIN gr_to_specialities  as grTs ON grTs.gr_id = gr.gr_id
                    JOIN master_specialities_V1 as ms ON ms.master_specialities_id = grTs.specialities_id
                    JOIN client_master as cln ON cln.client_master_id = gr.client_id

                    LEFT JOIN gr_to_sponsor as grTspon ON grTspon.gr_id = gr.gr_id
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = grTspon.sponsor_id


                    LEFT JOIN gr_to_session_doctor as grTsdoc ON grTsdoc.gr_id = gr.gr_id

                    LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = gr.gr_id and  rtmy.post_type='gr' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                    LEFT JOIN knwlg_vault as kv ON kv.post_id = gr.gr_id and  kv.type_text='gr' and  kv.user_id = " . $user_master_id . "
                    LEFT JOIN knwlg_rating as rt ON rt.post_id = gr.gr_id and  rt.post_type='gr'

                    WHERE
                    gr.status=3
                    and
                    gr.gr_id != 10


                    group by gr.gr_id
                    order by  gr.gr_date_of_publication desc, maxrank DESC  " . $limit . "";


        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $result = $query->result();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        foreach ($result as $val) {

            $sponsorLogoArry = explode(",", $val->sponsor_logo);

            if (count($sponsorLogoArry) > 0) {

                foreach ($sponsorLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . change_img_src($valueSponor);
                    }
                }
            } else {

                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . change_img_src($val->sponsor_logo);
                }
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);

            unset($sponsorLogomix);
            unset($sponsorLogoArry);

            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {

                    $var = session_doc_detail($single_doctor);
                    //$image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                         $logic_image = base_url() . "uploads/docimg/" . $image;
                     } else {

                         $logic_image = base_url() . "uploads/docimg/MConsult.png";
                     }*/

                    if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {

                        $logic_image = $var[0]['profile_image'];
                    } else {
                        // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = docimg; //$imgPr;
                        //$logic_image = $var[0]['profile_image'];
                    }
                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }

            $vx[] = array(

                "slno" => $i,
                "type_id" => $val->type_id,
                "type" => 'gr',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->gr_preview_image),

                "color" => ($val->color != '') ? $val->color : '#918c91',

                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',

                "client_name" => $val->client_name,
                "client_logo" => '' . change_img_src($val->client_logo),

                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,

                "session_doctor_entities" => $ses_doc_det_array,

                "comment_count" => $val->count_comment,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

            );


            $i++;
        }

        return $vx;
    }

    /**
     * @param string $booked_id
     * @param string $user_master_id
     * @return array
     */
    public function getsession(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $val,
        $specialities
    ) {

        if ($specialities) {


            $searchQuerySpecialy = "  and ( (sts.specialities_id IN (" . $specialities . ")   ) )";
        }


        if ($limitFromFn == 0 and $limitToFn != '') {

            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        } else {
            $limit = "limit " . $limitFromFn . " , " . $limitToFn;
        }
        $booked_id = $this->all_bookedmastersession($user_master_id);
        $new_arr = array();
        foreach ($booked_id as $sing) {

            $new_arr[] = $sing->knwlg_sessions_id;
        }
        $res_arr = implode(',', $new_arr);

        if ($res_arr) {

            $sqlStr = "and ( (ks.session_id  NOT IN(" . $res_arr . ") AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "') or ks.start_datetime >= '" . date("Y-m-d H:i:s") . "') ";
        } else {

            $sqlStr = "and (ks.start_datetime >= '" . date("Y-m-d H:i:s") . "')";
        }


        $env = get_user_env($user_master_id);
        if ($env) {

            if ($env != 'GL') {
                $envStatus = "AND (ks.env ='GL' or ks.env ='" . $env . "')";
            } else {
                $envStatus = "AND ks.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }

        //echo $res_arr; exit;

        //$this->db->cache_on();
        $cachename = "search_listing_session_" . $limitFromFn . "_" . $limitToFn . "_" . $specialities . "_" . $envStatus . "_" . $val;
        ;
        // // print_r($CI->myredis->get($cachename));
        // // exit;
        if ($this->myredis->exists($cachename)) {
            $result = $this->myredis->get($cachename);
            //print_r($result); exit;
        } else {
            $sql = "SELECT
		ksp.participant_id,
        ks.session_id,
		ks.*,
		sd.*,
		cln.client_name,
		cln.client_logo,
		GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,

		GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,


		msct.category_name,
		msct.category_logo,

		sd.document_path,
		sd.comment,
        ksd.knwlg_sessions_docs_id,
		ksd.document_path,
		ksd.comment,
		GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
		GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
		GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') as  speciality,




		GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as  profile,
		GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as  profile_images,
		GROUP_CONCAT(ksp.participant_id) as  PartName,
		GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED,
		(ks.total_buffer + ks.total_seats) as tot_seat


		FROM knwlg_sessions_V1 as ks


		left JOIN session_to_specialities as sts ON sts.session_id = ks.session_id
        left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = sts.specialities_id


		LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id

		LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id


		LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
		LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
		LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
		LEFT JOIN knwlg_sessions_participant as ksp ON ksp.knwlg_sessions_id = ks.session_id
		LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id

        WHERE
        ks.status = 3

        " . $envStatus . "
        and
        ks.session_status in (1,2,7)
        and
        ks.privacy_status = 0
        and
        ks.session_topic like '%" . $val . "%'
        and
        ks.session_description like '%" . $val . "%'

        " . $searchQuerySpecialy . "
        " . $sqlStr . "

        AND (ks.session_status=1 OR ks.session_status=4)
        and ks.start_datetime > (CURDATE() + INTERVAL 3 DAY)
        GROUP BY ks.session_id
        ORDER BY ks.start_datetime ASC  " . $limit . "";

            #echo $sql; exit();
            $query = $this->db->query($sql);
            //$this->db->cache_off();

            //
            $result = $query->result();
            $this->myredis->set($cachename, $result);
        }
        //print_r($result); exit();
        //ks.session_status
        $i = 0;
        $vx = array();
        foreach ($result as $val) {


            if (stripos($val->profile_image, "https://storage.googleapis.com") > -1) {
                $logic_image = '' . $val->profile_image;
            } else {

                $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
            }

            $start_time = $val->start_datetime;
            $start_time = date("g:i A", strtotime($start_time));
            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {

                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {

                    $var = session_doc_detail($single_doctor);


                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                         $logic_image = base_url() . "uploads/docimg/" . $image;
                     } else {

                         $logic_image = base_url() . "uploads/docimg/MConsult.png";
                     }*/
                    if ($image) {
                        // $logic_image_path = "uploads/docimg/" . $image;
                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        // $logic_image = $imgPr;


                        /////============================== updated by  ramanath  14-5-21
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            // $logic_image_path = "uploads/docimg/" . $image;
                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $image;
                        }
                        //=======================================


                    } else {

                        $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                    }
                    #print_r($image.' ');
                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }
            #print_r($ses_doc_det_array);
            //$entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $end_time = $val->end_datetime;
            $end_time = date("g:i A", strtotime($end_time));

            /**
             * new sponsor logic
             */
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsor);
            $sp = 0;
            $sponsorSESLogoArry = explode(",", $val->sponsor_logo);
            if (($val->sponsor_logo) && (count($sponsorSESLogoArry) > 0)) {

                foreach ($sponsorSESLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorSESLogomix[] = '' . change_img_src($valueSponor);
                    }
                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                    $sp++;
                }
            } else {

                if ($val->sponsor_logoSES) {
                    $sponsorSESLogomix[] = '' . change_img_src($val->sponsor_logoSES);
                }
                $allsponsor[] = array('name' => $val->sponsor, "logo" => $val->sponsor_logoSES);
            }
            $sponsorLogoSES = implode(",", (array)$sponsorSESLogomix);

            unset($sponsorSESLogomix);
            unset($sponsorSESLogoArry);

            $coverImg = base_url() . "uploads/sessionBgDefault.jpeg";

            //$entities[$i]['cover_image'] = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;

            $vx[] = array(

                "slno" => $i,
                "trending_type" => "session",
                "type_id" => $val->session_id,
                "session_id" => $val->session_id,
                "type" => 'session',
                "doctor_name" => $val->doctor_name,
                "cover_image" => ($val->cover_image != '') ? change_img_src($val->cover_image) : change_img_src($coverImg),
                "session_doctor_id" => $val->session_doctor_id,
                "date" => date(' jS F y', strtotime($val->start_datetime)),

                "start_datetime" => $val->start_datetime, //date(' jS F y', strtotime($val->start_datetime)),
                "display_date" => $start_time . "-" . $end_time,
                "ms_cat_name" => $val->category_name,
                "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo),


                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogoSES,
                "all_sponsor" => $allsponsor,

                "image" => change_img_src($logic_image),
                "image_raw_name" => change_img_src($val->profile_image),
                "session_status" => $val->session_status,
                "status_name" => $val->status_name,
                "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),
                "session_topic" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),

                "color" => ($val->color != '') ? $val->color : '#08cc9e',
                // "client_name" => $val->client_name,
                // "client_logo" => '' . change_img_src($val->client_logo),

                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                "session_doctor_entities" => $ses_doc_det_array,
                "cpddetail" => $this->getcpddetails($val->session_id),

            );
        }

        return $vx;
        //print_r($entities); exit();
    }

    /**
     * @param $user_master_id
     * @param $limitFromFn
     * @param $limitToFn
     * @param $val
     * @param $specialities
     * @return array
     */
    public function gettraining(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $val,
        $specialities
    ) {
        $env = get_user_env($user_master_id);
        if ($env) {

            if ($env != 'GL') {
                $envStatus = array("GL", $env);
            } else {
                $envStatus = array($env);
            }
        } else {
            $envStatus = array();
        }

        $cachename = "search_listing_session_" . $limitFromFn . "_" . $limitToFn . "_" . $specialities . "_" . $val . "_" . $envStatus;
        // print_r($CI->myredis->get($cachename));
        // exit;
        if ($this->myredis->exists($cachename)) {
            $result = $this->myredis->get($cachename);
            //print_r($result); exit;
        } else {




            $this->db->select("tm.*,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id) )as master_spec_id,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,

        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,(select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = tm.id and  rt.post_type='training')as averageRating");

            $this->db->from('training_master as tm');
            $this->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
            $this->db->join('client_master as clintspon', 'ts.sponsor_id=clintspon.client_master_id', 'left');
            $this->db->join('training_to_speciality tts', 'tts.training_id=tm.id', 'left');
            $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');

            $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
            $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id and  rt.post_type='training'", "left");


            if ($specialities != '') {
                $spids = explode(',', $specialities);
                $this->db->where_in('tts.specialities_id', $spids);
            }

            if ($val != '') {
                $this->db->like('tm.title', $val);
                $this->db->or_like('tm.description', $val);
            }
            $this->db->where('tm.status', 3);
            $this->db->where_in('tm.env', $envStatus);


            $this->db->where('date(tm.published_date)<=', date('Y-m-d'));
            $this->db->where('tm.privacy_status', 0);
            $this->db->group_by('tm.id');
            $this->db->order_by('tm.published_date', 'desc');
            if (($limitFromFn != '') && ($limitToFn != '')) {
                $this->db->limit($limitFromFn, $limitToFn);
            }

            $query = $this->db->get();
            $i = 1;
            $result = $query->result();
            $this->myredis->set($cachename, $result);
        }
        if (count($result) > 0) {
            foreach ($result as $key => $val) {


                $sponsorLogoArry = explode(",", $val->sponsor_logo);
                $allsponsor = array();
                $sponsorname = explode(",", $val->sponsor);
                $sp = 0;
                if (($val->sponsor_logo != '') && (count($sponsorLogoArry) > 0)) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = change_img_src($valueSponor);
                        }
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        $sp++;
                    }
                } else {

                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                    }
                    $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                }

                $sponsorLogo = implode(",", (array)$sponsorLogomix);

                unset($sponsorLogomix);
                unset($sponsorLogoArry);

                $ses_doc_det_array = array();
                if ($val->session_doctor_id) {
                    $session_doc_array = explode(",", $val->session_doctor_id);
                    $ses_doc_det_array = array();
                    $inc_pp = 0;
                    foreach ($session_doc_array as $single_doctor) {

                        $var = session_doc_detail($single_doctor);
                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);

                        if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {

                            $logic_image = $var[0]['profile_image'];
                        } else {
                            // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            // $logic_image_path = "uploads/docimg/" . $image;
                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = docimg; //$imgPr;
                            //$logic_image = $var[0]['profile_image'];
                        }


                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                        $inc_pp++;
                    }
                }

                $temp = array(
                    "slno" => $i,
                    "trending_type" => "training",
                    "type" => "training",
                    "type_id" => $val->id,
                    "id" => $val->id,
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "image" => change_img_src($val->preview_image),
                    "featured_video" => $val->featured_video,
                    "color" => ($val->color != '') ? $val->color : '#eb34e5',
                    "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "module_Count" => $this->count_module($val->id),
                    "live_session_status" => $this->livestatus($val->id),
                    "training_module_content" => $this->content_count($val->id),
                    "specialities" => $this->specialityname($val->master_spec_id), #($val->specialities_name != '') ? $val->specialities_name : '',
                    "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),

                    "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',

                    // "client_name" => $val->client_name,
                    // "client_logo" => change_img_src($val->client_logo),

                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => $sponsorLogo,
                    "all_sponsor" => $allsponsor,

                    "session_doctor_entities" => $ses_doc_det_array,

                    "duration" => $val->duration,

                    "is_certificate" => ($val->cert_template_id != '') ? true : false,
                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',


                    "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

                );

                $vx[] = $temp;

                $i++;
            }
        }
        # die;
        # print_r($vx); die;
        return $vx;
    }

    public function count_module($id)
    {
        if ($id != '') {
            $this->db->select('id');
            $this->db->from('training_module');
            $this->db->where(array('training_id' => $id, "status" => 3));

            $query = $this->db->get();
            if (($query) && ($query->num_rows() > 0)) {
                return $query->num_rows();
            } else {
                return 0;
            }
        } else {
            return 0;
        }
    }

    public function content_count($id)
    {
        $data = array();

        if ($id != '') {
            $data['comp'] = 0;
            $data['session'] = 0;
            $data['survey'] = 0;
            $data['video_archieve'] = 0;
            $data['live_training'] = 0;
            $this->db->select('id,type');
            $this->db->from('training_module_content');
            $this->db->where(array('training_id' => $id, 'status' => 3));
            $query = $this->db->get();

            if (($query) && ($query->num_rows() > 0)) {
                foreach ($query->result() as $key => $value) {
                    if ($value->type != '') {
                        switch ($value->type) {
                            case "comp":
                                $data['comp'] = $data['comp'] + 1;
                                break;
                            case "clinical_video":
                                $data['video_archieve'] = $data['video_archieve'] + 1;
                                break;
                            case "session":
                                $data['session'] = $data['session'] + 1;
                                break;
                            case "survey":
                                $data['survey'] = $data['survey'] + 1;
                                break;
                            case "video_archieve":
                                $data['video_archieve'] = $data['video_archieve'] + 1;
                                break;
                            case "live_video":
                                $data['live_training'] = $data['live_training'] + 1;
                                break;
                        }
                    }
                }
            }
        }
        return $data;
    }

    public function specialityname($id)
    {
        $name = '';
        $array = array();
        if ($id != '') {
            $ids = explode(',', $id);

            foreach ($ids as $value) {
                $array[] = $this->special($value, $name);
            }
            $name = implode(",", (array)$array);
        }
        return $name;
    }

    public function special($id, $name)
    {
        // $spec = '';
        //$spec = array();

        $sql = "SELECT ms.* FROM master_specialities_V1 as ms where ms.master_specialities_id = " . $id . " ";
        $query = $this->db->query($sql);
        //$data = array();
        $row = $query->row();
        $pid = $row->parent_id;
        // if($this->name ==''){
        $this->name = $row->specialities_name;
        // }else{
        //   $this->name = $row->specialities_name."-".$this->name;
        // }

        if ($pid != 0) {
            $this->specialityname($pid, $this->name);
        }
        return $this->name;
    }

    public function livestatus($id)
    {
        $currentdatetime = date('Y-m-d H:i:s');
        $status = 0;
        if ($id != '') {
            $this->db->select('tmc.id,tmc.type_id');
            $this->db->from('training_module_content as tmc');
            $this->db->join('knwlg_sessions_V1 as ks', 'ks.session_id = tmc.type_id');
            $this->db->where(array('tmc.type' => 'session', 'tmc.training_id' => $id, 'ks.session_status' => 2, "tmc.status" => 3));
            $this->db->where("'" . $currentdatetime . "' BETWEEN ks.start_datetime and ks.end_datetime");

            $query = $this->db->get();
            if (($query) && ($query->num_rows() > 0)) {
                $status = 1;
            } else {
                $this->db->select('id');
                $this->db->from('training_module_content');
                $this->db->where(array('type' => 'live_video', 'training_id' => $id));
                $this->db->where("'" . $currentdatetime . "' BETWEEN start_datetime and end_datetime");

                $querylivevideo = $this->db->get();
                if (($querylivevideo) && ($querylivevideo->num_rows() > 0)) {
                    $status = 1;
                }
            }
        }
        // print_r($status);
        // die;
        return $status;
    }

    public function getdoctorprofile(
        $user_master_id,
        $limitFromFn,
        $limitToFn,
        $val,
        $specialities
    ) {

        $env = get_user_env($user_master_id);
        if ($env) {

            if ($env != 'GL') {
                $envStatus = array("GL", $env);
            } else {
                $envStatus = array($env);
            }
        } else {
            $envStatus = array();
        }

        $this->db->select('ksd.sessions_doctors_id,ksd.doctor_name,ksd.profile,ksd.profile_image,ksd.subtitle');
        $this->db->from('knwlg_sessions_doctors as ksd');

        if ($specialities != '') {
            $spids = explode(',', $specialities);
            $this->db->join('doctors_to_specialities as dts', 'dts.sessions_doctors_id = ksd.sessions_doctors_id');
            $this->db->where_in('dts.specialities_id', $spids);
        }
        $this->db->where('ksd.status', 3);
        $this->db->where_in('ksd.env', $envStatus);
        if ($val != '') {
            $this->db->like('ksd.doctor_name', $val);
            $this->db->or_like('ksd.profile', $val);
            $this->db->or_like('ksd.subtitle', $val);
        }

        if (($limitFromFn != '') && ($limitToFn != '')) {

            $this->db->limit($limitFromFn, $limitToFn);
        }

        $query = $this->db->get();
        #print_r($this->db->last_query()); exit();
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            foreach ($result as $key => $value) {
                $response[] = array(
                    "trending_type" => "profile_doctor",
                    "type" => "profile_doctor",
                    "type_id" => $value->sessions_doctors_id,
                    "doctor_id" => $value->sessions_doctors_id,
                    "doctor_name" => $value->doctor_name,
                    "profile" => $value->profile,
                    "profile_image" => change_img_src($value->profile_image),
                    "subtitle" => $value->subtitle
                );
            }
        }
        return $response;
    }

    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function all_bookedmastersession($user_master_id)
    {
        if (!empty($user_master_id)) {

            $this->db->select('knwlg_sessions_id');
            $this->db->where('participant_id', $user_master_id);
            $this->db->where('participant_type', 'member');
            $this->db->where_not_in('session_approval_status', 3);

            $query = $this->db->get('knwlg_sessions_participant');
            $result = $query->result();
            //echo $this->db->last_query(); exit();
            return $result;
        }
    }

    public function explode_speciality_string($string)
    {
        $final = array();
        if (!empty($string)) {
            $temp_sp_array = explode(",", $string);
            foreach ($temp_sp_array as $ky => $sp_id_name) {
                $sp_id_name_array = explode("#", $sp_id_name);
                $final[$ky] = array();
                $final[$ky]['id'] = $sp_id_name_array[0];
                $final[$ky]['name'] = $sp_id_name_array[1];
            }
        }

        return $final;
    }

    public function insert($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        $this->insertdb->insert('user_search', $data);
        // print_r($this->db->last_query());
        // exit();
        return true;
    }

    public function searchlist(
        $user_master_id,
        $limitTo,
        $limitFrom,
        $type
    ) {
        // $this->db->save_queries = TRUE;

        $this->db->select('DISTINCT(data)');
        $this->db->from('user_search');
        $this->db->where('user_master_id', $user_master_id);
        $this->db->where('data !=', '""');
        if ($type != '') {
            $this->db->like('search_type', $type);
        }
        $this->db->order_by('added_on', 'desc');
        if ($limitFrom != '' and $limitTo != '') {

            $this->db->limit($limitTo, $limitFrom);
        }

        $query = $this->db->get();
        //print_r($query); exit;
        $count = $query->num_rows();
        //print_r($this->db->last_query()); exit;
        if (($query) && ($count > 0)) {

            return $query->result();
        } else {

            $serchresult = $this->searchtrendlist($limitTo, $limitFrom, $user_master_id);


            return $serchresult;
        }
    }

    public function searchtrendlist(
        $limitTo,
        $limitFrom,
        $user_master_id
    ) {


        $this->load->library('CrmMyredis');
        $cachename = USER_INFO_CACHE_KEY.$user_master_id;


        $resultInt = null;
        if ($this->crmmyredis->exists($cachename)) {
            $userDetail = $this->crmmyredis->get($cachename);
            if (!empty($userDetail) && $userDetail["is_id"] != '') {
                $interest_spec_ids = explode(",", $userDetail["is_id"]);
                $resultInt = array();
                foreach ($interest_spec_ids as $key => $val) {
                    $resultInt[] = array("specialities_id" => $val );
                }
            }
        } else {
            $sqlInt = "select distinct(specialities_id) from user_to_interest where user_master_id = " . $user_master_id . "";
            $queryInt = $this->db->query($sqlInt);
            $resultInt = $queryInt->result_array();
        }


        //print_r($resultInt); exit;
        //print_r($sqlInt); exit;
        $specialitiesUser = array();
        foreach ($resultInt as $value) {
            $specialitiesUser[] = $value['specialities_id'];
            //$specialities = array_merge($specialities, $val);
        }

        $this->db->select('DISTINCT(keyword) as data');
        $this->db->from('user_search_trending');
        if (!empty($specialitiesUser)) {

            $this->db->where_in('speciality_id', $specialitiesUser);
        }

        $this->db->order_by('added_on', 'desc');
        if ($limitFrom != '' and $limitTo != '') {

            $this->db->limit($limitTo, $limitFrom);
        }

        $query = $this->db->get();
        //print_R($query); exit;
        //print_r($this->db->last_query()); exit;
        if (($query) && ($query->num_rows() > 0)) {


            return $query->result();
        } else {
            return array();
        }
    }

    public function getcpddetails($session_id)
    {
        $this->db->select('id');
        $this->db->from('Master_service');
        $this->db->where('name', 'session');

        $query = $this->db->get();

        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();

            $this->db->select('ctc.id,ctc.points,c.name,c.short_name');
            $this->db->from('content_to_cpd as ctc');
            $this->db->join('council as c', 'c.id=ctc.mc_id');

            $this->db->where(array('ctc.type_id' => $session_id, 'ctc.status' => 3, 'ctc.type' => $result[0]->id));
            $query = $this->db->get();
            #print_r($this->db->last_query()); exit;
            if (($query) && ($query->num_rows())) {
                return $query->result();
            } else {
                return array();
            }
        } else {
            return array();
        }
    }

    public function getdata(
        $val,
        $user_master_id,
        $specialities
    ) {
        // print_r($val);
        // exit;

        if (!empty($val)) {
            $vx = array();

            foreach ($val as $key => $value) {
                // print_r($value); //exit;
                // echo '<pre>'; print_r($val);die;
                $ids = "";
                $ids = implode(",", (array)$value);
                // print_r($ids."<br>");
                switch ($key) {
                    case "medwiki":

                        return $this->all_compendium($user_master_id, $ids, $key);

                        // $env = get_user_env($user_master_id);
                        // if ($env) {

                        //     if ($env != 'GL') {
                        //         $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
                        //     } else {
                        //         $envStatus = "AND cm.env ='" . $env . "'";
                        //     }
                        // } else {
                        //     $envStatus = "";
                        // }

                        // $cachename = "medwiki_search_" . $ids;
                        // // print_r($CI->myredis->get($cachename));
                        // // exit;
                        // if ($this->myredis->exists($cachename)) {
                        //     $result = $this->myredis->get($cachename);
                        //     //print_r($result); exit;
                        // } else {

                        //     $sql = "SELECT
                        // cm.comp_qa_id as type_id,
                        // cm.comp_qa_question ,
                        // cm.comp_qa_answer ,
                        // cm.comp_qa_answer_raw as description,
                        // cm.comp_qa_question_raw as title,
                        // cm.comp_qa_file_img,
                        // cm.comp_qa_file_img_thumbnail,
                        // cm.added_on,
                        // cm.publication_date as publish_date,
                        // cln.client_name,
                        // cln.client_logo,
                        // cm.color,
                        // cm.type,
                        // cm.vendor,
                        // cm.src,
                        // cm.deeplink,
                        // kv.status as vault,

                        // GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        // GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                        // max( ms.rank) as maxrank,


                        // GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsorCM,
                        // GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logoCM,

                        // cm.comp_qa_speciality_id



                        // FROM knwlg_compendium_V1 as cm


                        // JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
                        // JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id


                        // LEFT JOIN compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = cm.comp_qa_id
                        // LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id


                        // JOIN client_master as cln ON cln.client_master_id = cm.client_id

                        // LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and  kv.type_text='comp' and  kv.user_id = " . $user_master_id . "

                        // WHERE cm.status=3
                        // " . $envStatus . "
                        // AND cm.privacy_status = 0 and
                        // cm.comp_qa_id in ($ids)
                        // group by cm.comp_qa_id
                        // order by  cm.comp_qa_id desc ";

                        //     // echo $sql;
                        //     // exit;
                        //     $query = $this->db->query($sql);
                        //     //print_R($this->db->last_query()); exit;
                        //     //$this->db->cache_off();
                        //     $result = $query->result();
                        //     $this->myredis->set($cachename, $result);
                        // }
                        // $i = 1;

                        // foreach ($result as $val) {
                        //     // print_r($val);
                        //     if ($val->comp_qa_file_img_thumbnail) {
                        //         $img = change_img_src($val->comp_qa_file_img_thumbnail); //base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
                        //     } else {

                        //         $img = '';
                        //     }
                        //     $allsponsor = array();
                        //     $sponsorname = explode(",", $val->sponsorCM);
                        //     $sp = 0;
                        //     $sponsorCMLogoArry = explode(",", $val->sponsor_logoCM);
                        //     if (($val->sponsor_logoCM != '') && (count($sponsorCMLogoArry) > 0)) {

                        //         foreach ($sponsorCMLogoArry as $valueSponor) {

                        //             if ($valueSponor) {
                        //                 $sponsorCMLogomix[] = '' . change_img_src($valueSponor);
                        //             }
                        //             $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        //             $sp++;
                        //         }
                        //     } else {

                        //         if ($val->sponsor_logoCM) {
                        //             $sponsorCMLogomix[] = '' . change_img_src($val->sponsor_logoCM);
                        //         }
                        //         if ($val->sponsor_logoCM != "") {
                        //             $allsponsor[] = array('name' => $val->sponsorCM, "logo" => $valueSponor);
                        //         }
                        //     }
                        //     $sponsorCMLogo = implode(",", (array)$sponsorCMLogomix);
                        //     unset($sponsorCMLogomix);
                        //     unset($sponsorCMLogoArry);


                        //     $string = htmlentities($val->title, null, 'utf-8');
                        //     //$string = html_entity_decode($string);
                        //     $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                        //     $main_description = "";
                        //     $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);


                        //     $vx[$key][$val->type_id] = array(

                        //         "slno" => $i,
                        //         "trending_type" => "comp",
                        //         "type" => "comp",
                        //         "con_type" => $val->type,
                        //         "vendor" => $val->vendor,
                        //         "src" => $val->src_cm,
                        //         "sponsor_name" => $val->sponsorCM,
                        //         "sponsor_logo" => $sponsorCMLogo,
                        //         "all_sponsor" => $allsponsor,
                        //         "type_id" => $val->type_id,
                        //         //"type" => $val->type,
                        //         "date" => date(' jS F y', strtotime($val->publish_date)),
                        //         "question" => html_entity_decode(strip_tags($string)),
                        //         "image" => change_img_src($img),
                        //         "imageF" => change_img_src($val->comp_qa_file_img_thumbnail),
                        //         //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                        //         "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                        //         "vault" => ($val->vault != '') ? $val->vault : 0,
                        //         "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                        //         "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                        //         "color" => ($val->color != '') ? $val->color : '#918c91',
                        //         "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                        //     );
                        //     $i++;
                        // }
                        //print_r($vx); exit;
                        break;
                    case "survey":
                        $sqlCompl = "SELECT sv.* FROM survey_user_answer sv WHERE sv.user_master_id = '" . $user_master_id . "'";

                        $queryCompl = $this->db->query($sqlCompl);
                        $resultCompl = $queryCompl->result();

                        $complID = array();
                        foreach ($resultCompl as $valCompl) {

                            $complID[] = $valCompl->survey_id;
                        }

                        $sqlInCompl = "SELECT
                                    sv.*

                                    FROM
                                    survey_user_incomplete_answer sv
                                    WHERE
                                    sv.status = 3
                                    and
                                    sv.user_master_id = '" . $user_master_id . "'";

                        $queryInCompl = $this->db->query($sqlInCompl);
                        $resultInCompl = $queryInCompl->result();

                        $incomplID = array();
                        foreach ($resultInCompl as $valInCompl) {

                            $incomplID[] = $valInCompl->survey_id;
                        }


                        $arrayFinal = array_unique(array_merge($complID, $incomplID));
                        //print_r($arrayFinal); exit;
                        $complIDStr = implode(",", (array)$arrayFinal);


                        // echo $complIDStr ; exit;

                        if ($complIDStr) {


                            $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
                        } else {


                            $qryStr = '';
                        }

                        //$this->db->cache_on();
                        $cachename = "survey_search_" . $ids;
                        if ($this->myredis->exists($cachename)) {
                            $result = $this->myredis->get($cachename);
                            //print_r($result); exit;
                        } else {

                            $env = get_user_env($user_master_id);
                            if ($env) {

                                if ($env != 'GL') {
                                    $envStatus = "AND (sv.env ='GL' or sv.env ='" . $env . "')";
                                } else {
                                    $envStatus = "AND sv.env ='" . $env . "'";
                                }
                            } else {
                                $envStatus = "";
                            }


                            $sql = "SELECT
                        sv.* ,
                        svd.data,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                        cln.client_name,
                        cln.client_logo,

                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo

                        FROM
                        survey sv
                        left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
                        left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                        left JOIN client_master as cln ON cln.client_master_id = sv.client_id



                        LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
                        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id



                        left JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
                        left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
                        WHERE

                        sv.status = 3
                        " . $envStatus . "
                        and
                        sv.category = 'quiz'
                        and
                        sv.display_in_dashboard = 1

                        and
                        sv.privacy_status = 0

                        and
                        sv.survey_id in ($ids)
                        group by sv.survey_id  ";
                            //echo $sql; exit;
                            //exit;
                            //" . $limit . ";
                            //add child checking in this sql
                            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
                            //exit;
                            //echo  $sql; exit;1
                            $query = $this->db->query($sql);
                            //$this->db->cache_off();
                            $result = $query->result();
                            $this->myredis->set($cachename, $result);
                        }
                        foreach ($result as $val) {


                            $dataArry = unserialize($val->data);
                            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                            $str = preg_replace('/\\\"/', "\"", $json);
                            $allsponsor = array();
                            $sponsorname = explode(",", $val->sponsor);
                            $sp = 0;

                            $sponsorLogoArry = explode(",", $val->sponsor_logo);

                            if (count($sponsorLogoArry) > 0) {

                                foreach ($sponsorLogoArry as $valueSponor) {

                                    if ($valueSponor) {
                                        $sponsorLogomix[] = '' . change_img_src($valueSponor);
                                    }
                                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                                    $sp++;
                                }
                            } else {

                                if ($val->sponsor_logo) {
                                    $sponsorLogomix[] = '' . change_img_src($val->sponsor_logo);
                                }
                                $allsponsor[] = array('name' => $val->sponsor, "logo" => $sponsor_logo);
                            }

                            if (!empty($sponsorLogomix)) {

                                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                            } else {

                                $sponsorLogo = "";
                            }

                            unset($sponsorLogomix);
                            unset($sponsorLogoArry);

                            //$searchresult = array_search($val->survey_id,$value,true);
                            $vx[$key][$val->survey_id] = array(

                                "survey_id" => $val->survey_id,
                                "trending_type" => "survey",

                                "type_id" => $val->survey_id,
                                "type" => "survey",
                                "category" => $val->category,
                                "point" => $val->survey_points,
                                "json_data" => $str,
                                "survey_title" => $val->survey_title,
                                "survey_duration" => timeconversion($val->survey_time),
                                "deeplink" => $val->deeplink,
                                "survey_description" => substr($val->survey_description, 0, 150),
                                "image" => change_img_src($val->image),
                                "specialities_name" => $val->specialities_name,
                                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                                // "client_name" => $val->client_name,
                                // "client_logo" => '' . change_img_src($val->client_logo),

                                "sponsor_name" => $val->sponsor,
                                "sponsor_logo" => $sponsorLogo,
                                "all_sponsor" => $allsponsor,

                                "publishing_date" => $val->publishing_date,

                            );
                        }
                        break;
                    case "course":

                        $cachename = "course_search_" . $ids;
                        if ($this->myredis->exists($cachename)) {
                            $result = $this->myredis->get($cachename);
                            //print_r($result); exit;
                        } else {

                            $env = get_user_env($user_master_id);
                            if ($env) {

                                if ($env != 'GL') {
                                    $envStatus = arry("GL", $env);
                                } else {
                                    $envStatus = arry($env);
                                }
                            } else {
                                $envStatus = array();
                            }

                            $this->db->select("tm.*,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id) )as master_spec_id,
                        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                        max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,

                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,(select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = tm.id and  rt.post_type='training')as averageRating");

                            $this->db->from('training_master as tm');
                            $this->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
                            $this->db->join('client_master as clintspon', 'ts.sponsor_id=clintspon.client_master_id', 'left');
                            $this->db->join('training_to_speciality tts', 'tts.training_id=tm.id', 'left');
                            $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');

                            $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
                            $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id and  rt.post_type='training'", "left");
                            $this->db->where('tm.status', 3);
                            $this->db->where('date(tm.published_date)<=', date('Y-m-d'));
                            $this->db->where('tm.privacy_status', 0);
                            $this->db->where_in('tm.id', $value);

                            $this->db->where_in('tm.env', $envStatus);

                            $this->db->group_by('tm.id');
                            $this->db->order_by('tm.published_date', 'desc');
                            $query = $this->db->get();
                            // print_r($this->db->last_query());
                            //  exit;
                            $i = 1;

                            $result = $query->result();
                            //echo "<pre>";print_r($result); exit;

                            $this->myredis->set($cachename, $result);
                        }
                        if (count($result) > 0) {
                            foreach ($result as $key => $val) {

                                $allsponsor = array();
                                $sponsorname = explode(",", $val->sponsor);
                                $sp = 0;
                                $sponsorLogoArry = explode(",", $val->sponsor_logo);

                                if (($val->sponsor_logo) && (count($sponsorLogoArry) > 0)) {

                                    foreach ($sponsorLogoArry as $valueSponor) {

                                        if ($valueSponor) {
                                            $sponsorLogomix[] = $valueSponor;
                                        }
                                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                                        $sp++;
                                    }
                                } else {

                                    if ($val->sponsor_logo) {
                                        $sponsorLogomix[] = $val->sponsor_logo;
                                    }
                                    $allsponsor[] = array('name' => $val->sponsorCM, "logo" => $valueSponor);
                                }

                                $sponsorLogo = implode(",", (array)$sponsorLogomix);

                                unset($sponsorLogomix);
                                unset($sponsorLogoArry);

                                $ses_doc_det_array = array();
                                if ($val->session_doctor_id) {
                                    $session_doc_array = explode(",", $val->session_doctor_id);
                                    $ses_doc_det_array = array();
                                    $inc_pp = 0;
                                    foreach ($session_doc_array as $single_doctor) {

                                        $var = session_doc_detail($single_doctor);
                                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);

                                        if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {

                                            $logic_image = $var[0]['profile_image'];
                                        } else {
                                            // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                            // $logic_image_path = "uploads/docimg/" . $image;
                                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                            $logic_image = docimg; //$imgPr;
                                            //$logic_image = $var[0]['profile_image'];
                                        }


                                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                                        $inc_pp++;
                                    }
                                }
                                //$searchresult = array_search($val->id,$value,true);
                                $temp = array(
                                    "slno" => $i,
                                    "trending_type" => "training",
                                    "type" => "training",
                                    "type_id" => $val->id,
                                    "id" => $val->id,
                                    "title" => html_entity_decode(strip_tags($val->title)),
                                    "image" => change_img_src($val->preview_image),
                                    "featured_video" => $val->featured_video,
                                    "color" => ($val->color != '') ? $val->color : '#eb34e5',
                                    "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                                    "module_Count" => $this->count_module($val->id),
                                    "live_session_status" => $this->livestatus($val->id),
                                    "training_module_content" => $this->content_count($val->id),
                                    "specialities" => $this->specialityname($val->master_spec_id), #($val->specialities_name != '') ? $val->specialities_name : '',
                                    "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),

                                    "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',

                                    // "client_name" => $val->client_name,
                                    // "client_logo" => change_img_src($val->client_logo),

                                    "sponsor_name" => $val->sponsor,
                                    "sponsor_logo" => change_img_src($sponsorLogo),
                                    "all_sponsor" => $allsponsor,
                                    "session_doctor_entities" => $ses_doc_det_array,

                                    "duration" => $val->duration,

                                    "is_certificate" => ($val->cert_template_id != '') ? true : false,
                                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',


                                    "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

                                );

                                $vx[$key][$val->id] = $temp;

                                $i++;
                            }
                        }

                        break;
                        // case  "gr":

                        //     $cachename = "gr_search_" . $ids;
                        //     if ($this->myredis->exists($cachename)) {
                        //         $result = $this->myredis->get($cachename);
                        //         //print_r($result); exit;
                        //     } else {

                        //         $env = get_user_env($user_master_id);
                        //         if ($env) {

                        //             if ($env != 'GL') {
                        //                 $envStatus = "AND (gr.env ='GL' or gr.env ='" . $env . "')";
                        //             } else {
                        //                 $envStatus = "AND gr.env ='" . $env . "'";
                        //             }
                        //         } else {
                        //             $envStatus = "";
                        //         }

                        //         $sql = "SELECT
                        //     gr.gr_id as type_id,
                        //     gr.gr_title as title,
                        //     gr.gr_description as description,
                        //     gr.gr_chief_scientific_editor ,
                        //     gr.gr_preview_image,
                        //     gr.added_on,
                        //     gr.gr_date_of_publication as publish_date,
                        //     gr.deeplink,
                        //     gr.color,

                        //     cln.client_name,
                        //     cln.client_logo,

                        //     GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        //     GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                        //     max( ms.rank) as maxrank,


                        //     GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        //     GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id) as session_doctor_id,


                        //     GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,

                        //     (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = gr.gr_id and  rt.post_type='gr')as averageRating,
                        //     rtmy.rating  as myrating,

                        //     (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = gr.gr_id and kcm.type = 'gr')as count_comment,
                        //     kv.status as vault

                        //     FROM knwlg_gr_register as gr

                        //     JOIN gr_to_specialities  as grTs ON grTs.gr_id = gr.gr_id
                        //     JOIN master_specialities_V1 as ms ON ms.master_specialities_id = grTs.specialities_id
                        //     JOIN client_master as cln ON cln.client_master_id = gr.client_id

                        //     LEFT JOIN gr_to_sponsor as grTspon ON grTspon.gr_id = gr.gr_id
                        //     LEFT JOIN client_master as clintspon ON clintspon.client_master_id = grTspon.sponsor_id


                        //     LEFT JOIN gr_to_session_doctor as grTsdoc ON grTsdoc.gr_id = gr.gr_id

                        //     LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = gr.gr_id and  rtmy.post_type='gr' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                        //     LEFT JOIN knwlg_vault as kv ON kv.post_id = gr.gr_id and  kv.type_text='gr' and  kv.user_id = " . $user_master_id . "
                        //     LEFT JOIN knwlg_rating as rt ON rt.post_id = gr.gr_id and  rt.post_type='gr'

                        //     WHERE

                        //     gr.status=3
                        //     " . $envStatus . "
                        //     and
                        //     gr.gr_id != 10 and gr.gr_id in ($ids)
                        //     group by gr.gr_id
                        //     order by  gr.gr_date_of_publication desc, maxrank DESC  ";


                        //         //echo $sql; exit;
                        //         //exit;
                        //         //add child checking in this sql
                        //         //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
                        //         //exit;
                        //         //echo  $sql; exit;
                        //         $query = $this->db->query($sql);
                        //         //$this->db->cache_off();
                        //         $result = $query->result();
                        //         $this->myredis->set($cachename, $result);
                        //     }
                        //     // print_r($result); exit;
                        //     $i = 1;

                        //     foreach ($result as $val) {

                        //         $sponsorLogoArry = explode(",", $val->sponsor_logo);

                        //         if (count($sponsorLogoArry) > 0) {

                        //             foreach ($sponsorLogoArry as $valueSponor) {

                        //                 if ($valueSponor) $sponsorLogomix[] = '' . change_img_src($valueSponor);
                        //             }
                        //         } else {

                        //             if ($val->sponsor_logo) $sponsorLogomix[] = '' . change_img_src($val->sponsor_logo);
                        //         }

                        //         $sponsorLogo = implode(",", (array)$sponsorLogomix);

                        //         unset($sponsorLogomix);
                        //         unset($sponsorLogoArry);

                        //         $ses_doc_det_array = array();
                        //         if ($val->session_doctor_id) {
                        //             $session_doc_array = explode(",", $val->session_doctor_id);
                        //             $inc_pp = 0;
                        //             foreach ($session_doc_array as $single_doctor) {

                        //                 $var = session_doc_detail($single_doctor);
                        //                 //$image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        //                 /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                        //                     $logic_image = base_url() . "uploads/docimg/" . $image;
                        //                 } else {

                        //                     $logic_image = base_url() . "uploads/docimg/MConsult.png";
                        //                 }*/

                        //                 if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {

                        //                     $logic_image = $var[0]['profile_image'];
                        //                 } else {
                        //                     // $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        //                     // $logic_image_path = "uploads/docimg/" . $image;
                        //                     // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        //                     $logic_image = docimg; //$imgPr;
                        //                     //$logic_image = $var[0]['profile_image'];
                        //                 }
                        //                 $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                        //                 $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                        //                 $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                        //                 $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                        //                 $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                        //                 $inc_pp++;
                        //             }
                        //         }
                        //         //$searchresult = array_search($val->type_id,$value,true);
                        //         $vx[$key][$val->type_id] = array(

                        //             "slno" => $i,
                        //             "type_id" => $val->type_id,
                        //             "type" => 'gr',
                        //             "date" => date(' jS F y', strtotime($val->publish_date)),
                        //             "title" => html_entity_decode(strip_tags($val->title)),
                        //             "image" => change_img_src($val->gr_preview_image),

                        //             "color" => ($val->color != '') ? $val->color : '#918c91',

                        //             "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                        //             "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                        //             "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                        //             "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',

                        //             "client_name" => $val->client_name,
                        //             "client_logo" => '' . change_img_src($val->client_logo),

                        //             "sponsor_name" => $val->sponsor,
                        //             "sponsor_logo" => $sponsorLogo,

                        //             "session_doctor_entities" => $ses_doc_det_array,

                        //             "comment_count" => $val->count_comment,
                        //             "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                        //             "myrating" => ($val->myrating != '') ? true : false,
                        //             "vault" => ($val->vault != '') ? $val->vault : 0,
                        //             "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

                        //         );


                        //         $i++;
                        //     }
                        //     break;

                    case "session":
                        //print_r($key); exit;
                        $booked_id = $this->all_bookedmastersession($user_master_id);
                        $new_arr = array();
                        foreach ($booked_id as $sing) {

                            $new_arr[] = $sing->knwlg_sessions_id;
                        }
                        $res_arr = implode(',', $new_arr);

                        if ($res_arr) {

                            $sqlStr = "and ( (ks.session_id  NOT IN(" . $res_arr . ") AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "') or ks.start_datetime >= '" . date("Y-m-d H:i:s") . "') ";
                        } else {

                            $sqlStr = "and (ks.start_datetime >= '" . date("Y-m-d H:i:s") . "')";
                        }
                        //echo $res_arr; exit;

                        //$this->db->cache_on();

                        $cachename = "session_search_" . $ids;
                        if ($this->myredis->exists($cachename)) {
                            $result = $this->myredis->get($cachename);
                            //print_r($result); exit;
                        } else {


                            $env = get_user_env($user_master_id);
                            if ($env) {

                                if ($env != 'GL') {
                                    $envStatus = "AND (ks.env ='GL' or ks.env ='" . $env . "')";
                                } else {
                                    $envStatus = "AND ks.env ='" . $env . "'";
                                }
                            } else {
                                $envStatus = "";
                            }


                            $sql = "SELECT
                        ksp.participant_id,
                        ks.session_id,
                        ks.*,
                        sd.*,
                        cln.client_name,
                        cln.client_logo,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,

                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,


                        msct.category_name,
                        msct.category_logo,

                        sd.document_path,
                        sd.comment,
                        ksd.knwlg_sessions_docs_id,
                        ksd.document_path,
                        ksd.comment,
                        GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
                        GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
                        GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') as  speciality,




                        GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as  profile,
                        GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as  profile_images,
                        GROUP_CONCAT(ksp.participant_id) as  PartName,
                        GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED,
                        (ks.total_buffer + ks.total_seats) as tot_seat


                        FROM knwlg_sessions_V1 as ks


                        left JOIN session_to_specialities as sts ON sts.session_id = ks.session_id
                        left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = sts.specialities_id


                        LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id

                        LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
                        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id


                        LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
                        LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                        LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
                        LEFT JOIN knwlg_sessions_participant as ksp ON ksp.knwlg_sessions_id = ks.session_id
                        LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id

                        WHERE
                        ks.status = 3
                        " . $envStatus . "
                        and
                        ks.session_status in (1,2)
                        and
                        ks.privacy_status = 0
                        and
                        ks.session_id in ($ids)

                        GROUP BY ks.session_id
                        ORDER BY ks.start_datetime DESC  ";

                            // echo $sql; exit();
                            $query = $this->db->query($sql);
                            //$this->db->cache_off();

                            //
                            $result = $query->result();
                            $this->myredis->set($cachename, $result);
                        }
                        //print_r($result); exit();
                        //ks.session_status
                        $i = 0;

                        foreach ($result as $val) {


                            if (@getimagesize(base_url() . "uploads/docimg/" . $val->profile_image)) {
                                $logic_image = '' . $val->profile_image;
                            } else {

                                $logic_image = base_url() . "uploads/docimg/MConsult.png";
                            }

                            $start_time = $val->start_datetime;
                            $start_time = date("g:i A", strtotime($start_time));
                            $ses_doc_det_array = array();
                            if ($val->session_doctor_id) {

                                $session_doc_array = explode(",", $val->session_doctor_id);
                                $inc_pp = 0;
                                foreach ($session_doc_array as $single_doctor) {

                                    $var = session_doc_detail($single_doctor);


                                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                    /* if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                                         $logic_image = base_url() . "uploads/docimg/" . $image;
                                     } else {

                                         $logic_image = base_url() . "uploads/docimg/MConsult.png";
                                     }*/
                                    if ($image) {
                                        // $logic_image_path = "uploads/docimg/" . $image;
                                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                        // $logic_image = $imgPr;


                                        /////============================== updated by  ramanath  14-5-21
                                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                                            $logic_image = $image;
                                        } else {
                                            // $logic_image_path = "uploads/docimg/" . $image;
                                            // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                            $logic_image = docimg; //$image ;

                                        }
                                        //=======================================


                                    } else {

                                        $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                                    }
                                    #print_r($image.' ');
                                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                                    $inc_pp++;
                                }
                            }
                            #print_r($ses_doc_det_array);
                            //$entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
                            $end_time = $val->end_datetime;
                            $end_time = date("g:i A", strtotime($end_time));

                            /**
                             * new sponsor logic
                             */
                            $allsponsor = array();
                            $sponsorname = explode(",", $val->sponsor);
                            $sp = 0;
                            $sponsorSESLogoArry = explode(",", $val->sponsor_logo);
                            if (($val->sponsor_logo != '') && (count($sponsorSESLogoArry) > 0)) {

                                foreach ($sponsorSESLogoArry as $valueSponor) {
                                    if ($valueSponor) {
                                        $sponsorSESLogomix[] = '' . change_img_src($valueSponor);
                                    }
                                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                                    $sp++;
                                }
                            } else {

                                if ($val->sponsor_logoSES) {
                                    $sponsorSESLogomix[] = '' . change_img_src($val->sponsor_logoSES);
                                }
                                $allsponsor[] = array('name' => $val->sponsorCM, "logo" => $valueSponor);
                            }
                            $sponsorLogoSES = implode(",", (array)$sponsorSESLogomix);

                            unset($sponsorSESLogomix);
                            unset($sponsorSESLogoArry);

                            $coverImg = base_url() . "uploads/sessionBgDefault.jpeg";

                            //$entities[$i]['cover_image'] = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
                            //$searchresult = array_search($val->session_id,$value,true);
                            $vx[$key][$val->session_id] = array(

                                "slno" => $i,
                                "trending_type" => "session",
                                "type_id" => $val->session_id,
                                "session_id" => $val->session_id,
                                "type" => 'session',
                                "doctor_name" => $val->doctor_name,
                                "cover_image" => ($val->cover_image != '') ? change_img_src($val->cover_image) : change_img_src($coverImg),
                                "session_doctor_id" => $val->session_doctor_id,
                                "date" => date(' jS F y', strtotime($val->start_datetime)),

                                "start_datetime" => $val->start_datetime, //date(' jS F y', strtotime($val->start_datetime)),
                                "display_date" => $start_time . "-" . $end_time,
                                "ms_cat_name" => $val->category_name,
                                "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo),


                                "sponsor_name" => $val->sponsor,
                                "sponsor_logo" => $sponsorLogoSES,
                                "all_sponsor" => $allsponsor,

                                "image" => change_img_src($logic_image),
                                "image_raw_name" => change_img_src($val->profile_image),
                                "session_status" => $val->session_status,
                                "status_name" => $val->status_name,
                                "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),
                                "session_topic" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                                "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),
                                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),

                                "color" => ($val->color != '') ? $val->color : '#08cc9e',
                                // "client_name" => $val->client_name,
                                // "client_logo" => '' . change_img_src($val->client_logo),

                                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                                "session_doctor_entities" => $ses_doc_det_array,
                                "cpddetail" => $this->getcpddetails($val->session_id),

                            );
                        }
                        //print_r($vx);exit;
                        break;
                    case "video":


                        $cachename = "video_search_" . $ids;
                        if ($this->myredis->exists($cachename)) {
                            $result = $this->myredis->get($cachename);
                            //print_r($result); exit;
                        } else {

                            $env = get_user_env($user_master_id);
                            if ($env) {

                                if ($env != 'GL') {
                                    $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
                                } else {
                                    $envStatus = "AND cm.env ='" . $env . "'";
                                }
                            } else {
                                $envStatus = "";
                            }


                            $sql = "SELECT
                            cm.video_archive_id as type_id,
                            cm.video_archive_question,
                            cm.video_archive_answer,
                            cm.video_archive_question_raw,
                            cm.video_archive_answer_raw,
                            cm.video_archive_file_img,
                            cm.video_archive_file_img_thumbnail,
                            cm.deeplink,

                            cm.added_on,
                            cm.publication_date,
                            cln.client_name,
                            cln.client_logo,
                            cm.duration,
                            cm.type,
                            cm.vendor,
                            cm.src,
                            ks.session_doctor_id,
                            msct.category_name,
                            msct.category_logo,

                            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,


                            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                            GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                            cm.video_archive_speciality_id ,
                            kv.status as vault,

                            (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.video_archive_id and  rt.post_type='video_archive' and rt.rating!=0) as averageRating,
                            rtmy.rating  as myrating


                            FROM knwlg_video_archive as cm
                            left JOIN video_archive_to_specialities  as cmTs ON cmTs.video_archive_id = cm.video_archive_id
                            left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id


                            LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
                            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id

                            LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
                            LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id

                            LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id and  kv.type_text='video_archive' and  kv.user_id = " . $user_master_id . "

                            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id and  rtmy.post_type='video_archive' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                            LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id and  rt.post_type='video_archive'

                            JOIN client_master as cln ON cln.client_master_id = cm.client_id
                            WHERE
                            cm.status=3
                            " . $envStatus . "

                            and
                            cm.privacy_status = 0
                            and
                            cm.video_archive_id in ($ids)

                            GROUP BY cm.video_archive_id
                            order by cm.publication_date DESC
                            ";

                            //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
                            // echo $sql; exit;
                            //and
                            //cm.publication_date <= CURDATE()

                            $query = $this->db->query($sql);
                            $result = $query->result();
                            $this->myredis->set($cachename, $result);
                        }
                        //print_r($this->db->last_query()); exit;
                        $i = 1;

                        foreach ($result as $val) {

                            if ($val->video_archive_file_img) {
                                $img = $val->video_archive_file_img;
                            } else {
                                $img = '';
                            }
                            $allsponsor = array();
                            $sponsorname = explode(",", $val->sponsor);
                            $sp = 0;
                            $sponsorLogoArry = explode(",", $val->sponsor_logo);
                            if (($val->sponsor) && (count($sponsorLogoArry) > 0)) {

                                foreach ($sponsorLogoArry as $valueSponor) {

                                    if ($valueSponor) {
                                        $sponsorLogomix[] = '' . change_img_src($valueSponor);
                                    }
                                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                                    $sp++;
                                }
                            } else {

                                if ($val->sponsor_logo) {
                                    $sponsorLogomix[] = '' . change_img_src($val->sponsor_logo);
                                }
                                $allsponsor[] = array('name' => $val->sponsor, "logo" => $val->sponsor_logo);
                            }

                            $sponsorLogo = implode(",", (array)$sponsorLogomix);
                            unset($sponsorLogomix);
                            unset($sponsorLogoArry);
                            $session_doc_array = explode(",", $val->session_doctor_id);
                            $ses_doc_det_array = array();
                            $inc_pp = 0;
                            foreach ($session_doc_array as $single_doctor) {

                                $var = session_doc_detail($single_doctor);
                                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                if ($image) {
                                    // $logic_image_path = "uploads/docimg/" . $image;
                                    // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                    // $logic_image = $imgPr;
                                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                                        $logic_image = $image;
                                    } else {
                                        // $logic_image_path = "uploads/docimg/" . $image;
                                        // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                        $logic_image = docimg; //$imgPr;

                                    }
                                } else {

                                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";

                                }
                                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                                $inc_pp++;
                            }


                            $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
                            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                            $main_description = "";
                            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
                            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

                            //$searchresult = array_search($val->type_id,$value,true);
                            $vx[$key][$val->type_id] = array(
                                "slno" => $i,
                                "trending_type" => "video_archive",
                                "con_type" => $val->type,
                                "type" => $val->type,
                                "type_id" => $val->type_id,
                                "vendor" => $val->vendor,
                                "src" => $val->src,
                                "type_id" => $val->type_id,
                                "date" => date(' jS F y', strtotime($val->publication_date)),
                                "question" => html_entity_decode(strip_tags($string)),
                                "image" => change_img_src($img),
                                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                                "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                                // "client_name" => $val->client_name,
                                // "client_logo" => '' . change_img_src($val->client_logo),

                                "category_name" => $val->category_name,

                                "sponsor_name" => $val->sponsor,
                                "sponsor_logo" => $sponsorLogo,
                                "all_sponsor" => $allsponsor,
                                "duration" => $val->duration,
                                "comment_count" => $val->count_comment,
                                "deeplink" => $val->deeplink,
                                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                                "myrating" => ($val->myrating != '') ? true : false,
                                "vault" => ($val->vault != '') ? $val->vault : 0,

                                "session_doctor_id" => $val->session_doctor_id,
                                "session_doctor_entities" => $ses_doc_det_array,

                            );
                            $i++;
                        }

                        break;
                    case "speakerdoctor":
                        $cachename = "speakerdoctor_" . $ids;
                        if ($this->myredis->exists($cachename)) {
                            $result = $this->myredis->get($cachename);
                            //print_r($result); exit;
                        } else {

                            $env = get_user_env($user_master_id);
                            if ($env) {

                                if ($env != 'GL') {
                                    $envStatus = array("GL", $env);
                                } else {
                                    $envStatus = array($env);
                                }
                            } else {
                                $envStatus = array();
                            }
                            //$this->db->save_queries = TRUE;
                            $this->db->select('ksd.sessions_doctors_id,ksd.doctor_name,ksd.profile,ksd.profile_image,ksd.subtitle');
                            $this->db->from('knwlg_sessions_doctors as ksd');


                            $this->db->where('ksd.status', 3);
                            $this->db->where_in('ksd.env', $envStatus);
                            $this->db->where_in('ksd.sessions_doctors_id', $value);

                            $query = $this->db->get();
                            //$this->db->save_queries = TRUE;
                            //print_r($this->db->last_query()); exit();
                            if (($query) && ($query->num_rows() > 0)) {
                                $result = $query->result();
                                $this->myredis->set($cachename, $result);
                            }
                        }
                        if (!empty($result)) {
                            foreach ($result as $k => $v) {
                                $vx[$key][$v->sessions_doctors_id] = array(
                                    "trending_type" => "profile_doctor",
                                    "type" => "profile_doctor",
                                    "type_id" => $v->sessions_doctors_id,
                                    "doctor_id" => $v->sessions_doctors_id,
                                    "doctor_name" => $v->doctor_name,
                                    "profile" => $v->profile,
                                    "profile_image" => change_img_src($v->profile_image),
                                    "subtitle" => $v->subtitle
                                );
                            }
                        }

                        break;
                    case "channel":

                        $env = get_user_env($user_master_id);
                        $env_id = get_user_env_id($user_master_id);

                        if ($env) {

                            if ($env != 'GL') {
                                $envStatus = "AND (cm.env ='GL' or cm.env ='" . $env . "')";
                            } else {
                                $envStatus = "AND cm.env ='" . $env . "'";
                            }
                        } else {
                            $envStatus = "";
                        }

                        //echo 'channel';


                        $cachename = "chan_search_" . $ids."_".$env;
                        if ($this->myredis->exists($cachename)) {
                            $result = $this->myredis->get($cachename);
                        } else {

                            $sql = "SELECT
                                        cm.channel_master_id,
                                        cm.title,
                                        cm.description,
                                        cm.follower_count,
                                        cm.cover_image,
                                        cm.deeplink,
                                        cm.logo,
                                        cm.is_share,
                                        cm.deeplink

                                    FROM
                                        channel_master as cm
                                    WHERE
                                        cm.status = 3
                                        AND cm.privacy_status = 0
                                        {$envStatus}
                                        AND cm.channel_master_id in ({$ids})

                            ";

                            //echo $sql;

                            $query = $this->db->query($sql);
                            // echo $sql;die;
                            if (($query) && ($query->num_rows() > 0)) {
                                $result = $query->result();
                                $this->myredis->set($cachename, $result);
                            } else {
                                $result = null;
                            }


                            if (!empty($result)) {
                                $key_locked = get_user_package($user_master_id, 'channel');
                                //echo 'hello..';
                                foreach ($result as $k => $v) {

                                    $followCount = get_channel_total_followers($v->channel_master_id);
                                    //echo $result->channel_master_id;
                                    $vx[$key][$v->channel_master_id] = array(
                                        "type" => "channel",
                                        "trending_type" => "channel",
                                        "type_id" => $v->channel_master_id,
                                        "title" => $v->title,
                                        "description" => $v->description,
                                        "description_view" => preg_replace('/[\r\n]/', '', html_entity_decode(strip_tags($v->description))),
                                        "follower_count" => $followCount + $v->follower_count,
                                        "cover_image" => change_img_src($v->cover_image),
                                        "deeplink" => $v->deeplink,
                                        "logo" => change_img_src($v->logo),
                                        "is_share" => get_a_content_is_share_status($v->is_share, '11'),
                                        "price" => get_a_content_price($v->channel_master_id, 11, $env_id),
                                        "user_content_payment" => get_user_content_status($v->channel_master_id, 11, $user_master_id),
                                        "is_lock" => $key_locked,
                                    );
                                }
                            }
                        }

                        // echo '<pre>'; print_r($vx);die;
                        break;

                    default:
                        break;
                }
            }
        }
        #print_r($vx);exit;
        //ksort($vx);
        // print_r($vx);exit;
        return $vx;
        //exit;
    }


    public function all_compendium(
        $user_master_id,
        $ids,
        $keyvalue
    ) {

        // Get environment settings
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'comp');
        // Set type filters
        // if ($type == 'featured') {
        //     $typeSql = "";
        //     $typedashboardstatus = "AND cm.display_in_dashboard =1 ";
        // } elseif ($type == 1) {
        //     $typeSql = " AND cm.type = 'video'";
        //     $typedashboardstatus = "AND cm.display_in_dashboard !=1 ";
        // } else {
        //     $typeSql = "";
        //     $typedashboardstatus = "AND cm.display_in_dashboard !=1 ";
        // }
        //$cacheKey = 'getcompFeed_optimized' . $typedashboardstatus . $spIds . $limit . $env . $type . $limit;
        $cacheKey = "medwiki_search_" . $ids;

        if ($this->myredis->exists($cacheKey)) {
            $result = $this->myredis->get($cacheKey);
        } else {
            // Use Common Table Expressions for better performance
            $sql = "WITH TopCompendiums AS (
                SELECT cm.comp_qa_id
                FROM knwlg_compendium_V1 as cm
                LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id and cTenv.type = 1
                LEFT JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
                WHERE
                    cm.status=3
                    and cm.is_draft = 0
                    and cm.comp_qa_id in ($ids)
                    and cm.privacy_status = 0                   
                order by cm.comp_qa_id desc
            ),
            SpecialitiesAgg AS (
                SELECT
                    cmTs.comp_qa_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                FROM TopCompendiums tc
                JOIN compendium_to_specialities cmTs ON cmTs.comp_qa_id = tc.comp_qa_id
                JOIN master_specialities_V1 ms ON ms.master_specialities_id = cmTs.specialities_id
                GROUP BY cmTs.comp_qa_id
            ),
            SponsorsAgg AS (
                SELECT
                    cmTspon.comp_qa_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                    GROUP_CONCAT(DISTINCT clintspon.client_master_id) as sponsor_ids
                FROM TopCompendiums tc
                JOIN compendium_to_sponsor cmTspon ON cmTspon.comp_qa_id = tc.comp_qa_id
                JOIN client_master clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                GROUP BY cmTspon.comp_qa_id
            )
            SELECT
                cm.comp_qa_id as type_id,
                cm.comp_qa_question,
                cm.comp_qa_answer,
                cm.is_share,
                cm.start_like,
                cm.comp_qa_answer_raw as description,
                cm.comp_qa_question_raw as title,
                cm.comp_qa_file_img,
                cm.comp_qa_file_img_thumbnail,
                cm.added_on,
                cm.publication_date as publish_date,
                cln.client_name,
                cln.client_logo,
                cm.type,
                cm.vendor,
                cm.src,
                cm.deeplink,
                cm.gl_deeplink,
                cm.color,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                rtmy.rating as myrating,
                spec.specialities_name,
                spec.specialities_ids_and_names,
                spon.sponsor,
                spon.sponsor_logo,
                spon.sponsor_ids,
                cm.comp_qa_speciality_id,
                kv.status as vault,
                (select count(rt.rating) from knwlg_rating rt where rt.post_id = cm.comp_qa_id and rt.post_type='comp') as averageRating,
                (select count(kcm.knwlg_comment_id) from knwlg_comment kcm where kcm.type_id = cm.comp_qa_id and kcm.type = 'comp') as count_comment
            FROM TopCompendiums tc
            JOIN knwlg_compendium_V1 as cm ON cm.comp_qa_id = tc.comp_qa_id
            LEFT JOIN client_master as cln ON cln.client_master_id = cm.client_id
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id and cTenv.type = 1
            LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and kv.type_text='comp' and kv.user_id = ?
            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.comp_qa_id and rtmy.post_type='comp' and rtmy.rating!=0 and rtmy.user_master_id = ?
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.comp_qa_id and uTpyCont.type = 1 and uTpyCont.user_master_id = ?
            LEFT JOIN SpecialitiesAgg spec ON spec.comp_qa_id = cm.comp_qa_id
            LEFT JOIN SponsorsAgg spon ON spon.comp_qa_id = cm.comp_qa_id
            order by cm.comp_qa_id desc";

            // echo $sql;
            // exit;
            $result = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id))->result();
            $this->myredis->set($cacheKey, $result);
        }
        // Format results
        $vx = array();
        $i = 1;

        // print_r($result);
        // exit;
        foreach ($result as $val) {


            //echo 'type_id ----- '.$val->type_id;

            if ($val->comp_qa_file_img_thumbnail) {
                $logic_image = $val->comp_qa_file_img_thumbnail;
            } else {
                $logic_image = docimg;
            }
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            $sponsorLogomix = array();
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        $sp++;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                    $allsponsor[] = array('name' => $val->sponsor, "logo" => $val->sponsor_logo);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            $vx[$keyvalue][$val->type_id] = array(
                "slno" => $i, //.'-----'.$val->type_id.'----'.$keyvalue
                "con_type" => $val->type,
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                "env" => $env,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "type_id" => $val->type_id,
                "is_share" => $val->is_share,
                "type" => 'comp',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "question" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($logic_image),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                "answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "sponsor_name" => $val->sponsor,
                "sponsor_id" => $val->sponsor_ids,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "comment_count" => $val->count_comment,
                "rating" => ($val->averageRating != '') ? $val->start_like + $val->averageRating : $val->start_like,
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "deeplink" => ($env == 'GL') ?
                    (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) :
                    (($val->deeplink != '') ? $val->deeplink : 0),
            );
            $i++;
        }

        // print_r($vx);
        // exit;
        return $vx;
    }


}
