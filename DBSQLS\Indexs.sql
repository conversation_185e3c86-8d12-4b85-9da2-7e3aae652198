ALTER TABLE knwlg_sessions_V1 
ADD INDEX idx_version_2_status_dates_privacy 
(status, session_status, parent_id, privacy_status, end_datetime, start_datetime);


ALTER TABLE knwlg_sessions_participant 
ADD INDEX idx_version_2_participant_session (participant_id, knwlg_sessions_id);

ALTER TABLE session_to_sessiondoctor 
ADD INDEX idx_version_2_session_doctor (session_id, sessions_doctors_id);


CREATE INDEX idx_version_2_speciality_session ON session_to_specialities (session_id, specialities_id);


-- For sponsor join
CREATE INDEX idx_version_2_sponsor_session ON session_to_sponsor (session_id, sponsor_id);

-- For derived joins to avoid full table scans
CREATE INDEX idx_version_2_msct_id ON master_session_category (mastersession_category_id);