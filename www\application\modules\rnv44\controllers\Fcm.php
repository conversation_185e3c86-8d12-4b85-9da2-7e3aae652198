<?php

defined('BASEPATH') or exit('No direct script access allowed');
require dirname(__FILE__) . '/../libraries/NEW_REST_Controller.php';

use Firebase\JWT\JWT;
use Google\Cloud\Storage\StorageClient;
use Google\Cloud\Core\Exception\NotFoundException;

class Fcm extends MY_REST_Controller
{
    private $token_data;
    private $logdata = array();

    private static $keyFileData = [
        "type" => GCP_JSON_TYPE,
        "project_id" => GCP_JSON_PROJECT_ID,
        "private_key_id" => GCP_JSON_PRIVATE_KEY_ID,
        "private_key" => GCP_JSON_PRIVATE_KEY,
        "client_email" => GCP_JSON_CLIENT_EMAIL,
        "client_id" => GCP_JSON_CLIENT_ID,
        "auth_uri" => GCP_JSON_AUTH_URI,
        "token_uri" => GCP_JSON_TOKEN_URI,
        "auth_provider_x509_cert_url" => GCP_JSON_AUTH_PROVIDER_X509_CERT_URL,
        "client_x509_cert_url" => GCP_JSON_CLIENT_X509_CERT_URL
    ];


    public function __construct()
    {
        parent::__construct();
        $token = $this->input->get_request_header('Authorization');
        //this->set_response($all_data, REST_Controller::HTTP_OK); //This is the respon if success
        //Validate user
        $this->validate_token($token);
        $this->token_data = $this->get_token_data($token);
        //print_r($token);
        $this->load->library('form_validation');
        $this->load->model('user_model');

        /**
         * For api detail table
         */
        $this->logdata['called_on'] = date('Y-m-d H:i:s', time());
        $this->logdata['process_start_time'] = date('Y-m-d H:i:s');
        $this->logdata['version'] = $this->input->get_request_header('version');
        /**
         * For api detail table
         */
    }

    public function token_get()
    {
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        //$timestamp_added = $this->input->get(' ');
        $user_mem_id = $this->user_model->get_mem_id($user_master_id);
        // Get your service account's email address and private key from the JSON key file
        $service_account_email = "<EMAIL>";
        $private_key =   self::$keyFileData['private_key'];
        ;
        $uid = $user_mem_id;
        $now_seconds = time();

        $payload = array(
            "iss" => $service_account_email,
            "sub" => $service_account_email,
            "aud" => "https://identitytoolkit.googleapis.com/google.identity.identitytoolkit.v1.IdentityToolkit",
            "iat" => $now_seconds,
            "exp" => $now_seconds + (60 * 60),  // Maximum expiration time is one hour
            "uid" => $uid,
            "claims" => array(
                "premium_account" => ''
            )
        );
        $token = JWT::encode($payload, $private_key, "RS256");
        $all_data['token'] = $token;
        $message = 'Success';


        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */


        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    // Helper function to get mime type
    private function getMimeType($filename)
    {
        $ext = pathinfo($filename, PATHINFO_EXTENSION);
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf'
            // Add more as needed
        ];
        return $mimeTypes[strtolower($ext)] ?? 'application/octet-stream';
    }

    private function makePublic($bucketName, $objectName)
    {
        try {
            $storage = new StorageClient([
                'projectId' => self::$keyFileData['project_id'],
                'keyFile' => self::$keyFileData
            ]);

            $bucket = $storage->bucket($bucketName);
            $object = $bucket->object($objectName);

            // Make the object public
            $object->update(['acl' => []], ['predefinedAcl' => 'PUBLICREAD']);

            return true;
        } catch (Exception $e) {
            return false;
        }
    }


    public function createBucketWithCors($bucketName)
    {
        try {
            $storage = new StorageClient([
                'projectId' => self::$keyFileData['project_id'],
                'keyFile' => self::$keyFileData
            ]);

            $bucket = $storage->createBucket($bucketName, [
                'cors' => [
                    [
                        'origin' => ['*'],
                        'method' => ['GET', 'HEAD', 'PUT', 'POST', 'DELETE'],
                        'responseHeader' => ['Content-Type', 'Authorization', 'x-goog-content-sha256'],
                        'maxAgeSeconds' => 3600
                    ]
                ]
            ]);

            return $bucket;
        } catch (Exception $e) {
            echo 'Failed to create bucket: ', $e->getMessage(), "\n";
        }
    }


    public function updateBucketCors($bucketName)
    {
        try {
            $storage = new StorageClient([
                'projectId' => self::$keyFileData['project_id'],
                'keyFile' => self::$keyFileData
            ]);

            $bucket = $storage->bucket($bucketName);
            $bucket->update([
                'cors' => [
                    [
                        'origin' => ['*'],
                        'method' => ['GET', 'HEAD', 'PUT', 'POST', 'DELETE'],
                        'responseHeader' => ['Content-Type', 'Authorization', 'x-goog-content-sha256'],
                        'maxAgeSeconds' => 3600
                    ]
                ]
            ]);

            return $bucket;
        } catch (Exception $e) {
            echo 'Failed to update bucket: ', $e->getMessage(), "\n";
        }
    }
    private function ensureBucketExists($bucketName)
    {
        $storage = new StorageClient([
            'projectId' => self::$keyFileData['project_id'],
            'keyFile' => self::$keyFileData
        ]);

        $bucket = $storage->bucket($bucketName);

        if (!$bucket->exists()) {
            $bucket = $storage->createBucket($bucketName, [
                'location' => 'asia-south1',
                'cors' => [
                    [
                        'origin' => ['*'],
                        'method' => ['GET', 'HEAD', 'PUT', 'POST', 'DELETE'],
                        'responseHeader' => ['Content-Type', 'Authorization', 'x-goog-content-sha256'],
                        'maxAgeSeconds' => 3600
                    ]
                ]
            ]);
        }

        return $bucket;
    }
    public function url_post()
    {
        try {
            $bucketName = GCP_UPLOAD_IMAGE_BUCKET_NAME;
            $objectName = $this->input->post('fileName');

            if (empty($bucketName) || empty($objectName)) {
                throw new Exception("bucketName and fileName are required", 400);
            }

            $storage = new StorageClient([
                'projectId' => self::$keyFileData['project_id'],
                'keyFile' => self::$keyFileData
            ]);

            // Ensure the bucket exists
            $bucket = $this->ensureBucketExists($bucketName);


            $object = $bucket->object($objectName);
            $contentType = $this->getMimeType($objectName);

            // Generate signed URL
            $signedUrl = $object->signedUrl(
                new \DateTime('+4 hours'),
                [
                    'method' => 'PUT',
                    'version' => 'v4',
                    'contentType' => $contentType,
                    'headers' => [
                        'host' => 'storage.googleapis.com',
                        'x-goog-content-sha256' => 'UNSIGNED-PAYLOAD',
                        'x-goog-resumable' => 'start',
                        'x-goog-acl' => 'public-read'  // Makes uploaded file public automatically
                    ],
                    'queryParams' => [
                        'uploadType' => 'media'
                    ]
                ]
            );

            // Generate the public URL for the uploaded object
            $publicUrl = sprintf(
                'https://storage.googleapis.com/%s/%s',
                $bucketName,
                $objectName
            );

            // Return both URLs and required headers
            $all_data = [
                'upload_url' => $signedUrl,
                'public_url' => $publicUrl,
                'headers' => [
                    'Content-Type' => $contentType,
                    'x-goog-content-sha256' => 'UNSIGNED-PAYLOAD',
                    'x-goog-resumable' => 'start',
                    'x-goog-acl' => 'public-read'
                ],
                'bucket_name' => $bucketName,
                'object_name' => $objectName,
                'expires_in' => '4 hours'
            ];

            $this->set_response($all_data, 'Success', REST_Controller::HTTP_OK, $this->logdata);
        } catch (Exception $e) {
            $this->set_response(
                ['error' => $e->getMessage()],
                'Failed to generate URL',
                REST_Controller::HTTP_INTERNAL_SERVER_ERROR,
                $this->logdata
            );
        }
    }

    // Add a new method to verify upload
    public function verify_upload_post()
    {
        try {
            $bucketName = $this->input->post('bucketName');
            $objectName = $this->input->post('fileName');

            $storage = new StorageClient([
                'projectId' => self::$keyFileData['project_id'],
                'keyFile' => self::$keyFileData
            ]);

            $bucket = $storage->bucket($bucketName);
            $object = $bucket->object($objectName);

            if ($object->exists()) {
                // Get object metadata
                $info = $object->info();

                $response = [
                    'status' => 'success',
                    'message' => 'File successfully uploaded',
                    'file_details' => [
                        'public_url' => sprintf('https://storage.googleapis.com/%s/%s', $bucketName, $objectName),
                        'size' => $info['size'],
                        'contentType' => $info['contentType'],
                        'timeCreated' => $info['timeCreated'],
                        'md5Hash' => $info['md5Hash']
                    ]
                ];

                $this->set_response($response, 'Upload verified', REST_Controller::HTTP_OK, $this->logdata);
            } else {
                $this->set_response(
                    ['error' => 'File not found'],
                    'Upload verification failed',
                    REST_Controller::HTTP_NOT_FOUND,
                    $this->logdata
                );
            }
        } catch (Exception $e) {
            $this->set_response(
                ['error' => $e->getMessage()],
                'Failed to verify upload',
                REST_Controller::HTTP_INTERNAL_SERVER_ERROR,
                $this->logdata
            );
        }
    }
}
