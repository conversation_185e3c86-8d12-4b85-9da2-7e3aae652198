<?php

defined('BASEPATH') or exit('No direct script access allowed');
require dirname(__FILE__) . '/../libraries/NEW_REST_Controller.php';
require_once APPPATH . 'libraries/dompdf/autoload.inc.php';
use Dompdf\Dompdf;

class Knwlgmastersessionnew extends MY_REST_Controller
{
    private $token_data;
    private $logdata = array();
    private $message;
    //private $json_data = array();
    private $payload = array();
    //private $dashboard_model;
    private $user_master_id;
    public function __construct()
    {
        parent::__construct();
        $token = $this->input->get_request_header('Authorization');
        /** notification environment declaration */
        $this->load->helper('crmlogin');
        //$this->load->library('Clirnetnotification_V1');
        //$this->load->library('NotificationBackground');
        //$this->load->library('Clirnetnotification_V4');
        //$this->load->library('Clirnetnotification_V3');
        $this->load->library('Clirnetnotification_V5');
        /** notification environment declaration */
        $this->validate_token($token);
        //echo 'inxxxxxxxxxxxxx'; exit;
        $this->token_data = $this->get_token_data($token);
        $this->load->library(array('parser'));
        //print_r($token);
        $this->load->library('form_validation');
        $this->load->model('Knwlgmastersessionnew_model');
        $this->load->model('Multidaysession_model');
        /** environment setup as per server switch */
        $this->session_reserve_email = session_reserve_email;
        $this->session_reserve_sms = session_reserve_sms;
        $this->session_reserve_onesignal = session_reserve_email_onesignal;
        $this->session_recording_request_sms = session_recording_request_sms;
        $this->session_recording_request_email = session_recording_request_email;
        /** environment setup as per server switch */
        /**
         * For api detail table
         */
        $this->logdata['called_on'] = date('Y-m-d H:i:s', time());
        $this->logdata['process_start_time'] = date('Y-m-d H:i:s');
        $this->logdata['version'] = $this->input->get_request_header('version');
        /**
         * For api detail table
         */
    }
    public function tracklivesession_post()
    {
        $array = json_decode(file_get_contents('php://input'), true);
        $array['user_master_id'] = $this->token_data->userdetail->user_master_id;
        // print_r($array); exit;
        $this->form_validation->set_data($array);
        $this->form_validation->set_rules('page_id', 'Session Id', 'trim|required');
        // $this->form_validation->set_rules('user_master_id', 'User Id', 'trim|required');
        if ($this->form_validation->run() == false) {
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $all_data = $this->Knwlgmastersessionnew_model->tracklivesession($array);
            // print_r($all_data); exit;
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    public function mastersession_get()
    {
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $all_data = $this->Knwlgmastersessionnew_model->all_mastersession($user_master_id);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    public function bookedmastersession_get()
    {
        if ($this->input->get('from') == "" || $this->input->get('to') == "") {
            //$output['error'] = $this->form_validation->error_array();
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $spids = $this->input->get('spIds');
            $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
            //print_r($user_booked_session_id); exit();
            $all_data = array();
            if (!empty($user_booked_session_id)) {
                $all_data = $this->Knwlgmastersessionnew_model->all_bookedmastersession_details($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $this->input->get('category'), $spids);
            } else {
            }
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    /**
     * upcomingsessionreserved
     */
    public function upcomingsessionreserved_get()
    {
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
        #print_r($user_booked_session_id); exit();
        $all_data = array();
        if (!empty($user_booked_session_id)) {
            $all_data = $this->Knwlgmastersessionnew_model->all_upcomingsessionreserved_details($user_booked_session_id, $user_master_id);
        } else {
        }
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    public function mybookedsessionToday_get()
    {
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
        //print_r($user_booked_session_id); exit();
        $all_data = array();
        if (!empty($user_booked_session_id)) {
            $all_data = $this->Knwlgmastersessionnew_model->all_bookedmastersession_today($user_booked_session_id, $user_master_id);
        } else {
        }
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    public function add_session_participant_post()
    {
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $dataArray = json_decode(file_get_contents('php://input'), true);
        if (!isset($dataArray['session_id']) || empty($dataArray['session_id'])) {
            $message = 'No Session_id Passed!!';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response(null, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }
        $all_data = $this->Knwlgmastersessionnew_model->add_participant($dataArray['session_id'], $user_master_id);
        $message = 'Participant added successfully';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    /**
     * upcomingmastersession
     * This for all session upcomeing listing
     * two get parameters (from and to for pagination)
     */
    public function upcomingmastersession_get()
    {
        if ($this->input->get('from') == "" || $this->input->get('to') == "") {
            //$output['error'] = $this->form_validation->error_array();
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            //print_r($this->token_data);
            //echo $token->userdetail->user_master_id
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
            $user_client_ids = $this->token_data->userdetail->client_ids;
            $user_group_ids = $this->token_data->userdetail->group_ids;
            if ($user_master_id == 4) {
                /*$all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details2
                ($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'));*/
                $all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'));
            } else {
                $all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'));
            }
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }


    public function upcomingmastersessioncme_get()
    {
        if ($this->input->get('from') == "" || $this->input->get('to') == "") {
            //$output['error'] = $this->form_validation->error_array();
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            //print_r($this->token_data);
            //echo $token->userdetail->user_master_id
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
            $user_client_ids = $this->token_data->userdetail->client_ids;
            $user_group_ids = $this->token_data->userdetail->group_ids;
            $all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details_cme($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'));
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    public function upcomingmastersessionslider_get()
    {
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
        $user_client_ids = $this->token_data->userdetail->client_ids;
        $user_group_ids = $this->token_data->userdetail->group_ids;
        $all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details_slider($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'), $this->input->get('spIds'));
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    /**
     * upcomingmastersessionwithspeciality
     * This for all session upcomeing listing
     * two get parameters (from and to for pagination)
     */
    public function upcomingmastersessionwithspeciality_post()
    {
        $dataArray = json_decode(file_get_contents('php://input'), true);
        //echo $dataArray['speciality_id'];
        //print_r($dataArray); exit;
        //echo $token->userdetail->user_master_id
        $this->form_validation->set_data($dataArray);
        $this->form_validation->set_rules('from', 'Pagination Start Limit', 'trim|required');
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        $this->form_validation->set_rules('to', 'Pagination Last Limit', 'trim|required');
        //$this->form_validation->set_rules('speciality_id', 'Speciality', 'trim|required');
        if ($this->form_validation->run() == false) {
            //$output['error'] = $this->form_validation->error_array();
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            //print_r($this->token_data);
            //echo $token->userdetail->user_master_id
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
            $user_client_ids = $this->token_data->userdetail->client_ids;
            $user_group_ids = $this->token_data->userdetail->group_ids;
            $all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersessionwithspeciality_details($user_booked_session_id, $user_master_id, $dataArray['from'], $dataArray['to'], $dataArray['speciality_id'], $dataArray['topic'], $user_client_ids, $user_group_ids);
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    public function zoom($email, $first_name, $last_name, $room_id)
    {
        //$parsed_data['email'], $parsed_data['first_name'], $user_phone, $roomid
        $data = '{
        "email": "' . $email . '",
        "first_name": "' . $first_name . '",
        "last_name": "' . $last_name . '"
        }';
        //echo $data; exit;
        $curl = curl_init();
        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => "https://api.zoom.us/v2/webinars/" . $room_id . "/registrants",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_POSTFIELDS => $data,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_HTTPHEADER => array(
                    "authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOm51bGwsImlzcyI6Im96Wm1lbGl2Umt5QXZuLXBPVUduUFEiLCJleHAiOjE2ODU1MzM1NjAsImlhdCI6MTY1Mzk5MjIyMX0.BlxC2m9ijauvHAYExTYUeRKRLaihD57WMVhihsGty0c",
                    "content-type: application/json"
                ),
            )
        );
        $response = curl_exec($curl);
        $err = curl_error($curl);
        //print_r($data);
        //print_r($response);
        //print_r($err); exit;
        curl_close($curl);
        if ($err) {
            return $err;
        } else {
            return $response;
        }
        exit;
    }
    public function zoom_meeting($email, $first_name, $last_name, $room_id)
    {
        $data = '{
        "email": "' . $email . '",
        "first_name": "' . $first_name . '",
        "last_name": "' . $last_name . '"
        }';
        // =================== generate token  =========================//
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://zoom.us/oauth/token");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "grant_type=account_credentials&account_id=Dex8GiHdRPmO39n7ejGXHw");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Authorization: Basic ejJyX3ltb0tUcndQRXRkWUxfQWd3OmoxdlhFNFY4N3AzNGVwOWJ3V092Qld5Vjk4ZzJON1da",
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $server_output = curl_exec($ch);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($httpcode == 200) {
            $token = json_decode($server_output, true)["access_token"];
        } else {
            $token = '';
        }
        // ===================== /generate token =======================//
        $curl = curl_init();
        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => "https://api.zoom.us/v2/meetings/" . $room_id . "/registrants",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_POSTFIELDS => $data,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_HTTPHEADER => array(
                    "authorization: Bearer " . $token,
                    "content-type: application/json"
                ),
            )
        );
        $response = curl_exec($curl);
        $err = curl_error($curl);
        //print_r($data);
        //print_r($response);
        //print_r($err); exit;
        curl_close($curl);
        if ($err) {
            return $err;
        } else {
            return $response;
        }
        exit;
    }

    public function filterSession_post()
    {
        $dataArray = json_decode(file_get_contents('php://input'), true);
        $dataJson = file_get_contents('php://input');
        // echo $dataArray['speciality_id'];die;
        //echo $token->userdetail->user_master_id
        $this->form_validation->set_data($dataArray);
        $this->form_validation->set_rules('from', 'Pagination Start Limit', 'trim|required');
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        $this->form_validation->set_rules('to', 'Pagination Last Limit', 'trim|required');
        //$this->form_validation->set_rules('speciality_id', 'Speciality', 'trim|required');
        if ($this->form_validation->run() == false) {
            //$output['error'] = $this->form_validation->error_array();
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            //print_r($this->token_data);
            //echo $token->userdetail->user_master_id
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
            $user_client_ids = $this->token_data->userdetail->client_ids;
            $user_group_ids = $this->token_data->userdetail->group_ids;
            $all_data = $this->Knwlgmastersessionnew_model->all_filter($user_booked_session_id, $user_master_id, $dataArray['from'], $dataArray['to'], $dataArray['speciality_id'], $dataArray['topic'], $user_client_ids, $user_group_ids, $dataArray);
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['detail_message'] = $dataJson;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    public function submitjoinrequest_post()
    {
        //echo 'hello';exit;
        $dataArray = json_decode(file_get_contents('php://input'), true);
        //print_r($dataArray); exit();
        $data['session_id'] = $dataArray['session_id'];
        $data['added_on'] = date('Y-m-d H:i:s', time());
        $data['user_master_id'] = $this->token_data->userdetail->user_master_id;
        //$data['type'] ="session";
        if ($data['session_id'] == "") {
            //$output['error'] = $this->form_validation->error_array();
            /**
             * For api detail table
             */
            $output['error'] = "Validation Error";
            $message = "Session Id Not Found";
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $insert_id = $this->Knwlgmastersessionnew_model->insert_join_data($data);
            if ($insert_id > 0) {
                $all_data = [];
                $message = 'Success';
                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['session_id_parse'] = $data['session_id'];
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
            }
        }
    }
    public function submitrecordingrequest_post()
    {
        $dataArray = json_decode(file_get_contents('php://input'), true);
        //print_r($dataArray); exit();
        $data['session_id'] = $dataArray['session_id'];
        $data['recording_type'] = $dataArray['recording_type'];
        $data['added_on'] = date('Y-m-d H:i:s', time());
        $data['user_master_id'] = $this->token_data->userdetail->user_master_id;
        if ($data['recording_type'] == "" || $data['session_id'] == "") {
            //$output['error'] = $this->form_validation->error_array();
            /**
             * For api detail table
             */
            $output['error'] = "Validation Error";
            $message = "Validation Error";
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            if ($this->Knwlgmastersessionnew_model->check_recording_request_exists($this->token_data->userdetail->user_master_id, $data['session_id'])) {
                $output['error'] = "Already Requested";
                $message = "You have already requested the recording. Please wait till the recording is ready!";
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            } else {
                $insert_id = $this->Knwlgmastersessionnew_model->insert_recording_request($data);
                if ($insert_id > 0) {
                    $userenv = get_user_env($data['user_master_id']); //(100627);
                    $userdetail = $this->getuserrecordrequestdetail($data['session_id'], $data['user_master_id']); //(5, 100627);
                    //print_r($userdetail); exit;
                    switch ($userenv) {
                        case 'GL':
                            $notify = new Clirnetnotification_V5();
                            $notify->requestsessionvideoemail($data['session_id'], $userdetail['userdetail'], $this->session_recording_request_email, $user_master_id);
                            break;
                        case 'IN':
                            $notify = new Clirnetnotification_V5();
                            if ($userdetail['userdetail'][0]->phone != '') {
                                $notify->requestsessionvideosms($data['session_id'], $userdetail['userdetail'], $this->session_recording_request_sms);
                            }
                            if ($userdetail['userdetail'][0]->email != '') {
                                $notify->requestsessionvideoemail($data['session_id'], $userdetail['userdetail'], $this->session_recording_request_email, $user_master_id);
                            }
                            // $notify->schedulein($userdetail,$data['user_master_id'],"reserve");
                            break;
                        default:
                            break;
                    }
                    $all_data = [];
                    $message = 'Thank you for your request! We will send you the recording when the session is complete';
                    /**
                     * For api detail table
                     */
                    $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                    $this->logdata['response'] = REST_Controller::HTTP_OK;
                    $this->logdata['message'] = $message;
                    $this->logdata['session_id_parse'] = $data['session_id'];
                    $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                    /**
                     * For api detail table
                     */
                    $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
                }
            }
        }
    }
    public function getuserrecordrequestdetail($session_id, $user_master_id)
    {
        $response = array();
        $response['userdetail'] = $this->Knwlgmastersessionnew_model->getuserrecorddetails($user_master_id, $session_id);
        // $response['one_signal'] = $this->Knwlgmastersessionnew_model->getusersessiononesignaldetail($user_master_id);
        return $response;
    }
    public function submitsessionreviews_post()
    {
        //echo 'hello';exit;
        $dataArray = json_decode(file_get_contents('php://input'), true);
        //print_r($dataArray); exit();
        $data['session_id'] = $dataArray['session_id'];
        $data['review'] = $dataArray['review'];
        $data['rating'] = $dataArray['rating'];
        $data['qna'] = $dataArray['qna'];
        $data['added_on'] = date('Y-m-d H:i:s', time());
        $data['user_master_id'] = $this->token_data->userdetail->user_master_id;
        //$data['type'] ="session";
        if ($data['review'] == "" && $data['rating'] == "") {
            //$output['error'] = $this->form_validation->error_array();
            /**
             * For api detail table
             */
            $output['error'] = "Validation Error";
            $message = "Please Enter Rewiew Or Rating.";
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $insert_id = $this->Knwlgmastersessionnew_model->insert_rating_review($data);
            if ($insert_id > 0) {
                $all_data = [];
                $message = 'Success';
                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['session_id_parse'] = $dataArray['session_id'];
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
            }
        }
    }
    public function submitsessioncomment_post()
    {
        //echo 'hello';exit;
        $dataArray = json_decode(file_get_contents('php://input'), true);
        // print_r($dataArray); exit();
        $data['comment'] = $dataArray['comment'];
        if (isset($dataArray['type']) && $dataArray['type'] != "") {
            $data['type'] = $dataArray['type'];
            $data['type_id'] = $dataArray['type_id'];
        } else {
            $data['type'] = 'session';
            $data['type_id'] = $dataArray['session_id'];
        }
        $data['user_master_id'] = $this->token_data->userdetail->user_master_id;
        $data['added_on'] = date('Y-m-d H:i:s', time());
        $data['status'] = 1;
        //$data['type'] ="session";
        if (trim($dataArray['comment']) == "") {
            //$output['error'] = $this->form_validation->error_array();
            /**
             * For api detail table
             */
            $output['error'] = "Validation Error";
            $message = "Validation Error";
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $insert_id = $this->Knwlgmastersessionnew_model->insert_comment_session($data);
            if ($insert_id > 0) {
                $all_data = [];
                $message = 'Success';
                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
            }
        }
    }

    public function sessioncomments_get()
    {
        if ($this->input->get('session_id') == "") {
            // $err_arr = $this->form_validation->error_array();
            $message = "Session ID Not Present";
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $all_data = $this->Knwlgmastersessionnew_model->getsessioncomment($this->token_data->userdetail->user_master_id, $this->input->get('session_id'));
            $message = 'Success';
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
        }
    }
    public function sessiondetail_get()
    {
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        if ($this->input->get('session_id') == "") {
            $err_arr = $this->form_validation->error_array();
            $message = "Session ID Not Present";
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $user_client_ids = $this->token_data->userdetail->client_ids;
            $user_group_ids = $this->token_data->userdetail->group_ids;
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $all_data = $this->Knwlgmastersessionnew_model->single_upcomingmastersession_details($user_master_id, $this->input->get('session_id'), $user_client_ids, $user_group_ids);
            // print_r($all_data); exit();
            $message = 'Success';
            //echo $all_data[0]['start_datetime']; exit();
            //-------------------------------------
            // ramanath //
            // get session tags ids
            $all_data[0]['start_datetime'] = date('Y-m-d H:i:s', $all_data[0]['start_datetimex']);
            //$all_data[0]['survey']= [];
            //echo $all_data[0]['start_datetime']; exit();
            $this->load->library('Background');
            /**
             * This is for background process
             */
            $basic_detail = array();
            $basic_detail['user_master_id'] = $this->token_data->userdetail->user_master_id;
            $basic_detail['tag_ids'] = $all_data[0]['master_tag_ids'];
            $basic_detail['content_type'] = 'session';
            $basic_detail['content_id'] = $this->input->get('session_id');
            $basic_detail['speciality_ids'] = $all_data[0]['speciality'];
            $basic_detail['Authorization'] = $this->input->get_request_header('Authorization');
            $url = base_url() . "rnv6/knwlgmastersession/addUserToTagDetails";
            $param = $basic_detail;
            $this->background->do_in_background($url, $param); /**/
            //-------------------------------------------------------------------
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    public function sessiondetail_new_get()
    {
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required')
        if ($this->input->get('session_id') == "") {
            $err_arr = $this->form_validation->error_array();
            $message = "Session ID Not Present";
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $user_client_ids = $this->token_data->userdetail->client_ids;
            // print_r($user_client_ids);
            // die;
            $session_id = $this->input->get('session_id');
            $user_group_ids = $this->token_data->userdetail->group_ids;
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $is_multiday = $this->Multidaysession_model->check_multiday_status($session_id);
            // print_r($is_multiday); exit;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            if ($is_multiday == 1) { //is draft and internal user is not implemented!!!
                $all_data = $this->Multidaysession_model->detail($session_id, $user_master_id, $user_client_ids);
            } else {
                $all_data = $this->Knwlgmastersessionnew_model->single_upcomingmastersession_details_new($user_master_id, $this->input->get('session_id'), $user_client_ids, $user_group_ids);
            }
            if (!empty($all_data)) {
                $this->logdata['response'] = REST_Controller::HTTP_OK;
            } else {
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            }
            $message = 'Success';
            // get session tags ids
            //$all_data[0]['start_datetime'] = date('Y-m-d H:i:s', $all_data[0]['start_datetimex']);
            //$this->load->library('Background');
            /**
             * This is for background process
             */
            /*$basic_detail = array();
            $basic_detail['user_master_id'] = $this->token_data->userdetail->user_master_id;
            $basic_detail['tag_ids'] = $all_data[0]['master_tag_ids'];
            $basic_detail['content_type'] = 'session';
            $basic_detail['content_id'] = $this->input->get('session_id');
            $basic_detail['speciality_ids'] = $all_data[0]['speciality'];
            $basic_detail['Authorization'] = $this->input->get_request_header('Authorization');
            $url = base_url() . "rnv6/knwlgmastersession/addUserToTagDetails";
            $param = $basic_detail;
            $this->background->do_in_background($url, $param);*/
            //-------------------------------------------------------------------
            /**
             * For api detail table
             */
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, $this->logdata['response'], $this->logdata); //This is the respon if success
        }
    }
    /**
     * @return bool
     */
    public function addUserToTagDetails_post()
    {
        $data['user_master_id'] = $this->input->post('user_master_id');
        $data['tag_ids'] = $this->input->post('tag_ids');
        $data['content_type'] = $this->input->post('content_type');
        $data['content_id'] = $this->input->post('content_id');
        $data['speciality_ids'] = $this->input->post('speciality_ids');
        $data['added_on'] = date('Y-m-d H:i:s', time());
        $rs = $this->Knwlgmastersessionnew_model->add_user_tag_details($data);
    }
    public function upcoming_mastersession_all_get()
    {
        if ($this->input->get('from') == "" || $this->input->get('to') == "") {
            //$output['error'] = $this->form_validation->error_array();
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            //print_r($this->token_data);
            //echo $token->userdetail->user_master_id
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
            $user_client_ids = $this->token_data->userdetail->client_ids;
            $user_group_ids = $this->token_data->userdetail->group_ids;
            if ($user_master_id == 4) {
                /*$all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details2
                ($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'));*/
                $all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_detailswithoutcategoryfilter($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'));
            } else {
                $all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_detailswithoutcategoryfilter($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'));
            }
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    public function upcoming_mastersession_featured_get()
    {
        //echo "sasasas";
        //exit();
        if ($this->input->get('from') == "" || $this->input->get('to') == "") {
            //$output['error'] = $this->form_validation->error_array();
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            //print_r($this->token_data);
            //echo $token->userdetail->user_master_id
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $user_booked_session_id = $this->Knwlgmastersessionnew_model->all_bookedmastersession($user_master_id);
            $user_client_ids = $this->token_data->userdetail->client_ids;
            $user_group_ids = $this->token_data->userdetail->group_ids;
            $spids = $this->input->get('spIds');
            if ($user_master_id == 4) {
                /*$all_data = $this->Knwlgmastersessionnew_model->all_upcomingmastersession_details2
                ($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'));*/
                $all_data = $this->Knwlgmastersessionnew_model->all_featuredmastersession_details($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'), $spids);
            } else {
                $all_data = $this->Knwlgmastersessionnew_model->all_featuredmastersession_details($user_booked_session_id, $user_master_id, $this->input->get('from'), $this->input->get('to'), $user_client_ids, $user_group_ids, $this->input->get('cme'), $spids);
            }
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }

    public function relatedcompendium_post()
    {
        $this->form_validation->set_rules('speciality_ids', 'Speciality_ids', 'trim|required');
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        if ($this->form_validation->run() == false) {
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $spec_array = explode(",", $this->input->post('speciality_ids'));
            if (!empty($spec_array)) {
                $user_client_ids = $this->token_data->userdetail->client_ids;
                $user_group_ids = $this->token_data->userdetail->group_ids;
                $all_data = $this->Knwlgmastersessionnew_model->related_compendium($this->input->post('speciality_ids'), $user_client_ids, $user_group_ids);
                $message = 'Success';
                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
            }
        }
    }

    public function submitquery_post()
    {
        ///print_r($_SERVER['HTTP_HOST']); exit;//$_SERVER['HTTP_HOST']); exit;
        $dataArray = json_decode(file_get_contents('php://input'), true);
        $version = $this->input->get_request_header('Version');
        $session_id = $dataArray['session_id'];
        $question = $dataArray['question'];
        $attachFilePath = $dataArray['attachFilePath'];
        $user_master_id = $this->token_data->userdetail->user_master_id;
        //print_R($this->token_data->userdetail); exit;
        /** development state activate when working and testing */
        $userenv = get_user_env($user_master_id); //(100627);//(100627);
        #$userdetail = $this->getuserdetail($session_id,$user_master_id);
        //echo $token->userdetail->user_master_id
        $this->form_validation->set_data($dataArray);
        $this->form_validation->set_rules('session_id', 'Session ID', 'trim|required');
        if ($this->form_validation->run() == false) {
            #echo 'here1'; exit();
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            #echo 'here'; exit();
            $data_parse['session_details_for_parse'] = $this->Knwlgmastersessionnew_model->fetch_particular_session($session_id);
            $parsr_var = $data_parse['session_details_for_parse'][0]->session_topic;
            $parse_var_id = $data_parse['session_details_for_parse'][0]->session_doctor_id;
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $basic_detail = $this->Knwlgmastersessionnew_model->get_basic_detail($user_master_id);
            $check_duplicate_user = $this->Knwlgmastersessionnew_model->check_duplicate_session_submit($session_id, $user_master_id);
            if ($check_duplicate_user > 0) {
                # echo '1'; exit();
                $err_arr = $this->form_validation->error_array();
                $message = implode(" & ", $err_arr);
                /**
                 * For api detail table
                 */
                $message = "Session Already Booked";
                $output['error'] = $message;
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            } else {
                #echo '2'; exit();
                $str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
                $rand_num = substr(str_shuffle($str_result), 0, 30);
                $t = time();
                $rand_num = $t . $rand_num;
                $data['secret_key'] = $rand_num;
                if ($data_parse['session_details_for_parse'][0]->vendor_id == 2 && $data_parse['session_details_for_parse'][0]->vouchpro_url != "" && !is_null($data_parse['session_details_for_parse'][0]->vouchpro_url)) {
                    $room_link = "" . $data_parse['session_details_for_parse'][0]->vouchpro_url . "?userID=" . $user_master_id . "&fullName=" . $dataArray['first_name'] . "_" . $dataArray['last_name'] . "";
                    $data['room_link'] = $room_link;
                } else {
                    $data['room_link'] = "";
                }
                if ($data_parse['session_details_for_parse'][0]->vendor_id == 4 && $data_parse['session_details_for_parse'][0]->room_id != "" && !is_null($data_parse['session_details_for_parse'][0]->room_id)) {
                    if ($data_parse['session_details_for_parse'][0]->session_cast_type == 2) {
                        $respose_zoom = $this->zoom('user_' . $user_master_id . '_' . rand(10, 100) . '@clirnet.com', $dataArray['first_name'], $dataArray['last_name'], $data_parse['session_details_for_parse'][0]->room_id);
                        //echo $email_participant."lll".$temp_first_name."lll".$temp_last_name."llll".$session_detail_by_slug[0]->room_id;
                        //print_r($respose_zoom);
                        //exit();
                        $myArray = json_decode($respose_zoom, true);
                        if (isset($myArray['join_url']) && $myArray['join_url'] != "") {
                            $data['room_link'] = $myArray['join_url'];
                        } else {
                            $data['room_link'] = "";
                        }
                    }
                    if ($data_parse['session_details_for_parse'][0]->session_cast_type == 1) {
                        //echo "asasasasasasasas";
                        // exit();
                        $respose_zoom = $this->zoom_meeting('user_' . $user_master_id . '_' . rand(10, 100) . '@clirnet.com', $dataArray['first_name'], $dataArray['last_name'], $data_parse['session_details_for_parse'][0]->room_id);
                        //echo $email_participant."lll".$temp_first_name."lll".$temp_last_name."llll".$session_detail_by_slug[0]->room_id;
                        $myArray = json_decode($respose_zoom, true);
                        if (isset($myArray['join_url']) && $myArray['join_url'] != "") {
                            $data['room_link'] = $myArray['join_url'];
                        } else {
                            $data['room_link'] = "";
                        }
                    }
                    //print_r($respose_zoom); exit();
                    //print_r($respose_zoom); exit();
                }
                $data['knwlg_sessions_id'] = $session_id;
                $data['participant_id'] = $user_master_id;
                $data['version'] = $version;
                $data['added_on'] = date('Y-m-d H:i:s', time());
                $data['participant_type'] = "member";
                $data['session_approval_status'] = 1;
                $data['session_user_status'] = 3;
                $data['bitly_link'] = "";
                // echo "<pre>";print_r($data);die();
                $submit_response = $this->Knwlgmastersessionnew_model->session_submit_user_participation_request($data);
                // ******* for leads ******* //
                $this->load->library('Background');
                $payloads = array(
                    "uid" => $user_master_id,
                    "project_id" => 0 ,
                    "utype" => null,
                    "utm_source" => (isset($data["utm_source"])) ? $data["utm_source"] : null,
                    "consent_source" => "session",
                    "type_id" => $session_id,
                    "ip_address" => getenv('HTTP_CLIENT_IP') ?: getenv('HTTP_X_FORWARDED_FOR') ?: getenv('HTTP_X_FORWARDED') ?: getenv('HTTP_FORWARDED_FOR') ?: getenv('HTTP_FORWARDED') ?: getenv('REMOTE_ADDR'),
                    "browser" => (isset($data["browser"])) ? $data["browser"] : null,
                    "status" => 3,
                    "demo_type" => 0,
                    "json_data" => null,
                    "timestamp" => date("Y-m-d H:i:s"),
                    "created_at" => date("Y-m-d H:i:s")
                );
                $this->background->do_in_background_http_post(LEADS_INSERT_URL, $this->input->get_request_header('Authorization'), $payloads);
                // ******* for leads ******* //
                $data_ques_and_files['question'] = strip_tags($question);
                $data_ques_and_files['sessions_participant_id'] = $submit_response;
                $data_ques_and_files['upload_documents'] = $attachFilePath;
                $question_submit_response = $this->Knwlgmastersessionnew_model->session_submit_user_question($data_ques_and_files);
                if ($submit_response > 0 && $question_submit_response > 0) {
                    //"http://localhost/rnv43/sessionNotification/send";
                    //print_r(base_url()); exit;
                    //print_r($urlnotification); print_r($params); exit;
                    //$this->background->notify_in_background($params);
                    #$this->load->library('Background');
                    #$param = array('session_id'=>$session_id,'user_master_id'=>$user_master_id);
                    #$url = base_url() . "rnv44/sessionNotification/send";
                    #$this->background->do_in_background($url, $param);
                    //exit;
                    /** old working code */
                    //echo $user_master_id; exit;
                    $userdetail = $this->getuserdetail($session_id, $user_master_id);
                    //print_r($userdetail); exit;
                    //echo $userenv; exit;
                    switch ($userenv) {
                        case 'GL':
                            $notify = new Clirnetnotification_V5();
                            $notify->reservesessionemail($session_id, $userdetail['userdetail'], $this->session_reserve_email, $user_master_id);
                            //         # $notify->reservesessiononesignal(9672,$userdetail,$this->session_reserve_onesignal);
                            $notify->schedulegl($userdetail['userdetail'], $user_master_id, "reserve");
                            //         #scheduler code will be activated
                            break;
                        case 'IN':
                            //echo 2;
                            $notify = new Clirnetnotification_V5();
                            //echo $userdetail['userdetail'][0]->phone; exit;
                            if ($userdetail['userdetail'][0]->phone != '') {
                                $notify->reservesessionsms($session_id, $userdetail['userdetail'], $this->session_reserve_sms);
                            }
                            if ($userdetail['userdetail'][0]->email != '') {
                                $notify->reservesessionemail($session_id, $userdetail['userdetail'], $this->session_reserve_email, $user_master_id);
                            }
                            $notify->schedulein($userdetail['userdetail'], $user_master_id, "reserve");
                            //         #$notify->reservesessiononesignal(9672,$userdetail,$this->session_reserve_onesignal);
                            //         #scheduler code will be activated
                            break;
                        default:
                            break;
                    }
                    /** old code */
                    // exit;
                    $all_data = array();
                    $message = 'Thank you for reserving the session';
                    /**
                     * For api detail table
                     */
                    /*$this->logdata['utm_source'] = $dataArray['utm_source'];
                    $this->logdata['session_id'] = $dataArray['session_id'];*/
                    if ($dataArray['redirect_to_live'] == 1 && $data['room_link'] != "") {
                        $all_data['redirect_to_live'] = $data['room_link'];
                        $all_data['is_redirect_to_live_thirdparty'] = true;
                    } else {
                        $all_data['is_redirect_to_live_thirdparty'] = false;
                    }
                    $all_data['participation_id'] = $submit_response;
                    $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                    $this->logdata['response'] = REST_Controller::HTTP_OK;
                    $this->logdata['message'] = $message;
                    $this->logdata['session_id_parse'] = $session_id;
                    $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                    /**
                     * For api detail table
                     */
                    $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
                }
            }
        }
    }

    public function editquery_post()
    {
        $dataArray = json_decode(file_get_contents('php://input'), true);
        $session_id = $dataArray['session_id'];
        $my_participant_id = $dataArray['my_participant_id'];
        $question = $dataArray['question'];
        $attachFilePath = $dataArray['attachFilePath'];
        //echo $dataArray['speciality_id'];
        //print_r($dataArray); exit;
        //echo $token->userdetail->user_master_id
        $this->form_validation->set_data($dataArray);
        $this->form_validation->set_rules('session_id', 'Session ID', 'trim|required');
        $this->form_validation->set_rules('my_participant_id', 'Session ID', 'trim|required');
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        if ($this->form_validation->run() == false) {
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $check_correct_user = $this->Knwlgmastersessionnew_model->check_duplicate_session_submit($session_id, $this->token_data->userdetail->user_master_id);
            if ($check_correct_user == 0) {
                $message = "You Haven't Booked The Session";
                /**
                 * For api detail table
                 */
                $output['error'] = $message;
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            } else {
                $user_master_id = $this->token_data->userdetail->user_master_id;
                $data_ques_and_files['knwlg_sessions_participant_id'] = $my_participant_id;
                $data_ques_and_files['question'] = strip_tags($question);
                $data_ques_and_files['upload_documents'] = $attachFilePath;
                $this->Knwlgmastersessionnew_model->session_update_user_question($data_ques_and_files);
                $all_data = array();
                $message = 'Your Request Updated Successfully';
                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
            }
        }
    }

    public function cancelsession_post()
    {
        $dataArray = json_decode(file_get_contents('php://input'), true);
        $session_id = $dataArray['session_id'];
        $cancelation_reason = $dataArray['cancelation_reason'];
        $other_reason = $dataArray['other_reason'];
        $participant_id = $dataArray['participant_id'];
        //echo $dataArray['speciality_id'];
        //print_r($dataArray); exit;
        //echo $token->userdetail->user_master_id
        $this->form_validation->set_data($dataArray);
        $this->form_validation->set_rules('session_id', 'Session id', 'trim|required');
        //$this->form_validation->set_rules('cancelation_reason', 'Reason Required', 'trim|required');
        $this->form_validation->set_rules('participant_id', 'Participant id', 'trim|required');
        //$this->form_validation->set_rules('reason_detail', 'Session_id', 'trim|required');
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        if ($this->form_validation->run() == false) {
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $session_id = $session_id;
            $session_can_res = $cancelation_reason;
            $participant_id = $participant_id;
            $session_can_res_text = strip_tags($other_reason);
            $time = date('Y-m-d H:i:s');
            $data_parse['session_details_for_parse'] = $this->Knwlgmastersessionnew_model->fetch_particular_session($session_id);
            $parsr_var = $data_parse['session_details_for_parse'][0]->session_topic;
            $parse_var_id = $data_parse['session_details_for_parse'][0]->session_doctor_id;
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $basic_detail = $this->Knwlgmastersessionnew_model->get_basic_detail($user_master_id);
            $data = array();
            $data['user_id'] = $this->token_data->userdetail->user_master_id;
            $data['session_id'] = $session_id;
            $data['participant_id'] = $participant_id;
            $data['reason'] = $session_can_res;
            $data['reason_detail'] = $session_can_res_text;
            $data['time'] = $time;
            //print_r($data); exit;
            $doc_del_insert = $this->Knwlgmastersessionnew_model->cancel_session($data);
            $success = 0;
            if ($doc_del_insert > 0) {
                $data = array();
                $data1 = $participant_id;
                $doc_del_del = $this->Knwlgmastersessionnew_model->delete_session_participant($data1);
                if ($doc_del_del > 0) {
                    $success = 1;
                }
            }
            if ($success == 1) {
                $all_data = array();
                $message = 'Your Reservation Has Been Cancelled.';
                $this->load->library('Background');
                /**
                 * This is for background process
                 */
                $basic_detail['user_master_id'] = $user_master_id;
                $basic_detail['parsr_var'] = $parsr_var;
                $basic_detail['parse_var_id'] = $parse_var_id;
                $basic_detail['session_id'] = $session_id;
                $basic_detail['Authorization'] = $this->input->get_request_header('Authorization');
                $url = base_url() . "rnv6/knwlgmastersession/sendmailCancel";
                $param = $basic_detail;
                $this->background->do_in_background($url, $param);
                /**
                 * This is for background process
                 */
                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
            } else {
                //$err_arr = $this->form_validation->error_array();
                $message = "Your Request Terminated, Please Try Later";
                $all_data = array();
                /**
                 * For api detail table
                 */
                $output['error'] = $message;
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
            }
        }
    }

    public function sessiondoctor_get()
    {
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        $profile_id = $this->input->get('profile_id');
        if ($profile_id == "") {
            //$err_arr = $this->form_validation->error_array();
            $message = "Profile  ID Not Present";
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $all_data = $this->Knwlgmastersessionnew_model->profile_details($profile_id);
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }

    public function submitquerydoctor_post()
    {
        $dataArray = json_decode(file_get_contents('php://input'), true);
        //$session_doc_id = $dataArray['session_doc_id'];
        //$question = $dataArray['question'];
        //$attachFilePath = $dataArray['attachFilePath'];
        //echo $dataArray['speciality_id'];
        //print_r($dataArray); exit;
        //echo $token->userdetail->user_master_id
        $this->form_validation->set_data($dataArray);
        $this->form_validation->set_rules('question', 'Question', 'trim|required');
        $this->form_validation->set_rules('session_doc_id', 'Doctor id', 'trim|required');
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        //$session_id = $dataArray['session_id'];
        if ($this->form_validation->run() == false) {
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
        }
    }
    /**
     * @return bool
     */
    public function sendmail_post()
    {
        $user_master_id = $this->input->post('user_master_id');
        $parsr_var = $this->input->post('parsr_var');
        $parse_var_id = $this->input->post('parse_var_id');
        $question = $this->input->post('question');
        $session_id = $this->input->post('session_id');
        $first_name = $this->input->post('first_name');
        $last_name = $this->input->post('last_name');
        $user_mem_id = $this->input->post('user_mem_id');
        $mobile_primary = $this->input->post('mobile_primary');
        //$txt = "user id date";
        //$myfile = file_put_contents('logs_back.txt', $user_master_id.PHP_EOL , FILE_APPEND | LOCK_EX);
        performsendsessionnewregistrationapi($first_name, $last_name, $user_mem_id, $parsr_var, $parse_var_id, strip_tags($question), $mobile_primary, $session_id, $user_master_id);
    }

    public function sendmailCancel_post()
    {
        $user_master_id = $this->input->post('user_master_id');
        $parsr_var = $this->input->post('parsr_var');
        $parse_var_id = $this->input->post('parse_var_id');
        $question = $this->input->post('question');
        $session_id = $this->input->post('session_id');
        $first_name = $this->input->post('first_name');
        $last_name = $this->input->post('last_name');
        $user_mem_id = $this->input->post('user_mem_id');
        $mobile_primary = $this->input->post('mobile_primary');
        //$txt = "user id date";
        //$myfile = file_put_contents('logs_back.txt', $user_master_id.PHP_EOL , FILE_APPEND | LOCK_EX);
        performsendsessionCancelregistrationapi($first_name, $last_name, $user_mem_id, $parsr_var, $parse_var_id, $mobile_primary, $session_id, $user_master_id);
    }

    public function sessionDoctorsListByClientID_get()
    {
        $search_name = empty($this->input->get('name')) ? "" : $this->input->get('name');
        $filter_sp_ids = empty($this->input->get('spids')) ? "" : $this->input->get('spids');
        if ($search_name == "" && $filter_sp_ids == "") {
            $isRequestBlank = true;
        } else {
            $isRequestBlank = false;
        }
        $sp_ids_optional = "";
        $doc_ids_optional = "";
        if ($search_name != "") {
            $spID_list = $this->Knwlgmastersessionnew_model->getSpecialityIDsByName($search_name);
            $sp_ids_optional = implode(",", (array)$spID_list);
            $docID_list = $this->Knwlgmastersessionnew_model->getSessionDoctorIDsByName($search_name);
            $doc_ids_optional = implode(",", (array)$docID_list);
        }
        $client_ids = $this->token_data->userdetail->client_ids;
        if ($client_ids == "") {
            //$err_arr = $this->form_validation->error_array();
            $message = "Client ID  NULL!";
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            /*$all_data = array();
            $all_data = $this->Knwlgmastersessionnew_model->sessionsDoctorsListByClientID($client_ids);
            $message = 'Success';*/
            $all_data = $this->Knwlgmastersessionnew_model->sessionsDoctorsListByClientID(
                $client_ids,
                $doc_ids_optional,
                $sp_ids_optional,
                $filter_sp_ids,
                $isRequestBlank
            );
            $message = 'Success';
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    public function featuredDoctorsDetail_get($doctor_id)
    {
        $client_ids = $this->token_data->userdetail->client_ids;
        if ($client_ids == "") {
            //$err_arr = $this->form_validation->error_array();
            $message = "Client ID  NULL!";
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $all_data = $this->Knwlgmastersessionnew_model->sessionsDoctorsDetail($doctor_id);
            $message = 'Success';
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    public function featuredDoctorsList_get()
    {
        $search_name = empty($this->input->get('name')) ? "" : $this->input->get('name');
        $filter_sp_ids = empty($this->input->get('spids')) ? "" : $this->input->get('spids');
        if ($search_name == "" && $filter_sp_ids == "") {
            $isRequestBlank = true;
        } else {
            $isRequestBlank = false;
        }
        $sp_ids_optional = "";
        $doc_ids_optional = "";
        if ($search_name != "") {
            $spID_list = $this->Knwlgmastersessionnew_model->getSpecialityIDsByName($search_name);
            $sp_ids_optional = implode(",", (array)$spID_list);
            $docID_list = $this->Knwlgmastersessionnew_model->getSessionDoctorIDsByName($search_name);
            $doc_ids_optional = implode(",", (array)$docID_list);
        }
        $client_ids = $this->token_data->userdetail->client_ids;

        $all_data = $this->Knwlgmastersessionnew_model->sessionsDoctorsListByClientID(
            $this->token_data->userdetail->user_master_id,
            $client_ids,
            $doc_ids_optional,
            $sp_ids_optional,
            $filter_sp_ids,
            $isRequestBlank,
            $this->input->get('from'),
            $this->input->get('to')
        );
        $message = 'Success';
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);

    }


    public function abctest_get()
    {
        $search_name = empty($this->input->get('name')) ? "" : $this->input->get('name');
        $filter_sp_ids = empty($this->input->get('spids')) ? "" : $this->input->get('spids');
        $sp_ids_optional = "";
        $doc_ids_optional = "";
        if ($search_name != "") {
            $spID_list = $this->Knwlgmastersessionnew_model->getSpecialityIDsByName($search_name);
            $sp_ids_optional = implode(",", (array)$spID_list);
            $docID_list = $this->Knwlgmastersessionnew_model->getSessionDoctorIDsByName($search_name);
            $doc_ids_optional = implode(",", (array)$docID_list);
        }
        $client_ids = $this->token_data->userdetail->client_ids;
        $all_data = $this->Knwlgmastersessionnew_model->sessionsDoctorsListByClientID(
            $client_ids,
            $doc_ids_optional,
            $sp_ids_optional,
            $filter_sp_ids
        );
        $message = 'Success';
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    public function submitrequestpeer_post()
    {
        $dataArray = json_decode(file_get_contents('php://input'), true);
        $session_doctor_id = $dataArray['session_doctor_id'];
        $user_query = $dataArray['user_query'];
        $attach_file = $dataArray['attach_file'];
        $session_id = $dataArray['session_id'];
        //echo $dataArray['speciality_id'];
        //print_r($dataArray); exit;
        //echo $token->userdetail->user_master_id
        $this->form_validation->set_data($dataArray);
        $this->form_validation->set_rules('user_query', 'Query', 'trim|required');
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        //$session_id = $dataArray['session_id'];
        if ($this->form_validation->run() == false) {
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $data['session_doctor_id'] = $session_doctor_id;
            $data['request_type'] = ($session_doctor_id == 0) ? "general" : "peer";
            $data['user_query'] = $user_query;
            $data['added_on'] = date('Y-m-d H:i:s', time());
            $data['attach_file'] = $attach_file;
            $data['user_master_id'] = $this->token_data->userdetail->user_master_id;
            $data['status'] = 3;
            //print_r($data);
            $submit_response = $this->Knwlgmastersessionnew_model->session_submit_user_peer_request($data);
            //============== RAMANAT H ====================
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $user_details = $this->Knwlgmastersessionnew_model->get_basic_detail($user_master_id);
            $user_name = $user_details['first_name'] . " " . $user_details['last_name'];
            $user_mobile = $user_details['mobile_primary'];
            $session_doc_name = "";
            if ($session_doctor_id != 0 && $session_doctor_id != "") {
                $session_doctor_details = $this->Knwlgmastersessionnew_model->profile_details($session_doctor_id);
                $session_doc_name = $session_doctor_details['doctor_name'];
            }
            $session_topic = "";
            if ($session_id != 0 && $session_id != "") {
                $sessionDetails = $this->Knwlgmastersessionnew_model->get_session_detail_by_session_id($session_id);
                if (!empty($sessionDetails[0])) {
                    $session_topic = $sessionDetails[0]->session_topic;
                }
            }
            $param = array();
            $param['user_query'] = $user_query;
            $param['user_name'] = $user_name;
            $param['user_mobile'] = $user_mobile;
            $param['session_doc_name'] = $session_doc_name;
            $param['attach_file_name'] = $attach_file;
            $param['session_topic'] = $session_topic;
            $param['session_id'] = $session_id == 0 ? "" : $session_id;
            performsendsessionnewregistrationpeer($param, $data['request_type']);
            //==============================================
            //  performsendsessionnewregistrationpeer($user_query ,$data['request_type']  );
            $all_data = array();
            $message = 'Your Request Taken Successfully';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
        }
    }
    public function submitrequestfeatureddoctor_post()
    {
        $dataArray = json_decode(file_get_contents('php://input'), true);
        $session_doctor_id = $dataArray['session_doctor_id'];
        $user_query = $dataArray['user_query'];
        $attach_file = $dataArray['attach_file'];
        $session_id = $dataArray['session_id'];
        //echo $dataArray['speciality_id'];
        //print_r($dataArray); exit;
        //echo $token->userdetail->user_master_id
        $this->form_validation->set_data($dataArray);
        $this->form_validation->set_rules('user_query', 'Query', 'trim|required');
        //$this->form_validation->set_rules('middle_name', 'Middle Name', 'trim|required');
        //$session_id = $dataArray['session_id'];
        if ($this->form_validation->run() == false) {
            $err_arr = $this->form_validation->error_array();
            $message = implode(" & ", $err_arr);
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $data['session_doctor_id'] = $session_doctor_id;
            $data['request_type'] = ($session_doctor_id == 0) ? "general" : "peer";
            $data['user_query'] = $user_query;
            $data['added_on'] = date('Y-m-d H:i:s', time());
            $data['attach_file'] = $attach_file;
            $data['user_master_id'] = $this->token_data->userdetail->user_master_id;
            $data['status'] = 3;
            //print_r($data);
            $submit_response = $this->Knwlgmastersessionnew_model->session_submit_user_peer_request($data);
            //============== RAMANAT H ====================
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $user_details = $this->Knwlgmastersessionnew_model->get_basic_detail($user_master_id);
            $user_name = $user_details['first_name'] . " " . $user_details['last_name'];
            $user_mobile = $user_details['mobile_primary'];
            $session_doc_name = "";
            if ($session_doctor_id != 0 && $session_doctor_id != "") {
                $session_doctor_details = $this->Knwlgmastersessionnew_model->profile_details($session_doctor_id);
                $session_doc_name = $session_doctor_details['doctor_name'];
            }
            $session_topic = "";
            if ($session_id != 0 && $session_id != "") {
                $sessionDetails = $this->Knwlgmastersessionnew_model->get_session_detail_by_session_id($session_id);
                if (!empty($sessionDetails[0])) {
                    $session_topic = $sessionDetails[0]->session_topic;
                }
            }
            $param = array();
            $param['user_query'] = $user_query;
            $param['user_name'] = $user_name;
            $param['user_mobile'] = $user_mobile;
            $param['session_doc_name'] = $session_doc_name;
            $param['attach_file_name'] = $attach_file;
            $param['session_topic'] = $session_topic;
            $param['session_id'] = $session_id == 0 ? "" : $session_id;
            performsendsessionnewregistrationpeer($param, $data['request_type']);
            //==============================================
            //  performsendsessionnewregistrationpeer($user_query ,$data['request_type']  );
            $all_data = array();
            $message = 'Your Request Taken Successfully';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
        }
    }
    /**
     * @param string $user_master_id
     * @return arrayx
     */
    public function getMeetingDetailsBySessionID_get()
    {
        $id = $this->input->get('id');
        if (!empty($id)) {
            $all_data = array();
            $message = "";
            //$user_master_id = $this->token_data->userdetail->user_master_id;
            //print_r($this->token_data);
            //echo $token->userdetail->user_master_id
            $all_data = $this->Knwlgmastersessionnew_model->getMeetingDetailsBySessionID($id);
            if (count($all_data) > 0) {
                $message = 'Success';
            } else {
                $message = 'Record not found!';
            }
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    /**
     * @param string $session doctor id
     * @return arrayx
     */
    public function get_question_featured_doctor_get()
    {
        $id = $this->input->get('id');
        if (!empty($id)) {
            $all_data = array();
            $message = "";
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $all_data = $this->Knwlgmastersessionnew_model->get_user_peer_request_by_session_doctor_id($id, $user_master_id);
            if (count($all_data) > 0) {
                $message = 'Success';
            } else {
                $message = 'Record not found!';
            }
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
    public function editCertificate_post()
    {
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);

        $this->load->library(array('ImageGCPnew'));
        $dataArray = json_decode(file_get_contents('php://input'), true);
        //print_r($dataArray); exit();
        $type = $dataArray['type'];
        if ($type == "namechange") {
            $first_name = $dataArray['first_name'];
            $middle_name = $dataArray['middle_name'];
            $last_name = $dataArray['last_name'];
            $certificate_id = $dataArray['certificate_id'];
            $session_id = $dataArray['session_id'];
            $user_master_id = $this->token_data->userdetail->user_master_id;
            //echo $user_master_id; exit();
            if (trim($first_name) == "" || trim($last_name) == "" || trim($user_master_id) == "" || trim($certificate_id) == "" || trim($session_id) == "") {
                $message = "First Name, last Name Required";
                $output['error'] = "First Name, last Name Required";
                $phone_no = $this->post('email');
                $this->logdata['username'] = $phone_no;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            } else {
                $data['first_name'] = $first_name;
                $data['middle_name'] = $middle_name;
                $data['last_name'] = $last_name;
                // print_r($data);
                // exit();
                $effected_row = $this->Knwlgmastersessionnew_model->update_participant_name($data, $user_master_id);
                //echo $effected_row; exit;
                if ($effected_row > 0) {
                    //get type of certificate
                    $status = $this->Knwlgmastersessionnew_model->getcertificatetype($certificate_id);
                    // $is_generated = $this->Knwlgmastersessionnew_model->is_generated_status($user_master_id, $session_id);
                    // echo 'status ----- '.$status;
                    // exit;
                    // print_r($status);
                    // exit;
                    //  die;
                    //$this->Knwlgmastersessionnew_model->delete_certificate($certificate_id, $session_id, $user_master_id);
                    // //activate after implementation of code
                    // new certificate develop code activation
                    $result = $this->Knwlgmastersessionnew_model->session_user($session_id, $user_master_id);

                    // echo 'session_result -------- ';
                    // print_r($result);
                    // exit;
                    $this->pdf = new Dompdf();
                    define('UPLOAD_DIR', './uploads/session_participant_certificate/');
                    foreach ($result as $single_result) {
                        //  print_r($single_result); exit;
                        if ($single_result->is_new == 1) {
                            $participant_name = $single_result->first_name . ' ' . $single_result->middle_name . ' ' . $single_result->last_name;
                            $postion = $single_result->certificate_position;
                            //echo $postion; exit;
                            $positionobj = json_decode($postion);
                            //  print_r($positionobj->certificate_position_top);die();
                            //print_r($positionobj);die();
                            $position_nameobj_top = $positionobj->certificate_name_position_top;
                            $positiono_namebj_left = $positionobj->certificate_name_position_left;
                            //$positionobj_left = $positionobj->certificate_position_left;
                            $participant_font_size = $positionobj->participant_font_size;
                            $cpd_points_obj = $positionobj->cpd_certificate_points;
                            $cpd_points_top_obj = $positionobj->cpd_points_top;
                            $cpd_points_left_obj = $positionobj->cpd_points_left;
                            $cpd_points_fontsize_obj = $positionobj->cpd_points_font_size;
                            $course_date = "";
                            $topic_top_obj = $positionobj->topic_top;
                            $topic_left_obj = $positionobj->topic_left;
                            $topic_fontsize_obj = $positionobj->topic_font_size;
                            $certificate_page = $positionobj->certificate_page;
                            // print_R($certificate_page);
                            // print_r($status);
                            // exit;
                            if ($status == 'cpd_session') {
                                $cpd_certificate_points = $this->Knwlgmastersessionnew_model->getcpdpoints($session_id, $user_master_id); //$positionobj->cpd_certificate_points;
                                // print_R($cpd_certificate_points);
                                $result_array = array(
                                    'dataURI' => $single_result->image_url,
                                    'participant_name' => $participant_name,
                                    'name_top' => $position_nameobj_top,
                                    'name_left' => $positiono_namebj_left,
                                    'participant_font_size' => $participant_font_size,
                                    'points' => $cpd_certificate_points,
                                    'cpd_points_top' => $cpd_points_top_obj,
                                    'cpd_points_left' => $cpd_points_left_obj,
                                    'cpd_points_font_size' => $cpd_points_fontsize_obj,
                                    'course_date' => $course_date,
                                    'topic' => $single_result->certificate_text,
                                    'topic_top' => $topic_top_obj,
                                    'topic_left' => $topic_left_obj,
                                    'topic_font_size' => $topic_fontsize_obj
                                );
                            } else {
                                $cpd_certificate_points = "";
                                $result_array = array(
                                    'dataURI' => $single_result->image_url,
                                    'participant_name' => $participant_name,
                                    'name_top' => $position_nameobj_top,
                                    'name_left' => $positiono_namebj_left,
                                    'participant_font_size' => $participant_font_size,
                                    //'points'=>$cpd_certificate_points,
                                    'cpd_points_top' => $cpd_points_top_obj,
                                    'cpd_points_left' => $cpd_points_left_obj,
                                    'cpd_points_font_size' => $cpd_points_fontsize_obj,
                                    'course_date' => $course_date,
                                    'topic' => $single_result->certificate_text,
                                    'topic_top' => $topic_top_obj,
                                    'topic_left' => $topic_left_obj,
                                    'topic_font_size' => $topic_fontsize_obj
                                );
                            }
                            // print_r($result_array);
                            // exit;
                            $alldocids = $this->Knwlgmastersessionnew_model->session_doctor_ids($session_id);
                            $result_doc = $this->Knwlgmastersessionnew_model->session_doctor($alldocids);
                            $signature = $this->Knwlgmastersessionnew_model->session_doctor_signature($alldocids);
                            // print_r($single_result['image_url']);
                            // die();
                            // echo $certificate_page;
                            // exit;

                            if ($signature != '') {
                                $path = $signature;
                                $type = pathinfo($path, PATHINFO_EXTENSION);
                                $data = file_get_contents($path);
                                $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
                            } else {
                                $base64 = '';
                            }


                            // print_r($result_array);
                            // exit;
                            if ($certificate_page == 'potrait') {
                                $pdf_html = $this->parser->parse('cms_certificate/certificate_updated_portrait', $result_array, true);
                                $this->pdf->set_paper('a4', 'portrait');
                                //echo $pdf_html; exit;
                            } else {
                                //certificate_updated_landscape
                                $pdf_html = $this->parser->parse('cms_certificate/certificate_updated_landscape', $result_array, true);
                                $this->pdf->set_paper('letter', 'landscape');
                            }
                            //render and output our pdf
                            $pdf_html = preg_replace('/>\s+</', '><', $pdf_html);
                            // echo 'pdf_html ------- '.$pdf_html;
                            // exit;
                            $this->pdf->load_html($pdf_html);
                            $this->pdf->render();
                            $output = $this->pdf->output(array("compress" => 0));
                            // echo $output;
                            // exit();
                            define('UPLOAD_DIR_TEMP', './tempdocuments2/');
                            $unique_id = uniqid();
                            $file = getcwd() . "/application/views/cms_certificate/" . $unique_id . 'Cert' . '.pdf';
                            file_put_contents($file, $output);
                            $output_firebase = "./application/views/cms_certificate/" . $unique_id . 'Cert' . '.pdf';
                            // Make sure output_firebase is a string, not an array
                            if (is_array($output_firebase)) {
                                // If it's an array, use the first element or a default path
                                $output_firebase = isset($output_firebase[0]) ? $output_firebase[0] : "./application/views/cms_certificate/" . $unique_id . 'Cert' . '.pdf';
                            }
                            // Debug the file path
                            if (!file_exists($output_firebase)) {
                                echo "Error: File does not exist at path: " . $output_firebase;
                                exit();
                            }
                            // echo $output_firebase;
                            // exit;
                            $__file_temp_path = $unique_id . 'Cert' . '.pdf';
                            $imageGCPObject = new ImageGCPnew();
                            $doc_path = "";
                            //print_r($output_firebase);
                            // echo 'output_firebase ----- '.$output_firebase;
                            // exit;
                            $filepath = $imageGCPObject->upload_certificate_pdf($output_firebase);
                            // print_r($filepath);
                            // exit;
                            if (!empty($filepath)) {
                                $doc_path = $filepath;
                            }
                            $datam['user_master_id'] = $single_result->participant_id;
                            $datam['certificate_title'] = "Certificate For Attending " . $single_result->session_topic . "";
                            $datam['file'] = $doc_path;
                            $datam['image'] = '';
                            $datam['certificate_number'] = $unique_id;
                            $datam['certificate_issue_date'] = date('Y-m-d H:i:s', time());
                            $datam['type'] = $status;
                            $datam['type_id'] = $single_result->session_id;
                            $datam['is_converted'] = 0;
                            $datam['status'] = 3;
                            //print_r('hello');
                            // $data_id = $this->Knwlgmastersessionnew_model->insert_user_specific_certificate($datam);
                            $data_id = $this->Knwlgmastersessionnew_model->update_user_certificate($datam, $certificate_id);
                            //success
                            $message = 'Success';
                            /**
                             * For api detail table
                             */
                            $all_data['new_url'] = $datam['file'];
                            $all_data['new_cert_id'] = $data_id;
                            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                            $this->logdata['response'] = REST_Controller::HTTP_OK;
                            $this->logdata['message'] = $message;
                            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                            /**
                             * For api detail table
                             */
                            $path = './application/views/cms_certificate/' . $single_result->html . '';
                            unlink($output_firebase); //unlink($path);
                            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
                        } else {
                            // echo "old";die();
                            $alldocids = $this->Knwlgmastersessionnew_model->session_doctor_ids($session_id);
                            $result_doc = $this->Knwlgmastersessionnew_model->session_doctor($alldocids);
                            $signature = $this->Knwlgmastersessionnew_model->session_doctor_signature($alldocids);

                            if ($signature != '') {
                                $path = $signature;
                                $type = pathinfo($path, PATHINFO_EXTENSION);
                                $data = file_get_contents($path);
                                $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
                            } else {
                                $base64 = '';
                            }
                            // $path = $signature;
                            // $type = pathinfo($path, PATHINFO_EXTENSION);
                            // $data = file_get_contents($path);
                            // $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
                            //echo $base64; exit();
                            $data = array();
                            $data['name'] = $single_result->first_name . " " . $single_result->middle_name . " " . $single_result->last_name;
                            $data['topic'] = $single_result->session_topic;
                            $data['doctors_signature'] = $base64;
                            $datatime = date('jS \ F Y', strtotime($single_result->start_datetime));
                            $data['time'] = $datatime;
                            $data['doctors'] = $result_doc;
                            $data['session_id'] = $session_id;
                            $data['user_master_id'] = $user_master_id;
                            $pdf_html = $this->parser->parse('cms_certificate/' . $single_result->html . '', $data, true);
                            // $this->pdf = new DOMPDF();
                            //render and output our pdf
                            $pdf_html = preg_replace('/>\s+</', '><', $pdf_html);
                            //   print_r($pdf_html);
                            //   exit;
                            //$this->pdf->set_paper('letter', 'landscape');
                            $this->pdf->load_html('' . $pdf_html . '');
                            $this->pdf->render();
                            $output = $this->pdf->output(array("compress" => 0));
                            #echo $output; exit();
                            define('UPLOAD_DIR_TEMP', './tempdocuments2/');
                            $unique_id = uniqid();
                            $file = "./application/views/cms_certificate/" . $unique_id . 'Cert' . '.pdf';
                            # echo $file; exit;
                            file_put_contents($file, $output);
                            $output_firebase = "./application/views/cms_certificate/" . $unique_id . 'Cert' . '.pdf';
                            $__file_temp_path = $unique_id . 'Cert' . '.pdf';
                            $imageGCPObject = new ImageGCPnew();
                            $doc_path = "";
                            $filepath = $imageGCPObject->upload_certificate_pdf($output_firebase);
                            //print_r($filepath); exit;
                            if (!empty($filepath)) {
                                $doc_path = $filepath;
                            }
                            // $this->load->library('Dom_pdf');
                            // //$pdf_html = preg_replace('/>\s+</', '><', $pdf_html);
                            //  $this->dompdf->load_html(''.$pdf_html.'');
                            //  $this->dompdf->set_option('isHtml5ParserEnabled', true);
                            //  $this->dompdf->setPaper('A4', 'potrait');
                            //  $this->dompdf->render();
                            // // $options->set('isHtml5ParserEnabled', true);
                            //  $output = $this->dompdf->output();
                            //  $unique_id=uniqid();
                            //  $file = UPLOAD_DIR . $unique_id .'Cert'. '.pdf';
                            //  file_put_contents( $file, $output);
                            $datam['user_master_id'] = $single_result->participant_id;
                            $datam['certificate_title'] = "Certificate For Attending " . $single_result->session_topic . "";
                            $datam['file'] = $doc_path;
                            $datam['certificate_number'] = $unique_id;
                            $datam['certificate_issue_date'] = date('Y-m-d H:i:s', time());
                            $datam['type'] = $status;
                            $datam['type_id'] = $single_result->session_id;
                            $datam['status'] = 3;
                            //$data_id = $this->Knwlgmastersessionnew_model->insert_user_specific_certificate($datam);
                            $data_id = $this->Knwlgmastersessionnew_model->update_user_certificate($datam, $certificate_id);
                            $message = 'Success';

                            $all_data['new_url'] = $datam['file'];
                            $all_data['new_cert_id'] = $data_id;
                            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                            $this->logdata['response'] = REST_Controller::HTTP_OK;
                            $this->logdata['message'] = $message;
                            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

                            unlink($output_firebase);
                            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
                        }

                    }
                } else {
                    $message = "Certificate Not Issued, Please Check your input name.";
                    $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                    $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                    $this->logdata['message'] = $message;
                    $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                    $this->set_response($all_data, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);

                }

            }
        }
        if ($type == "other") {
            $certificate_id = $dataArray['certificate_id'];
            $session_id = $dataArray['session_id'];
            $user_master_id = $this->token_data->userdetail->user_master_id;
            //$other_text = $dataArray['other_text'];
            //echo $user_master_id; exit();
            if (trim($certificate_id) == "" || trim($session_id) == "") {
                $message = "Mandetory Data Missing";
                $output['error'] = "Mandetory Data Missing";
                $phone_no = $this->post('email');
                $this->logdata['username'] = $phone_no;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            } else {
                $message = 'Request received, You will get A call Shortly';
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
            }
        }

    }
    public function editCertificateold_post()
    {
        $domain = mb_substr($_SERVER['SERVER_NAME'], 0, 3);
        if (($domain == "doc") || ($domain == "dev")) {
            $address = "https://devadmin.clirnet.com/admin/application/views/cms_certificate/";
        } else {
            $address = "https://admin.clirnet.com/admin/application/views/cms_certificate/";
        }
        $dataArray = json_decode(file_get_contents('php://input'), true);
        #print_r($dataArray); exit();
        $type = $dataArray['type'];
        if ($type == "namechange") {
            $first_name = $dataArray['first_name'];
            $middle_name = $dataArray['middle_name'];
            $last_name = $dataArray['last_name'];
            $certificate_id = $dataArray['certificate_id'];
            $session_id = $dataArray['session_id'];
            $user_master_id = $this->token_data->userdetail->user_master_id;
            //echo $user_master_id; exit();
            if (trim($first_name) == "" || trim($last_name) == "" || trim($user_master_id) == "" || trim($certificate_id) == "" || trim($session_id) == "") {
                $message = "First Name, last Name Required";
                $output['error'] = "First Name, last Name Required";
                $phone_no = $this->post('email');
                $this->logdata['username'] = $phone_no;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            } else {
                $data['first_name'] = $first_name;
                $data['middle_name'] = $middle_name;
                $data['last_name'] = $last_name;
                #print_r($data); exit();
                $effected_row = $this->Knwlgmastersessionnew_model->update_participant_name($data, $user_master_id);
                #echo $effected_row; exit;
                if ($effected_row > 0) {
                    $this->Knwlgmastersessionnew_model->delete_certificate($certificate_id, $session_id, $user_master_id);
                    $result = $this->Knwlgmastersessionnew_model->session_user($session_id, $user_master_id);
                    #print_r($result); exit;
                    define('UPLOAD_DIR', './uploads/session_participant_certificate/');
                    foreach ($result as $single_result) {
                        //print_r($single_result); exit();
                        if (!is_null($single_result->html) && $single_result->html != "") {
                            $data = array();
                            $data['name'] = $single_result->first_name . " " . $single_result->middle_name . " " . $single_result->last_name;
                            $data['topic'] = $single_result->session_topic;
                            $data['time'] = $single_result->start_datetime;
                            $data['session_id'] = $session_id;
                            $data['user_master_id'] = $user_master_id;
                            //echo 'https://devadmin.clirnet.com/admin/application/views/cms_certificate/'.$single_result->html.''; exit();
                            # $fileContents = file_get_contents('https://devadmin.clirnet.com/admin/application/views/cms_certificate/'.$single_result->html.'');
                            $fileContents = file_get_contents($address . "" . $single_result->html . '');
                            //echo $fileContents; exit();
                            $upload = file_put_contents('./application/views/cms_certificate/' . $single_result->html . '', $fileContents);
                            # echo $upload; exit();
                            $pdf_html = $this->parser->parse('cms_certificate/' . $single_result->html . '', $data, true);
                            //echo $pdf_html; exit();
                            $this->pdf = new DOMPDF();
                            //render and output our pdf
                            $pdf_html = preg_replace('/>\s+</', '><', $pdf_html);
                            $this->pdf->set_paper('letter', 'landscape');
                            $this->pdf->load_html('' . $pdf_html . '');
                            $this->pdf->render();
                            $output = $this->pdf->output(array("compress" => 0));
                            //echo $output; exit();
                            $unique_id = uniqid();
                            $file = UPLOAD_DIR . $unique_id . 'Cert' . '.pdf';
                            file_put_contents($file, $output);
                            $datam['user_master_id'] = $single_result->participant_id;
                            $datam['certificate_title'] = "Certtificate For Attending " . $single_result->session_topic . "";
                            $datam['file'] = base_url() . "uploads/session_participant_certificate/" . $unique_id . 'Cert' . '.pdf';
                            $datam['certificate_number'] = $unique_id;
                            $datam['certificate_issue_date'] = date('Y-m-d H:i:s', time());
                            $datam['type'] = 'session';
                            $datam['type_id'] = $single_result->session_id;
                            $datam['status'] = 3;
                            $data_id = $this->Knwlgmastersessionnew_model->insert_user_specific_certificate($datam);
                            $message = 'Success';
                            /**
                             * For api detail table
                             */
                            $all_data['new_url'] = $datam['file'];
                            $all_data['new_cert_id'] = $data_id;
                            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                            $this->logdata['response'] = REST_Controller::HTTP_OK;
                            $this->logdata['message'] = $message;
                            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                            /**
                             * For api detail table
                             */
                            $path = './application/views/cms_certificate/' . $single_result->html . '';
                            unlink($path);
                            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
                        } else {
                            $output['error'] = "No Template assigned";
                            $phone_no = $this->post('email');
                            $this->logdata['username'] = $phone_no;
                            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                            $this->logdata['message'] = $message;
                            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
                        }
                    }
                } else {
                    $output['error'] = "No Changes Done.";
                    $phone_no = $this->post('email');
                    $this->logdata['username'] = $phone_no;
                    $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                    $this->logdata['message'] = $message;
                    $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                    $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
                }
            }
        }
        if ($type == "other") {
            $certificate_id = $dataArray['certificate_id'];
            $session_id = $dataArray['session_id'];
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $other_text = $dataArray['other_text'];
            //echo $user_master_id; exit();
            if (trim($certificate_id) == "" || trim($session_id) == "") {
                $message = "Mandetory Data Missing";
                $output['error'] = "Mandetory Data Missing";
                $phone_no = $this->post('email');
                $this->logdata['username'] = $phone_no;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            } else {
                $message = 'Request received, You will get A call Shortly';
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
            }
        }
    }
    // Code writter by rakesh maity start from here
    /**
     * Summary of set_user_master_id
     * @param mixed $user_master_id
     * @return void
     */
    private function set_user_master_id($user_master_id = null)
    {
        if (!empty($user_master_id)) {
            $this->user_master_id = $user_master_id;
        } else {
            $this->user_master_id = (int) $this->token_data->userdetail->user_master_id; //290
        }
    }
    /**
     * Summary of live_sessions_get
     * @return void
     */
    public function live_sessions_get()
    {
        $this->set_user_master_id();
        // echo $this->user_master_id;
        // exit;
        $key = 'live_sessions_' . $this->user_master_id;
        $this->load->library('Myredis');
        // Load redis library and check if it exists then fetch data from redis cache
        if ($this->myredis->exists($key)) {
            $this->payload = array();
            $this->payload  = $this->myredis->get($key);
            $this->message = 'Data has been successfully fetched from redis';
            $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata);
        }
        $this->payload['live_sessions'] = $this->Knwlgmastersessionnew_model->live_sessions($this->user_master_id);
        $this->payload['total_sessions'] = ($this->Knwlgmastersessionnew_model->live_sessions($this->user_master_id)) ? count($this->Knwlgmastersessionnew_model->live_sessions($this->user_master_id)) : 0;
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $this->message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        // Set redis key value pair
        $this->myredis->set($key, $this->payload);
        $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    // Code writter by rakesh maity end from here
    /** notification user information */
    public function getuserdetail($session_id, $user_master_id)
    {
        $result = array();
        $result['userdetail'] = $this->Knwlgmastersessionnew_model->getusersessiondetail($session_id, $user_master_id);
        //$result['one_signal'] = $this->Knwlgmastersessionnew_model->getusersessiononesignaldetail($user_master_id);
        return $result;
    }
    /** notification user information */
    public function reserve_get()
    {
        //print_r(); exit;
        // $dataArray = json_decode(file_get_contents('php://input'), true);
        // $version = $this->input->get_request_header('Version');
        $session_id = $this->uri->segment(4); //$dataArray['session_id'];
        if ($session_id == "") {
            $message = "Please provide session id";
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $data_parse['session_details_for_parse'] = $this->Knwlgmastersessionnew_model->fetch_particular_session($session_id);
            $getcpdstatus = $this->Knwlgmastersessionnew_model->getcpdstatus($session_id);
            $parsr_var = $data_parse['session_details_for_parse'][0]->session_topic; //
            $parse_var_id = $data_parse['session_details_for_parse'][0]->session_doctor_id;
            $user_master_id = $this->token_data->userdetail->user_master_id;
            $basic_detail = $this->Knwlgmastersessionnew_model->get_basic_detail($user_master_id);
            $check_duplicate_user = $this->Knwlgmastersessionnew_model->check_duplicate_session_submit($session_id, $user_master_id);
            if ($check_duplicate_user > 0) {
                $err_arr = $this->form_validation->error_array();
                $message = implode(" & ", $err_arr);
                /**
                 * For api detail table
                 */
                $message = "Session Already Booked";
                $output['error'] = $message;
                $output['participation_id'] = $user_master_id;
                $output['certificateid'] = $this->sessioncertificate($user_master_id, $session_id, $getcpdstatus);
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            } else {
                #echo '2'; exit();
                $str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
                $rand_num = substr(str_shuffle($str_result), 0, 30);
                $t = time();
                $rand_num = $t . $rand_num;
                $data['secret_key'] = $rand_num;
                if ($data_parse['session_details_for_parse'][0]->vendor_id == 2 && $data_parse['session_details_for_parse'][0]->vouchpro_url != "" && !is_null($data_parse['session_details_for_parse'][0]->vouchpro_url)) {
                    $room_link = "" . $data_parse['session_details_for_parse'][0]->vouchpro_url . "?userID=" . $user_master_id . "&fullName=" . $dataArray['first_name'] . "_" . $dataArray['last_name'] . "";
                    $data['room_link'] = $room_link;
                } else {
                    $data['room_link'] = "";
                }
                if ($data_parse['session_details_for_parse'][0]->vendor_id == 4 && $data_parse['session_details_for_parse'][0]->room_id != "" && !is_null($data_parse['session_details_for_parse'][0]->room_id)) {
                    if ($data_parse['session_details_for_parse'][0]->session_cast_type == 2) {
                        $respose_zoom = $this->zoom('user_' . $user_master_id . '_' . rand(10, 100) . '@clirnet.com', $dataArray['first_name'], $dataArray['last_name'], $data_parse['session_details_for_parse'][0]->room_id);
                        //echo $email_participant."lll".$temp_first_name."lll".$temp_last_name."llll".$session_detail_by_slug[0]->room_id;
                        //print_r($respose_zoom);
                        //exit();
                        $myArray = json_decode($respose_zoom, true);
                        if (isset($myArray['join_url']) && $myArray['join_url'] != "") {
                            $data['room_link'] = $myArray['join_url'];
                        } else {
                            $data['room_link'] = "";
                        }
                    }
                    if ($data_parse['session_details_for_parse'][0]->session_cast_type == 1) {
                        //echo "asasasasasasasas";
                        // exit();
                        $respose_zoom = $this->zoom_meeting('user_' . $user_master_id . '_' . rand(10, 100) . '@clirnet.com', $dataArray['first_name'], $dataArray['last_name'], $data_parse['session_details_for_parse'][0]->room_id);
                        //echo $email_participant."lll".$temp_first_name."lll".$temp_last_name."llll".$session_detail_by_slug[0]->room_id;
                        $myArray = json_decode($respose_zoom, true);
                        if (isset($myArray['join_url']) && $myArray['join_url'] != "") {
                            $data['room_link'] = $myArray['join_url'];
                        } else {
                            $data['room_link'] = "";
                        }
                    }
                    //print_r($respose_zoom); exit();
                    //print_r($respose_zoom); exit();
                }
                $data['knwlg_sessions_id'] = $session_id;
                $data['participant_id'] = $user_master_id;
                $data['version'] = 0; //$version;
                $data['added_on'] = date('Y-m-d H:i:s', time());
                $data['participant_type'] = "member";
                $data['session_approval_status'] = 1;
                $data['session_user_status'] = 3;
                $data['bitly_link'] = "";
                $submit_response = $this->Knwlgmastersessionnew_model->session_submit_user_participation_request($data);
                // print_r($submit_response); exit;
                $data_ques_and_files['question'] = ""; //strip_tags($question);
                $data_ques_and_files['sessions_participant_id'] = $submit_response;
                $data_ques_and_files['upload_documents'] = ""; //$attachFilePath;
                #print_r($data_ques_and_files); exit;
                $question_submit_response = $this->Knwlgmastersessionnew_model->session_submit_user_question($data_ques_and_files);
                // print_r($question_submit_response);
                // exit();
                if ($submit_response > 0 && $question_submit_response > 0) {
                    # print_r($basic_detail); exit;
                    //print_r($basic_detail); exit;
                    /* performsendsessionnewregistrationapi($basic_detail['first_name'], $basic_detail['last_name'], $basic_detail['user_mem_id'], $parsr_var, $parse_var_id, strip_tags($question), $basic_detail['mobile_primary'], $session_id,$user_master_id); */
                    /**/
                    $this->load->library('Background');
                    /**
                     * This is for background process
                     */
                    $basic_detail['user_master_id'] = $user_master_id;
                    $basic_detail['parsr_var'] = $parsr_var;
                    $basic_detail['parse_var_id'] = $parse_var_id;
                    $basic_detail['question'] = $question;
                    $basic_detail['session_id'] = $session_id;
                    $basic_detail['question'] = $question;
                    $basic_detail['Authorization'] = $this->input->get_request_header('Authorization');
                    $url = base_url() . "rnv6/knwlgmastersession/sendmail";
                    $param = $basic_detail;
                    $this->background->do_in_background($url, $param); /**/
                    /**
                     * This is for background process
                     */
                    $all_data = array();
                    $message = 'Thank you for reserving the session';
                    /**
                     * For api detail table
                     */
                    /*$this->logdata['utm_source'] = $dataArray['utm_source'];
                    $this->logdata['session_id'] = $dataArray['session_id'];*/
                    if ($dataArray['redirect_to_live'] == 1 && $data['room_link'] != "") {
                        $all_data['redirect_to_live'] = $data['room_link'];
                        $all_data['is_redirect_to_live_thirdparty'] = true;
                    } else {
                        $all_data['is_redirect_to_live_thirdparty'] = false;
                    }
                    $all_data['participation_id'] = $user_master_id; //$submit_response;
                    $all_data['certificateid'] = $this->sessioncertificate($user_master_id, $session_id, $getcpdstatus);
                    $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                    $this->logdata['response'] = REST_Controller::HTTP_OK;
                    $this->logdata['message'] = $message;
                    //$this->logdata['session_id_parse'] = $session_id;
                    $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                    //print_
                    /**
                     * For api detail table
                     */
                    $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
                }
            }
        }
    }
    public function sessioncertificate($user_master_id, $session_id, $getcpdstatus)
    {
        $this->load->library(array('ImageGCPnew'));
        $checkcertificate = $this->Knwlgmastersessionnew_model->certificate_available($user_master_id, $session_id, $getcpdstatus);
        //echo $checkcertificate; exit;
        if ($checkcertificate != '') {
            //echo 1; exit;
            return $checkcertificate;
        } else {
            //echo 2; exit;
            $is_generated = $this->Knwlgmastersessionnew_model->is_generated_status($user_master_id, $session_id);
            $result = $this->Knwlgmastersessionnew_model->session_user($session_id, $user_master_id);
            //print_r($result); exit;
            $this->pdf = new DOMPDF();
            define('UPLOAD_DIR', './uploads/session_participant_certificate/');
            foreach ($result as $single_result) {
                //         //  print_r($single_result); exit;
                if ($single_result->is_new == 1) {
                    $participant_name = $single_result->first_name . ' ' . $single_result->middle_name . ' ' . $single_result->last_name;
                    $postion = $single_result->certificate_position;
                    //echo $postion; exit;
                    $positionobj = json_decode($postion);
                    //  print_r($positionobj->certificate_position_top);die();
                    //print_r($positionobj);die();
                    $position_nameobj_top = $positionobj->certificate_name_position_top;
                    $positiono_namebj_left = $positionobj->certificate_name_position_left;
                    //$positionobj_left = $positionobj->certificate_position_left;
                    $participant_font_size = $positionobj->participant_font_size;
                    $cpd_points_obj = $positionobj->cpd_certificate_points;
                    $cpd_points_top_obj = $positionobj->cpd_points_top;
                    $cpd_points_left_obj = $positionobj->cpd_points_left;
                    $cpd_points_fontsize_obj = $positionobj->cpd_points_font_size;
                    $course_date = "";
                    $topic_top_obj = $positionobj->topic_top;
                    $topic_left_obj = $positionobj->topic_left;
                    $topic_fontsize_obj = $positionobj->topic_font_size;
                    $certificate_page = $positionobj->certificate_page;
                    //print_R($certificate_page); exit;
                    //print_r($status); exit;
                    if ($getcpdstatus == 'cpd_session') {
                        $cpd_certificate_points = $this->Knwlgmastersessionnew_model->getcpdpoints($session_id, $user_master_id); //$positionobj->cpd_certificate_points;
                        // print_R($cpd_certificate_points);
                        $result_array = array(
                            'dataURI' => $single_result->image_url,
                            'participant_name' => $participant_name,
                            'name_top' => $position_nameobj_top,
                            'name_left' => $positiono_namebj_left,
                            'participant_font_size' => $participant_font_size,
                            'points' => $cpd_certificate_points,
                            'cpd_points_top' => $cpd_points_top_obj,
                            'cpd_points_left' => $cpd_points_left_obj,
                            'cpd_points_font_size' => $cpd_points_fontsize_obj,
                            'course_date' => "",
                            'topic' => "",
                            'topic_top' => $topic_top_obj,
                            'topic_left' => $topic_left_obj,
                            'topic_font_size' => $topic_fontsize_obj
                        );
                    } else {
                        $cpd_certificate_points = "";
                        $result_array = array(
                            'dataURI' => $single_result->image_url,
                            'participant_name' => $participant_name,
                            'name_top' => $position_nameobj_top,
                            'name_left' => $positiono_namebj_left,
                            'participant_font_size' => $participant_font_size,
                            //'points'=>$cpd_certificate_points,
                            'cpd_points_top' => $cpd_points_top_obj,
                            'cpd_points_left' => $cpd_points_left_obj,
                            'cpd_points_font_size' => $cpd_points_fontsize_obj,
                            'course_date' => $course_date,
                            'topic' => "",
                            'topic_top' => "",
                            'topic_left' => $topic_left_obj,
                            'topic_font_size' => $topic_fontsize_obj
                        );
                    }
                    //print_r($result_array); exit;
                    $alldocids = $this->Knwlgmastersessionnew_model->session_doctor_ids($session_id);
                    $result_doc = $this->Knwlgmastersessionnew_model->session_doctor($alldocids);
                    $signature = $this->Knwlgmastersessionnew_model->session_doctor_signature($alldocids);
                    //print_r($single_result['image_url']);die();
                    $path = $signature;
                    $type = pathinfo($path, PATHINFO_EXTENSION);
                    $data = file_get_contents($path);
                    $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
                    //print_r($result_array); exit;
                    if ($certificate_page == 'potrait') {
                        $pdf_html = $this->parser->parse('cms_certificate/certificate_updated_portrait', $result_array, true);
                        $this->pdf->set_paper('a4', 'portrait');
                        //echo $pdf_html; exit;
                    } else {
                        //certificate_updated_landscape
                        $pdf_html = $this->parser->parse('cms_certificate/certificate_updated_landscape', $result_array, true);
                        $this->pdf->set_paper('letter', 'landscape');
                    }
                    //render and output our pdf
                    $pdf_html = preg_replace('/>\s+</', '><', $pdf_html);
                    $this->pdf->load_html('' . $pdf_html . '');
                    $this->pdf->render();
                    $output = $this->pdf->output(array("compress" => 0));
                    #echo $output; exit();
                    define('UPLOAD_DIR_TEMP', './tempdocuments2/');
                    $unique_id = uniqid();
                    $file = "./application/views/cms_certificate/" . $unique_id . 'Cert' . '.pdf';
                    # echo $file; exit;
                    file_put_contents($file, $output);
                    $output_firebase = "./application/views/cms_certificate/" . $unique_id . 'Cert' . '.pdf';
                    $__file_temp_path = $unique_id . 'Cert' . '.pdf';
                    $imageGCPObject = new ImageGCPnew();
                    $doc_path = "";
                    $filepath = $imageGCPObject->upload_certificate_pdf($output_firebase);
                    //print_r($filepath); exit;
                    if (!empty($filepath)) {
                        $doc_path = $filepath;
                    }
                    $datam['user_master_id'] = $single_result->participant_id;
                    $datam['certificate_title'] = "Certificate For Attending " . $single_result->session_topic . "";
                    $datam['file'] = $doc_path;
                    $datam['certificate_number'] = $unique_id;
                    $datam['certificate_issue_date'] = date('Y-m-d H:i:s', time());
                    $datam['type'] = $getcpdstatus;
                    $datam['type_id'] = $single_result->session_id;
                    $datam['status'] = 3;
                    // print_r('hello'); exit;
                    $data_id = $this->Knwlgmastersessionnew_model->insert_user_specific_certificate($datam);
                    return $data_id;
                }
            }
        }
    }
}
