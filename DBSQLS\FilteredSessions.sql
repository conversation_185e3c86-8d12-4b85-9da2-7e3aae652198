WITH FilteredSessions AS (
    SELECT
    ks.session_id
    FROM
    knwlg_sessions_V1 AS ks
    LEFT JOIN
    content_to_env AS cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
    LEFT JOIN
    session_to_specialities AS sTs ON sTs.session_id = ks.session_id

    LEFT JOIN
    knwlg_sessions_participant sp  ON sp.knwlg_sessions_id = ks.session_id

   
    WHERE
    ks.status = 3
    AND (ks.session_status=1 OR ks.session_status=4)
    AND sp.participant_id != 879
    AND ks.parent_id = 0
    AND ks.privacy_status = 0
    AND ks.end_datetime >= '2025-05-16 00:36:34'
    AND ks.start_datetime >= '2025-05-16 00:36:34'
    AND (cTenv.env = 2 OR cTenv.env = 1)
    GROUP BY ks.session_id

    LIMIT 0, 5

),
SpecialitiesAgg AS (


        SELECT
        sTs.session_id,
        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
        FROM session_to_specialities sTs
        LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = sTs.specialities_id
        GROUP BY sTs.session_id

),
SponsorsAgg AS (
        SELECT
        fs.session_id,
        GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
        FROM
        FilteredSessions fs
        JOIN
        session_to_sponsor sTspon ON sTspon.session_id = fs.session_id
        JOIN
        client_master clintspon ON clintspon.client_master_id = sTspon.sponsor_id
        GROUP BY
        fs.session_id
),
DoctorsAgg AS (
        -- SELECT
        -- fs.session_id,
        -- GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
        -- GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
        -- GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') AS speciality,
        -- GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
        -- GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images
        -- FROM
        -- FilteredSessions fs
        -- JOIN
        -- knwlg_sessions_V1 ks ON ks.session_id = fs.session_id
        -- LEFT JOIN
        -- knwlg_sessions_doctors sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
        -- GROUP BY
        -- fs.session_id
        SELECT
        fs.session_id,
        GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
        GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
        GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') AS speciality,
        GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
        GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images
        FROM
        FilteredSessions fs
        JOIN
        session_to_sessiondoctor s2sd ON s2sd.session_id = fs.session_id
        JOIN
        knwlg_sessions_doctors sdoc ON sdoc.sessions_doctors_id = s2sd.sessions_doctors_id
        GROUP BY
        fs.session_id
)

SELECT
        -- ksp.participant_id,
        ks.*,

        cln.client_name,
        cln.client_logo,
        msct.category_name,
        msct.category_logo,
        cTenv.price,
        uTpyCont.status AS user_content_payment_status,

        kv.status AS vault,
        stci.cover_image1,
        stci.cover_image2,
        stci.cover_image3,
        stci.cover_image4,
        stci.cover_image5,
        (ks.total_buffer + ks.total_seats) AS tot_seat,
        spec.specialities_name,
        spec.specialities_ids_and_names,
        spon.sponsor,
        spon.sponsor_logo,
        doc.session_doctor_id,
        doc.doctor_name,
        doc.speciality,
        doc.profile,
        doc.profile_images
-- part.PartName,
-- part.users,
-- part.IS_ATTENDED
FROM
FilteredSessions fs
JOIN
knwlg_sessions_V1 ks ON ks.session_id = fs.session_id
LEFT JOIN
client_master cln ON cln.client_master_id = ks.client_id
LEFT JOIN
session_to_cover_image stci ON stci.session_id = ks.session_id
LEFT JOIN
knwlg_vault kv ON kv.type_text = 'session' AND kv.post_id = ks.session_id
LEFT JOIN
content_to_env cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
LEFT JOIN
payment_user_to_content uTpyCont ON uTpyCont.type_id = ks.session_id AND uTpyCont.type = 2 AND uTpyCont.user_master_id =
879
LEFT JOIN
master_session_category msct ON msct.mastersession_category_id = ks.category_id

LEFT JOIN
SpecialitiesAgg spec ON spec.session_id = ks.session_id
LEFT JOIN
SponsorsAgg spon ON spon.session_id = ks.session_id
LEFT JOIN
DoctorsAgg doc ON doc.session_id = ks.session_id


