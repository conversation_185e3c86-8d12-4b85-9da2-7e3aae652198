<?php

defined('BASEPATH') or exit('No direct script access allowed');
require dirname(__FILE__) . '/../libraries/NEW_REST_Controller.php';
///require  'vendor/autoload.php';
//use \Firebase\JWT\JWT;
class Epub extends MY_REST_Controller
{
    private $token_data;
    private $logdata = array();
    public function __construct()
    {
        parent::__construct();
        $token = $this->input->get_request_header('Authorization');
        //this->set_response($all_data, REST_Controller::HTTP_OK); //This is the respon if success
        //Validate user
        $this->validate_token($token);
        $this->token_data = $this->get_token_data($token);
        //print_r($token);
        $this->load->library('form_validation');
        $this->load->model('Epub_model');
        $this->load->helper('image');

        /**
         * For api detail table
         */
        $this->logdata['called_on'] = date('Y-m-d H:i:s', time());
        $this->logdata['process_start_time'] = date('Y-m-d H:i:s');
        $this->logdata['version'] = $this->input->get_request_header('version');
        /**
         * For api detail table
         */
        //
    }
    public function listing_get()
    {
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;


        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $val = $this->input->get('val'); // for search value.
        $type_id = $this->input->get('type_id'); // for search value.
        $spIds = $this->input->get('spIds'); // for search value.
        $type = $this->input->get('type'); // for search value.

        $from_date = $this->input->get('from_date'); // for search value.
        $to_date = $this->input->get('to_date'); // for search value.
        $convert = $this->input->get('convert');
        $featured = ($this->input->get('featured')) ? $this->input->get('featured') : 0;
        $client = ($this->input->get('client')) ? $this->input->get('client') : 0;
        //print_r($featured); exit;
        $all_data = $this->Epub_model->all_epub(
            $user_master_id,
            $client_ids,
            $limitFrom,
            $limitTo,
            $val,
            $spIds,
            $type,
            $from_date,
            $to_date,
            $type_id,
            $convert,
            $featured
        );

        /*if($user_master_id == 4){

            $all_data = $this->Knwlg_model->all_compendium2($user_master_id,$client_ids,$group_ids,$limitFrom,$limitTo,
                $val,$spIds,$type,$from_date,$to_date);

        }else{

            $all_data = $this->Knwlg_model->all_compendium2($user_master_id,$client_ids,$group_ids,$limitFrom,$limitTo,
                $val,$spIds,$type,$from_date,$to_date);
        }*/


        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    public function categories_get()
    {

        $type = $this->input->get("type") ? $this->input->get("type") : "";
        if ($type == "community") {
            $this->load->model('Discover_model');
            $all_data = $this->Discover_model->communitycategory();
        } else {
            $all_data = array(
                array(
                        "category_id" => 1,
                        "specialities_name" => "Popular",
                        "status" => 1
                ),
                array(
                    "category_id" => 2,
                    "specialities_name" => "Recent",
                    "status" => 1
                ),
                array(
                    "category_id" => 3,
                    "specialities_name" => "Trending",
                    "status" => 1
                ),
                array(
                    "category_id" => 4,
                    "specialities_name" => "Top Rating",
                    "status" => 1
                ),
                array(
                    "category_id" => 5,
                    "specialities_name" => "Featured",
                    "status" => 1
                )
            );
        }


        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

    }

    /**
     *
     */
    public function userChannel_get()
    {
        $user_master_id = $this->token_data->userdetail->user_master_id;
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $client_ids = $this->token_data->userdetail->client_ids;
        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');

        $all_data = $this->Channel_model->all_userChannel($user_master_id, $client_ids, $limitTo, $limitFrom);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    /**
     *
     */
    public function suggestionList_get()
    {
        $user_master_id = $this->token_data->userdetail->user_master_id;
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $client_ids = $this->token_data->userdetail->client_ids;
        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $type_id = $this->input->get('type_id');

        $all_data = $this->Channel_model->all_channelSuggestion($user_master_id, $client_ids, $limitTo, $limitFrom, $type_id);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    /**
     *
     */
    public function popular_get()
    {
        $user_master_id = $this->token_data->userdetail->user_master_id;
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $client_ids = $this->token_data->userdetail->client_ids;
        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');

        $all_data = $this->Channel_model->all_popular($user_master_id, $client_ids, $limitTo, $limitFrom);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    /**
     *
     */
    public function detail_get()
    {
        $type_id = $this->input->get('type_id');
        $from_type = $this->input->get('from');
        $user_master_id = $this->token_data->userdetail->user_master_id;
        //print_r($user_master_id); exit;
        $all_data = $this->Epub_model->detail($type_id, $user_master_id, $from_type);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;

        if (!empty($all_data)) {

            increment_user_content_view($user_master_id, 'epub');

            $this->logdata['response'] = REST_Controller::HTTP_OK;
        } else {
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
        }
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, $this->logdata['response'], $this->logdata); //This is the respon if success
    }

    public function mobileview_get()
    {

        $epub_id = $this->input->get('id', true);
        $from = $this->input->get('from', true);
        $to = $this->input->get('to', true);

        if (($epub_id != '') && ($from != '') && ($to != '')) {
            //echo "here"; exit;
            $all_data = $this->Epub_model->getmobileview($epub_id, $from, $to);
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

        } else {
            // error
            $message = 'Failed';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }


    public function mobileviewcount_get()
    {

        $epub_id = $this->input->get('id', true);


        if ($epub_id != '') {
            //echo "here"; exit;
            $all_data = $this->Epub_model->getmobileviewcount($epub_id);
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

        } else {
            // error
            $message = 'Failed';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }
}
