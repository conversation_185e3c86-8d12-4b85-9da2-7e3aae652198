<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Training_model extends CI_Model
{
    /**
     * @param $user_master_id
     * @param $client_ids
     * @param $limitTo
     * @param $limitFrom
     * @return array
     *
     */
    public function __construct()
    {
        $ci = get_instance();
        $ci->load->library('Myredis');
        $ci->load->helper('image');
        $ci->load->helper('utility');

    }

    public function get_profile_speciality($user_id)
    {
        $sql = "SELECT 
        usp.specialities_id as master_specialities_id,
        ms.specialities_name 
        FROM  user_to_specialities usp 
        left join master_specialities_V1 ms on ms.master_specialities_id = usp.specialities_id
        WHERE usp.user_master_id=? ";
        $query = $this->db->query($sql, array($user_id));
        return $query->result_array();
    }
    public function review_fetch($id, $user_master_id)
    {
        $all_user_response = array();
        $sql = "SELECT tr.rating, tr.review, tr.user_id, ud.first_name, ud.middle_name, ud.last_name 
                FROM training_review as tr 
                LEFT JOIN user_detail as ud ON  tr.user_id = ud.user_master_id 
                WHERE tr.training_id = " . $id . " and tr.status = 3 and tr.user_id != " . $user_master_id . " order by tr.rating desc";
        $query = $this->db->query($sql);
        if ($query->num_rows() > 0) {
            $all_user_response = $query->result_array();
            // echo '<pre>'; print_r($all_user_response); exit;
            foreach ($query->result() as $key => $value) {
                $all_user_response[$key]['specialities'] =  $this->get_profile_speciality($value->user_id);
            }
        }
        $single_user_response = array();
        $sql = "SELECT tr.rating, tr.review, tr.user_id, ud.first_name, ud.middle_name, ud.last_name 
                FROM training_review as tr 
                LEFT JOIN user_detail as ud ON  tr.user_id = ud.user_master_id 
                WHERE tr.training_id = " . $id . " and tr.status != 2 and tr.user_id = " . $user_master_id . " order by tr.rating desc";
        $query = $this->db->query($sql);
        if ($query->num_rows() > 0) {
            $single_user_response = $query->result_array();
            foreach ($query->result() as $key => $value) {
                $single_user_response[$key]['specialities'] =  $this->get_profile_speciality($value->user_id);
            }
        }
        return array_merge($single_user_response, $all_user_response);
    }
    public function review_insert($array, $table)
    {
        $this->insertdb = $this->load->database('insert', true);
        $this->insertdb->insert($table, $array);
        return true;
    }
    public function getSpecific_data($tm_id)
    {
        $response = array();
        $this->db->select("`id`, `url`, `max_participants`, `start_date`, `status`, (SELECT COUNT(user_master_id) FROM `payment_user_to_content` WHERE `type_id` = tm.id) AS `active_users`");
        $this->db->from('training_master AS tm');
        $this->db->where('tm.id', $tm_id);
        $query = $this->db->get();
        if (($query) && ($query->num_rows())) {
            $res = $query->result();
        } else {
            $res = array();
        }
        $currentdatetime = date('Y-m-d H:i:s');
        foreach ($res as $key => $val) {
            if (isset($val->start_date) && $currentdatetime < $val->start_date && $val->max_participants > 0) {
                if ($val->max_participants > $val->active_users) {
                    $is_registrable = true;
                } else {
                    $is_registrable = false;
                }
            }
            // elseif(isset($val->start_date) && $currentdatetime > $val->start_date){
            //     $is_registrable = false;
            // }
            elseif (!isset($val->start_date) && $val->max_participants == 0) {
                $is_registrable = true;
            } elseif (!isset($val->start_date) && $val->max_participants > 0) {
                $is_registrable = false;
            } else {
                $is_registrable = true;
            }
            $response = array(
                "id" => $val->id,
                "max_participants" => $val->max_participants,
                "start_date" => $val->start_date,
                "status" => $val->status,
                "active_users" => $val->active_users,
                "url" => $val->url,
                "is_registrable" => $is_registrable
            );
        }
        return $response;
    }
    public function list(
        $type,
        $user_master_id,
        $limitTo,
        $limitFrom,
        $spids
    ) {
        #$this->myredis->delall();
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $env_arr = array(2, $env);
            } else {
                $env_arr = array($env);
            }
        } else {
            $env_arr = array();
        }
        $key_locked = get_user_package($user_master_id, 'training');
        $response = array();
        $id = array();
        $ignore_qry_flag = false;
        if ($type == "completed" || $type == "incompleted") {
            $ignore = $this->performancestatus($user_master_id, '');
            //print_R($ignore); exit;
            if ($type == "completed") {
                $should_fetch_ids = $ignore['id'];
                $ignore_qry_flag = true;
            } elseif ($type == "incompleted") {
                $should_fetch_ids = $ignore['id1'];
                $ignore_qry_flag = true;
            }
        }
        $spids_imp = implode("_", (array)$spids);
        // $cachename = "training_listing_".$user_master_id."_".$limitFrom."_".$limitTo."_".$spids_imp."_".$env."_".$type;
        $response =  array();
        /** complete checking as per user data */
        $datastatus = true;
        //echo $user_master_id; exit;
        //print_R($ignore);
        //echo $user_master_id."->";
        if (@count((array)$ignore) === 0) {
            //echo 1;
            if ($type == "completed" || $type == "incompleted") {
                $datastatus = false;
            } elseif ($type == "incompleted") {
                $datastatus = false;
            }
        } else {
            //echo 2;
            if ($type == "completed") {
                if ((isset($ignore['complete'])) && ($ignore['complete'] === 0)) {
                    $datastatus = false;
                    // echo "dd";
                } elseif (!(isset($ignore['complete']))) {
                    $datastatus = false;
                    //echo "dssx";
                }
            } elseif ($type == "incompleted") {
                if ((isset($ignore['incomplete'])) && ($ignore['incomplete']) === 0) {
                    $datastatus = false;
                } elseif (!(isset($ignore['incomplete']))) {
                    $datastatus = false;
                }
            }
        }
        //exit;
        /** complete checking as per user data */
        //print_r($ignore); exit;
        if ($datastatus == false) {
            //echo 1; exit;
            $res = array();
        } else {
            //echo 2; exit;
            $this->db->select("
                            tm.id,
                            tm.title,
                            tm.description,
                            tm.preview_image,
                            tm.client_id,
                            tm.channel_id,
                            tm.template_id,
                            tm.color,
                            tm.display_in_dashboard,
                            tm.featured_video,
                            tm.deeplink,
                            tm.in_deeplink,
                            tm.gl_deeplink,
                            tm.start_like,
                            tm.start_date,
                            tm.url,
                            tm.max_participants,
                            tm.added_on,
                            tm.added_by,
                            tm.modified_on,
                            tm.modified_by,
                            tm.status,
                            tm.cert_template_id,
                            tm.duration,
                            tm.privacy_status,
                            
                            tm.published_date,
                            tm.env,
                            tm.is_share,
                            tm.is_like,
                            tm.is_comment,
                            tm.enable_maxparticipants,
                            tm.allow_postStart,
            cTenv.price,
            uTpyCont.status as user_contnet_payment_status,
            GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
            GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id)) AS master_spec_id,
            GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
            MAX(ms.rank) AS maxrank,
            GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
            GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) AS session_doctor_id,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
            clintspon.client_name,
            clintspon.client_logo,
            (SELECT COUNT(user_master_id) FROM `payment_user_to_content` WHERE `type_id` = tm.id) AS `active_users`,
            COUNT(rt.rating) AS averageRating,
            COUNT(DISTINCT training_module.id) AS count_module
        ");
            $this->db->from('training_master as tm');
            $this->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
            $this->db->join('client_master as clintspon', 'ts.sponsor_id = clintspon.client_master_id', 'left');
            $this->db->join('training_to_speciality tts', 'tts.training_id = tm.id', 'left');
            $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
            $this->db->join('content_to_env as cTenv', 'cTenv.type_id = tm.id and cTenv.type = 4', 'left');
            $this->db->join('payment_user_to_content as uTpyCont', 'uTpyCont.type_id = tm.id and  uTpyCont.type = 4 and uTpyCont.user_master_id = ' . $user_master_id, 'left');
            $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
            $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id AND rt.post_type='training'", 'left');
            $this->db->join('training_module', 'training_module.training_id = tm.id AND training_module.status = 3', 'left');
            // $this->db->join('training_module_content', 'training_module_content.training_id = tm.id AND training_module_content.status = 3', 'left');
            if (($spids != '') && ($spids != 0) && !empty($spids)) {
                $this->db->where_in('tts.specialities_id', $spids);
            }
            if ($type == "featured") {
                $this->db->where('tm.display_in_dashboard', 1);
            }
            $this->db->where('tm.status', 3);
            $this->db->where('date(tm.published_date) <=', date('Y-m-d'));
            $this->db->where('tm.privacy_status', 0);
            $this->db->where_in('cTenv.env', $env_arr);
            if ($ignore_qry_flag) {
                $this->db->where_in('tm.id', $should_fetch_ids);
            }
            $this->db->group_by([
                'tm.id',
                'tm.title',
                'tm.description',
                'tm.preview_image',
                'tm.client_id',
                'tm.channel_id',
                'tm.template_id',
                'tm.color',
                'tm.display_in_dashboard',
                'tm.featured_video',
                'tm.deeplink',
                'tm.in_deeplink',
                'tm.gl_deeplink',
                'tm.start_like',
                'tm.start_date',
                'tm.url',
                'tm.max_participants',
                'tm.added_on',
                'tm.added_by',
                'tm.modified_on',
                'tm.modified_by',
                'tm.status',
                'tm.cert_template_id',
                'tm.duration',
                'tm.privacy_status',

                'tm.published_date',
                'tm.env',
                'tm.is_share',
                'tm.is_like',
                'tm.is_comment',
                'tm.enable_maxparticipants',
                'tm.allow_postStart',
                'cTenv.price',
                'uTpyCont.status',
                'clintspon.client_name',
                'clintspon.client_logo'
            ]);
            $this->db->order_by('tm.published_date', 'desc');
            if (($limitTo != '') || ($limitFrom != '')) {
                $this->db->limit($limitTo, $limitFrom);
            }
            $query = $this->db->get();
            if (($query) && ($query->num_rows())) {
                $res = $query->result();
            } else {
                $res = array();
            }
        }
        $i = 1;
        //echo "<pre>";print_r( $query );die;
        foreach ($res as $key => $val) {
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsor);
            $sp = 0;
            if (@count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] =  change_img_src($valueSponor);
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        $sp++;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                    $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($ignore_qry_flag == false) {
                $completestatus = $this->complete_status_enchanced($val->id, $user_master_id);
            } elseif ($type == "completed") {
                $completestatus = (!empty($ignore["val_cmp_stts"])) ? $ignore["val_cmp_stts"][$val->id][0] : 0;
            } elseif ($type == "incompleted") {
                $completestatus = (!empty($ignore["val_in_stts"])) ? $ignore["val_in_stts"][$val->id][0] : 0;
            }
            $currentdatetime = date('Y-m-d H:i:s');
            if (isset($val->start_date) && $currentdatetime < $val->start_date && $val->max_participants > 0) {
                if ($val->max_participants > $val->active_users) {
                    $is_registrable = true;
                } else {
                    $is_registrable = false;
                }
            } elseif (isset($val->start_date) && $currentdatetime > $val->start_date) {
                $is_registrable = false;
            } elseif (!isset($val->start_date) && $val->max_participants == 0) {
                $is_registrable = true;
            } elseif (!isset($val->start_date) && $val->max_participants > 0) {
                $is_registrable = false;
            } else {
                $is_registrable = true;
            }
            $temp = array(
                "slno" => $i,
                "id" => $val->id,
                "url" => $val->url,
                "type" => "training",
                "is_share" => $val->is_share,
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->preview_image),
                "featured_video" => $val->featured_video,
                "max_participants" => $val->max_participants,
                "start_date" => $val->start_date,
                "active_users" => $val->active_users,
                "is_registrable" => $is_registrable,
                "color" => ($val->color != '') ? $val->color : '#eb34e5',
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "module_Count" => $val->count_module, //$this->count_module($val->id),
                "live_session_status" => $this->livestatus($val->id),
                "specialities" => $this->explode_speciality_string($val->specialities_ids_and_names, 0), //$this->specialityname($val->master_spec_id), #($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                // "client_name" => $val->client_name,
                // "client_logo" => change_img_src($val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "all_sponsor" => $allsponsor,
                "duration" => $val->duration,
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->id, 4, $user_master_id),
                //============ integrated for subscription ============//
                "is_completed" => ($completestatus >= 100) ? 100 : $completestatus, #$this->complete_status($val->id,$user_master_id),
                "is_certificate" => ($val->cert_template_id != '') ? true : false,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            $vx[] = $temp;
            $i++;
        }
        // return $temp;
        return $vx;
    }
    public function list_bk_before_enhanced($type, $user_master_id, $limitTo, $limitFrom, $spids)
    {
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                $env_arr = array(2, $env);
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
                $env_arr = array($env);
            }
        } else {
            $envStatus = "";
            $env_arr = array();
        }
        $key_locked = get_user_package($user_master_id, 'survey');
        // echo "<pre>";print_r($key_locked );die;
        $response = array();
        $id = array();
        $ignore = $this->performancestatus($user_master_id, '');
        $id = $ignore['id'];
        // $this->db->save_queries = TRUE;
        // $this->db->select("tm.*,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id) )as master_spec_id,
        // GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        // max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        // GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,(select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = tm.id and  rt.post_type='training')as averageRating,,(select count(id) from training_module where training_module.training_id=tm.id and training_module.status=3) as count_module,,(select count(id) from training_module_content where training_module_content.type='comp' and status=3 and training_module_content.training_id=tm.id) as total_medwiki,(select count(id) from training_module_content where training_module_content.type='session' and status=3 and training_module_content.training_id=tm.id) as total_session,(select count(id) from training_module_content where training_module_content.type='survey' and status=3 and training_module_content.training_id=tm.id) as total_survey,(select count(id) from training_module_content where training_module_content.type='live_video' and status=3 and training_module_content.training_id=tm.id) as total_live_training,(select count(id) from training_module_content where training_module_content.type='clinical_video' and status=3 and training_module_content.training_id=tm.id) as total_clinical_video");
        // $this->db->from('training_master as tm');
        // $this->db->join('training_to_sponsor as ts','tm.id = ts.training_id','left');
        // $this->db->join('client_master as clintspon','ts.sponsor_id=clintspon.client_master_id','left');
        // $this->db->join('training_to_speciality tts','tts.training_id=tm.id','left');
        // $this->db->join('master_specialities_V1 as ms','ms.master_specialities_id = tts.specialities_id','left');
        // $this->db->join('training_to_session_doctor as Tdoc','Tdoc.training_id = tm.id','left');
        // $this->db->join('knwlg_rating as rt',"rt.post_id = tm.id and  rt.post_type='training'","left");
        // //print_r($spids); exit;
        // if($spids != ''){
        //     $this->db->where_in('tts.specialities_id', $spids);
        // }
        // $this->db->where('tm.status',3);
        // if($type=="featured"){
        //     # $this->db->where_not_in('tm.id',$id);
        //     $this->db->where('tm.display_in_dashboard',1);
        // }else if($type == ''){
        //     # $this->db->where_not_in('tm.id',$id);
        // }
        // $this->db->where('date(tm.published_date)<=',date('Y-m-d'));
        // $this->db->where('tm.privacy_status',0);
        // $this->db->group_by('tm.id');
        // $this->db->order_by('tm.published_date','desc');
        // if(($limitTo != '')||($limitFrom != '')){
        //     $this->db->limit($limitTo,$limitFrom);
        // }
        //Bhanjo Code Start
        $spids_imp = implode("_", (array)$spids);
        $cachename = "training_listing_" . $user_master_id . "_" . $limitFrom . "_" . $limitTo . "_" . $spids_imp . "_" . $env . "_" . $type;
        $response =  array();
        if ($this->myredis->exists($cachename)) {
            $res = $this->myredis->get($cachename);
            // print_r($res); exit;
        } else {
            // echo "hey";
            $this->db->select("
            tm.*,
            cTenv.price,
            uTpyCont.status as user_contnet_payment_status,
            GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
            GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id)) AS master_spec_id,
            GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
            MAX(ms.rank) AS maxrank,
            GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
            GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) AS session_doctor_id,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
            clintspon.client_name,
            clintspon.client_logo,
            (SELECT COUNT(user_master_id) FROM `payment_user_to_content` WHERE `type_id` = tm.id) AS `active_users`,
            COUNT(rt.rating) AS averageRating,
            COUNT(DISTINCT training_module.id) AS count_module,
            COUNT(DISTINCT CASE WHEN training_module_content.type = 'comp' THEN training_module_content.id END) AS total_medwiki,
            COUNT(DISTINCT CASE WHEN training_module_content.type = 'session' THEN training_module_content.id END) AS total_session,
            COUNT(DISTINCT CASE WHEN training_module_content.type = 'survey' THEN training_module_content.id END) AS total_survey,
            COUNT(DISTINCT CASE WHEN training_module_content.type = 'live_video' THEN training_module_content.id END) AS total_live_training,
            COUNT(DISTINCT CASE WHEN training_module_content.type = 'clinical_video' THEN training_module_content.id END) AS total_clinical_video
        ");
            $this->db->from('training_master as tm');
            $this->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
            $this->db->join('client_master as clintspon', 'ts.sponsor_id = clintspon.client_master_id', 'left');
            $this->db->join('training_to_speciality tts', 'tts.training_id = tm.id', 'left');
            $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
            $this->db->join('content_to_env as cTenv', 'cTenv.type_id = tm.id and cTenv.type = 4', 'left');
            $this->db->join('payment_user_to_content as uTpyCont', 'uTpyCont.type_id = tm.id and  uTpyCont.type = 4 and uTpyCont.user_master_id = ' . $user_master_id, 'left');
            $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
            $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id AND rt.post_type='training'", 'left');
            $this->db->join('training_module', 'training_module.training_id = tm.id AND training_module.status = 3', 'left');
            $this->db->join('training_module_content', 'training_module_content.training_id = tm.id AND training_module_content.status = 3', 'left');
            if ($spids != '') {
                $this->db->where_in('tts.specialities_id', $spids);
            }
            $this->db->where('tm.status', 3);
            $this->db->where('date(tm.published_date) <=', date('Y-m-d'));
            $this->db->where('tm.privacy_status', 0);
            $this->db->where_in('cTenv.env', $env_arr);
            $this->db->group_by('tm.id');
            $this->db->order_by('tm.published_date', 'desc');
            if (($limitTo != '') || ($limitFrom != '')) {
                $this->db->limit($limitTo, $limitFrom);
            }
            $query = $this->db->get();
            if (($query) && ($query->num_rows())) {
                $res = $query->result();
                //print_r($res); exit;
                $this->myredis->set($cachename, $res);
            } else {
                $res = array();
            }
        }
        //Bhanjo Code End
        // $this->db->save_queries = TRUE;
        // $str = $this->db->last_query();
        // echo $str;
        //  die;
        $i = 1;
        // if (($query)&&($query->num_rows())) {
        foreach ($res as $key => $val) {
            $content_count['comp'] = $val->total_medwiki;
            $content_count['session'] = $val->total_session;
            $content_count['survey'] = $val->total_survey;
            $content_count['video_archieve'] = $val->total_clinical_video;
            $content_count['live_training'] = $val->total_live_training;
            //print_r($content_count); exit;
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (@count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] =  change_img_src($valueSponor);
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            // session doctor entitues are not in working from frontend
            // $ses_doc_det_array = array();
            // if($val->session_doctor_id){
            //     $session_doc_array = explode(",", $val->session_doctor_id);
            //     $ses_doc_det_array = array();
            //     $inc_pp = 0;
            //     foreach ($session_doc_array as $single_doctor) {
            //         $var = session_doc_detail($single_doctor);
            //         $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
            //         if ( stripos( $var[0]['profile_image'] , "https://storage.googleapis.com") > -1 ) {
            //             $logic_image = $var[0]['profile_image'];
            //         }else{
            //             $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
            //             $logic_image_path = "uploads/docimg/" . $image;
            //             $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
            //             $logic_image = $imgPr;
            //             //$logic_image = $var[0]['profile_image'];
            //         }
            //         $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
            //         $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
            //         $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
            //         $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
            //         $inc_pp++;
            //     }
            // }
            // echo "<pre>";print_r("training id => ".$val->id."\n");
            // ============== session doc ent end ====================//
            // $completestatus = $this->complete_status($val->id,$user_master_id);
            $completestatus = $this->complete_status_enchanced($val->id, $user_master_id);
            $currentdatetime = date('Y-m-d H:i:s');
            if (isset($val->start_date) && $currentdatetime < $val->start_date && $val->max_participants > 0) {
                if ($val->max_participants > $val->active_users) {
                    $is_registrable = true;
                } else {
                    $is_registrable = false;
                }
            } elseif (isset($val->start_date) && $currentdatetime > $val->start_date) {
                $is_registrable = false;
            } elseif (!isset($val->start_date) && $val->max_participants == 0) {
                $is_registrable = true;
            } elseif (!isset($val->start_date) && $val->max_participants > 0) {
                $is_registrable = false;
            } else {
                $is_registrable = true;
            }
            $temp = array(
                "slno" => $i,
                "id" => $val->id,
                "url" => $val->url,
                "is_share" => $val->is_share,
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->preview_image),
                "featured_video" => $val->featured_video,
                "max_participants" => $val->max_participants,
                "start_date" => $val->start_date,
                "active_users" => $val->active_users,
                "is_registrable" => $is_registrable,
                "color" => ($val->color != '') ? $val->color : '#eb34e5',
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "module_Count" => $val->count_module, //$this->count_module($val->id),
                "live_session_status" => $this->livestatus($val->id),
                "training_module_content" => $content_count, //$this->content_count($val->id),
                "specialities" => $this->explode_speciality_string($val->specialities_ids_and_names, 0), //$this->specialityname($val->master_spec_id), #($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src($val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                // "session_doctor_entities" => $ses_doc_det_array,
                "duration" => $val->duration,
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->id, 4, $user_master_id),
                //============ integrated for subscription ============//
                "is_completed" => $completestatus, #$this->complete_status($val->id,$user_master_id),
                "is_certificate" => ($val->cert_template_id != '') ? true : false,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            switch ($type) {
                case "completed":
                    if ($completestatus >= 100) {
                        $vx[] = $temp;
                    }
                    break;
                case "incompleted":
                    if (($completestatus > 0) && ($completestatus < 100)) {
                        $vx[] = $temp;
                    }
                    break;
                default:
                    $vx[] = $temp;
                    break;
            }
            $i++;
        }
        // }
        # die;
        return $vx;
    }
    public function list_paid(
        $type,
        $user_master_id,
        $limitTo,
        $limitFrom,
        $spids
    ) {
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                $env_arr = array(2, $env);
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
                $env_arr = array($env);
            }
        } else {
            $envStatus = "";
            $env_arr = array();
        }
        //$key_locked = get_user_package($user_master_id, 'survey');
        //$response = array();
        //$id = array();
        //$ignore = $this->performancestatus($user_master_id, '');
        //$id = $ignore['id'];
        // $this->db->save_queries = TRUE;
        // $this->db->select("tm.*,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id) )as master_spec_id,
        // GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        // max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        // GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,(select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = tm.id and  rt.post_type='training')as averageRating,,(select count(id) from training_module where training_module.training_id=tm.id and training_module.status=3) as count_module,,(select count(id) from training_module_content where training_module_content.type='comp' and status=3 and training_module_content.training_id=tm.id) as total_medwiki,(select count(id) from training_module_content where training_module_content.type='session' and status=3 and training_module_content.training_id=tm.id) as total_session,(select count(id) from training_module_content where training_module_content.type='survey' and status=3 and training_module_content.training_id=tm.id) as total_survey,(select count(id) from training_module_content where training_module_content.type='live_video' and status=3 and training_module_content.training_id=tm.id) as total_live_training,(select count(id) from training_module_content where training_module_content.type='clinical_video' and status=3 and training_module_content.training_id=tm.id) as total_clinical_video");
        // $this->db->from('training_master as tm');
        // $this->db->join('training_to_sponsor as ts','tm.id = ts.training_id','left');
        // $this->db->join('client_master as clintspon','ts.sponsor_id=clintspon.client_master_id','left');
        // $this->db->join('training_to_speciality tts','tts.training_id=tm.id','left');
        // $this->db->join('master_specialities_V1 as ms','ms.master_specialities_id = tts.specialities_id','left');
        // $this->db->join('training_to_session_doctor as Tdoc','Tdoc.training_id = tm.id','left');
        // $this->db->join('knwlg_rating as rt',"rt.post_id = tm.id and  rt.post_type='training'","left");
        // //print_r($spids); exit;
        // if($spids != ''){
        //     $this->db->where_in('tts.specialities_id', $spids);
        // }
        // $this->db->where('tm.status',3);
        // if($type=="featured"){
        //     # $this->db->where_not_in('tm.id',$id);
        //     $this->db->where('tm.display_in_dashboard',1);
        // }else if($type == ''){
        //     # $this->db->where_not_in('tm.id',$id);
        // }
        // $this->db->where('date(tm.published_date)<=',date('Y-m-d'));
        // $this->db->where('tm.privacy_status',0);
        // $this->db->group_by('tm.id');
        // $this->db->order_by('tm.published_date','desc');
        // if(($limitTo != '')||($limitFrom != '')){
        //     $this->db->limit($limitTo,$limitFrom);
        // }
        //Bhanjo Code Start
        $spids_imp = implode("_", (array)$spids);
        $cachename = "training_paidlisting_" . $user_master_id . "_" . $limitFrom . "_" . $limitTo . "_" . $spids_imp . "_" . $env . "_" . $type;
        $response =  array();
        // if($this->myredis->exists($cachename)){
        //     $res = $this->myredis->get($cachename);
        //    // print_r($res); exit;
        // }else{
        // echo "hey";


        // $this->db->select("
        //     tm.id,
        //     tm.title,
        //     tm.description,
        //     tm.preview_image,
        //     tm.client_id,
        //     tm.channel_id,
        //     tm.template_id,
        //     tm.color,
        //     tm.display_in_dashboard,
        //     tm.featured_video,
        //     tm.deeplink,
        //     tm.in_deeplink,
        //     tm.gl_deeplink,
        //     tm.start_like,
        //     tm.start_date,
        //     tm.url,
        //     tm.max_participants,
        //     tm.added_on,
        //     tm.added_by,
        //     tm.modified_on,
        //     tm.modified_by,
        //     tm.status,
        //     tm.cert_template_id,
        //     tm.duration,
        //     tm.privacy_status,

        //     tm.published_date,
        //     tm.env,
        //     tm.is_share,
        //     tm.is_like,
        //     tm.is_comment,
        //     tm.enable_maxparticipants,
        //     tm.allow_postStart,
        //     cTenv.price,
        //     uTpyCont.status as user_contnet_payment_status,
        //     GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
        //     GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id)) AS master_spec_id,
        //     GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
        //     MAX(ms.rank) AS maxrank,
        //     GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
        //     GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) AS session_doctor_id,
        //     GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
        //     clintspon.client_name,
        //     clintspon.client_logo,
        //     (SELECT COUNT(user_master_id) FROM `payment_user_to_content` WHERE `type_id` = tm.id) AS `active_users`,
        //     COUNT(rt.rating) AS averageRating,
        //     COUNT(DISTINCT training_module.id) AS count_module,
        //     COUNT(DISTINCT CASE WHEN training_module_content.type = 'comp' THEN training_module_content.id END) AS total_medwiki,
        //     COUNT(DISTINCT CASE WHEN training_module_content.type = 'session' THEN training_module_content.id END) AS total_session,
        //     COUNT(DISTINCT CASE WHEN training_module_content.type = 'survey' THEN training_module_content.id END) AS total_survey,
        //     COUNT(DISTINCT CASE WHEN training_module_content.type = 'live_video' THEN training_module_content.id END) AS total_live_training,
        //     COUNT(DISTINCT CASE WHEN training_module_content.type = 'clinical_video' THEN training_module_content.id END) AS total_clinical_video
        // ");
        // $this->db->from('training_master as tm');
        // $this->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
        // $this->db->join('client_master as clintspon', 'ts.sponsor_id = clintspon.client_master_id', 'left');
        // $this->db->join('training_to_speciality tts', 'tts.training_id = tm.id', 'left');
        // $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
        // $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
        // $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id AND rt.post_type='training'", 'left');
        // $this->db->join('content_to_env as cTenv', 'cTenv.type_id = tm.id and cTenv.type = 4', 'left');
        // $this->db->join('payment_user_to_content as uTpyCont', 'uTpyCont.type_id = tm.id and  uTpyCont.type = 4 and uTpyCont.user_master_id = ' . $user_master_id, 'left');
        // $this->db->join('training_module', 'training_module.training_id = tm.id AND training_module.status = 3', 'left');
        // $this->db->join('training_module_content', 'training_module_content.training_id = tm.id AND training_module_content.status = 3', 'left');
        // $this->db->join('order_master AS om', 'tm.id = om.type_id');
        // if ($spids != '' && !empty($spids)) {
        //     $this->db->where_in('tts.specialities_id', $spids);
        // }
        // $this->db->where('tm.status', 3);
        // $this->db->where('tm.published_date <=', date('Y-m-d'));
        // $this->db->where('tm.privacy_status', 0);
        // $this->db->where_in('cTenv.env', $env_arr);
        // $this->db->where('om.product_type', 2);
        // $this->db->where('om.user_master_id', $user_master_id);
        // $this->db->group_by([
        //     'tm.id',
        //     'tm.title',
        //     'tm.description',
        //     'tm.preview_image',
        //     'tm.client_id',
        //     'tm.channel_id',
        //     'tm.template_id',
        //     'tm.color',
        //     'tm.display_in_dashboard',
        //     'tm.featured_video',
        //     'tm.deeplink',
        //     'tm.in_deeplink',
        //     'tm.gl_deeplink',
        //     'tm.start_like',
        //     'tm.start_date',
        //     'tm.url',
        //     'tm.max_participants',
        //     'tm.added_on',
        //     'tm.added_by',
        //     'tm.modified_on',
        //     'tm.modified_by',
        //     'tm.status',
        //     'tm.cert_template_id',
        //     'tm.duration',
        //     'tm.privacy_status',

        //     'tm.published_date',
        //     'tm.env',
        //     'tm.is_share',
        //     'tm.is_like',
        //     'tm.is_comment',
        //     'tm.enable_maxparticipants',
        //     'tm.allow_postStart',
        //     'cTenv.price',
        //     'uTpyCont.status',
        //     'clintspon.client_name',
        //     'clintspon.client_logo'
        // ]);
        // $this->db->order_by('tm.published_date', 'desc');
        // if (($limitTo != '') || ($limitFrom != '')) {
        //     $this->db->limit($limitTo, $limitFrom);
        // }
        // $query = $this->db->get();
        $sqlSpeciality = "";
        if ($spids != '' && !empty($spids)) {
            $sqlSpeciality = "AND tts.specialities_id IN (" . implode(',', $spids) . ")";
        }

        if ($env != 2) {
            $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
        } else {
            $envStatus = "AND cTenv.env =" . $env . "";
        }





        //$currentdatetime = date('Y-m-d');

        $sql = "WITH trainingFilter AS (
                SELECT
                    tm.id,
                    tm.title,
                    tm.description,
                    tm.preview_image,
                    tm.client_id,
                    tm.channel_id,
                    tm.template_id,
                    tm.color,
                    tm.display_in_dashboard,
                    tm.featured_video,
                    tm.deeplink,
                    tm.in_deeplink,
                    tm.gl_deeplink,
                    tm.start_like,
                    tm.start_date,
                    tm.url,
                    tm.max_participants,
                    tm.added_on,
                    tm.added_by,
                    tm.modified_on,
                    tm.modified_by,
                    tm.status,
                    tm.cert_template_id,
                    tm.duration,
                    tm.privacy_status,
                    tm.published_date,
                    tm.env,
                    tm.is_share,
                    tm.is_like,
                    tm.is_comment,
                    cTenv.price,
                    tm.enable_maxparticipants,
                    tm.allow_postStart
                FROM 
                    training_master AS tm
                LEFT JOIN 
                content_to_env AS cTenv ON cTenv.type_id = tm.id AND cTenv.type = 4 

                JOIN 
                    order_master AS om ON tm.id = om.type_id
                join
                    training_to_speciality AS tts ON tts.training_id = tm.id

                WHERE 
                    tm.status = 3
                    -- AND tm.published_date <= {$currentdatetime}
                    -- AND tm.privacy_status = 0
                    AND om.product_type = 2
                    AND om.user_master_id = {$user_master_id}
                    {$sqlSpeciality}
                    {$envStatus}
                    GROUP by tm.id
                    ORDER BY tm.published_date DESC
                 LIMIT {$limitFrom}, {$limitTo}   
                    
            ),

            specialties_data AS (
                SELECT 
                    tts.training_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id)) AS master_spec_id,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                FROM 
                    training_to_speciality AS tts
                JOIN 
                    master_specialities_V1 AS ms ON ms.master_specialities_id = tts.specialities_id
               
                GROUP BY 
                    tts.training_id
            ),

            sponsor_data AS (
                SELECT 
                    ts.training_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
                    MAX(clintspon.client_name) AS client_name,
                    MAX(clintspon.client_logo) AS client_logo
                FROM 
                    training_to_sponsor AS ts
                LEFT JOIN 
                    client_master AS clintspon ON ts.sponsor_id = clintspon.client_master_id
                GROUP BY 
                    ts.training_id
            ),

            doctor_data AS (
                SELECT 
                    Tdoc.training_id,
                    GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) AS session_doctor_id
                FROM 
                    training_to_session_doctor AS Tdoc
                GROUP BY 
                    Tdoc.training_id
            ),

            rating_data AS (
                SELECT 
                    rt.post_id,
                    COUNT(rt.rating) AS averageRating
                FROM 
                    knwlg_rating AS rt
                WHERE 
                    rt.post_type = 'training'
                GROUP BY 
                    rt.post_id
            ),

            active_users_data AS (
                SELECT 
                    type_id,
                    COUNT(user_master_id) AS active_users
                FROM 
                    payment_user_to_content
                GROUP BY 
                    type_id
            ),

            module_counts AS (
                SELECT 
                    tm.training_id,
                    COUNT(DISTINCT tm.id) AS count_module
                FROM 
                    training_module AS tm
                WHERE 
                    tm.status = 3
                GROUP BY 
                    tm.training_id
            ),

            content_counts AS (
                SELECT 
                    tmc.training_id,
                    COUNT(DISTINCT CASE WHEN tmc.type = 'comp' THEN tmc.id END) AS total_medwiki,
                    COUNT(DISTINCT CASE WHEN tmc.type = 'session' THEN tmc.id END) AS total_session,
                    COUNT(DISTINCT CASE WHEN tmc.type = 'survey' THEN tmc.id END) AS total_survey,
                    COUNT(DISTINCT CASE WHEN tmc.type = 'live_video' THEN tmc.id END) AS total_live_training,
                    COUNT(DISTINCT CASE WHEN tmc.type = 'clinical_video' THEN tmc.id END) AS total_clinical_video
                FROM 
                    training_module_content AS tmc
                WHERE 
                    tmc.status = 3
                GROUP BY 
                    tmc.training_id
            )

            SELECT
                tb.*,
                tb.price,
                uTpyCont.status AS user_contnet_payment_status,
                sd.specialities_name,
                sd.master_spec_id,
                sd.specialities_ids_and_names,
                
                spd.sponsor,
                dd.session_doctor_id,
                spd.sponsor_logo,
                spd.client_name,
                spd.client_logo,
                COALESCE(aud.active_users, 0) AS active_users,
                COALESCE(rd.averageRating, 0) AS averageRating,
                COALESCE(mc.count_module, 0) AS count_module,
                COALESCE(cc.total_medwiki, 0) AS total_medwiki,
                COALESCE(cc.total_session, 0) AS total_session,
                COALESCE(cc.total_survey, 0) AS total_survey,
                COALESCE(cc.total_live_training, 0) AS total_live_training,
                COALESCE(cc.total_clinical_video, 0) AS total_clinical_video
            FROM 
                trainingFilter AS tb
            LEFT JOIN 
                specialties_data AS sd ON sd.training_id = tb.id
            LEFT JOIN 
                sponsor_data AS spd ON spd.training_id = tb.id
            LEFT JOIN 
                doctor_data AS dd ON dd.training_id = tb.id
            LEFT JOIN 
                rating_data AS rd ON rd.post_id = tb.id
            LEFT JOIN 
                active_users_data AS aud ON aud.type_id = tb.id
            LEFT JOIN 
                module_counts AS mc ON mc.training_id = tb.id
            LEFT JOIN 
                content_counts AS cc ON cc.training_id = tb.id
            
            LEFT JOIN 
                payment_user_to_content AS uTpyCont ON uTpyCont.type_id = tb.id AND uTpyCont.type = 4 AND uTpyCont.user_master_id = {$user_master_id} ";

        // if (($limitTo != '') || ($limitFrom != '')) {
        //     $sql .= " LIMIT " . $limitFrom . ", " . $limitTo;
        // }

        // echo $sql;
        // exit;

        $query = $this->db->query($sql);



        if (($query) && ($query->num_rows())) {
            $res = $query->result();
            //print_r($res); exit;
            $this->myredis->set($cachename, $res);
        } else {
            $res = array();
        }
        // }
        //Bhanjo Code End
        // $this->db->save_queries = TRUE;
        // $str = $this->db->last_query();
        // echo $str;
        //  die;
        $i = 1;
        // if (($query)&&($query->num_rows())) {
        foreach ($res as $key => $val) {
            $content_count['comp'] = $val->total_medwiki;
            $content_count['session'] = $val->total_session;
            $content_count['survey'] = $val->total_survey;
            $content_count['video_archieve'] = $val->total_clinical_video;
            $content_count['live_training'] = $val->total_live_training;
            //print_r($content_count); exit;
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (@count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] =  change_img_src($valueSponor);
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $ses_doc_det_array = array();
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {
                    $var = session_doc_detail($single_doctor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                        $logic_image = $var[0]['profile_image'];
                    } else {
                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        $logic_image_path = "uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                        //$logic_image = $var[0]['profile_image'];
                    }
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }
            $completestatus = $this->complete_status($val->id, $user_master_id);
            $currentdatetime = date('Y-m-d H:i:s');
            if (isset($val->start_date) && $currentdatetime < $val->start_date && $val->max_participants > 0) {
                if ($val->max_participants > $val->active_users) {
                    $is_registrable = true;
                } else {
                    $is_registrable = false;
                }
            } elseif (isset($val->start_date) && $currentdatetime > $val->start_date) {
                $is_registrable = false;
            } elseif (!isset($val->start_date) && $val->max_participants == 0) {
                $is_registrable = true;
            } elseif (!isset($val->start_date) && $val->max_participants > 0) {
                $is_registrable = false;
            } else {
                $is_registrable = true;
            }
            $temp = array(
                "slno" => $i,
                "id" => $val->id,
                "url" => $val->url,
                "type" => "training",
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->preview_image),
                "featured_video" => $val->featured_video,
                "max_participants" => $val->max_participants,
                "start_date" => $val->start_date,
                "active_users" => $val->active_users,
                "is_registrable" => $is_registrable,
                "color" => ($val->color != '') ? $val->color : '#eb34e5',
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "module_Count" => $val->count_module, //$this->count_module($val->id),
                "live_session_status" => $this->livestatus($val->id),
                "training_module_content" => $content_count, //$this->content_count($val->id),
                "specialities" => $this->explode_speciality_string($val->specialities_ids_and_names, 0), //$this->specialityname($val->master_spec_id), #($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src($val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "session_doctor_entities" => $ses_doc_det_array,
                "duration" => $val->duration,
                "is_completed" => $completestatus, #$this->complete_status($val->id,$user_master_id),
                "is_certificate" => ($val->cert_template_id != '') ? true : false,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            switch ($type) {
                case "completed":
                    if ($completestatus >= 100) {
                        $vx[] = $temp;
                    }
                    break;
                case "incompleted":
                    if (($completestatus > 0) && ($completestatus < 100)) {
                        $vx[] = $temp;
                    }
                    break;
                default:
                    $vx[] = $temp;
                    break;
            }
            $i++;
        }
        // }
        # die;
        return $vx;
    }

    public function specialityname($id)
    {
        $name = '';
        $array = array();
        if ($id != '') {
            $ids = explode(',', $id);
            foreach ($ids as $value) {
                $array[] = $this->special($value, $name);
            }
            $name = implode(",", (array)$array);
        }
        return $name;
    }
    public function special($id, $name)
    {
        // $spec = '';
        $spec = array();
        $sql = "SELECT ms.* FROM master_specialities_V1 as ms where ms.master_specialities_id = " . $id . " ";
        $query = $this->db->query($sql);
        $data = array();
        $row = $query->row();
        $pid = $row->parent_id;
        // if($this->name ==''){
        $this->name = $row->specialities_name;
        // }else{
        //   $this->name = $row->specialities_name."-".$this->name;
        // }
        if ($pid != 0) {
            $this->specialityname($pid, $this->name);
        } elseif ($pid == 0) {
        }
        return $this->name;
    }
    public function performancestatus($user_master_id, $type)
    {
        $response['incomplete'] = 0;
        $response['complete'] = 0;
        $response = array();
        $this->db->select('distinct(training_id) as id');
        $this->db->from('training_user_tracking as tut');
        $this->db->join('training_master as tm', 'tm.id =tut.training_id');
        $this->db->where(array('user_master_id' => $user_master_id, 'tm.status' => 3 , 'tm.privacy_status' => 0));
        $query = $this->db->get();
        // echo "<pre>";print_r( $query );die;
        $i = 0;
        $j = 0;
        if (($query) && ($query->num_rows() > 0)) {
            foreach ($query->result() as $key => $val) {
                // $completestatus = $this->complete_status($val->id,$user_master_id);
                $completestatus = $this->complete_status_enchanced($val->id, $user_master_id);
                if (($completestatus < 100) && ($completestatus > 0)) {
                    $response['incomplete'] = $response['incomplete'] + 1;
                    $response['id1'][] = $val->id;
                    $response['val_in_stts'][$val->id][] = $completestatus;
                } elseif ($completestatus >= 100) {
                    $response['complete'] = $response['complete'] + 1;
                    $response['id'][] = $val->id;
                    $response['val_cmp_stts'][$val->id][] = $completestatus;
                }
            }
        }
        //print_R($response); exit;
        return $response;
    }

    public function complete_status($training_id, $user_master_id)
    {
        $response = 0;
        $i = 0;
        $total_tmc = array();
        $total = 0;
        if (($training_id != '') && ($user_master_id != '')) {
            $this->db->select('id');
            $this->db->from('training_module');
            $this->db->where(array('training_id' => $training_id, 'status' => 3));
            $query_tm = $this->db->get();
            if (($query_tm) && ($query_tm->num_rows() > 0)) {
                #print_r($query_tm->result());
                foreach ($query_tm->result() as $keytm => $valuetm) {
                    # print_r($valuetm);
                    $module[] = $valuetm->id;
                }
            }
            $this->db->select('count(id) as total');
            $this->db->from('training_module_content');
            $this->db->where_in('module_id', $module);
            $this->db->where(array('training_id' => $training_id, 'status' => 3));
            $query_tmc = $this->db->get();
            if (($query_tmc) && ($query_tmc->num_rows() > 0)) {
                $total_tmc = $query_tmc->result();
                $total = $total_tmc[0]->total;
            }
            $this->db->select('id,training_details');
            $this->db->from('training_user_tracking');
            $this->db->where(array('training_id' => $training_id, 'user_master_id' => $user_master_id));
            $query = $this->db->get();
            #print_r($this->db->last_query()); die;
            if (($query) && ($query->num_rows() > 0)) {
                $result = $query->result();
                foreach ($result as $key => $value) {
                    foreach (json_decode($value->training_details) as $key => $value) {
                        $i = $i + 1;
                    }
                }
                # print_r($i);
                # $response = $result[0]->view_percentage;
                #print_r($response);
                #return $response;
            }
            if (($total != 0) && ($i != 0)) {
                $response = ($i / $total) * 100;
            }
        }
        #die;
        return $response;
    }
    public function complete_status_enchanced($training_id, $user_master_id)
    {
        $response = 0;
        $i = 0;
        $total_tmc = array();
        $total = 0;
        if (($training_id != '') && ($user_master_id != '')) {
            $sql = "SELECT 
                        count(id) as total
                    FROM
                        training_module_content 
                    WHERE
                        training_id = {$training_id}
                    and 
                        status =3
                    and 
                        module_id in (select id from training_module where training_id = {$training_id} and status = 3)";
            //echo $sql;exit;
            $query = $this->db->query($sql);
            $total =  $query->result()[0]->total;
            // echo "<pre>";print_r($total);exit;
            $this->db->select('id,training_details');
            $this->db->from('training_user_tracking');
            $this->db->where(array('training_id' => $training_id, 'user_master_id' => $user_master_id));
            $query = $this->db->get();
            //print_r($query); exit;
            #print_r($this->db->last_query()); die;
            if (($query) && ($query->num_rows() > 0)) {
                $result = $query->result();
                // foreach ($result as $key => $value) {
                //     //print_r($value);
                //     $i += count(json_decode($value->training_details, true));
                // }
                foreach ($result as $key => $value) {
                    // Decode the JSON and check if it's an array
                    $decoded = json_decode($value->training_details, true);
                    if (is_array($decoded)) {
                        $i += count($decoded);
                    } else {
                        // Handle the case where training_details is null or invalid
                        // You can log it, or just skip it, depending on your use case
                        // Example: Log or print a message
                        // error_log("Invalid or null training_details for key: $key");
                    }
                }
                # print_r($i);
                # $response = $result[0]->view_percentage;
                #print_r($response);
                #return $response;
            }
            if (($total != 0) && ($i != 0)) {
                $response = ($i / $total) * 100;
            }
        }
        #die;
        //print_r($response); exit;
        return $response;
    }
    public function detail_bk($id, $user_master_id)
    {
        // echo 'value-'.$this->myredis->delall();
        // exit;
        $cachename = "training_detail_" . $id;
        $response =  array();
        //print_r($this->myredis->exists($cachename)); exit;
        if ($id != '') {
            $env = get_user_env($user_master_id);
            if ($this->myredis->exists($cachename)) {
                $res = $this->myredis->get($cachename);
                // print_r($res); exit;
            } else {
                // $this->db->save_queries = TRUE;
                // echo "upto here";
                if ($env) {
                    if ($env != 'GL') {
                        //$envStatus = "AND tm.env ='GL' or tm.env ='" . $env . "')";
                        $envStatus = array('GL', $env);
                    } else {
                        // $envStatus = "AND tm.env ='" . $env . "'";
                        $envStatus = array($env);
                    }
                } else {
                    $envStatus = "";
                }
                $response = array();
                $this->db->select("tm.*,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,
                (SELECT count(id) as total FROM `training_module` WHERE `training_id` = '" . $id . "' AND `status` = 3) as countmoduletotal");
                $this->db->from('training_master as tm');
                $this->db->join('training_to_speciality tts', 'tts.training_id=tm.id', 'left');
                $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
                $this->db->join('training_to_sponsor as ts', 'ts.training_id = tm.id', 'left');
                $this->db->join('client_master as clintspon', 'clintspon.client_master_id = ts.sponsor_id', 'left');
                $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
                $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id and  rt.post_type='training'", "left");
                $this->db->where(array('tm.status' => 3, 'tm.id' => $id));
                $this->db->where_in('tm.privacy_status', [0, 1, 2]);
                $this->db->where_in('tm.env', $envStatus);
                $query = $this->db->get();
                //$row = array();
                //$query = $this->db->get();
                // $this->db->save_queries = TRUE;
                // $str = $this->db->last_query();
                // echo $str;
                if (($query) && ($query->num_rows())) {
                    $res = $query->result();
                    //print_r($res); exit;
                    $this->myredis->set($cachename, $res);
                } else {
                    $res = array();
                }
            }
            $i = 1;
            //print_R($res); exit;
            //die;
            if (!empty($res)) {
                foreach ($res as $key => $val) {
                    $sponsorLogoArry = explode(",", $val->sponsor_logo);
                    if (@count($sponsorLogoArry) > 0) {
                        foreach ($sponsorLogoArry as $valueSponor) {
                            if ($valueSponor) {
                                $sponsorLogomix[] =  $valueSponor;
                            }
                        }
                    } else {
                        if ($val->sponsor_logo) {
                            $sponsorLogomix[] = $val->sponsor_logo;
                        }
                    }
                    $sponsorLogo = implode(",", (array)$sponsorLogomix);
                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);
                    $ses_doc_det_array = array();
                    if ($val->session_doctor_id) {
                        $session_doc_array = explode(",", $val->session_doctor_id);
                        $ses_doc_det_array = array();
                        $inc_pp = 0;
                        foreach ($session_doc_array as $single_doctor) {
                            $var = session_doc_detail($single_doctor);
                            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                                $logic_image = $var[0]['profile_image'];
                            } else {
                                $logic_image = docimg;
                                //$logic_image = $var[0]['profile_image'];
                            }
                            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                            $inc_pp++;
                        }
                    }
                    // print_r($val->cert_template_id);
                    // die;
                    $response = array(
                        #  "slno"=>$i,
                        "id" => $val->id,
                        "title" => html_entity_decode(strip_tags($val->title)),
                        "image" => change_img_src($val->preview_image),
                        "featured_video" => $val->featured_video,
                        "color" => ($val->color != '') ? $val->color : '#eb34e5',
                        "description" => $val->description,
                        "module_Count" => $val->countmoduletotal, //$this->count_module($val->id),
                        "resource_count" => $this->resourcecount($val->id),
                        "live_session_status" => $this->livestatus($val->id),
                        "training_module_content" => $this->content_count($val->id),
                        "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                        "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                        "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                        "client_name" => $val->client_name,
                        "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                        "sponsor_name" => $val->sponsor,
                        "sponsor_logo" => $sponsorLogo,
                        "session_doctor_entities" => $ses_doc_det_array,
                        "duration" => $val->duration,
                        "is_completed" => $this->complete_status($val->id, $user_master_id),
                        "is_certificate" => ($val->cert_template_id != '') ? true : false,
                        "rating" => $this->ratingaverage($val->id),
                        "user_rating_review" => $this->user_rating_review($val->id, $user_master_id),
                        "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                    );
                    #$i++;
                }
            }
            return $response;
        }
    }
    public function detail($id, $user_master_id, $client_ids)
    {
        // echo 'value-'.$this->myredis->delall();
        // exit;
        $env = get_user_env_id($user_master_id);
        $cachename = "training_detail_" . $id . "_env_" . $env;
        $response =  array();
        // print_r($this->myredis->exists($cachename)); exit;
        if ($id != '') {
            // =============== env_id implementation ===================//
            if ($env) {
                if ($env != 2) {
                    $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                    $env_arr = array("2", $env);
                } else {
                    $envStatus = "AND cTenv.env =" . $env . "";
                    $env_arr = array($env);
                }
            } else {
                $envStatus = "";
            }
            $key_locked = get_user_package($user_master_id, 'training');
            if ($key_locked == '') {
                return null;
            }
            // =============== env_id implementation ===================//
            $master_user_type_id = get_master_user_type_id($user_master_id);
            //print_r($master_user_type_id); exit;
            if ($master_user_type_id == false) {
                return;
            } elseif ($master_user_type_id == 5) { //internal then fetch data what is active(==3) and draft(==5)
                // $response = array();
                $this->db->select("tm.*,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,
                (SELECT count(id) as total FROM `training_module` WHERE `training_id` = '" . $id . "' AND `status` = 3) as countmoduletotal,");
                $this->db->from('training_master as tm');
                $this->db->join('training_to_speciality tts', 'tts.training_id=tm.id', 'left');
                $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
                $this->db->join('training_to_sponsor as ts', 'ts.training_id = tm.id', 'left');
                $this->db->join('client_master as clintspon', 'clintspon.client_master_id = ts.sponsor_id', 'left');
                $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
                $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id and  rt.post_type='training'", "left");
                // $this->db->where(array('tm.status'=>3,'tm.id'=>$id,'tm.privacy_status'=>0));
                $this->db->where('tm.id', $id);
                $this->db->where_in('tm.privacy_status', array(0, 1, 2));
                $this->db->where_in('tm.status', array(3, 5));
                $query = $this->db->get();
                // $this->db->save_queries = TRUE;
                // $str = $this->db->last_query();
                // echo $str; exit;
                // echo $this->db->last_query(); exit;
                //  print_r($query->result()); exit;
                if (($query) && ($query->num_rows())) {
                    $res = $query->result();
                    if ($res[0]->id == '') {
                        return;
                    }
                    // $this->myredis->set($cachename, $res);
                } else {
                    $res = array();
                }
                // print_r($query);
            } else { //not internal user
                //print_r( $this->myredis->exists($cachename)); exit;
                // checking if cache exist
                if ($this->myredis->exists($cachename)) {
                    $res = $this->myredis->get($cachename);
                } else { //no cache and not internal user
                    // $response = array();
                    $this->db->select("tm.*, cTenv.price,  uTpyCont.status as user_contnet_payment_status ,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                    max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,
                    (SELECT COUNT(user_master_id) FROM `payment_user_to_content` WHERE `type_id` = '$id') AS `active_users`,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,
                    (SELECT count(id) as total FROM `training_module` WHERE `training_id` = '" . $id . "' AND `status` = 3) as countmoduletotal,");
                    $this->db->from('training_master as tm');
                    $this->db->join('training_to_speciality tts', 'tts.training_id=tm.id', 'left');
                    $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
                    $this->db->join('training_to_sponsor as ts', 'ts.training_id = tm.id', 'left');
                    $this->db->join('client_master as clintspon', 'clintspon.client_master_id = ts.sponsor_id', 'left');
                    $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', "left");
                    $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id and  rt.post_type='training'", "left");
                    $this->db->join('content_to_env as cTenv', "cTenv.type_id = tm.id and  cTenv.type = 4", "left");
                    $this->db->join('payment_user_to_content as uTpyCont', "uTpyCont.type_id = tm.id and  uTpyCont.type = 4", "left");
                    // $this->db->where(array('tm.status'=>3,'tm.id'=>$id,'tm.privacy_status'=>0));
                    $this->db->where('tm.id', $id);
                    $this->db->where_in('tm.privacy_status', array(0, 1, 2));
                    $this->db->where('tm.status', 3);
                    $this->db->where_in('cTenv.env', $env_arr);

                    $this->db->group_by('tm.id, cTenv.price, uTpyCont.status, ms.rank, clintspon.client_name, clintspon.client_logo');

                    //      $this->db->save_queries = TRUE;
                    $query = $this->db->get();
                    // $str = $this->db->last_query();
                    // echo $str; exit;
                    //print_r($query); exit;
                    if (($query) && ($query->num_rows())) {
                        $res = $query->result();
                        // print_r($res); exit;
                        if ($res[0]->id == '') {
                            return;
                        } else {
                            $this->myredis->set($cachename, $res);
                        }
                    } else {
                        $res = array();
                    }
                }
            }
            //print_r($res); exit;
            $i = 1;
            if (!empty($res)) {
                foreach ($res as $key => $val) {
                    $allsponsor = array();
                    $sponsorname = explode(",", $val->sponsor);
                    $sp = 0;
                    $sponsorLogoArry = explode(",", $val->sponsor_logo);
                    if (@count($sponsorLogoArry) > 0) {
                        foreach ($sponsorLogoArry as $valueSponor) {
                            if ($valueSponor) {
                                $sponsorLogomix[] =  change_img_src($valueSponor);
                                $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => change_img_src($valueSponor));
                                $sp++;
                            }
                        }
                    } else {
                        if ($val->sponsor_logo) {
                            $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                            $allsponsor[] = array('name' => $val->sponsor, "logo" => change_img_src($valueSponor));
                        }
                    }
                    $sponsorLogo = implode(",", (array)$sponsorLogomix);
                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);
                    $ses_doc_det_array = array();
                    if ($val->session_doctor_id) {
                        $session_doc_array = explode(",", $val->session_doctor_id);
                        $ses_doc_det_array = array();
                        $inc_pp = 0;
                        foreach ($session_doc_array as $single_doctor) {
                            $var = session_doc_detail_by_env($single_doctor, $user_master_id);
                            if ($var[0]['doctor_name']) {
                                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                                    $logic_image = $var[0]['profile_image'];
                                } else {
                                    $logic_image = docimg;
                                    //$logic_image = $var[0]['profile_image'];
                                }
                                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                                $inc_pp++;
                            }
                        }
                    }
                    $currentdatetime = date('Y-m-d H:i:s');
                    if (isset($val->start_date) && $currentdatetime < $val->start_date && $val->max_participants > 0) {
                        if ($val->max_participants > $val->active_users) {
                            $is_registrable = true;
                        } else {
                            $is_registrable = false;
                        }
                    } elseif (isset($val->start_date) && $currentdatetime > $val->start_date) {
                        $is_registrable = false;
                    } elseif (!isset($val->start_date) && $val->max_participants == 0) {
                        $is_registrable = true;
                    } elseif (!isset($val->start_date) && $val->max_participants > 0) {
                        $is_registrable = false;
                    } else {
                        $is_registrable = true;
                    }
                    // echo $is_registrable;
                    $response = array(
                        #  "slno"=>$i,
                        "id" => $val->id,
                        "url" => $val->url,
                        "privacy_status" => $val->privacy_status,
                        "env" => $val->env,
                        "title" => html_entity_decode(strip_tags($val->title)),
                        "image" => change_img_src($val->preview_image),
                        "featured_video" => $val->featured_video,
                        "color" => ($val->color != '') ? $val->color : '#eb34e5',
                        "description" => change_img_src($val->description),
                        "citation" => $val->citation,
                        "transcript" => $val->transcript,
                        "module_Count" => $val->countmoduletotal, //$this->count_module($val->id),
                        "resource_count" => $this->resourcecount($val->id),
                        "live_session_status" => $this->livestatus($val->id),
                        "start_date" => $val->start_date,
                        "is_registrable" => $is_registrable,
                        "max_participants" => $val->max_participants,
                        "active_users" => $val->active_users,
                        "training_module_content" => $this->content_count($val->id),
                        "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                        "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                        "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                        // "client_name" => $val->client_name,
                        // "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                        //============ integrated for subscription ============//
                        "is_locked" => $key_locked,
                        "price" => get_a_content_price($id, 4, $env),
                        "user_content_payment" => get_user_content_status($id, 4, $user_master_id),
                        //============ integrated for subscription ============//
                        "is_share" => get_a_content_is_share_status($val->id, '4'),
                        "sponsor_name" => $val->sponsor,
                        "sponsor_logo" => $sponsorLogo,
                        "all_sponsor" => $allsponsor,
                        "session_doctor_entities" => $ses_doc_det_array,
                        "duration" => $val->duration,
                        "is_completed" => $this->complete_status($val->id, $user_master_id),
                        "is_certificate" => ($val->cert_template_id != '') ? true : false,
                        "rating" => $this->ratingaverage($val->id),
                        "user_rating_review" => $this->user_rating_review($val->id, $user_master_id),
                        "deeplink" => ($env == '2') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                    );
                    #$i++;
                }
            }
            $campaign  = getContentCampiagn($user_master_id, $client_ids, $id, 'training');
            $response['display_banner'] = $campaign['banner_dispaly'];
            $response['campaign_data'] = $campaign['creative_data'];
            if ($master_user_type_id != 5) {
                if ($env == "2" && $response['env'] == "IN") {
                    $response = [];
                }
            }
            return $response;
        }
    }
    /**
     * Code written by rakesh for get traing detail for related api
     */
    // public function detail_for_related($id, $user_master_id)
    // {
    //     // echo 'value-'.$this->myredis->delall();
    //     // exit;
    //     $cachename = "training_detail_" . $id;
    //     $response =  array();
    //     // print_r($this->myredis->exists($cachename)); exit;
    //     if ($id != '') {
    //         if ($this->myredis->exists($cachename)) {
    //             $res = $this->myredis->get($cachename);
    //             // print_r($res); exit;
    //         } else {
    //             $response = array();
    //             $this->db->select("tm.*,GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
    //             GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
    //             max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
    //             GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,
    //             GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,
    //             (SELECT count(id) as total FROM `training_module` WHERE `training_id` = '" . $id . "' AND `status` = 3) as countmoduletotal,");
    //             $this->db->from('training_master as tm');
    //             $this->db->join('training_to_speciality tts', 'tts.training_id=tm.id', 'left');
    //             $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
    //             $this->db->join('training_to_sponsor as ts', 'ts.training_id = tm.id');
    //             $this->db->join('client_master as clintspon', 'clintspon.client_master_id = ts.sponsor_id');
    //             $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
    //             $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id and  rt.post_type='training'", "left");
    //             $this->db->where(array('tm.status' => 3, 'tm.id' => $id, 'tm.privacy_status' => 0));
    //             $this->db->group_by('tm.id, clintspon.client_name, clintspon.client_logo');

    //             $query = $this->db->get();
    //             //$row = array();
    //             //$query = $this->db->get();
    //             //  print_r($this->db->last_query());
    //             //     die;
    //             if (($query) && ($query->num_rows())) {
    //                 $res = $query->result();
    //                 //print_r($res); exit;
    //                 $this->myredis->set($cachename, $res);
    //             } else {
    //                 $res = array();
    //             }
    //         }
    //         $i = 1;
    //         //print_R($res); exit;
    //         //die;
    //         if (!empty($res)) {
    //             foreach ($res as $key => $val) {
    //                 $sponsorLogoArry = explode(",", $val->sponsor_logo);
    //                 $allsponsor = array();
    //                 $sponsorname = explode(",", $val->sponsor);
    //                 $sp = 0;
    //                 if (@count($sponsorLogoArry) > 0) {
    //                     foreach ($sponsorLogoArry as $valueSponor) {
    //                         if ($valueSponor) {
    //                             $sponsorLogomix[] =  change_img_src($valueSponor);
    //                         }
    //                         $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
    //                         $sp++;
    //                     }
    //                 } else {
    //                     if ($val->sponsor_logo) {
    //                         $sponsorLogomix[] = change_img_src($val->sponsor_logo);
    //                     }
    //                     $allsponsor[] = array('name' => $val->sponsor, "logo" => $val->sponsor_logo);
    //                 }
    //                 $sponsorLogo = implode(",", (array)$sponsorLogomix);
    //                 unset($sponsorLogomix);
    //                 unset($sponsorLogoArry);
    //                 $ses_doc_det_array = array();
    //                 if ($val->session_doctor_id) {
    //                     $session_doc_array = explode(",", $val->session_doctor_id);
    //                     $ses_doc_det_array = array();
    //                     $inc_pp = 0;
    //                     foreach ($session_doc_array as $single_doctor) {
    //                         $var = session_doc_detail($single_doctor);
    //                         $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
    //                         if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
    //                             $logic_image = $var[0]['profile_image'];
    //                         } else {
    //                             $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
    //                             $logic_image_path = "uploads/docimg/" . $image;
    //                             $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
    //                             $logic_image = $imgPr;
    //                             //$logic_image = $var[0]['profile_image'];
    //                         }
    //                         $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
    //                         $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
    //                         $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
    //                         $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
    //                         $inc_pp++;
    //                     }
    //                 }
    //                 // print_r($val->cert_template_id);
    //                 // die;
    //                 // print_r($val);
    //                 // exit;
    //                 $response = array(
    //                     #  "slno"=>$i,
    //                     "id" => $val->id,
    //                     "type_id" => $val->id,
    //                     "trending_type" => 'training',
    //                     "type" => 'training',
    //                     "title" => html_entity_decode(strip_tags($val->title)),
    //                     "image" => change_img_src($val->preview_image),
    //                     "featured_video" => $val->featured_video,
    //                     "color" => ($val->color != '') ? $val->color : '#eb34e5',
    //                     "description" => $val->description,
    //                     "module_Count" => $val->countmoduletotal, //$this->count_module($val->id),
    //                     "resource_count" => $this->resourcecount($val->id),
    //                     "live_session_status" => $this->livestatus($val->id),
    //                     "training_module_content" => $this->content_count($val->id),
    //                     "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
    //                     "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names, 1),
    //                     "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
    //                     // "client_name" => $val->client_name,
    //                     // "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
    //                     "sponsor_name" => $val->sponsor,
    //                     "sponsor_logo" => $sponsorLogo,
    //                     "all_sponsor" => $allsponsor,
    //                     "session_doctor_entities" => $ses_doc_det_array,
    //                     "duration" => $val->duration,
    //                     "is_completed" => $this->complete_status($val->id, $user_master_id),
    //                     "is_certificate" => ($val->cert_template_id != '') ? true : false,
    //                     "rating" => $this->ratingaverage($val->id),
    //                     "user_rating_review" => $this->user_rating_review($val->id, $user_master_id),
    //                     "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
    //                 );
    //                 #$i++;
    //             }
    //         }
    //         return $response;
    //     }
    // }

    public function detail_for_related($id, $user_master_id)
    {
        $cachename = "training_detail_related_" . $id;
        $response = array();

        if (empty($id)) {
            return $response;
        }

        if ($this->myredis->exists($cachename)) {
            $res = $this->myredis->get($cachename);
        } else {
            // Use Common Table Expressions for better performance
            $sql = "WITH TrainingData AS (
                SELECT 
                    tm.id,
                    tm.title,
                    tm.description,
                    tm.preview_image,
                    tm.featured_video,
                    tm.color,
                    tm.duration,
                    tm.cert_template_id,
                    tm.deeplink,
                    tm.gl_deeplink,
                    (SELECT COUNT(id) FROM training_module WHERE training_id = tm.id AND status = 3) AS countmoduletotal
                FROM 
                    training_master AS tm
                WHERE 
                    tm.status = 3 
                    AND tm.id = " . $id . " 
                    AND tm.privacy_status = 0
            ),
            SpecialitiesData AS (
                SELECT 
                    tts.training_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                    MAX(ms.rank) AS maxrank
                FROM 
                    training_to_speciality AS tts
                JOIN 
                    master_specialities_V1 AS ms ON ms.master_specialities_id = tts.specialities_id
                WHERE 
                    tts.training_id = " . $id . "
                GROUP BY 
                    tts.training_id
            ),
            SponsorsData AS (
                SELECT 
                    ts.training_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                FROM 
                    training_to_sponsor AS ts
                JOIN 
                    client_master AS clintspon ON clintspon.client_master_id = ts.sponsor_id
                WHERE 
                    ts.training_id = " . $id . "
                GROUP BY 
                    ts.training_id
            ),
            DoctorsData AS (
                SELECT 
                    Tdoc.training_id,
                    GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) AS session_doctor_id
                FROM 
                    training_to_session_doctor AS Tdoc
                WHERE 
                    Tdoc.training_id = " . $id . "
                GROUP BY 
                    Tdoc.training_id
            )
            SELECT 
                td.*,
                sd.specialities_name,
                sd.specialities_ids_and_names,
                sd.maxrank,
                sp.sponsor,
                sp.sponsor_logo,
                dd.session_doctor_id
            FROM 
                TrainingData AS td
            LEFT JOIN 
                SpecialitiesData AS sd ON td.id = sd.training_id
            LEFT JOIN 
                SponsorsData AS sp ON td.id = sp.training_id
            LEFT JOIN 
                DoctorsData AS dd ON td.id = dd.training_id";


            // echo $sql;
            // exit;

            $query = $this->db->query($sql);

            if ($query && $query->num_rows() > 0) {
                $res = $query->result();
                $this->myredis->set($cachename, $res);
            } else {
                $res = array();
            }
        }

        if (empty($res)) {
            return $response;
        }

        // print_r($res);
        // exit;

        foreach ($res as $val) {
            // Process sponsor data
            $sponsorLogoArry = !empty($val->sponsor_logo) ? explode(",", $val->sponsor_logo) : array();
            $allsponsor = array();
            $sponsorLogomix = array();
            $sponsorname = !empty($val->sponsor) ? explode(",", $val->sponsor) : array();

            if (!empty($sponsorLogoArry)) {
                for ($sp = 0; $sp < count($sponsorLogoArry); $sp++) {
                    $valueSponor = $sponsorLogoArry[$sp];
                    if (!empty($valueSponor)) {
                        $sponsorLogomix[] = change_img_src($valueSponor);
                    }
                    $allsponsor[] = array(
                        'name' => isset($sponsorname[$sp]) ? $sponsorname[$sp] : '',
                        "logo" => $valueSponor
                    );
                }
            } elseif (!empty($val->sponsor_logo)) {
                $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                $allsponsor[] = array(
                    'name' => $val->sponsor,
                    "logo" => $val->sponsor_logo
                );
            }

            $sponsorLogo = !empty($sponsorLogomix) ? implode(",", $sponsorLogomix) : '';

            // Process doctor data
            $ses_doc_det_array = array();



            if (!empty($val->session_doctor_id)) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;

                foreach ($session_doc_array as $single_doctor) {
                    if (empty($single_doctor)) {
                        continue;
                    }

                    $var = session_doc_detail($single_doctor);



                    if (empty($var) || empty($var[0]['doctor_name'])) {
                        continue;
                    }


                    // print_r($var);
                    // echo 'session_doctor_id--'.$val->session_doctor_id;
                    // exit;


                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    $logic_image = '';

                    if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                        $logic_image = $var[0]['profile_image'];
                    } else {
                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        $logic_image_path = "uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }

                    $ses_doc_det_array[$inc_pp] = array(
                        'session_doctor_name' => $var[0]['doctor_name'],
                        'session_doctor_image' => change_img_src($logic_image),
                        'DepartmentName' => $var[0]['DepartmentName'],
                        'profile' => $var[0]['profile']
                    );

                    $inc_pp++;
                }
            }






            // echo 'session_doctor_id--'.$val->session_doctor_id;
            // exit;
            // Build response array
            $response = array(
                "id" => $val->id,
                "type_id" => $val->id,
                "trending_type" => 'training',
                "type" => 'training',
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->preview_image),
                "featured_video" => $val->featured_video,
                "color" => ($val->color != '') ? $val->color : '#eb34e5',
                "description" => $val->description,
                "module_Count" => $val->countmoduletotal,
                "resource_count" => $this->resourcecount($val->id),
                "live_session_status" => $this->livestatus($val->id),
                "training_module_content" => $this->content_count($val->id),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "all_sponsor" => $allsponsor,
                "session_doctor_entities" => $ses_doc_det_array,
                "duration" => $val->duration,
                "is_completed" => $this->complete_status($val->id, $user_master_id),
                "is_certificate" => ($val->cert_template_id != '') ? true : false,
                "rating" => $this->ratingaverage($val->id),
                "user_rating_review" => $this->user_rating_review($val->id, $user_master_id),
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );


            // print_r($response);
            // exit;
        }

        return $response;
    }
    public function ratingaverage($id)
    {
        $total = 0;
        $result = 0;
        if ($id != '') {
            $this->db->select('user_id,rating');
            $this->db->from('training_review');
            $this->db->where('training_id', $id);
            $this->db->group_by('user_id,rating');
            $this->db->order_by('rating', 'desc');
            $query = $this->db->get();
            if (($query) && ($query->num_rows() > 0)) {
                foreach ($query->result() as $key => $val) {
                    $total = $total + $val->rating;
                }
                $result = ($total / $query->num_rows());
            }
        }
        return $result;
    }
    public function resourcecount($id)
    {
        if ($id != '') {
            $cachename = "training_detail_resourcecount" . $id;
            //print_R($this->myredis->exists($cachename)); //exit;
            if ($this->myredis->exists($cachename)) {
                $res = $this->myredis->get($cachename);
                //print_r($res); exit;
            } else {
                $this->db->select('type');
                $this->db->from('training_module_content_resource');
                $this->db->where(array('training_id' => $id));
                $query = $this->db->get();
                //print_r($this->db->last_query()); exit;
                $i = 0;
                $c = 0;
                if (($query) && ($query->num_rows() > 0)) {
                    foreach ($query->result() as $key => $value) {
                        if ($value->type == 'pdf') {
                            $i++;
                        } elseif ($value->type == 'image') {
                            $i++;
                        } else {
                            $i++;
                        }
                    }
                }
                $res = $i;
                $this->myredis->set($cachename, $res);
            }
        }
        return $res;
    }
    public function getmodulelist($id, $user_master_id)
    {
        $resource = array();
        if ($id != '') {
            $this->db->select('id,module_name,s_date');
            $this->db->from('training_module');
            $this->db->where(array('training_id' => $id, 'status' => 3));
            $query = $this->db->get();
            // print_r($this->db->last_query());
            // die;
            if (($query) && ($query->num_rows() > 0)) {
                foreach ($query->result() as $key => $value) {
                    $resource[] = array(
                        "module_id" => $value->id,
                        "s_date" => $value->s_date,
                        "module_title" => $value->module_name,
                        "module_status" => $this->modulestatus($id, $value->id, $user_master_id),
                        "live_session_status" => $this->livestatus_in_module($id, $value->id),
                        "content_count" => $this->content_count_in_module($id, $value->id),
                        "progress_status" => $this->progressstatus($user_master_id, $id, $value->id)
                    );
                }
            }
        }
        // print_r($resource);
        // die;
        return $resource;
    }
    public function getmodulestatus($id, $user_master_id)
    {
        $resource = array();
        if ($id != '') {
            $this->db->select('count(id) as total_module');
            $this->db->from('training_module');
            $this->db->where(array('training_id' => $id, 'status' => 3));
            $query = $this->db->get();
            // print_r($this->db->last_query());
            // die;
            if (($query) && ($query->num_rows() > 0)) {
                $result = $query->result();
                $response['totalmodule'] = $result[0]->total_module;
            }
            $this->db->select('count(id) as total_module');
            $this->db->from('training_module');
            $this->db->where(array('training_id' => $id, 'status' => 3));
            $query = $this->db->get();
            // print_r($this->db->last_query());
            // die;
            if (($query) && ($query->num_rows() > 0)) {
                $result = $query->result();
                $response['totalmodule'] = $result[0]->total_module;
            } else {
                $response['totalmodule'] = 0;
            }
            $this->db->select('count(id) as currentmodule');
            $this->db->from('training_user_tracking');
            $this->db->where(array('user_master_id' => $user_master_id, 'training_id' => $id));
            $querymoduletrack = $this->db->get();
            if (($querymoduletrack) && ($querymoduletrack->num_rows() > 0)) {
                $resultmoduletrack = $querymoduletrack->result();
                $response['currentmodule'] = $resultmoduletrack[0]->currentmodule;
            } else {
                $response['currentmodule'] = 0;
            }
            $this->db->select('count(id) as total_content_module');
            $this->db->from('training_module_content');
            $this->db->where(array('training_id' => $id, 'status' => 3));
            $querycontentcount = $this->db->get();
            // print_r($this->db->last_query());
            // die;
            if (($querycontentcount) && ($querycontentcount->num_rows() > 0)) {
                $resultcontentcount = $querycontentcount->result();
                $response['total_content_module'] = $resultcontentcount[0]->total_content_module;
            } else {
                $response['total_content_module'] = 0;
            }
            $this->db->select('training_details');
            $this->db->from('training_user_tracking');
            $this->db->where(array('user_master_id' => $user_master_id, 'training_id' => $id));
            $queryusercontenttrack = $this->db->get();
            $contentcounter = 0;
            if (($queryusercontenttrack) && ($queryusercontenttrack->num_rows() > 0)) {
                $resultusercontenttrack = $queryusercontenttrack->result();
                foreach ($resultusercontenttrack as $keyuser => $valueuser) {
                    $jsondata = json_decode($valueuser->training_details, true);
                    foreach ($jsondata as $key => $value) {
                        # code...
                        if ($value['content_id'] != '') {
                            $contentcounter = $contentcounter + 1;
                        }
                        //print_r($contentcounter);
                    }
                }
                $response['usercontentcount'] = $contentcounter;
            } else {
                $response['usercontentcount'] = 0;
            }
            $this->db->select('sum(`s`.`survey_points`) as totalpoints');
            $this->db->from('survey as s');
            $this->db->join('training_module_content as tms', "(tms.type='survey' and tms.type_id = s.survey_id)");
            $this->db->where(array('tms.status' => 3, 'tms.training_id' => $id));
            $querysurvey = $this->db->get();
            if (($querysurvey) && ($querysurvey->num_rows() > 0)) {
                $resultsurveytotal = $querysurvey->result();
                $response['totalsurveypoints'] = $resultsurveytotal[0]->totalpoints;
            } else {
                $response['totalsurveypoints'] = 0;
            }
            $this->db->select('sum(`s`.`survey_points`) as totalpoints');
            $this->db->from('survey as s');
            $this->db->join('training_module_content as tms', "(tms.type='survey' and tms.type_id = s.survey_id)");
            $this->db->where(array('tms.status' => 3, 'tms.training_id' => $id));
            $querysurvey = $this->db->get();
            if (($querysurvey) && ($querysurvey->num_rows() > 0)) {
                $resultsurveytotal = $querysurvey->result();
                $response['totalsurveypoints'] = $resultsurveytotal[0]->totalpoints;
            } else {
                $response['totalsurveypoints'] = 0;
            }
            $this->db->select('sua.report');
            $this->db->from('survey_user_answer as sua');
            $this->db->join('training_module_content as tms', "(tms.type='survey' and tms.type_id = sua.survey_id and sua.user_master_id=" . $user_master_id . ")");
            $this->db->where(array('tms.status' => 3, 'tms.training_id' => $id));
            $querysurveyuseranswer = $this->db->get();
            $surveyscore = 0;
            if (($querysurveyuseranswer) && ($querysurveyuseranswer->num_rows() > 0)) {
                foreach ($querysurveyuseranswer->result() as $kuserpoint => $valuepoint) {
                    $surveypoint = json_decode($valuepoint->report);
                    $surveyscore = $surveyscore + $surveypoint->totalPoint;
                }
                $response['usersurveypoints'] = $surveyscore;
                //$response['totalsurveypoints'] = $resultsurveytotal[0]->totalpoints;
            } else {
                $response['usersurveypoints'] = 0;
            }
        }
        // print_r($resource);
        // die;
        return $response;
    }
    public function modulestatus($id, $module_id, $user)
    {
        $this->db->from('training_user_tracking');
        $this->db->where(array('user_master_id' => $user, 'training_id' => $id, 'module_id' => $module_id));
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            return $query->result();
        } else {
            return array();
        }
    }
    public function progressstatus($uid, $id, $mid)
    {
        $response = "incomplete";
        $this->db->select('status');
        $this->db->from('training_user_tracking');
        $this->db->where(array('user_master_id' => $uid, 'training_id' => $id, 'module_id' => $mid));
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            $response = $result[0]->status;
        }
        return $response;
    }
    public function content_count_in_module($training_id, $module_id)
    {
        if (($training_id != '') && ($module_id != '')) {
            $data =  array();
            $data['comp'] = 0;
            $data['session'] = 0;
            $data['survey'] = 0;
            $data['video_archieve'] = 0;
            $data['live_training'] = 0;
            $this->db->select('id,type');
            $this->db->from('training_module_content');
            $this->db->where(array('training_id' => $training_id, 'module_id' => $module_id, "status" => 3));
            $query = $this->db->get();
            if (($query) && ($query->num_rows() > 0)) {
                foreach ($query->result() as $key => $value) {
                    if ($value->type != '') {
                        switch ($value->type) {
                            case "comp":
                                $data['comp'] = $data['comp'] + 1;
                                break;
                            case "session":
                                $data['session'] = $data['session'] + 1;
                                break;
                            case "survey":
                                $data['survey'] = $data['survey'] + 1;
                                break;
                            case "video_archieve":
                                $data['video_archieve'] = $data['video_archieve'] + 1;
                                break;
                            case "clinical_video":
                                $data['video_archieve'] = $data['video_archieve'] + 1;
                                break;
                            case "live_video":
                                $data['live_training'] = $data['live_training'] + 1;
                                break;
                        }
                    }
                }
            }
            return $data;
        }
    }
    public function livestatus_in_module($training_id, $module_id)
    {
        $status = 0;
        if (($training_id != '') && ($module_id != '')) {
            $currentdatetime = date('Y-m-d H:i:s');
            if ($training_id != '') {
                $this->db->select('tmc.id,tmc.type_id');
                $this->db->from('training_module_content as tmc');
                $this->db->join('knwlg_sessions_V1 as ks', 'ks.session_id = tmc.type_id');
                $this->db->where(array('tmc.type' => 'session', 'tmc.module_id' => $module_id, 'tmc.training_id' => $training_id, 'ks.session_status' => 2, "tmc.status" => 3));
                $this->db->where("'" . $currentdatetime . "' BETWEEN ks.start_datetime and ks.end_datetime");
                $query = $this->db->get();
                if (($query) && ($query->num_rows() > 0)) {
                    $status = 1;
                } else {
                    $this->db->select('id');
                    $this->db->from('training_module_content');
                    $this->db->where(array('type' => 'live_video', 'training_id' => $training_id, 'module_id' => $module_id, 'status' => 3));
                    $this->db->where("'" . $currentdatetime . "' BETWEEN start_datetime and end_datetime");
                    $querylivevideo = $this->db->get();
                    if (($querylivevideo) && ($querylivevideo->num_rows() > 0)) {
                        $status = 1;
                    }
                }
            }
        }
        return $status;
    }
    public function contentbymodule($training_id, $module_id, $user_master_id, $client_ids)
    {
        //echo $training_id; exit;
        $data = array();
        $response = array();
        if (($training_id !=  '') && ($module_id != '')) {
            $this->db->select('*');
            $this->db->from('training_module_content');
            $this->db->where(array('training_id' => $training_id, 'module_id' => $module_id, 'status' => 3));
            $query = $this->db->get();
            //  print_r($this->db->last_query());
            // die;
            if (($query) && ($query->num_rows() > 0)) {
                $this->db->select('training_details');
                $this->db->from('training_user_tracking');
                $this->db->where(array('training_id' => $training_id, "module_id" => $module_id, "user_master_id" => $user_master_id));
                $queryusertrack = $this->db->get();
                $userstatus = array();
                if (($queryusertrack) && ($queryusertrack->num_rows() > 0)) {
                    $jsonresult = $queryusertrack->result();
                    $resultusertrack = json_decode($jsonresult[0]->training_details, true);
                    foreach ($resultusertrack as $kusertrack => $vusertrack) {
                        $userstatus[$vusertrack['content_id']] = "completed";
                    }
                }

                // print_r($query->result());
                // exit;
                foreach ($query->result() as $key => $value) {
                    // print_r($value);
                    # die;
                    switch ($value->type) {
                        case 'session':
                            $session['content_id'] = $value->id;
                            $session['type'] = "session";
                            $session['userstatus'] = (isset($userstatus[$value->id])) ? $userstatus[$value->id] : "";
                            $session['detail'] = productdetail('session', $value->type_id, $user_master_id, $client_ids);
                            $session['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            $response[] = $session;
                            #$cme[]=$session;
                            #$response['session']=$cme;
                            break;
                        case 'comp':
                            $comp['content_id'] = $value->id;
                            $comp['type'] = 'comp';
                            $comp['userstatus'] = (isset($userstatus[$value->id])) ? $userstatus[$value->id] : "";
                            $comp['detail'] = productdetail('comp', $value->type_id, $user_master_id, $client_ids);
                            $comp['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            $response[] = $comp;
                            break;
                        case 'survey':
                            $survey['content_id'] = $value->id;
                            $survey['type'] = "survey";
                            $survey['userstatus'] = (isset($userstatus[$value->id])) ? $userstatus[$value->id] : "";
                            $survey['detail'] = productdetail("survey", $value->type_id, $user_master_id, $client_ids);
                            $survey['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            $response[] = $survey;
                            break;
                        case "archivevideo":
                            $cachename = "archivevideo_detail_" . $value->type;
                            //print_R($this->myredis->exists($cachename)); //exit;
                            if ($this->myredis->exists($cachename)) {
                                $response[] = $this->myredis->get($cachename);
                                // print_r($res); exit;
                            } else {
                                $archivideo['content_id'] = $value->id;
                                $archivideo['type'] = "archivevideo";
                                $archivideo['userstatus'] = (isset($userstatus[$value->id])) ? $userstatus[$value->id] : "";
                                $archivideo['detail'] = productdetail('archivevideo', $value->type_id, $user_master_id, $client_ids);
                                $archivideo['resource'] = $this->getresource($training_id, $module_id, $value->id);
                                $response[] = $archivideo;
                                $this->myredis->set($cachename, $response);
                            }
                            break;
                        case "clinical_video":
                            $archivideo['content_id'] = $value->id;
                            $archivideo['type'] = "archivevideo";
                            $archivideo['userstatus'] = (isset($userstatus[$value->id])) ? $userstatus[$value->id] : "";
                            $archivideo['detail'] = productdetail('archivevideo', $value->type_id, $user_master_id, $client_ids);
                            $archivideo['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            $response[] = $archivideo;
                            // $archieve_id_aray[]= $archivevideo;
                            // $response['archivevideo']=$archieve_id_aray;
                            break;
                        case "live_video":
                            $live_video['content_id'] = $value->id;
                            $live_video['type'] = "live_video";
                            $live_video['userstatus'] = (isset($userstatus[$value->id])) ? $userstatus[$value->id] : "";
                            $live_video['detail']['startdatetime'] = $value->start_datetime;
                            $live_video['detail']['enddatetime'] = $value->end_datetime;
                            $live_video['detail']['url'] = $value->live_video_url;
                            $live_video['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            $response[] = $live_video;
                            //     $video_id_aray[]= $live_video;
                            // $response['live_video']=$video_id_aray;
                            break;
                        case "video":
                            $live_video['detail']['recorded_url'] = $value->resource_file;
                            $live_video['userstatus'] = (isset($userstatus[$value->id])) ? $userstatus[$value->id] : "";
                            $response[] = $live_video;
                            //    $video_id_aray[]= $live_video;
                            //    $response['live_video']=$video_id_aray;
                            #$data['live_video'] = array_merge($data['live_video'],$data['live_video']);
                            break;
                        default:
                            #$data[$value->type] = array('resource'=>$value->resource_file);
                            break;
                    }
                }
            }
        }
        return $response;
    }

    public function contentbymodule_bk($training_id, $module_id, $user_master_id, $client_ids)
    {
        $data = array();
        if (($training_id !=  '') && ($module_id != '')) {
            $this->db->select('*');
            $this->db->from('training_module_content');
            $this->db->where(array('training_id' => $training_id, 'module_id' => $module_id));
            $query = $this->db->get();
            // print_r($this->db->last_query());
            // die;
            if (($query) && ($query->num_rows() > 0)) {
                foreach ($query->result() as $key => $value) {
                    // print_r($value);
                    // die;
                    switch ($value->type) {
                        case 'session':
                            $data['session']['content_id'] = $value->id;
                            $data['session']['detail'] = cta('session', $value->type_id, $user_master_id, $client_ids);
                            $data['session']['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            break;
                        case 'comp':
                            $data['comp']['content_id'] = $value->id;
                            $data['comp']['detail'] = cta('comp', $value->type_id, $user_master_id, $client_ids);
                            $data['comp']['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            break;
                        case 'survey':
                            $data['survey']['content_id'] = $value->id;
                            $data['survey']['detail'] = $this->surveydetail($value->type_id, $user_master_id);
                            $data['survey']['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            break;
                        case "archivevideo":
                            $data['archivevideo']['content_id'] = $value->id;
                            $data['archivevideo']['detail'] = $this->archiveVideoDetial($value->type_id, $user_master_id);
                            $data['archivevideo']['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            break;
                        case "clinical_video":
                            $data['archivevideo']['content_id'] = $value->id;
                            $data['archivevideo']['detail'] = $this->archiveVideoDetial($value->type_id, $user_master_id);
                            $data['archivevideo']['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            break;
                        case "live_video":
                            $data['live_video']['content_id'] = $value->id;
                            $data['live_video']['detail']['startdatetime'] = $value->start_datetime;
                            $data['live_video']['detail']['enddatetime'] = $value->end_datetime;
                            $data['live_video']['detail']['url'] = $value->resource_file;
                            $data['live_video']['resource'] = $this->getresource($training_id, $module_id, $value->id);
                            break;
                        case "video":
                            $data['live_video']['detail']['recorded_url'] = $value->resource_file;
                            #$data['live_video'] = array_merge($data['live_video'],$data['live_video']);
                            break;
                        default:
                            #$data[$value->type] = array('resource'=>$value->resource_file);
                            break;
                    }
                }
            }
        }
        return $data;
    }
    public function totalcontent($tid, $mid)
    {
        $this->db->select('id');
        $this->db->from('training_module_content');
        $this->db->where(array('training_id' => $tid, 'module_id' => $mid, 'status' => 3));
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            return $query->num_rows();
        } else {
            return 0;
        }
    }
    public function getresource($training_id, $module_id, $content_id)
    {
        $result = array();
        if (($training_id != '') && ($module_id != '') && ($content_id != '')) {
            $this->db->select('*');
            $this->db->from('training_module_content_resource');
            $this->db->where(array('training_id' => $training_id, 'module_id' => $module_id, 'content_id' => $content_id));
            $query = $this->db->get();
            // if(($training_id == 49)&&($module_id == 208)&&($content_id == 215)){
            //     print_r('   '.$this->db->last_query());
            // }
            if (($query) && ($query->num_rows() > 0)) {
                foreach ($query->result() as $key => $value) {
                    $res[] = $value;
                }
                $result = $res;
            }
        }
        return $result;
    }
    public function archiveVideoDetial($type_id = '', $user_master_id = '')
    {
        if ($type_id) {
            $env = get_user_env($user_master_id);
            $sql = "SELECT
                        cm.video_archive_id as type_id,
                        cm.video_archive_question,
                        cm.video_archive_answer,
                        cm.video_archive_question_raw,
                        cm.video_archive_answer_raw,
                        cm.video_archive_file_img,
                        cm.video_archive_file_img_thumbnail,
                        cm.deeplink,
                        cm.gl_deeplink,
                        cm.start_like,
                        cm.added_on,
                        cm.publication_date,
                        cln.client_name,
                        cln.client_logo,
                        cm.type,
                        cm.vendor,
                        cm.src,
                        ks.session_doctor_id,
                        msct.category_name,
                        msct.category_logo,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        cm.video_archive_speciality_id,
                        kv.status as vault,
                        (SELECT COUNT(rt.rating) FROM knwlg_rating rt 
                        WHERE rt.post_id = cm.video_archive_id 
                        AND rt.post_type = 'video_archive' 
                        AND rt.rating != 0) as averageRating,
                        rtmy.rating as myrating
                    FROM knwlg_video_archive as cm 
                    JOIN video_archive_to_specialities as cmTs ON cmTs.video_archive_id = cm.video_archive_id
                    JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                    LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                    LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
                    LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
                    LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id 
                        AND kv.type_text = 'video_archive' 
                        AND kv.user_id = " . $user_master_id . "
                    LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id 
                        AND rtmy.post_type = 'video_archive' 
                        AND rtmy.rating != 0 
                        AND rtmy.user_master_id = " . $user_master_id . "
                    LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id 
                        AND rt.post_type = 'video_archive'
                    JOIN client_master as cln ON cln.client_master_id = cm.client_id
                    WHERE cm.status = 3 
                    AND cm.video_archive_id = " . $type_id . "
                    GROUP BY cm.video_archive_id, 
                            cm.video_archive_question, 
                            cm.video_archive_answer, 
                            cm.video_archive_question_raw, 
                            cm.video_archive_answer_raw, 
                            cm.video_archive_file_img, 
                            cm.video_archive_file_img_thumbnail, 
                            cm.deeplink, 
                            cm.gl_deeplink, 
                            cm.start_like, 
                            cm.added_on, 
                            cm.publication_date, 
                            cln.client_name, 
                            cln.client_logo, 
                            cm.type, 
                            cm.vendor, 
                            cm.src, 
                            ks.session_doctor_id, 
                            msct.category_name, 
                            msct.category_logo, 
                            cm.video_archive_speciality_id, 
                            kv.status, 
                            rtmy.rating
                    ORDER BY cm.publication_date DESC";
            //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
            //echo $sql; exit;
            //and
            //cm.publication_date <= CURDATE()
            $query = $this->db->query($sql);
            $val = $query->row();
            //print_r($result); exit;
            $i = 1;
            $vx = array();
            if ($val->video_archive_file_img) {
                $img = change_img_src($val->video_archive_file_img);
            } else {
                $img = '';
            }
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (@count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . change_img_src($valueSponor);
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . change_img_src($val->sponsor_logo);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $session_doc_array = explode(",", $val->session_doctor_id);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if ($image) {
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image = docimg; //$imgPr;
                    }
                } else {
                    $logic_image =  docimg; //base_url() . "uploads/docimg/no-image.png";
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] =  $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
            //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
            if ($val->type_id) {
                $vx[] = array(
                    "slno" => $i,
                    "con_type" => $val->type,
                    "type_id" => $val->type_id,
                    "vendor" => $val->vendor,
                    "src" => $val->src,
                    // "type_id" => $val->type_id,
                    "date" => date(' jS F y', strtotime($val->publication_date)),
                    "question" => html_entity_decode(strip_tags($string)),
                    "image" => change_img_src($img),
                    //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                    "client_name" => $val->client_name,
                    "client_logo" => '' . change_img_src($val->client_logo),
                    "category_name" => $val->category_name,
                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => $sponsorLogo,
                    "comment_count" => $val->count_comment,
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                    "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : '',
                    "myrating" => ($val->myrating != '') ? true : false,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "session_doctor_id" => $val->session_doctor_id,
                    "session_doctor_entities" => $ses_doc_det_array,
                );
            }
        }
        return $vx;
    }
    /**
     * @param string $user_master_id
     * @return array
     */
    public function surveydetail($id = '', $user_master_id = '')
    {
        $env = get_user_env($user_master_id);
        //echo $user_master_id;
        $sql = "SELECT 
        sv.* ,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        cln.client_name,
        cln.client_logo,
        sd.data,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
        FROM 
        survey sv
        left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id          
        JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
        JOIN client_master as cln ON cln.client_master_id = sv.client_id
        LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
        JOIN survey_detail as sd ON   sd.survey_id = sv.survey_id 
        WHERE 
        sv.status = 3  and sd.is_draft = 0
        AND 
        sv.survey_id = " . $id . " ";
        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $val = $query->row();
        $dataArry = unserialize($val->data);
        //remove correct answer from the json
        foreach ($dataArry as $key => $value) {
            foreach ($value as $key2 => $value2) {
                unset($dataArry[$key][$key2]['correctoption']);
                //print_r($value2);
            }
        }
        //remove correct answer from the json
        foreach ($dataArry['surveys'] as $key => $surveyArry) {
            // if (  $id != 1035 && $id != 1036  && $id != 1158 ) {
            if ($id != 1158) {
                shuffle($surveyArry['options']);
            }
            //print_r($surveyArry['options']);
            $dataArry['surveys'][$key]['options'] = $surveyArry['options'];
        }
        // print_r($dataArry);
        //exit;
        //$optionArray = $dataArry['surveys']['question1']['options'];
        //print_r($optionArray);
        //shuffle($optionArray);
        //$dataArry['surveys']['question1']['options'] = $optionArray;
        //print_r($dataArry);
        //exit;
        $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
        $str = preg_replace('/\\\"/', "\"", $json);
        //echo stripslashes($json);
        $user_survey_status = $this->detail_user_status($id, $user_master_id);
        $sponsorLogoArry = explode(",", $val->sponsor_logo);
        if (@count($sponsorLogoArry) > 0) {
            foreach ($sponsorLogoArry as $valueSponor) {
                if ($valueSponor) {
                    $sponsorLogomix[] =  $valueSponor;
                }
            }
        } else {
            if ($val->sponsor_logo) {
                $sponsorLogomix[] =  $val->sponsor_logo;
            }
        }
        $sponsorLogo = implode(",", (array)$sponsorLogomix);
        unset($sponsorLogomix);
        unset($sponsorLogoArry);
        $vx[] = array(
            "survey_id" => $val->survey_id,
            "category" => $val->category,
            "survey_title" => $val->survey_title,
            "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
            "survey_description" => html_entity_decode($val->survey_description),
            "survey_points" => $val->survey_points,
            "point" => $val->survey_points,
            "points_on_approval" => $val->points_on_approval,
            "survey_time" => $val->survey_time,
            "question_count" => $val->question_count,
            "image" => change_img_src($val->image),
            "specialities_name" => $val->specialities_name,
            "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names, 1),
            "client_name" => $val->client_name,
            "client_logo" =>  change_img_src($val->client_logo),
            "sponsor_name" => $val->sponsor,
            "sponsor_logo" => change_img_src($sponsorLogo),
            "verified" => $val->verified,
            "publishing_date" => $val->publishing_date,
            "data_json" => $str,
            "user_survey_status" => $user_survey_status,
            'disclaimer' => 'The Sponsor of this Survey is solely responsible for its content. The purpose of this survey is for research & information only and is meant for participation by registered medical practitioners only. Your participation in this survey is voluntary. The information given and results expressed in this activity are those of the participants and not that of CLIRNET or the Sponsor. The information given and results do not represent and cannot be construed as an  endorsement by CLIRNET or the Sponsor. CLIRNET at the request of the Survey Sponsor, may share your personal details such as name, location and survey results with the Sponsor for information purposes only. If you wish to not share your personal information with the Sponsor, then please do not respond to the survey. You may refer to our Privacy Policy for further details. CLIRNET reserves the right to terminate or withdraw a survey, or your opportunity to participate in the survey, at any time for any reason.'
        );
        return $vx;
        //add child checking in this sql
        //echo $sql;
        //exit;
    }
    /**
     * @param string $id
     * @param string $user_master_id
     * @return mixed
     */
    public function detail_user_status($id = '', $user_master_id = '')
    {
        $sql = "SELECT 
        id
        FROM 
        survey_user_answer
        WHERE  
        survey_id = " . $id . "
        AND
        user_master_id = " . $user_master_id . " ";
        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query = $this->db->query($sql);
        //$this->db->cache_off();
        $val = $query->row();
        if ($val->id > 0) {
            $status = "completed";
        } else {
            $sqlIncomp = "SELECT 
            id
            FROM 
            survey_user_incomplete_answer
            WHERE  
            survey_id = " . $id . "
            AND
            user_master_id = " . $user_master_id . " ";
            //echo $sql; exit;
            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;
            $queryIncom = $this->db->query($sqlIncomp);
            //$this->db->cache_off();
            $valIncom = $queryIncom->row();
            if ($valIncom->id > 0) {
                $status = "pending";
            } else {
                $status = "incomplete";
            }
        }
        return $status;
        //add child checking in this sql
        //echo $sql;
        //exit;
    }
    public function list1($type, $user_master_id, $limitTo, $limitFrom)
    {
        $env = get_user_env($user_master_id);
        $this->db->select("tm.id,
        tm.title,
        tm.description,
        tm.preview_image,
        tm.client_id,
        tm.channel_id,
        tm.template_id,
        tm.color,
        tm.display_in_dashboard,
        tm.featured_video,
        tm.deeplink,
        tm.in_deeplink,
        tm.gl_deeplink,
        tm.start_like,
        tm.start_date,
        tm.url,
        tm.max_participants,
        tm.added_on,
        tm.added_by,
        tm.modified_on,
        tm.modified_by,
        tm.status,
        tm.cert_template_id,
        tm.duration,
        tm.privacy_status,
       
        tm.published_date,
        tm.env,
        tm.is_share,
        tm.is_like,
        tm.is_comment,
        tm.enable_maxparticipants,
        tm.allow_postStart,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        max( ms.rank) as maxrank, GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,clintspon.client_name,clintspon.client_logo,(select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = tm.id and  rt.post_type='training')as averageRating");
        $this->db->from('training_master as tm');
        $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tm.speciality_id');
        $this->db->join('training_to_sponsor as ts', 'ts.training_id = tm.id');
        $this->db->join('client_master as clintspon', 'clintspon.client_master_id = ts.sponsor_id');
        $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = ts.id', 'left');
        $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id and  rt.post_type='training'", "left");
        $this->db->where('tm.status', 3);
        if ($type = "featured") {
            $this->db->where('tm.display_in_dashboard', 1);
        }
        $this->db->where('date(tm.published_date)<=', date('Y-m-d'));
        $this->db->group_by([
            'tm.id',
            'tm.title',
            'tm.description',
            'tm.preview_image',
            'tm.client_id',
            'tm.channel_id',
            'tm.template_id',
            'tm.color',
            'tm.display_in_dashboard',
            'tm.featured_video',
            'tm.deeplink',
            'tm.in_deeplink',
            'tm.gl_deeplink',
            'tm.start_like',
            'tm.start_date',
            'tm.url',
            'tm.max_participants',
            'tm.added_on',
            'tm.added_by',
            'tm.modified_on',
            'tm.modified_by',
            'tm.status',
            'tm.cert_template_id',
            'tm.duration',
            'tm.privacy_status',

            'tm.published_date',
            'tm.env',
            'tm.is_share',
            'tm.is_like',
            'tm.is_comment',
            'tm.enable_maxparticipants',
            'tm.allow_postStart'
        ]);
        $this->db->order_by('date(tm.published_date)', 'desc');
        if (($limitTo != '') && ($limitFrom != '')) {
            $this->db->limit($limitFrom, $limitTo);
        }
        $query = $this->db->get();
        //$row = array();
        //$query = $this->db->get();
        //print_r($this->db->last_query());
        //    die;
        $i = 1;
        if ($query->result()) {
            foreach ($query->result() as $key => $val) {
                // $content_count[''] =
                $sponsorLogoArry = explode(",", $val->sponsor_logo);
                if (@count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] =  change_img_src($valueSponor);
                        }
                    }
                } else {
                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                $ses_doc_det_array = array();
                if ($val->session_doctor_id) {
                    $session_doc_array = explode(",", $val->session_doctor_id);
                    $ses_doc_det_array = array();
                    $inc_pp = 0;
                    foreach ($session_doc_array as $single_doctor) {
                        $var = session_doc_detail($single_doctor);
                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                            $logic_image = $var[0]['profile_image'];
                        } else {
                            $logic_image = docimg;
                            //$logic_image = $var[0]['profile_image'];
                        }
                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                        $inc_pp++;
                    }
                }
                $vx[] = array(
                    "slno" => $i,
                    "id" => $val->id,
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "image" => change_img_src($val->preview_image),
                    "color" => ($val->color != '') ? $val->color : '#eb34e5',
                    "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "module_Count" => $this->count_module($val->id),
                    "live_session_status" => $this->livestatus($val->id),
                    "training_module_content" => $this->content_count($val->id),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                    "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src(base_url('uploads/logo/') . $val->client_logo),
                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => $sponsorLogo,
                    "session_doctor_entities" => $ses_doc_det_array,
                    "duration" => $val->duration,
                    "is_completed" => false,
                    "is_certificate" => ($val->cert_template_id != '') ? true : false,
                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                );
                $i++;
            }
        }
        return $vx;
    }
    public function count_module($id)
    {
        if ($id != '') {
            $this->db->select('count(id) as total');
            $this->db->from('training_module');
            $this->db->where(array('training_id' => $id, "status" => 3));
            $query = $this->db->get();
            #print_r($this->db->last_query()); exit;
            if (($query) && ($query->num_rows() > 0)) {
                return $query->num_rows();
                //$res =  $this->myredis->set($cachename, $query->num_rows());
            } else {
                return 0;
                //$res = 0;
            }
            //return $res;
        }
    }
    public function content_count($id)
    {
        $data =  array();
        if ($id != '') {
            //
            $data['comp'] = 0;
            $data['session'] = 0;
            $data['survey'] = 0;
            $data['video_archieve'] = 0;
            $data['live_training'] = 0;
            $cachename = "training_detail_contentcount" . $id;
            if ($this->myredis->exists($cachename)) {
                $data = $this->myredis->get($cachename);
                // print_r($data); exit;
            } else {
                $this->db->select('id,type');
                $this->db->from('training_module_content');
                $this->db->where(array('training_id' => $id, 'status' => 3));
                $query = $this->db->get();
                if (($query) && ($query->num_rows() > 0)) {
                    foreach ($query->result() as $key => $value) {
                        if ($value->type != '') {
                            switch ($value->type) {
                                case "comp":
                                    $data['comp'] = $data['comp'] + 1;
                                    break;
                                case "clinical_video":
                                    $data['video_archieve'] = $data['video_archieve'] + 1;
                                    break;
                                case "session":
                                    $data['session'] = $data['session'] + 1;
                                    break;
                                case "survey":
                                    $data['survey'] = $data['survey'] + 1;
                                    break;
                                case "video_archieve":
                                    $data['video_archieve'] = $data['video_archieve'] + 1;
                                    break;
                                case "live_video":
                                    $data['live_training'] = $data['live_training'] + 1;
                                    break;
                            }
                        }
                    }
                }
            }
            $this->myredis->set($cachename, $data);
        }
        return $data;
    }
    public function livestatus($id)
    {
        $currentdatetime = date('Y-m-d H:i:s');
        $status = 0;
        if ($id != '') {
            $this->db->select('tmc.id,tmc.type_id');
            $this->db->from('training_module_content as tmc');
            $this->db->join('knwlg_sessions_V1 as ks', 'ks.session_id = tmc.type_id');
            $this->db->where(array('tmc.type' => 'session', 'tmc.training_id' => $id, 'ks.session_status' => 2, "tmc.status" => 3));
            $this->db->where("'" . $currentdatetime . "' BETWEEN ks.start_datetime and ks.end_datetime");
            $query = $this->db->get();
            if (($query) && ($query->num_rows() > 0)) {
                $status = 1;
            } else {
                $this->db->select('id');
                $this->db->from('training_module_content');
                $this->db->where(array('type' => 'live_video', 'training_id' => $id));
                $this->db->where("'" . $currentdatetime . "' BETWEEN start_datetime and end_datetime");
                $querylivevideo = $this->db->get();
                if (($querylivevideo) && ($querylivevideo->num_rows() > 0)) {
                    $status = 1;
                }
            }
        }
        // print_r($status);
        // die;
        return $status;
    }
    public function tracking_rcord($user_master_id, $training_id, $module_id)
    {
        $this->db->select('training_details');
        $this->db->from('training_user_tracking');
        $this->db->where(array('user_master_id' => $user_master_id, 'training_id' => $training_id, 'module_id' => $module_id));
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $oldrecord = $query->result();
            return json_decode($oldrecord[0]->training_details);
        } else {
            return '';
        }
    }
    public function insert_tracking_data($uid, $id, $mid, $array)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($array)) {
            $this->insertdb->where(array("training_id" => $id, "module_id" => $mid, "user_master_id" => $uid));
            $this->insertdb->delete("training_user_tracking");
            $this->insertdb->insert('training_user_tracking', $array);
            return true;
        }
    }
    public function insert_audit($array)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($array)) {
            $this->insertdb->insert('training_audit', $array);
            //  print_r($this->db->last_query());
            //  die;
            return true;
        }
    }
    /**
     * @param $eid
     * @param $cid
     * @return array
     */
    public function share($eid, $cid)
    {
        $final_array = array();
        if (($eid != '') && ($cid != '')) {
            $this->db->select('cms.channel_id,cm.title as title,cms.id as election_id,cm.logo,cms.startdate,cms.enddate,epm.id,epm.position_name');
            $this->db->from('channel_election_schedule as cms');
            $this->db->join('election_position_master as epm', 'epm.channel_id=cms.channel_id');
            $this->db->join('channel_master as cm', 'cm.channel_master_id = cms.channel_id');
            $this->db->where(array('cms.id' => $eid, 'cms.channel_id' => $cid));
            $query_position = $this->db->get();
            //print_r($this->db->last_query());
            if ($query_position->num_rows() > 0) {
                foreach ($query_position->result() as $key => $value) {
                    $final_array['channeldetail']['channel_title'] = $value->title;
                    $final_array['channeldetail']['channel_logo'] = change_img_src($value->logo);
                    $this->db->select('enl.session_doctor_id');
                    $this->db->from('election_nominiee_list as enl');
                    $this->db->where(array('enl.election_id' => $eid, 'enl.position_id' => $value->id));
                    $query = $this->db->get();
                    if ($query->num_rows() > 0) {
                        foreach ($query->result() as $keydoc => $valuedoc) {
                            $doctor_detail = session_doc_detail($valuedoc->session_doctor_id);
                            $doctor_id = array('session_doctor_id' => $valuedoc->session_doctor_id);
                            $doctorlist = array_merge($doctor_id, $doctor_detail[0]);
                            $positionid['id'] = $value->id;
                            $doctors[$value->id]['data'][] = $doctorlist;
                        }
                    }
                    $doctordetail['position'] = array('position_id' => $value->id, 'position_name' => $value->position_name);
                    if ($positionid['id'] == $value->id) {
                        $doctor[] = array_merge($doctordetail, $doctors[$value->id]);
                    }
                    $final_array['nominielist'] = $doctor;
                }
            }
        }
        return $final_array;
    }
    /**
     * @param $user_master_id
     * @param $election_id
     * @return int
     */
    public function electioncastvotecheck($user_master_id, $election_id)
    {
        $response = 0;
        $this->db->select('channel_id');
        $this->db->from('election_ballotbox');
        $this->db->where(array('election_id' => $election_id, 'user_master_id' => $user_master_id));
        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            $response = $query->num_rows();
        }
        return $response;
    }
    public function explode_speciality_string($string, $status)
    {
        $final = array();
        if (!empty($string)) {
            $temp_sp_array = explode(",", $string);
            foreach ($temp_sp_array as $ky => $sp_id_name) {
                $sp_id_name_array = explode("#", $sp_id_name);
                $final[$ky] = array();
                $final[$ky]['id'] = $sp_id_name_array[0];
                $final[$ky]['name'] = $sp_id_name_array[1];
                $result[] = $sp_id_name_array[1];
            }
        }
        if ($status == 0) {
            $final = implode(",", (array)$result);
        }
        return $final;
    }
    public function surveyscoredetail($user_master_id, $training_id)
    {
        $surveylist = array();
        $response['survey_total_points'] = 0;
        $response['survey_user_points'] = 0;
        $this->db->select('type_id');
        $this->db->from('training_module_content');
        $this->db->where(array('training_id' => $training_id, 'type' => 'survey'));
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $list = $query->result();
            foreach ($list as $k => $v) {
                $surveylist[] = $v->type_id;
            }
        }
        if ($surveylist != '' && !empty($surveylist)) {
            $this->db->select('sum(survey_points) as totalpoints');
            $this->db->from('survey');
            $this->db->where_in('survey_id', $surveylist);
            $query_surveytotalpoints = $this->db->get();
            if (($query_surveytotalpoints) && ($query_surveytotalpoints->num_rows() > 0)) {
                $totalpoints = $query_surveytotalpoints->result();
                $response['survey_total_points'] = $totalpoints[0]->totalpoints;
            }
            $this->db->select('sum(point) as usertotalpoints');
            $this->db->from('survey_user_ponts');
            $this->db->where('user_master_id', $user_master_id);
            $this->db->where_in('survey_id', $surveylist);
            $query_surveyuserpoints = $this->db->get();
            if (($query_surveyuserpoints) && ($query_surveytotalpoints->num_rows() > 0)) {
                $user_points = $query_surveyuserpoints->result();
                $response['survey_user_points'] = $user_points[0]->usertotalpoints;
            }
        }
        return $response;
    }
    public function user_rating_review($id, $user_master_id)
    {
        $response = array();
        if ($id == '') {
            return $response;
        }
        $sql = "SELECT tr.rating, tr.review, ud.first_name, ud.middle_name, ud.last_name 
                FROM training_review as tr 
                LEFT JOIN user_detail as ud ON  tr.user_id = ud.user_master_id 
                WHERE tr.training_id = " . $id . " and tr.user_id = " . $user_master_id . " order by tr.review_id desc limit 1";
        // echo $sql;
        // exit;
        // exit;
        $query = $this->db->query($sql);
        if (($query) && ($query->num_rows() > 0)) {
            $response = $query->result_array();
            foreach ($query->result() as $key => $value) {
                $response[$key]['specialities'] =  $this->get_profile_speciality($user_master_id);
            }
        }
        return $response;
    }
    //cerrtificate functionality
    public function check_training_status($training_id)
    {
        $this->db->select('status');
        $this->db->from('training_master');
        $this->db->where('id', $training_id);
        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        if (($query) && ($query->num_rows() > 0)) {
            return 1;
        } else {
            return 0;
        }
    }
    public function getcertificate($training_id)
    {
        $this->db->select('cert_template_id');
        $this->db->from('training_master');
        $this->db->where('id', $training_id);
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $res = $query->result();
            return $res[0]->cert_template_id;
        } else {
            return "";
        }
    }
    public function course_user($training_id, $user_master_id, $status)
    {
        if ($status == 0) {
            $sql = "SELECT user_detail.first_name,
            user_detail.middle_name,
            user_detail.last_name,
            training_master.title,
            clircert_certificate_templete.*,
            training_master.id as training_id,
            training_user_tracking.id as participant_id,
            training_master.cert_template_id
            FROM  training_user_tracking 
            LEFT JOIN user_master ON user_master.user_master_id= training_user_tracking.user_master_id 
            JOIN user_detail ON user_detail.user_master_id=training_user_tracking.user_master_id
            LEFT JOIN training_master ON training_master.id=training_user_tracking.training_id  
            LEFT JOIN clircert_certificate_templete ON clircert_certificate_templete.id = training_master.cert_template_id
            WHERE  training_master.cert_template_id IS NOT NULL 
            AND training_master.cert_template_id!=0 
            AND training_user_tracking.training_id=" . $training_id .
                " AND training_user_tracking.user_master_id=" . $user_master_id . " limit 1";
        } else {
            $sql = "SELECT user_detail.first_name,
            user_detail.middle_name,
            user_detail.last_name,
            training_master.title,
            clircert_certificate_templete.*,
            training_master.id as training_id,
            training_user_tracking.id as participant_id,
            training_master.cert_template_id
            FROM  training_user_tracking 
            LEFT JOIN user_master ON user_master.user_master_id= training_user_tracking.user_master_id 
            JOIN user_detail ON user_detail.user_master_id=training_user_tracking.user_master_id
            LEFT JOIN training_master ON training_master.id=training_user_tracking.training_id  
            LEFT JOIN clircert_certificate_templete ON clircert_certificate_templete.id = training_master.cert_template_id
            LEFT JOIN training_certificate_status ON training_certificate_status.training_id = training_master.id and training_certificate_status = " . $user_master_id . "
            WHERE  training_master.cert_template_id IS NOT NULL 
            AND training_master.cert_template_id!=0 
            and training_certificate_status.is_generated = 0
            AND training_user_tracking.training_id=" . $training_id .
                " AND training_user_tracking.user_master_id=" . $user_master_id . " limit 1";
        }
        #echo $sql; exit;
        $query = $this->db->query($sql);
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
        } else {
            $result = array();
        }
        return $result;
    }
    public function getenddate($training_id, $user_master_id)
    {
        $this->db->select('added_on');
        $this->db->from('training_user_tracking');
        $this->db->where(array('training_id' => $training_id, 'user_master_id' => $user_master_id));
        $this->db->order_by('added_on', 'desc');
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            $response['date'] = $result[0]->added_on;
        } else {
            $response['date'] = '';
        }
        return $response;
    }
    public function check_user_certificate_status($training_id, $user_master_id)
    {
        $this->db->select('is_generated');
        $this->db->from('training_certificate_status');
        $this->db->where(array('training_id' => $training_id, 'user_master_id' => $user_master_id));
        $query = $this->db->get();
        // echo "<pre>";print_r( $query);die;
        //print_r($this->db->last_query()); exit;
        if (($query) && ($query->num_rows() > 0)) {
            $res = $query->result();
            return $res[0]->is_generated;
        } else {
            return "";
        }
    }
    public function check_user_certificate_status_old($training_id, $user_master_id)
    {
        $this->db->select('is_generated');
        $this->db->from('training_certificate_status');
        $this->db->where(array('training_id' => $training_id, 'user_master_id' => $user_master_id));
        $query = $this->db->get();
        // echo "<pre>";print_r( $query);die;
        //print_r($this->db->last_query()); exit;
        if (($query) && ($query->num_rows() > 0)) {
            $res = $query->result();
            return $res[0]->is_generated;
        } else {
            return "";
        }
    }
    public function insert_user_specific_certificate($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $this->db->insert('clircert_certificate_master', $data);
            //  print_r($this->db->last_query());
            //  die;
            $insert_id = $this->db->insert_id();
            if ($insert_id > 0) {
                $add['training_id'] = $data['type_id'];
                $add['user_master_id'] = $data['user_master_id'];
                $add['modified_by'] = 0;
                $add['created_at'] = date('Y-m-d H:i:s');
                $add['updated_at'] = date('Y-m-d H:i:s');
                // check data is available or not
                $this->db->select('is_generated');
                $this->db->from('training_certificate_status');
                $this->db->where(array('training_id' => $data['type_id'], 'user_master_id' => $data['user_master_id']));
                $query = $this->db->get();
                //      print_r($this->db->last_query());
                //  die;
                if (($query) && ($query->num_rows() > 0)) {
                    $update['is_generated'] = 1;
                    $result = $query->result();
                    if ($result[0]->is_generated == 0) {
                        $this->insertdb->where(array('training_id' => $data['type_id'], 'user_master_id' => $data['user_master_id']));
                        $this->insertdb->update('training_certificate_status', $update);
                    }
                } else {
                    $add['is_generated'] = 1;
                    $this->insertdb->insert('training_certificate_status', $add);
                }
                // check data is available or not
                #echo $this->db->last_query() ; exit();
                #echo $insert_id; exit;
                return $insert_id;
            }
        }
    }
    public function certificateurl($training_id, $user_master_id)
    {
        $this->db->select('file,id');
        $this->db->from('clircert_certificate_master');
        $this->db->where(array('type_id' => $training_id, 'user_master_id' => $user_master_id));
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $res = $query->result();
            return $res;
        } else {
            return "";
        }
    }
    public function update_participant_name($data, $user_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $this->insertdb->where('user_master_id', $user_id);
            $this->insertdb->update('user_detail', $data);
            return $this->insertdb->affected_rows();
            //echo $this->db->last_query() ; exit();
        }
    }
    public function delete_certificate($cert_id, $training_id, $user_master_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        $sqlSpecialityDel = "DELETE
        FROM 
        clircert_certificate_master
        where  
        id = " . $cert_id;
        $this->insertdb->query($sqlSpecialityDel);
        if ($this->insertdb->affected_rows() > 0) {
            $update['is_generated'] = 0;
            $this->insertdb->where(array('training_id' => $training_id, 'user_master_id' => $user_master_id));
            $this->insertdb->update('training_certificate_status', $update);
        }
    }
    public function errorlog($array)
    {
        //$this->insertdb = $this->load->database('insert', TRUE);
        //$this->insertdb->insert('notification_server_api_call_status', $array);
    }
    public function updatestatus($training_id, $user_master_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        $update['is_generated'] = 0;
        $this->insertdb->where(array('training_id' => $training_id, 'user_master_id' => $user_master_id));
        $this->insertdb->update('training_certificate_status', $update);
    }
}
