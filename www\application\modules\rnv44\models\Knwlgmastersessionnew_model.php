<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Knwlgmastersessionnew_model extends CI_Model
{
    public function __construct()
    {
        $ci = get_instance();
        $ci->load->helper('image');
        $ci->load->helper('utility');
        $this->load->library('Myredis');
    }
    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function tracklivesession($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        $this->insertdb->insert('session_analytics', $data);
        if ($this->insertdb->affected_rows() > 0) {
            return $this->insertdb->insert_id();
        } else {
            return $this->insertdb->error();
        }
    }
    public function all_bookedmastersession($user_master_id)
    {
        if (!empty($user_master_id)) {

            $sql = "select
                    knwlg_sessions_id
                    from 
                    knwlg_sessions_participant as sp
                    join knwlg_sessions_V1 ks on ks.session_id = sp.knwlg_sessions_id
                    where
                    sp.participant_id ={$user_master_id}
                    and
                    participant_type = 'member'
                    and
                    ks.status = 3 ";
            $query = $this->db->query($sql);
            return $query->result();
            // $this->db->select('knwlg_sessions_id');
            // $this->db->where('participant_id', $user_master_id);
            // $this->db->where('participant_type', 'member');
            // //$this->db->where_not_in('session_approval_status', 3);
            // $query = $this->db->get('knwlg_sessions_participant');
            // return $query->result();
        }
    }
    /**
     * @param $booked_id
     * @param $user_master_id
     * @param $from
     * @param $to
     * @return array
     */
    public function all_bookedmastersession_today($booked_id, $user_master_id)
    {
        $new_arr = array();
        foreach ($booked_id as $sing) {
            $new_arr[] = $sing->knwlg_sessions_id;
        }
        $res_arr = implode(',', $new_arr);
        $sql = "SELECT
        ks.session_id
		FROM knwlg_sessions_V1 as ks
        WHERE ks.session_id IN(" . $res_arr . ")   and   DATE(ks.start_datetime) = CURDATE()";
        //echo $sql; exit;
        $query = $this->db->query($sql);
        //echo $this->db->last_query(); exit();AND DATE(ks.end_datetime) > NOW()
        $result = $query->result_array();
        //print_r($result); exit();
        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $i++;
        }
        return $entities;
        //print_r($entities); exit();
    }
    public function add_participant($session_id, $user_master_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        $insert_data = array(
            "knwlg_sessions_id" => $session_id,
            "participant_id" => $user_master_id,
            "participant_type" => "member",
            "added_on" => date("Y-m-d H:i:s"),
            "added_by_admin" => 0,
            "session_approval_status" => 1,
            "is_attended" => 0,
            "is_certificate_generated" => 0,
            "session_user_status" => 0,
            "status" => 3,
            "scheduler_status" => 0,
        );
        $this->insertdb->insert('knwlg_sessions_participant', $insert_data);
        $data = $this->insertdb->insert_id();
        //echo $this->db->last_query(); exit();
        return $data;
    }
    /**
     * @param $booked_id
     * @param $user_master_id
     * @param $from
     * @param $to
     * @return array
     */
    public function all_bookedmastersession_details(
        $booked_id,
        $user_master_id,
        $from,
        $to,
        $category,
        $spids
    ) {

        // echo $from.'to'.$to;
        // exit;


        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")";
            } else {
                $envStatus = "AND cTenv.env = " . $env . "";
            }
        } else {
            $envStatus = "";
        }


        // $booked_ids = $booked_id;
        // $getchildsession = $this->get_all_childsession();

        // if (!empty($getchildsession)) {
        //     //print_r( $getchildsession); exit;
        //     $cids = implode(",", (array)$getchildsession['sessions']);
        //     //print_r($cids);
        //     $childsessionids = " and ks.session_id NOT IN (" . $cids . ")";
        // } else {
        //     $childsessionids = "";
        // }
        // foreach ($booked_ids as $sing) {
        //     $new_arr[] = $sing->knwlg_sessions_id;
        // }
        // if (!empty($new_arr)) {
        //     $res_arr = implode(',', array_unique($new_arr));
        // }
        // if ($res_arr) {
        //     $sqlStr = "and  ks.session_id   IN(" . $res_arr . ") ";
        // }

        // echo $sqlStr;
        // exit;

        // $querySmart = "";
        // $envStatus = "";
        // $global_status = "ks.status IN (3)";
        // $session_status = "AND ks.session_status in (1,2,7)";
        // $privacy_status = "AND ks.privacy_status = 0 ";
        // $session_end_datetime = "AND ks.end_datetime >= '" . date("Y-m-d H:i:s") . "' ";



        $global_status = "ks.status = 3";
        $session_status = "AND ks.session_status in (1,2,7)";
        $privacy_status = "";
        $session_end_datetime = "";
        $session_start_datetime = "";
        $orderBydate = "ks.start_datetime DESC";
        $bookedStatus = true;


        $result = $this->getSessionList(
            $session_start_datetime,
            $querySmart,
            $envStatus,
            $childsessionids,
            $from,
            $to,
            $user_master_id,
            $global_status,
            $session_status,
            $privacy_status,
            $session_end_datetime,
            $orderBydate,
            $bookedStatus
        );


        // $result = $query->result_array();
        // print_r($result);
        // exit();
        $i = 0;
        $entities = array();
        foreach ($result as $row) {

            $entities[$i]['participant_id'] = $row['participant_id'];
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type'] = "session";
            $entities[$i]['session_topic'] = $row['session_topic'];
            $entities[$i]['session_description'] = $row['session_description'];
            $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            // $entities[$i]['client_id'] = $row['client_id'];
            // $entities[$i]['client_name'] = $row['client_name'];
            $entities[$i]['color'] = ($row['color'] != '') ? $row['color'] : '#eb34e5';
            $entities[$i]['deeplink'] = $row['deeplink'];
            $entities[$i]['is_share'] = $row['is_share'];
            $entities[$i]['is_locked'] = $key_locked;
            $entities[$i]['price'] = $row['price'];
            $entities[$i]['user_content_payment'] = get_user_content_status($row['session_id'], 2, $user_master_id);
            /**
             * new sponsor logic
             */
            $allsponsor = array();
            $sponsorname = explode(",", $row['sponsor']);
            $sp = 0;
            $sponsorLogoArry = explode(",", $row['sponsor_logo']);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        // if full path exist
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = base_url('uploads/logo/') . $valueSponor;
                        }
                    }
                }
                $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                $sp++;
            } else {
                if ($row['sponsor_logo']) {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                    $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
            $entities[$i]['all_sponsor'] = $allsponsor;
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($row['document_path'] != "" || $row['document_path'] != null) {
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
            } else {
                $entities[$i]['document_path'] = "";
            }
            if ($row['comment'] != "" || $row['comment'] != null) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['start_datetime'] = date(' jS F y', strtotime($row['start_datetime']));
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            //$post_date = $row['added_on'];
            $start_date = $row['ms_start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            //$now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            $entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $this->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            $entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }
            $entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }
            //$coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
            $entities[$i]['is_multiday_session'] = $row['is_multiday_session'];
            $entities[$i]['cover_image'] = change_img_src($row['cover_image']);
            $entities[$i]['cover_image1'] = change_img_src($row['cover_image1']);
            $entities[$i]['cover_image2'] = change_img_src($row['cover_image2']);
            $entities[$i]['cover_image3'] = change_img_src($row['cover_image3']);
            $entities[$i]['cover_image4'] = change_img_src($row['cover_image4']);
            $entities[$i]['cover_image5'] = change_img_src($row['cover_image5']);
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['is_share'] = $row['is_share'];

            $entities[$i]['ms_start_datetime'] = $row['start_datetime'];
            $entities[$i]['start_datetime_old'] = $row['start_datetime']; //date(' jS M y', strtotime($row['start_datetime']));
            $entities[$i]['end_datetime_old'] = $row['end_datetime'];
            //$entities[$i]['date_default_timezone_get'] = date_default_timezone_get();
            $entities[$i]['start_datetime'] = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $entities[$i]['end_datetime'] = (new DateTime($row['end_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['category_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            //$session_doc_array = explode(",", $row['session_doctor_id']);
            if ($row['session_doctor_id'] != '') {
                $session_doc_array = explode(",", $row['session_doctor_id']);
            } else {
                //print_r($getchildsession['session_doctors']);
                $session_doc_array = explode(",", $getchildsession['session_doctors'][$row['session_id']]);
                //print_r($session_doc_array); exit;
            }
            $ses_doc_det_array = array();
            $inc_pp = 0;
            $store_total_doctors[$row->session_id] = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);

                if ($image) {

                    if (stripos($image, "https://") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                    //=======================================
                } else {
                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $k = array_keys((array)$getchildsession['sessioncount']);
            //print_r(in_array($val->session_id,$k));
            // print_r($k);
            if (in_array($row['session_id'], $k)) {
                $total_multiday_session = $getchildsession['sessioncount'][$row['session_id']];
                //   print_R($total_multiday_session."/n");
            } else {
                $total_multiday_session = 0;
            }
            $keyvalue_1 = array_keys((array)$getchildsession['doctorcount']);
            if (in_array($row['session_id'], $keyvalue_1)) {
                $total_doctors = $getchildsession['doctorcount'][$row['session_id']];
            } else {
                $total_doctors = $store_total_doctors[$row['session_id']];
            }
            $datetime1 = new DateTime(date('Y-m-d H:i:s'));
            $datetime2 = new DateTime($row['end_datetime']);
            $difference = $datetime1->diff($datetime2);
            $entities[$i]["total_session"] = $total_multiday_session;
            $entities[$i]["total_doctors"] = $total_doctors;
            $entities[$i]["total_days"] = $difference->d;
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $entities[$i]['cpddetail'] = $this->getcpddetails($row['session_id']);
            $i++;
        }
        return $entities;
        //print_r($entities); exit();
    }
    public function session_doctor_signature($doctor_id)
    {
        $sql = "SELECT GROUP_CONCAT(signature) as all_doctors_signature  FROM `knwlg_sessions_doctors` WHERE `sessions_doctors_id` IN(" . $doctor_id . ")";
        $query = $this->db->query($sql);
        // print_r($this->db->last_query());
        // exit;
        $resultSet = $query->row_array();
        #print_r($resultSet['all_doctors_signature']);
        #die;
        return $resultSet['all_doctors_signature'];
    }
    public function session_doctor($doctor_id)
    {
        $sql = "SELECT
        GROUP_CONCAT(doctor_name) as all_doctors
        FROM knwlg_sessions_doctors
        WHERE sessions_doctors_id IN(" . $doctor_id . ")";
        $query = $this->db->query($sql);
        $resultSet = $query->row_array();
        return $resultSet['all_doctors'];
    }
    public function all_upcomingsessionreserved_details($booked_id, $user_master_id)
    {
        // $new_arr = array();
        // foreach ($booked_id as $sing) {
        //     $new_arr[] = $sing->knwlg_sessions_id;
        // }
        // //date_default_timezone_set("Asia/Calcutta");
        // $timemusk = date('Y-m-d H:i:s', time());
        // $res_arr = implode(',', $new_arr);
        // $sql = "SELECT

        //             ks.session_id

        //         FROM knwlg_sessions_V1 as ks
        //         LEFT JOIN master_specialities_V1 as ms
        //             ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
        //         LEFT JOIN client_master as cln
        //             ON cln.client_master_id = ks.client_id
        //         LEFT JOIN session_to_sponsor as sTspon
        //             ON sTspon.session_id = ks.session_id
        //         LEFT JOIN client_master as clintspon
        //             ON clintspon.client_master_id = sTspon.sponsor_id
        //         LEFT JOIN master_session_category as msct
        //             ON msct.mastersession_category_id = ks.category_id
        //         LEFT JOIN knwlg_sessions_doctors as sdoc
        //             ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
        //         LEFT JOIN knwlg_sessions_documents as sd
        //             ON sd.knwlg_sessions_id = ks.session_id
        //         LEFT JOIN knwlg_sessions_participant as ksp
        //             ON ksp.knwlg_sessions_id = ks.session_id
        //         LEFT JOIN knwlg_sessions_documents as ksd
        //             ON ksd.knwlg_sessions_id = ks.session_id
        //         WHERE ks.session_id IN(" . $res_arr . ")
        //             AND ks.end_datetime >= '" . $timemusk . "'
        //             AND ks.session_status != 6
        //         GROUP BY

        //             ks.session_id

        //         ORDER BY ks.start_datetime ASC";
        // $query = $this->db->query($sql);
        // // $sql = "SELECT
        // // ksp.participant_id,
        // // ks.session_id,
        // // ks.*,
        // // sd.*,
        // // cln.client_name,
        // // cln.client_logo,
        // // GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        // // GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        // // GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        // // GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
        // // msct.category_name,
        // // msct.category_logo,
        // // sd.document_path,
        // // sd.comment,
        // // ksd.knwlg_sessions_docs_id,
        // // ksd.document_path,
        // // ksd.comment,
        // // GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
        // // GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
        // // GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') as  speciality,
        // // GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as  profile,
        // // GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as  profile_images,
        // // GROUP_CONCAT(ksp.participant_id) as  PartName,
        // // GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED,
        // // (ks.total_buffer + ks.total_seats) as tot_seat
        // // FROM knwlg_sessions_V1 as ks
        // // LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
        // // LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id
        // // LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
        // // LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
        // // LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
        // // LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
        // // LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
        // // LEFT JOIN knwlg_sessions_participant as ksp ON ksp.knwlg_sessions_id = ks.session_id
        // // LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id
        // // WHERE ks.session_id IN(" . $res_arr . ") AND ks.start_datetime >='" . $timemusk . "'
        // // GROUP BY ks.session_id   ORDER BY ks.start_datetime ASC";
        // // $query = $this->db->query($sql);
        // //echo $this->db->last_query(); exit();
        // //AND DATE(ks.end_datetime) > NOW()
        // $result = $query->result_array();
        // //print_r($result); exit();
        // return count($result);
        // //print_r($entities); exit();



        $global_status = "ks.status = 3";
        $session_status = "AND ks.session_status in (1,2,7)";
        $privacy_status = "";
        $session_end_datetime = "";
        $session_start_datetime = "";
        $orderBydate = "ks.start_datetime DESC";
        $bookedStatus = true;


        $result = $this->getSessionList(
            $session_start_datetime,
            $querySmart,
            $envStatus,
            $childsessionids,
            $from,
            $to,
            $user_master_id,
            $global_status,
            $session_status,
            $privacy_status,
            $session_end_datetime,
            $orderBydate,
            $bookedStatus
        );

    }
    /**
     * Fetch details of a particular session with optimized performance
     *
     * @param int $session_id The ID of the session to fetch
     * @return array Session details
     */
    public function fetch_particular_session($session_id)
    {
        // Get cache key based on session ID
        $cache_key = "session_details_{$session_id}";

        // Try to get from cache first
        if (isset($this->myredis) && $this->myredis->exists($cache_key)) {
            return $this->myredis->get($cache_key);
        }

        $user_id = $this->session->userdata['user_master_id'];

        // Use query builder with specific column selection to reduce data transfer
        $this->db->select([
            'ks.session_id',
            'ks.session_doctor_id',
            'ks.session_topic',
            'ks.session_description',
            'ks.sessions_question',
            'ks.master_tag_ids',
            'ks.client_id',
            'ks.sponsor_id',
            'ks.user_group_id',
            'ks.category_id',
            'ks.start_datetime',
            'ks.end_datetime',
            'ks.speciality_id',
            'ks.total_seats',
            'ks.total_buffer',
            'ks.add_question_buffer_days',
            'ks.session_link',
            'ks.master_conf_provider_id',
            'ks.session_access_code',
            'ks.deeplink',
            'ks.in_deeplink',
            'ks.gl_deeplink',
            'ks.template_id',
            'ks.cert_template_id',
            'ks.display_in_dashboard',
            'ks.conf_phone_no',
            'ks.privacy_status',
            'ks.color',
            'ks.added_on',
            'ks.added_by',
            'ks.session_status',
            'ks.cover_image',
            'ks.modified_on',
            'ks.modified_by',
            'ks.is_recommended',
            'ks.is_multiday_session',
            'ks.break_json',
            'ks.status',
            'ks.is_featured',
            'ks.rating_flag',
            'ks.remarks',
            'ks.crm_id',
            'ks.img_credits',
            'ks.session_json',
            'ks.certified',
            'ks.env',
            'ks.notification_template',
            'ks.shortlink',
            'ks.invitefile',
            'ks.exitroute',
            'ks.is_share',
            'ks.is_like',
            'ks.is_comment',
            'cm.client_name',
            'cm.client_logo',
            'ms.specialities_name',
            'ksv.vendor_id',
            'ksv.video_embed_src',
            'ksv.room_id',
            'ksv.vouchpro_url',
            'ksv.go_to_meeting_url',
            'ksv.landing_page_url',
            'ksv.session_cast_type'
        ]);

        // Use table aliases for better readability and performance
        $this->db->from('knwlg_sessions_V1 ks');

        // Use LEFT JOIN instead of INNER JOIN to ensure we get the session even if related data is missing
        $this->db->join('client_master cm', 'ks.client_id = cm.client_master_id', 'LEFT');
        $this->db->join('master_specialities_V1 ms', 'ks.speciality_id = ms.master_specialities_id', 'LEFT');
        $this->db->join('knwlg_sessions_vendor ksv', 'ksv.session_id = ks.session_id', 'LEFT');

        // Add WHERE conditions with proper indexing
        $this->db->where('ks.session_id', $session_id);
        $this->db->where('ks.status', 3);

        // Add index hints if needed for large tables
        // $this->db->_protect_identifiers = FALSE;
        // $this->db->from('knwlg_sessions_V1 ks USE INDEX (PRIMARY)');
        // $this->db->_protect_identifiers = TRUE;

        // Execute query
        $query = $this->db->get();
        $result = $query->result();

        // Cache the result for 15 minutes (900 seconds)
        if (isset($this->myredis) && !empty($result)) {
            $this->myredis->setex($cache_key, 900, $result);
        }

        return $result;
    }
    /**
     * @param $user_master_id
     * @param $session_id
     * @param $user_client_ids
     * @param $user_group_ids
     * @return array
     */

    public function single_upcomingmastersession_details_new(
        $user_master_id,
        $session_id,
        $user_client_ids,
        $user_group_ids
    ) {
        //$usr_env = get_user_env($user_master_id);
        $env = get_user_env_id($user_master_id);

        $envStatus = "";
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")";
            } else {
                $envStatus = "AND cTenv.env = " . $env . "";
            }
        } else {
            $envStatus = "";
        }

        $key_locked = get_user_package($user_master_id, 'session');
        $cachename = "session_detail_" . $session_id;
        // checking for internal user
        $user_type = get_master_user_type_id($user_master_id);

        if (!$user_type) {
            // garbage value present in the place of user_type_id
            return;
        }

        // Determine status condition based on user type
        $statusCondition = ($user_type == 5) ? "AND ks.status IN(3,5)" : "AND ks.status = 3";

        // Build the unified query with conditional status
        $sql = "WITH 
            session_doctors AS (
                SELECT 
                    ks.session_id,
                    GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_soctor_id,
                    GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
                    GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') AS speciality,
                    GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
                    GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images
                FROM knwlg_sessions_V1 AS ks
                LEFT JOIN knwlg_sessions_doctors AS sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                WHERE ks.session_id = {$session_id}
                GROUP BY ks.session_id
            ),
            session_specialities AS (
                SELECT 
                    ks.session_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                FROM knwlg_sessions_V1 AS ks
                LEFT JOIN master_specialities_V1 AS ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
                WHERE ks.session_id = {$session_id}
                GROUP BY ks.session_id
            ),
            session_sponsors AS (
                SELECT 
                    ks.session_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                FROM knwlg_sessions_V1 AS ks
                LEFT JOIN session_to_sponsor AS sTspon ON sTspon.session_id = ks.session_id
                LEFT JOIN client_master AS clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                WHERE ks.session_id = {$session_id}
                GROUP BY ks.session_id
            ),
            session_participants AS (
                SELECT 
                    ks.session_id,
                    GROUP_CONCAT(ksp.participant_id) AS PartName,
                    GROUP_CONCAT(DISTINCT ksp.participant_id) AS users,
                    GROUP_CONCAT(ksp.is_attended) AS IS_ATTENDED,
                    MAX(ksp.room_link) AS room_link
                FROM knwlg_sessions_V1 AS ks
                LEFT JOIN knwlg_sessions_participant AS ksp ON ksp.knwlg_sessions_id = ks.session_id
                WHERE ks.session_id = {$session_id}
                GROUP BY ks.session_id
            )
            SELECT
                ks.session_id,
                ks.*,
                sd.*,
                cln.client_name,
                cln.client_logo,
                ss.specialities_name,
                ss.specialities_ids_and_names,
                msct.category_name,
                msct.category_logo,
                ktci.cover_image1,
                ktci.cover_image2,
                ktci.cover_image3,
                ktci.cover_image4,
                ktci.cover_image5,
                sd.document_path,
                sd.comment,
                ksd.knwlg_sessions_docs_id,
                ksd.document_path,
                ksd.comment,
                cTenv.price,
                uTpyCont.status AS user_contnet_payment_status,
                mst.status_name,
                kv.meta_data,
                sdoc.session_soctor_id,
                sdoc.doctor_name,
                sdoc.speciality,
                spon.sponsor,
                spon.sponsor_logo,
                sdoc.profile,
                sdoc.profile_images,
                sp.PartName,
                sp.users,
                kstc.multidaysession_id AS parent_session,
                sp.IS_ATTENDED,
                sp.room_link,
                (ks.total_buffer + ks.total_seats) AS tot_seat,
                ksv.vendor_id,
                ksv.video_embed_src,
                ksv.room_id,
                ksv.vouchpro_url,
                ksv.go_to_meeting_url,
                ksv.landing_page_url,
                ksv.session_cast_type,
                ksjt.added_on AS last_join_date
            FROM knwlg_sessions_V1 AS ks
            LEFT JOIN session_doctors sdoc ON sdoc.session_id = ks.session_id
            LEFT JOIN session_specialities ss ON ss.session_id = ks.session_id
            LEFT JOIN session_sponsors spon ON spon.session_id = ks.session_id
            LEFT JOIN session_participants sp ON sp.session_id = ks.session_id
            LEFT JOIN client_master AS cln ON cln.client_master_id = ks.client_id
            LEFT JOIN knwlg_sessions_vendor AS ksv ON ksv.session_id = ks.session_id
            LEFT JOIN master_vendor AS kv ON kv.vendor_id = ksv.vendor_id
            LEFT JOIN master_session_category AS msct ON msct.mastersession_category_id = ks.category_id
            LEFT JOIN knwlg_sessions_documents AS sd ON sd.knwlg_sessions_id = ks.session_id
            LEFT JOIN knwlg_sessions_documents AS ksd ON ksd.knwlg_sessions_id = ks.session_id
            LEFT JOIN master_session_status AS mst ON mst.master_session_status_id = ks.session_status
            LEFT JOIN content_to_env AS cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
            LEFT JOIN payment_user_to_content AS uTpyCont ON uTpyCont.type_id = ks.session_id AND uTpyCont.type = 2 AND uTpyCont.user_master_id = {$user_master_id}
            LEFT JOIN knwlg_session_join_tracking AS ksjt ON ksjt.session_id = ks.session_id AND ksjt.user_master_id = {$user_master_id}
            LEFT JOIN knwlg_session_to_child AS kstc ON kstc.childsession_id = ks.session_id
            LEFT JOIN session_to_cover_image AS ktci ON ktci.session_id = ks.session_id
            WHERE
                ks.session_id = {$session_id } 
                {$envStatus}
                {$statusCondition} 
                AND ks.privacy_status IN(0,1,2)";

        $query = $this->db->query($sql);
        $result = $query->result_array();

        // The rest of the function remains the same
        if ($user_master_id != '') {
            $participant_id = "AND participant_id =" . $user_master_id . " ";
        } else {
            $participant_id = "";
        }

        // Process the results
        $i = 0;
        $entities = array();


        foreach ($result as $row) {

            $entities[$i]['is_multiday_session'] = $row['is_multiday_session'];
            $entities[$i]['env'] = $row['env'];
            $entities[$i]['parent_session'] = $row['parent_session'];


            $sql_check = "SELECT
            knwlg_sessions_participant_details.question,
            knwlg_sessions_participant_details.upload_documents,
            knwlg_sessions_participant.knwlg_sessions_participant_id,
            knwlg_sessions_participant.room_link,
            knwlg_sessions_participant.is_attended as IS_ATTENDED,
            ksjt.added_on as last_join_date
            FROM knwlg_sessions_participant
            LEFT JOIN knwlg_sessions_participant_details ON knwlg_sessions_participant_details.sessions_participant_id=knwlg_sessions_participant.knwlg_sessions_participant_id
            LEFT JOIN knwlg_session_join_tracking as ksjt ON ksjt.session_id = knwlg_sessions_participant.knwlg_sessions_id and ksjt.user_master_id = $user_master_id
            WHERE
            knwlg_sessions_participant.status=3
            AND knwlg_sessions_participant.participant_type='member'
            AND knwlg_sessions_participant.knwlg_sessions_id=" . $row['session_id'] . " " . $participant_id;
            $query_check = $this->db->query($sql_check);
            //echo $CI->db->last_query(); exit();
            $result_check = $query_check->row_array();
            if (!empty($result_check)) {
                $entities[$i]['is_booked'] = true;
                $entities[$i]['room_link'] = $result_check['room_link'];
                $entities[$i]['asked_query'] = $result_check['question'];
                $entities[$i]['upload_documents'] = $result_check['upload_documents'];
                $entities[$i]['my_participant_id'] = $result_check['knwlg_sessions_participant_id'];
                $entities[$i]['last_join_date'] = $result_check['last_join_date'];
            } else {
                $entities[$i]['is_booked'] = false;
                $entities[$i]['room_link'] = '';
                $entities[$i]['asked_query'] = "";
                $entities[$i]['my_participant_id'] = "";
                $entities[$i]['last_join_date'] = "";
            }
            $entities[$i]['recorded_video_id'] = '';
            $entities[$i]['channel_details'] = [];
            $sql_check_rating = "SELECT
            *
            FROM knwlg_session_rating_reviews
            WHERE session_id=" . $row['session_id'] . "
            AND user_master_id=" . $user_master_id . "";
            $query_check_rating = $this->db->query($sql_check_rating);
            $result_check_rating_array = $query_check_rating->row_array();

            if (!empty($result_check_rating_array)) {
                $entities[$i]['review'] = $result_check_rating_array['review'];
                $entities[$i]['rating'] = $result_check_rating_array['rating'];
                $entities[$i]['is_rating_review'] = true;
            } else {
                $entities[$i]['is_rating_review'] = false;
            }

            $sql_check_recording = "SELECT
            *
            FROM knwlg_session_recording_request
            WHERE session_id=" . $row['session_id'] . "
            AND user_master_id=" . $user_master_id . "";
            $query_check_recording = $this->db->query($sql_check_recording);
            //echo $this->db->last_query(); exit();
            $result_check_recording_array = $query_check_recording->row_array();
            // print_r($result_check); exit();
            if (!empty($result_check_recording_array)) {
                // echo $result_check['knwlg_sessions_participant_id']; exit();
                //$result_check['knwlg_sessions_participant_id']
                $entities[$i]['recording_type'] = $result_check_recording_array['recording_type'];
                $entities[$i]['is_sent_recording'] = true;
            } else {
                $entities[$i]['is_sent_recording'] = false;
            }


            // Get related surveys using the optimized function
            $vxPoll = $this->getRelatedSurveys($row['session_id'], $user_master_id, $env);

            $entities[$i]['is_available'] = (strtotime($row['start_datetime']) < time()) ? false : true;
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $tempcover[] = change_img_src($row['cover_image1']);
            $tempcover[] = change_img_src($row['cover_image2']);
            $tempcover[] = change_img_src($row['cover_image3']);
            $tempcover[] = change_img_src($row['cover_image4']);
            $tempcover[] = change_img_src($row['cover_image5']);
            if (!empty($tempcover)) {
                $coverimageArry = $tempcover;
            } else {
                $coverImg = base_url() . "uploads/sessionBgDefault.jpeg";
                $cov_img = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
                $tempcover = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
                $coverimageArry = explode(",", $tempcover);
            }
            $entities[$i]['cover_image'] = change_img_src($coverimageArry);
            //$entities[$i]['cover_image'] = $row['cover_image'];
            $entities[$i]['session_topic'] = $row['session_topic'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['speciality_id'] = $row['speciality_id'];
            $entities[$i]['session_description'] = $row['session_description'];
            $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            $entities[$i]['vendor_meta_data'] = $row['meta_data'];
            $entities[$i]['status_name'] = $row['status_name'];
            $entities[$i]['vendor_id'] = (string)$row['vendor_id'];
            $entities[$i]['room_id'] = $row['room_id'];
            $entities[$i]['vouchpro_url'] = $row['vouchpro_url'];
            $entities[$i]['go_to_meeting_url'] = $row['go_to_meeting_url'];
            $entities[$i]['landing_page_url'] = $row['landing_page_url'];
            $entities[$i]['video_embed_src'] = $row['video_embed_src'];
            $entities[$i]['session_cast_type'] = $row['session_cast_type'];
            $sponserentity = array();
            $sponsorLogoArry = explode(",", $row['sponsor']);
            $sponsorNameArry = explode(",", $row['sponsor_logo']);
            $ii = 0;
            foreach ($sponsorLogoArry as $singlelogo) {
                if ($singlelogo != "") {
                    $sponserentity[$ii]['sponsor_name'] = $singlelogo;
                    $sponserentity[$ii]['sponsor_logo'] = change_img_src($sponsorNameArry[$ii]);
                }
                $ii++;
            }
            $entities[$i]["all_sponsor"] = $sponserentity;
            $entities[$i]['sponsor_entity'] = $sponserentity;
            /**
             * new sponsor logic
             */
            $sponsorLogoArry = explode(",", $row['sponsor_logo']);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = $valueSponor;
                    }
                }
            } else {
                if ($row['sponsor_logo']) {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($row['document_path'] != "" || $row['document_path'] != null) {
                $entities[$i]['file_size'] = round((filesize('./uploads/mastersession_docs/' . $row['document_path'] . '') / 1024)) . "Kb";
                $entities[$i]['document_path_exact_file_name'] = $row['document_path'];
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
                $entities[$i]['extension_logo_path'] = base_url() . "themes/front/images/" . get_logo_by_file_extension($row['document_path']);
            } else {
                $entities[$i]['document_path_exact_file_name'] = "";
                $entities[$i]['document_path'] = "";
                $entities[$i]['file_size'] = "";
                $entities[$i]['extension_logo_path'] = "";
            }
            if ($row['comment'] != "" || $row['comment'] != null) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['category_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['category_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['start_datetime_old'] = $row['start_datetime'];
            $entities[$i]['timezone'] = date_default_timezone_get();
            $entities[$i]['start_datetime'] = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $entities[$i]['end_datetime'] = (new DateTime($row['end_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $entities[$i]['start_datetimex'] = strtotime($row['start_datetime']);
            $entities[$i]['now_datetimex'] = time();
            $start_time = $row['start_datetime'];
            $date = new DateTime($start_time);
            //$start_time = date("g:i A", strtotime($start_time));
            $now = new DateTime();
            $diff = date_diff($date, $now);
            $entities[$i]['days_remaining'] = abs($diff->format("%R%a")) + 1;
            $end_time = $row['end_datetime'];
            $end_time_display = date("g:i A", strtotime($entities[$i]['end_datetime']));
            $entities[$i]['display_time_format'] = $entities[$i]['start_datetime'] . "-" . $end_time_display;
            $post_time = $row['start_datetime'];
            $phpdate = strtotime($post_time);
            $mysqldate = date("D, j M 'y  ", $phpdate);
            //$entities[$i]['display_date_format'] = $mysqldate;
            $dateTime_start = new DateTime($row['start_datetime']);
            $entities[$i]['start_displaydate'] =  $dateTime_start->format('d M, h:i A');
            $dateTime_end = new DateTime($row['end_datetime']);
            $entities[$i]['end_displaydate'] =  $dateTime_end->format('d M, h:i A');
            $entities[$i]['display_date_format'] = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format("D, j M'y ");
            $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            $post_date = $row['added_on'];
            $start_date = $row['start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            $entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $this->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            $entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }
            $entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }
            $cpt_flag = 0;
            $on_of_booking_button = 0;
            $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
            if ($row['users'] == null) {
                $total_original_booking = 0;
            } else {
                $users_array = array();
                $users_array = explode(",", $row['users']);
                $total_original_booking = count($users_array);
            }
            if ($total_original_booking < $row['total_seats']) {
                $total_booking = $total_original_booking;
            }
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $minus_flag = $total_after_buffer - $total_original_booking;
                $total_booking = ($row['total_seats']) - 1;
            }
            if ($total_original_booking >= ($total_after_buffer)) {
                $total_booking = $row['total_seats'];
                $on_of_booking_button = 1;
                $cpt_flag = 1;
            }
            if ($total_booking > 0) {
                $available_percent = ($total_booking / $row['total_seats']) * 100;
            } else {
                $available_percent = 0;
            }
            $available_percent = round($available_percent);
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $total_booking;
            } else {
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
            }
            $perc = $available_percent;
            if ($cpt_flag == 0) {
                $entities[$i]['percentage'] = ceil($perc);
            } else {
                $entities[$i]['percentage'] = ceil($perc);
            }
            $color = get_progress_color($perc);
            $entities[$i]['color_profress_bar'] = $color;
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['is_share'] = $row['is_share'];

            $end_time = (new DateTime($row['end_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('g:i A');
            //$start_time = $row['start_datetime'];
            $start_time = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('g:i A');
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            $entities[$i]['deeplink'] = ($env == 'GL') ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0);
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['category_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            $session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                #print_r($single_doctor);
                $var = session_doc_detail($single_doctor);
                # print_r($var);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if ($image) {
                    if (stripos($image, "https://") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg; //"uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                } else {
                    $logic_image = docimg;
                }
                #print_r($var);
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $var[0]['sessions_doctors_id'];
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $ses_doc_det_array[$inc_pp]['description'] = $var[0]['description'];
                $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                $inc_pp++;
            }
            $entities[$i]['sponsor_id'] = $row['sponsor_id'];
            $sponsor_array = explode(",", $row['sponsor_id']);
            $sponsor_det_array = array();
            if (count($sponsor_array) > 1) {
                $inc_spp = 0;
                foreach ($sponsor_array as $single_sponsor) {
                    $var = sponsor_detail($single_sponsor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image = '';
                    }
                    $sponsor_det_array[$inc_spp]['sponsor_name'] = $var[0]['client_name'];
                    $sponsor_det_array[$inc_spp]['sponsor_logo'] = change_img_src($logic_image);
                    $sponsor_det_array[$inc_spp]['sponsor_id'] = $var[0]['client_master_id'];
                    $inc_spp++;
                }
            } else {
                if ($row['sponsor_id']) {
                    $var = sponsor_detail($row['sponsor_id']);
                    $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image = ''; //
                    }
                    $sponsor_det_array['sponsor_name'] = $var[0]['client_name'];
                    $sponsor_det_array['sponsor_logo'] = change_img_src($logic_image);
                    $sponsor_det_array['sponsor_id'] = $var[0]['client_master_id'];
                }
            }
            //$row['sessions_question'];
            if ($row['sessions_question'] != '') {
                $qu_val = explode("#", $row['sessions_question']);
                $queries = $qu_val;
            } else {
                $queries = array();
            }
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $entities[$i]['sponsor_entities'] = $sponserentity;
            $entities[$i]['is_locked'] = $key_locked;
            $entities[$i]['price'] = $row['price'];
            $entities[$i]['user_content_payment'] = get_user_content_status($row['session_id'], 2, $user_master_id);
            $entities[$i]['redirection_url'] = (!empty($row['exitroute'])) ? $row['exitroute'] : null;
            $entities[$i]['session_queries'] = $queries;
            $entities[$i]['is_share'] = get_a_content_is_share_status($row['session_id'], '2');
            $entities[$i]['cpddetail'] = $this->getcpddetails($row['session_id']);
            $entities[$i]["channel"] = $this->getchannel($session_id, $user_master_id);
            $entities[$i]['survey'] = $vxPoll;
            $entities[$i]['disclaimer'] = disclaimer('session');
            $campaign  = getContentCampiagn($user_master_id, $user_client_ids, $session_id, 'session');
            $entities[$i]['display_banner'] = $campaign['banner_dispaly'];
            $entities[$i]['campaign_data'] = $campaign['creative_data'];
            $i++;
        }
        // ******* getting user certificates of that against taht session id ******* //
        $userCertData = $this->getUserCertificate($user_master_id, $session_id);
        $entities[0]["user_certificates"] = $userCertData;
        // ******* getting clinical video tagged against taht session id ******* //
        $taggedVideoData = $this->getASessionTaggedCinicalVideos($user_master_id, $session_id);
        $entities[0]["videos"] = $taggedVideoData;

        return $entities;
        // print_r($entities); exit();

    }



    private function getRelatedSurveys(
        $type_id,
        $user_master_id,
        $env
    ) {
        // Use CTEs for better performance
        $sql = "WITH CompletedSurveys AS (
                SELECT 
                    survey_id
                FROM 
                    survey_user_answer
                WHERE 
                    user_master_id = {$user_master_id}
                UNION
                SELECT 
                    survey_id
                FROM 
                    survey_user_incomplete_answer
                WHERE 
                    status = 3
                    AND user_master_id = {$user_master_id}
            ),
            FilteredSurveys AS (
                SELECT
                    sv.survey_id
                FROM 
                    survey sv
                JOIN 
                    survey_to_session as stm ON stm.survey_id = sv.survey_id
                WHERE 
                    sv.status = 3
                    AND DATE(sv.publishing_date) <= CURDATE()
                    AND stm.session_id = {$type_id}
                    AND sv.survey_id NOT IN (SELECT survey_id FROM CompletedSurveys)
            ),
            SpecialitiesAgg AS (
                SELECT 
                    svts.survey_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name
                FROM 
                    survey_to_speciality as svts
                JOIN 
                    master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                WHERE 
                    svts.survey_id IN (SELECT survey_id FROM FilteredSurveys)
                GROUP BY 
                    svts.survey_id
            ),
            SponsorsAgg AS (
                SELECT 
                    suvTspon.survey_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM 
                    survey_to_sponsor as suvTspon
                JOIN 
                    client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                WHERE 
                    suvTspon.survey_id IN (SELECT survey_id FROM FilteredSurveys)
                GROUP BY 
                    suvTspon.survey_id
            )
            SELECT
                sv.survey_id,
                sv.category,
                sv.survey_points,
                sv.survey_title,
                sv.deeplink,
                sv.gl_deeplink,
                sv.survey_description,
                sv.image,
                sv.publishing_date,
                svd.data,
                cln.client_name,
                cln.client_logo,
                spec.specialities_name,
                spon.sponsor,
                spon.sponsor_logo
            FROM 
                FilteredSurveys fs
            JOIN 
                survey sv ON sv.survey_id = fs.survey_id
            JOIN 
                survey_detail as svd ON svd.survey_id = sv.survey_id
            JOIN 
                client_master as cln ON cln.client_master_id = sv.client_id
            LEFT JOIN 
                SpecialitiesAgg spec ON spec.survey_id = sv.survey_id
            LEFT JOIN 
                SponsorsAgg spon ON spon.survey_id = sv.survey_id";

        $query = $this->db->query($sql);
        $resultPoll = $query->result();
        $vxPoll = array();

        foreach ($resultPoll as $valSurvey) {
            $dataArry = unserialize($valSurvey->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);

            // Process sponsor data
            $allsponsor = array();
            $sponsorname = explode(",", $valSurvey->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);
            $sponsorLogomix = [];

            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => change_img_src($valueSponor));
                        $sp++;
                    }
                }
            } elseif ($valSurvey->sponsor_logo != '') {
                $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                $allsponsor[] = array('name' => $valSurvey->sponsor, "logo" => change_img_src($valSurvey->sponsor_logo));
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            $vxPoll[] = array(
                "survey_id" => $valSurvey->survey_id,
                "category" => $valSurvey->category,
                "point" => $valSurvey->survey_points,
                "json_data" => $str,
                "survey_title" => $valSurvey->survey_title,
                "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0),
                "survey_description" => substr($valSurvey->survey_description, 0, 150),
                "image" => change_img_src($valSurvey->image),
                "specialities_name" => $valSurvey->specialities_name,
                "sponsor_name" => $valSurvey->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "publishing_date" => $valSurvey->publishing_date,
            );

        }

        return $vxPoll;
    }

    public function getUserCertificate($user_master_id, $session_id)
    {
        $cacheKey = "session_user_cert_".$user_master_id."_".$session_id;
        if ($this->myredis->exists($cacheKey)) {
            $returnData = $this->myredis->get($cacheKey);
            return $returnData ;
        }
        $sql = "SELECT
                  *
                FROM
                    clircert_certificate_master
                WHERE
                     user_master_id = {$user_master_id}
                     AND type  ='session_participation'
                     AND type_id = {$session_id}
        ";
        $result = $this->db->query($sql);
        if ($result->num_rows() > 0) {
            $certificateData = $result->result();
            foreach ($certificateData as $key => $val) {
                $vx[] = array(
                     "type_id" => $val->type_id,
                     "type" => 'certificate',
                     "cert_type" => $val->type,
                     // "type_id" => $val->type_id,
                     "date" => $val->certificate_issue_date,//date(' jS F y', strtotime($val->certificate_issue_date)),
                     "title" => html_entity_decode(strip_tags($val->certificate_title)),
                     "description" => html_entity_decode(strip_tags(substr($val->certificate_description, 0, 300))),
                     "description_short" => html_entity_decode(strip_tags(substr($val->certificate_description, 0, 300))),
                     "image" => change_img_src($val->image),
                     "file" => $val->file,
                     "year" => $val->year,
                     "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                     "disclaimer" => disclaimer('knowledge')
                     //"disclaimer" => 'All scientific content on the platform is provided for general medical education purposes meant for registered medical practitioners only. The content is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. In no event will CLIRNET be liable for any decision made or action taken in reliance upon the information provided through this content.',
                 );
            }
        }
        if (!empty($vx)) {
            $this->myredis->set($cacheKey, $vx, 60 * 60 * 24);
        }
        return $vx ;
    }
    public function getASessionTaggedCinicalVideos($user_master_id, $session_id)
    {
        $cacheKey = "session_clinical_vid_".$session_id."_".$user_master_id ;
        if ($this->myredis->exists($cacheKey)) {
            return $this->myredis->get($cacheKey);

        }
        $sql = "SELECT
                    video_archive_id
                FROM
                    knwlg_video_archive
                WHERE
                   video_archive_session_id =  {$session_id}
        ";
        $query = $this->db->query($sql);
        if ($query->num_rows() > 0) {
            $result = $query->result();
            $this->load->model("Knwlg_model");
            foreach ($result as $key => $val) {
                $clinicalVideoData = $this->Knwlg_model->detail_archiveVideo($val->video_archive_id, $user_master_id);
                unset($clinicalVideoData["campaign_data"]);
                unset($clinicalVideoData["display_banner"]);
                $vx[] = $clinicalVideoData ;
            }
        }
        if (!empty($vx)) {
            $this->myredis->set($cacheKey, $vx, 60 * 60 * 24);
        }
        return $vx;
    }
    /**
     * @param $user_id
     * @param $session_id
     * @return bool
     */
    public function check_recording_request_exists($user_id, $session_id)
    {
        $sql = "SELECT request_id FROM knwlg_session_recording_request WHERE user_master_id=" . $user_id . " AND session_id=" . $session_id . "";
        //echo $sql; exit;
        //FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
        $query = $this->db->query($sql);
        //echo $this->db->last_query(); exit();
        $result = $query->result_array();
        if (empty($result)) {
            return false;
        } else {
            return true;
        }
    }
    /**
     * @param $booked_id
     * @param $user_master_id
     * @param $from
     * @param $to
     * @param $user_client_ids
     * @param $user_group_ids
     * @return array
     */
    public function all_featuredmastersession_details(
        $booked_id,
        $user_master_id,
        $from,
        $to,
        $user_client_ids,
        $user_group_ids,
        $cme,
        $spids
    ) {

        // echo $from.'to'.$to;
        // exit;

        //echo 'user_master_id:   '.$user_master_id;


        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")";
            } else {
                $envStatus = "AND cTenv.env = " . $env . "";
            }
        } else {
            $envStatus = "";
        }


        // $booked_ids = $booked_id;
        // $getchildsession = $this->get_all_childsession();
        // if (!empty($getchildsession)) {
        //     //print_r( $getchildsession); exit;
        //     $cids = implode(",", (array)$getchildsession['sessions']);
        //     //print_r($cids);
        //     $childsessionids = " and ks.session_id NOT IN (" . $cids . ")";
        // } else {
        //     $childsessionids = "";
        // }
        // foreach ($booked_ids as $sing) {
        //     $new_arr[] = $sing->knwlg_sessions_id;
        // }
        // $res_arr = implode(',', $new_arr);
        // if ($res_arr) {
        //     $sqlStr = "and ( (ks.session_id  NOT IN(" . $res_arr . ") AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "') ) ";
        // } else {
        //     $sqlStr = "and (ks.start_datetime >= '" . date("Y-m-d H:i:s") . "')";
        // }



        $global_status = "ks.status = 3";
        $session_status = "AND ( ks.session_status = 1 OR ks.session_status = 4 )";
        $privacy_status = "AND ks.privacy_status = 0 ";
        $session_end_datetime = "";
        $session_start_datetime = "AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "' ";
        $optionalQuery = "AND ks.display_in_dashboard = 1 AND ks.is_featured = 1";
        $orderBydate = "ks.start_datetime ASC";
        $bookedStatus = false;



        $result = $this->getSessionList(
            $session_start_datetime,
            $optionalQuery,
            $envStatus,
            $childsessionids,
            $from,
            $to,
            $user_master_id,
            $global_status,
            $session_status,
            $privacy_status,
            $session_end_datetime,
            $orderBydate,
            $bookedStatus
        );


        $key_locked = get_user_package($user_master_id, 'session');

        //print_r($result); exit();
        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            $entities[$i]['participant_id'] = $row['knwlg_sessions_participant_id'];
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $entities[$i]['session_topic'] = $row['session_topic'];
            $entities[$i]['is_share'] = $row['is_share'];
            $entities[$i]['vault'] = ($row['vault'] != "") ? $row['vault'] : 0;
            $coverImg = base_url() . "uploads/sessionBgDefault.jpeg";
            $cov_img = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
            $coverimageArry = explode(",", $coverImg);
            $entities[$i]['cover_image'] = change_img_src($cov_img);
            $entities[$i]['cover_image1'] = change_img_src($row['cover_image1']);
            $entities[$i]['cover_image2'] = change_img_src($row['cover_image2']);
            $entities[$i]['cover_image3'] = change_img_src($row['cover_image3']);
            $entities[$i]['cover_image4'] = change_img_src($row['cover_image4']);
            $entities[$i]['cover_image5'] = change_img_src($row['cover_image5']);
            $entities[$i]['session_description'] = strip_tags($row['session_description']);
            //$entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            // $entities[$i]['client_id'] = $row['client_id'];
            $entities[$i]['deeplink'] = ($env == 'GL') ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0);
            //$row['deeplink'];
            // $entities[$i]['client_name'] = $row['client_name'];
            $entities[$i]['color'] = ($row['color'] != '') ? $row['color'] : '#eb34e5';
            $entities[$i]["is_multiday_session"] = $row['is_multiday_session'];
            $entities[$i]['cpddetail'] = $this->getcpddetails($row['session_id']);
            /**
             * new sponsor logic
             */
            $sponserentity = array();
            $allsponsor = array();
            $sponsorLogoArry = explode(",", $row['sponsor']);
            $sponsorNameArry = explode(",", $row['sponsor_logo']);
            $ii = 0;
            $sp = 0;
            foreach ($sponsorLogoArry as $singlelogo) {
                if ($singlelogo != "") {
                    $sponserentity[$ii]['sponsor_logo'] = $sponsorNameArry[$ii];
                    $sponserentity[$ii]['sponsor_name'] = change_img_src($singlelogo);
                }
                $ii++;
            }
            $entities[$i]['all_sponsor'] = $sponserentity;
            $entities[$i]['sponsor_entity'] = $sponserentity;
            //print_R($entities); exit;
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        // if full path exist
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = base_url('uploads/logo/') . $valueSponor;
                        }
                        $allsponsor[] = array('name' => $sponsorNameArry[$sp], "logo" => $valueSponor);
                        $sp++;
                    }
                }
            } else {
                if ($row['sponsor_logo'] != "") {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                    $allsponsor[] = array('name' => $row['sponsor'], "logo" => $row['sponsor_logo']);
                }
            }
            //print_r($sponsorLogomix); exit();
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
            $entities[$i]['all_sponsor'] = $allsponsor;
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            /*if ($row['document_path'] != "" || $row['document_path'] != NULL) {
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
            } else {
                $entities[$i]['document_path'] = "";
            }*/
            /*if ($row['comment'] != "" || $row['comment'] != NULL) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }*/
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['category_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['start_datetime'] = $row['start_datetime'];
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            //$question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            $post_date = $row['added_on'];
            $start_date = $row['start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            //$entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            /*if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }*/
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $this->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            /*$entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }*/
            //$entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            /*if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }*/
            $cpt_flag = 0;
            $on_of_booking_button = 0;
            $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
            if ($row['users'] == null) {
                $total_original_booking = 0;
            } else {
                $users_array = array();
                $users_array = explode(",", $row['users']);
                $total_original_booking = count($users_array);
            }
            if ($total_original_booking < $row['total_seats']) {
                $total_booking = $total_original_booking;
            }
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $minus_flag = $total_after_buffer - $total_original_booking;
                $total_booking = ($row['total_seats']) - 1;
            }
            if ($total_original_booking >= ($total_after_buffer)) {
                $total_booking = $row['total_seats'];
                $on_of_booking_button = 1;
                $cpt_flag = 1;
            }
            if ($total_booking > 0) {
                $available_percent = ($total_booking / $row['total_seats']) * 100;
            } else {
                $available_percent = 0;
            }
            $available_percent = round($available_percent);
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $total_booking;
            } else {
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
            }
            $perc = $available_percent;
            if ($cpt_flag == 0) {
                $entities[$i]['percentage'] = $perc;
            } else {
                $entities[$i]['percentage'] = $perc;
            }
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['is_share'] = $row['is_share'];

            $entities[$i]['start_datetime_old'] = $row['start_datetime'];
            $entities[$i]['end_datetime_old'] = $row['end_datetime'];
            $entities[$i]['start_datetime'] = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $entities[$i]['end_datetime'] = (new DateTime($row['end_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['category_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            if ($row['session_doctor_id'] != '') {
                $session_doc_array = explode(",", $row['session_doctor_id']);
            } else {
                //print_r($getchildsession['session_doctors']);
                $session_doc_array = explode(",", $getchildsession['session_doctors'][$row['session_id']]);
                //print_r($session_doc_array); exit;
            }
            #$session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            $store_total_doctors[$row['session_id']] = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                /*if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                    $logic_image = base_url() . "uploads/docimg/" . $image;
                } else {
                    $logic_image = base_url() . "uploads/docimg/no-image.png";
                }*/
                if ($image) {
                    /////============================== updated by  ramanath  14-5-21
                    if (stripos($image, "https://") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg; //"uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                    //=======================================
                    //  $logic_image_path = "uploads/docimg/" . $image;
                    //  $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    //  $logic_image = $imgPr;
                } else {
                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $k = array_keys((array)$getchildsession['sessioncount']);
            //print_r(in_array($val->session_id,$k));
            if (in_array($row['session_id'], $k)) {
                $total_multiday_session = $getchildsession['sessioncount'][$row['session_id']];
                //print_R($total_multiday_session."/n");
            } else {
                $total_multiday_session = 0;
            }
            $keyvalue_1 = array_keys((array)$getchildsession['doctorcount']);
            if (in_array($row['session_id'], $keyvalue_1)) {
                $total_doctors = $getchildsession['doctorcount'][$row['session_id']];
            } else {
                $total_doctors = $store_total_doctors[$row['session_id']];
            }
            $datetime1 = new DateTime(date('Y-m-d H:i:s'));
            $datetime2 = new DateTime($row['end_datetime']);
            $difference = $datetime1->diff($datetime2);
            $entities[$i]["total_session"] = $total_multiday_session;
            $entities[$i]["total_doctors"] = $total_doctors;
            $entities[$i]["is_locked"] = $key_locked;
            $entities[$i]["price"] = $row['price'];
            $entities[$i]["user_content_payment"] = get_user_content_status($row['session_id'], 2, $user_master_id);
            $entities[$i]["total_days"] = $difference->d;
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $entities[$i]['cpddetail'] = $this->getcpddetails($row['session_id']);
            $i++;
        }
        return $entities;
        //print_r($entities); exit();
    }
    public function get_all_childsession()
    {
        $ids = array();
        $this->db->select(
            "kstc.multidaysession_id,
            GROUP_CONCAT(DISTINCT kstc.childsession_id SEPARATOR ',') as childsession_id,
            GROUP_CONCAT(DISTINCT sts.sessions_doctors_id SEPARATOR ',') as session_soctor_id"
        );
        $this->db->from('knwlg_session_to_child as kstc');
        $this->db->join('session_to_sessiondoctor as sts', 'sts.session_id = kstc.childsession_id', 'left');
        $this->db->group_by('kstc.multidaysession_id');
        $query = $this->db->get();
        //print_r($this->db->last_query()); exit;
        $ids = array();
        $getdoctors = array();
        if (($query) && ($query->num_rows() > 0)) {
            $session_ids = $query->result();
            foreach ($session_ids as $key => $value) {
                // print_R($value);
                $totalids = explode(',', $value->childsession_id);
                $getids[$value->multidaysession_id] = count(explode(',', $value->childsession_id));
                //$value->childsession_id;
                $getdoctorcount[$value->multidaysession_id] = count(explode(',', $value->session_soctor_id));
                $getdoctors[$value->multidaysession_id] = $value->session_soctor_id;
                $ids = array_merge($ids, $totalids);
                //$value->childsession_id;
                // $countmultidayminisession[$value->multidaysession_id] = sizeof($getids[$value->multidaysession_id]);
            }
        }
        $response['doctorcount'] = $getdoctorcount;
        $response['sessioncount'] = $getids; //$countmultidayminisession;
        $response['sessions'] = $ids;
        $response['session_doctors'] = $getdoctors;
        //print_R($response); exit;
        return $response;
    }
    public function all_upcomingmastersession_detailswithoutcategoryfilter(
        $booked_id,
        $user_master_id,
        $from,
        $to,
        $user_client_ids,
        $user_group_ids
    ) {
        //echo 'dddd'; exit;

        $global_status = "ks.status = 3";
        $session_status = "AND ( ks.session_status=1 OR ks.session_status=4 )";
        $privacy_status = "AND ks.privacy_status = 0 ";
        $session_end_datetime = "AND ks.end_datetime >= '" . date("Y-m-d H:i:s") . "' ";
        $session_start_datetime = "AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "' ";
        $optionalQuery = "";
        $orderBydate = "ks.start_datetime ASC";
        $bookedStatus = false;



        $result = $this->getSessionList(
            $session_start_datetime,
            $optionalQuery,
            $envStatus,
            $childsessionids,
            $from,
            $to,
            $user_master_id,
            $global_status,
            $session_status,
            $privacy_status,
            $session_end_datetime,
            $orderBydate,
            $bookedStatus
        );


        $key_locked = get_user_package($user_master_id, 'session');

        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            //$entities[$i]['participant_id'] = $row['participant_id'];
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $entities[$i]['session_topic'] = $row['session_topic'];
            $coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
            $cov_img = ($row['cover_image'] != '') ? change_img_src($row['cover_image']) : $coverImg;
            $coverimageArry = explode(",", $coverImg);
            $entities[$i]['cover_image'] = change_img_src($cov_img);
            $entities[$i]['session_description'] = strip_tags($row['session_description']);
            //$entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            $entities[$i]['client_id'] = $row['client_id'];
            $entities[$i]['deeplink'] = $row['deeplink'];
            $entities[$i]['client_name'] = $row['client_name'];
            $entities[$i]['color'] = ($row['color'] != '') ? $row['color'] : '#eb34e5';
            /**
             * new sponsor logic
             */
            $sponserentity = array();
            $sponsorLogoArry = explode(",", $row['sponsor']);
            $sponsorNameArry = explode(",", $row['sponsor_logo']);
            $ii = 0;
            foreach ($sponsorLogoArry as $singlelogo) {
                if ($singlelogo != "") {
                    $sponserentity[$ii]['sponsor_logo'] = $sponsorNameArry[$ii];
                    $sponserentity[$ii]['sponsor_name'] = change_img_src($singlelogo);
                }
                $ii++;
            }
            $entities[$i]['sponsor_entity'] = $sponserentity;
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        // if full path exist
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = base_url('uploads/logo/') . $valueSponor;
                        }
                    }
                }
            } else {
                /// echo $row['sponsor_logo';
                if ($row['sponsor_logo']) {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                }
            }
            //print_r($sponsorLogomix); exit();
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            //$entities[$i]['sponsor_logo'] =  $sponsorLogo;
            $entities[$i]['sponsor_logo'] = change_img_src($row['sponsor_logo']);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            /*if ($row['document_path'] != "" || $row['document_path'] != NULL) {
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
            } else {
                $entities[$i]['document_path'] = "";
            }*/
            /*if ($row['comment'] != "" || $row['comment'] != NULL) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }*/
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['category_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['start_datetime'] = $row['start_datetime'];
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            //$question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            $post_date = $row['added_on'];
            $start_date = $row['start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            //$entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            /*if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }*/
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $this->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            /*$entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }*/
            //$entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            /*if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }*/
            $cpt_flag = 0;
            $on_of_booking_button = 0;
            $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
            if ($row['users'] == null) {
                $total_original_booking = 0;
            } else {
                $users_array = array();
                $users_array = explode(",", $row['users']);
                $total_original_booking = count($users_array);
            }
            if ($total_original_booking < $row['total_seats']) {
                $total_booking = $total_original_booking;
            }
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $minus_flag = $total_after_buffer - $total_original_booking;
                $total_booking = ($row['total_seats']) - 1;
            }
            if ($total_original_booking >= ($total_after_buffer)) {
                $total_booking = $row['total_seats'];
                $on_of_booking_button = 1;
                $cpt_flag = 1;
            }
            if ($total_booking > 0) {
                $available_percent = ($total_booking / $row['total_seats']) * 100;
            } else {
                $available_percent = 0;
            }
            $available_percent = round($available_percent);
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $total_booking;
            } else {
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
            }
            $perc = $available_percent;
            if ($cpt_flag == 0) {
                $entities[$i]['percentage'] = $perc;
            } else {
                $entities[$i]['percentage'] = $perc;
            }
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['is_share'] = $row['is_share'];

            $entities[$i]['start_datetime'] = $row['start_datetime'];
            $entities[$i]['end_datetime'] = $row['end_datetime'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            $session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                /*if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                    $logic_image = base_url() . "uploads/docimg/" . $image;
                } else {
                    $logic_image = base_url() . "uploads/docimg/no-image.png";
                }*/
                if ($image != '') {
                    /////============================== updated by  ramanath  14-5-21
                    if (stripos($image, "https://") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg; //"uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                    //=======================================
                    //  $logic_image_path = "uploads/docimg/" . $image;
                    //  $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    //  $logic_image = $imgPr;
                } else {
                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $i++;
        }
        return $entities;
        //print_r($entities); exit();
    }
    /**
     * @param $user_master_id
     * @return mixed
     */
    public function get_basic_detail($user_master_id)
    {
        $sql = "SELECT
        user_detail.first_name,
        user_detail.last_name,
        user_master.user_mem_id,
        user_master.mobile_primary
        FROM user_master
        JOIN user_detail ON user_detail.user_master_id=user_master.user_master_id
        WHERE user_master.user_master_id=" . $user_master_id . "";
        $query = $this->db->query($sql);
        //echo $this->db->last_query(); exit();
        return $result = $query->row_array();
    }
    /**
     * @param $booked_id
     * @param $user_master_id
     * @param $from
     * @param $to
     * @param $user_client_ids
     * @param $user_group_ids
     * @return array
     */
    public function all_upcomingmastersession_details(
        $booked_id,
        $user_master_id,
        $from,
        $to,
        $user_client_ids,
        $user_group_ids
    ) {

        // echo $from.'to'.$to;
        // exit;


        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")";
            } else {
                $envStatus = "AND cTenv.env = " . $env . "";
            }
        } else {
            $envStatus = "";
        }


        //$booked_ids = $booked_id;
        // $getchildsession = $this->get_all_childsession();
        // if (!empty($getchildsession)) {
        //     //print_r( $getchildsession); exit;
        //     $cids = implode(",", (array)$getchildsession['sessions']);
        //     //print_r($cids);
        //     $childsessionids = " and ks.session_id NOT IN (" . $cids . ")";
        // } else {
        //     $childsessionids = "";
        // }
        // foreach ($booked_ids as $sing) {
        //     $new_arr[] = $sing->knwlg_sessions_id;
        // }
        //$res_arr = implode(',', $new_arr);
        // if ($res_arr) {
        //     $sqlStr = "and ( (ks.session_id  NOT IN(" . $res_arr . ") AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "') ) ";
        // } else {

        // }



        $global_status = "ks.status = 3";
        $session_status = "AND ( ks.session_status=1 OR ks.session_status=4 )";
        $privacy_status = "AND ks.privacy_status = 0 ";
        $session_end_datetime = "AND ks.end_datetime >= '" . date("Y-m-d H:i:s") . "' ";
        $session_start_datetime = "AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "' ";
        $optionalQuery = "";
        $orderBydate = "ks.start_datetime ASC";
        $bookedStatus = false;



        $result = $this->getSessionList(
            $session_start_datetime,
            $optionalQuery,
            $envStatus,
            $childsessionids,
            $from,
            $to,
            $user_master_id,
            $global_status,
            $session_status,
            $privacy_status,
            $session_end_datetime,
            $orderBydate,
            $bookedStatus
        );



        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            //$entities[$i]['participant_id'] = $row['participant_id'];
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $entities[$i]['session_topic'] = $row['session_topic'];
            $coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
            $entities[$i]['cover_image'] = ($row['cover_image'] != '') ? change_img_src($row['cover_image']) : $coverImg;
            //$entities[$i]['cover_image'] = $row['cover_image'];
            $entities[$i]['session_description'] = strip_tags($row['session_description']);
            //$entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            $entities[$i]['client_id'] = $row['client_id'];
            $entities[$i]['deeplink'] = ($env == 'GL') ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0);
            //$row['deeplink'];
            $entities[$i]['client_name'] = $row['client_name'];
            $entities[$i]['color'] = ($row['color'] != '') ? $row['color'] : '#eb34e5';
            /**
             * new sponsor logic
             */
            $sponsorLogoArry = explode(",", $row['sponsor_logo']);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        // if full path exist
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = base_url('uploads/logo/') . $valueSponor;
                        }
                    }
                }
            } else {
                if ($row['sponsor_logo']) {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            /*if ($row['document_path'] != "" || $row['document_path'] != NULL) {
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
            } else {
                $entities[$i]['document_path'] = "";
            }*/
            /*if ($row['comment'] != "" || $row['comment'] != NULL) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }*/
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['category_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['start_datetime'] = $row['start_datetime'];
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            //$question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            $post_date = $row['added_on'];
            $start_date = $row['start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            //$entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            /*if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }*/
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $this->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            /*$entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }*/
            //$entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            /*if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }*/
            $cpt_flag = 0;
            $on_of_booking_button = 0;
            $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
            if ($row['users'] == null) {
                $total_original_booking = 0;
            } else {
                $users_array = array();
                $users_array = explode(",", $row['users']);
                $total_original_booking = count($users_array);
            }
            if ($total_original_booking < $row['total_seats']) {
                $total_booking = $total_original_booking;
            }
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $minus_flag = $total_after_buffer - $total_original_booking;
                $total_booking = ($row['total_seats']) - 1;
            }
            if ($total_original_booking >= ($total_after_buffer)) {
                $total_booking = $row['total_seats'];
                $on_of_booking_button = 1;
                $cpt_flag = 1;
            }
            if ($total_booking > 0) {
                $available_percent = ($total_booking / $row['total_seats']) * 100;
            } else {
                $available_percent = 0;
            }
            $available_percent = round($available_percent);
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $total_booking;
            } else {
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
            }
            $perc = $available_percent;
            if ($cpt_flag == 0) {
                $entities[$i]['percentage'] = $perc;
            } else {
                $entities[$i]['percentage'] = $perc;
            }
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['is_share'] = $row['is_share'];


            $entities[$i]['start_datetime'] = $row['start_datetime'];
            $entities[$i]['end_datetime'] = $row['end_datetime'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            $session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                /*if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                    $logic_image = base_url() . "uploads/docimg/" . $image;
                } else {
                    $logic_image = base_url() . "uploads/docimg/no-image.png";
                }*/
                if ($image) {
                    /////============================== updated by  ramanath  14-5-21
                    if (stripos($image, "https://") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg; //"uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                    //=======================================
                    //  $logic_image_path = "uploads/docimg/" . $image;
                    //  $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    //  $logic_image = $imgPr;
                } else {
                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $i++;
        }
        return $entities;
        //print_r($entities); exit();
    }



    private function getSessionList(
        $session_start_datetime,
        $optionalQuery,
        $envStatus,
        $childsessionids,
        $limitFromFn,
        $limitToFn,
        $user_master_id,
        $global_status,
        $session_status,
        $privacy_status,
        $session_end_datetime,
        $orderBydate,
        $bookedStatus = false
    ) {



        if ($bookedStatus) {
            //$bookedIdsql = "ks.session_id IN (".implode(',', $bookedStatus).")";
            $bookedIdsql = "AND sp.participant_id = {$user_master_id}";
        } else {
            $bookedIdsql = "AND ( sp.participant_id is null OR  sp.participant_id != {$user_master_id})";
        }



        // Build optimized query with CTEs

        $sql = "WITH FilteredSessions AS (
                 SELECT
                        ks.session_id,
                        sp.participant_id
                 FROM
                        knwlg_sessions_V1 AS ks
                LEFT JOIN
                        content_to_env AS cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
                LEFT JOIN
                        session_to_specialities AS sTs ON sTs.session_id = ks.session_id
                LEFT JOIN
                        knwlg_sessions_participant sp  ON sp.knwlg_sessions_id = ks.session_id
                WHERE
                        {$global_status}
                        {$session_status}
                        {$childsessionids}
                        {$privacy_status}
                        {$session_end_datetime}
                        {$session_start_datetime}
                        {$bookedIdsql}
                        {$optionalQuery}
                        {$envStatus}

                        GROUP BY ks.session_id
                        ORDER BY {$orderBydate}
                        LIMIT ?, ?

            ),
            SpecialitiesAgg AS (
                

                SELECT
                sTs.session_id,
                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                FROM session_to_specialities sTs
                LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = sTs.specialities_id
                GROUP BY sTs.session_id
            ),
            SponsorsAgg AS (
                SELECT
                    fs.session_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                FROM
                    FilteredSessions fs
                JOIN
                    session_to_sponsor sTspon ON sTspon.session_id = fs.session_id
                JOIN
                    client_master clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                GROUP BY
                    fs.session_id
            ),
            DoctorsAgg AS (
                -- SELECT
                --     fs.session_id,
                --     GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
                --     GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
                --     GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') AS speciality,
                --     GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
                --     GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images
                -- FROM
                --     FilteredSessions fs
                -- JOIN
                --     knwlg_sessions_V1 ks ON ks.session_id = fs.session_id
                -- LEFT JOIN
                --     knwlg_sessions_doctors sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                -- GROUP BY
                --     fs.session_id

                    SELECT
                    fs.session_id,
                    GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
                    GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
                    GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') AS speciality,
                    GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
                    GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images
                    FROM
                    FilteredSessions fs
                    JOIN
                    session_to_sessiondoctor s2sd ON s2sd.session_id = fs.session_id
                    JOIN
                    knwlg_sessions_doctors sdoc ON sdoc.sessions_doctors_id = s2sd.sessions_doctors_id
                    GROUP BY
                    fs.session_id
            )
            
            SELECT
                fs.participant_id,
                ks.*,
                
                cln.client_name,
                cln.client_logo,
                msct.category_name,
                msct.category_logo,
                cTenv.price,
                uTpyCont.status AS user_content_payment_status,
               
                kv.status AS vault,
                stci.cover_image1,
                stci.cover_image2,
                stci.cover_image3,
                stci.cover_image4,
                stci.cover_image5,
                (ks.total_buffer + ks.total_seats) AS tot_seat,
                spec.specialities_name,
                spec.specialities_ids_and_names,
                spon.sponsor,
                spon.sponsor_logo,
                doc.session_doctor_id,
                doc.doctor_name,
                doc.speciality,
                doc.profile,
                doc.profile_images
                -- part.PartName,
                -- part.users,
                -- part.IS_ATTENDED
            FROM
                FilteredSessions fs
            JOIN
                knwlg_sessions_V1 ks ON ks.session_id = fs.session_id
            LEFT JOIN
                client_master cln ON cln.client_master_id = ks.client_id
            LEFT JOIN
                session_to_cover_image stci ON stci.session_id = ks.session_id
            LEFT JOIN
                knwlg_vault kv ON kv.type_text = 'session' AND kv.post_id = ks.session_id
            LEFT JOIN
                content_to_env cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
            LEFT JOIN
                payment_user_to_content uTpyCont ON uTpyCont.type_id = ks.session_id AND uTpyCont.type = 2 AND uTpyCont.user_master_id = ?
            LEFT JOIN
                master_session_category msct ON msct.mastersession_category_id = ks.category_id
            
            LEFT JOIN
                SpecialitiesAgg spec ON spec.session_id = ks.session_id
            LEFT JOIN
                SponsorsAgg spon ON spon.session_id = ks.session_id
            LEFT JOIN
                DoctorsAgg doc ON doc.session_id = ks.session_id ";

        // Execute query with prepared statements

        // echo $sql;
        // exit;
        // exit;
        $query = $this->db->query($sql, [(int)$limitFromFn, (int)$limitToFn, (int)$user_master_id]);
        // echo $this->db->last_query();
        // exit();
        return $query->result_array();
        // echo $this->db->last_query();
        // print_r($v);
        // exit();

    }

    public function all_upcomingmastersession_details_cme(
        $booked_id,
        $user_master_id,
        $from,
        $to,
        $user_client_ids,
        $user_group_ids
    ) {
        $env = get_user_env($user_master_id);
        //$env = get_user_env($user_master_id);
        if ($env) {
            if ($env != 'GL') {
                $envStatus = "AND (ks.env ='GL' or ks.env ='" . $env . "')";
            } else {
                $envStatus = "AND ks.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        $new_arr = array();
        foreach ($booked_id as $sing) {
            $new_arr[] = $sing->knwlg_sessions_id;
        }
        array_push($new_arr, 0);
        $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
            return "FIND_IN_SET('$x', ks.client_id)";
        }, explode(',', $user_client_ids))) . ')';
        $group_list = ' and (' . implode(' OR ', array_map(function ($x) {
            return "FIND_IN_SET('$x', ks.user_group_id)";
        }, explode(',', $user_group_ids))) . ')';
        $res_arr = implode(',', $new_arr);
        $sql = "SELECT
                    ks.session_id,
                    ks.session_doctor_id,
                    ks.session_topic,
                    ks.session_description,
                    ks.sessions_question,
                    ks.master_tag_ids,
                    ks.client_id,
                    ks.sponsor_id,
                    ks.user_group_id,
                    ks.category_id,
                    ks.start_datetime,
                    ks.end_datetime,
                    ks.speciality_id,
                    ks.total_seats,
                    ks.total_buffer,
                    ks.add_question_buffer_days,
                    ks.session_link,
                    ks.master_conf_provider_id,
                    ks.session_access_code,
                    ks.deeplink,
                    ks.in_deeplink,
                    ks.gl_deeplink,
                    ks.template_id,
                    ks.cert_template_id,
                    ks.display_in_dashboard,
                    ks.conf_phone_no,
                    ks.privacy_status,
                    ks.color,
                    ks.added_on,
                    ks.added_by,
                    ks.session_status,
                    ks.cover_image,
                    ks.modified_on,
                    ks.modified_by,
                    ks.is_recommended,
                    ks.is_multiday_session,
                    ks.break_json,
                    ks.status,
                    ks.is_featured,
                    ks.rating_flag,
                    ks.remarks,
                    ks.crm_id,
                    ks.img_credits,
                    ks.session_json,
                    ks.certified,
                    ks.env,
                    ks.notification_template,
                    ks.shortlink,
                    ks.invitefile,
                    ks.exitroute,
                    ks.is_share,
                    ks.is_like,
                    ks.is_comment,
                    cln.client_name,
                    cln.client_logo,
                    msct.category_name,
                    msct.category_logo,
                    GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
                    GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
                    GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') as speciality,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                    GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as profile,
                    GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as profile_images,
                    (ks.total_buffer + ks.total_seats) as tot_seat
                FROM knwlg_sessions_V1 as ks
                LEFT JOIN session_to_specialities as sesTs ON sesTs.session_id = ks.session_id
                LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = sesTs.specialities_id
                LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id
                LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
                LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                WHERE msct.mastersession_category_id IN (1,2)
                AND ks.session_id NOT IN (" . $res_arr . ") " . $client_list . $envStatus . "
                AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "'
                GROUP BY
                    ks.session_id, ks.session_doctor_id, ks.session_topic, ks.session_description, ks.sessions_question,
                    ks.master_tag_ids, ks.client_id, ks.sponsor_id, ks.user_group_id, ks.category_id, ks.start_datetime,
                    ks.end_datetime, ks.speciality_id, ks.total_seats, ks.total_buffer, ks.add_question_buffer_days,
                    ks.session_link, ks.master_conf_provider_id, ks.session_access_code, ks.deeplink, ks.in_deeplink,
                    ks.gl_deeplink, ks.template_id, ks.cert_template_id, ks.display_in_dashboard, ks.conf_phone_no,
                    ks.privacy_status, ks.color, ks.added_on, ks.added_by, ks.session_status, ks.cover_image,
                    ks.modified_on, ks.modified_by, ks.is_recommended, ks.is_multiday_session, ks.break_json,
                    ks.status, ks.is_featured, ks.rating_flag, ks.remarks, ks.crm_id, ks.img_credits, ks.session_json,
                    ks.certified, ks.env, ks.notification_template, ks.shortlink, ks.invitefile, ks.exitroute,
                    ks.is_share, ks.is_like, ks.is_comment, cln.client_name, cln.client_logo,
                    msct.category_name, msct.category_logo
                ORDER BY ks.start_datetime ASC
                LIMIT " . $from . "," . $to . "";
        // AND ((ks.session_status=1 AND ks.status NOT IN (1,2)) OR (ks.session_status=4 AND ks.status NOT IN (1,2)))
        //echo $sql; exit;
        $query = $this->db->query($sql);
        //echo $this->db->last_query(); exit();" . $group_list . "
        $result = $query->result_array();
        //print_r($result); exit();
        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            //$entities[$i]['participant_id'] = $row['participant_id'];
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $entities[$i]['session_topic'] = $row['session_topic'];
            $entities[$i]['session_description'] = strip_tags($row['session_description']);
            $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            $coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
            $entities[$i]['cover_image'] = ($row['cover_image'] != '') ? change_img_src($row['cover_image']) : $coverImg;
            //$entities[$i]['cover_image'] = $row['cover_image'];
            $entities[$i]['client_id'] = $row['client_id'];
            $entities[$i]['deeplink'] = ($env == 'GL') ? (($$row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0);
            //$row['deeplink'];
            $entities[$i]['client_name'] = $row['client_name'];
            $entities[$i]['color'] = ($row['color'] != '') ? $row['color'] : '#eb34e5';
            /**
             * new sponsor logic
             */
            $sponsorLogoArry = explode(",", $row['sponsor_logo']);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        // if full path exist
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                }
            } else {
                if ($row['sponsor_logo']) {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($row['document_path'] != "" || $row['document_path'] != null) {
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
            } else {
                $entities[$i]['document_path'] = "";
            }
            if ($row['comment'] != "" || $row['comment'] != null) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['category_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            // $entities[$i]['start_datetime'] = '';//date("Y-m-d h:i:sa", strtotime($row['start_datetime'])); //$row['start_datetime'];
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            //$question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            $post_date = $row['added_on'];
            $start_date = $row['start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            $entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            /*if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }*/
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $this->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            /*$entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }*/
            $entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }
            $cpt_flag = 0;
            $on_of_booking_button = 0;
            $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
            if ($row['users'] == null) {
                $total_original_booking = 0;
            } else {
                $users_array = array();
                $users_array = explode(",", $row['users']);
                $total_original_booking = count($users_array);
            }
            if ($total_original_booking < $row['total_seats']) {
                $total_booking = $total_original_booking;
            }
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $minus_flag = $total_after_buffer - $total_original_booking;
                $total_booking = ($row['total_seats']) - 1;
            }
            if ($total_original_booking >= ($total_after_buffer)) {
                $total_booking = $row['total_seats'];
                $on_of_booking_button = 1;
                $cpt_flag = 1;
            }
            if ($total_booking > 0) {
                $available_percent = ($total_booking / $row['total_seats']) * 100;
            } else {
                $available_percent = 0;
            }
            $available_percent = round($available_percent);
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $total_booking;
            } else {
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
            }
            $perc = $available_percent;
            if ($cpt_flag == 0) {
                $entities[$i]['percentage'] = $perc;
            } else {
                $entities[$i]['percentage'] = $perc;
            }
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['is_share'] = $row['is_share'];

            $entities[$i]['start_datetime'] = $row['start_datetime'];
            $entities[$i]['end_datetime'] = $row['end_datetime'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            $session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);

                if ($image) {

                    if (stripos($image, "https://") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                    //=======================================
                } else {
                    $logic_image = docimg;
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $i++;
        }
        return $entities;
        //print_r($entities); exit();
    }
    public function all_upcomingmastersession_details_slider(
        $booked_id,
        $user_master_id,
        $from,
        $to,
        $user_client_ids,
        $user_group_ids,
        $cme,
        $spIds
    ) {


        // echo $from.'to'.$to;
        // exit;


        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")";
            } else {
                $envStatus = "AND cTenv.env = " . $env . "";
            }
        } else {
            $envStatus = "";
        }


        // $booked_ids = $booked_id;
        // $getchildsession = $this->get_all_childsession();
        // if (!empty($getchildsession)) {
        //     //print_r( $getchildsession); exit;
        //     $cids = implode(",", (array)$getchildsession['sessions']);
        //     //print_r($cids);
        //     $childsessionids = " and ks.session_id NOT IN (" . $cids . ")";
        // } else {
        //     $childsessionids = "";
        // }
        // foreach ($booked_ids as $sing) {
        //     $new_arr[] = $sing->knwlg_sessions_id;
        // }
        // $res_arr = implode(',', $new_arr);
        // if ($res_arr) {
        //     $sqlStr = "and ( (ks.session_id  NOT IN(" . $res_arr . ") AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "') ) ";
        // } else {
        //     $sqlStr = "and (ks.start_datetime >= '" . date("Y-m-d H:i:s") . "')";
        // }



        // $global_status = "ks.status IN (3)";
        // $session_status = "AND (ks.session_status=1 OR ks.session_status=4) AND ks.session_status!=6";
        // $privacy_status = "AND ks.privacy_status = 0 ";
        // $session_end_datetime = "AND ks.end_datetime >= '" . date("Y-m-d H:i:s") . "' ";


        $global_status = "ks.status = 3";
        $session_status = "AND ( ks.session_status = 1 OR ks.session_status = 4 )";
        $privacy_status = "AND ks.privacy_status = 0 ";
        $session_end_datetime = "";
        $session_start_datetime = "AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "' ";
        $optionalQuery = "";
        $orderBydate = "ks.start_datetime ASC";
        $bookedStatus = false;

        $result = $this->getSessionList(
            $session_start_datetime,
            $optionalQuery,
            $envStatus,
            $childsessionids,
            $from,
            $to,
            $user_master_id,
            $global_status,
            $session_status,
            $privacy_status,
            $session_end_datetime,
            $orderBydate,
            $bookedStatus
        );


        $key_locked = get_user_package($user_master_id, 'session');

        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            //$entities[$i]['participant_id'] = $row['participant_id'];
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $entities[$i]['session_topic'] = $row['session_topic'];
            $entities[$i]['session_description'] = strip_tags($row['session_description']);
            $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            $entities[$i]['vault'] = ($row['vault'] != "") ? $row['vault'] : 0;
            $coverImg = base_url() . "uploads/sessionBgDefault.jpeg";
            $entities[$i]['cover_image'] = change_img_src($row['cover_image']);
            $entities[$i]['cover_image1'] = change_img_src($row['cover_image1']);
            $entities[$i]['cover_image2'] = change_img_src($row['cover_image2']);
            $entities[$i]['cover_image3'] = change_img_src($row['cover_image3']);
            $entities[$i]['cover_image4'] = change_img_src($row['cover_image4']);
            $entities[$i]['cover_image5'] = change_img_src($row['cover_image5']);
            //$entities[$i]['cover_image'] = $row['cover_image'];
            // $entities[$i]['client_id'] = $row['client_id'];
            $entities[$i]['deeplink'] = ($env != 1) ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0);
            //$row['deeplink'];
            // $entities[$i]['client_name'] = $row['client_name'];
            $entities[$i]['color'] = ($row['color'] != '') ? $row['color'] : '#918c91';
            $entities[$i]['cpddetail'] = $this->getcpddetails($row['session_id']);
            /**
             * new sponsor logic
             */
            $sponsorLogoArry = explode(",", $row['sponsor_logo']);
            $allsponsor = array();
            $sponsorname = explode(",", $row['sponsor']);
            $sp = 0;
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        // if full path exist
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                }
                $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                $sp++;
            } else {
                if ($row['sponsor_logo']) {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                    $allsponsor[] = array('name' => $row['sponsor'], "logo" => $row['sponsor_logo']);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
            $entities[$i]["all_sponsor"] = $allsponsor;
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($row['document_path'] != "" || $row['document_path'] != null) {
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
            } else {
                $entities[$i]['document_path'] = "";
            }
            if ($row['comment'] != "" || $row['comment'] != null) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['category_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['category_logo'] = change_img_src($row['category_logo']);
            // $entities[$i]['start_datetime'] = '';//date("Y-m-d h:i:sa", strtotime($row['start_datetime'])); //$row['start_datetime'];
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            $dateTime_start = new DateTime($row['start_datetime']);
            $entities[$i]['start_displaydate'] =  $dateTime_start->format('d M, h:i A');
            $dateTime_end = new DateTime($row['end_datetime']);
            $entities[$i]['end_displaydate'] =  $dateTime_end->format('d M, h:i A');
            $interval = $dateTime_start->diff($dateTime_end);
            $entities[$i]['interval'] = $interval->format('%y years, %m months, %d days, %h hours, %i minutes, and %s seconds');
            $post_date = $row['added_on'];
            $start_date = $row['start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            $entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            /*if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }*/
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $this->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            /*$entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }*/
            $entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }
            $cpt_flag = 0;
            $on_of_booking_button = 0;
            $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
            if ($row['users'] == null) {
                $total_original_booking = 0;
            } else {
                $users_array = array();
                $users_array = explode(",", $row['users']);
                $total_original_booking = count($users_array);
            }
            if ($total_original_booking < $row['total_seats']) {
                $total_booking = $total_original_booking;
            }
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $minus_flag = $total_after_buffer - $total_original_booking;
                $total_booking = ($row['total_seats']) - 1;
            }
            if ($total_original_booking >= ($total_after_buffer)) {
                $total_booking = $row['total_seats'];
                $on_of_booking_button = 1;
                $cpt_flag = 1;
            }
            if ($total_booking > 0) {
                $available_percent = ($total_booking / $row['total_seats']) * 100;
            } else {
                $available_percent = 0;
            }
            $available_percent = round($available_percent);
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $total_booking;
            } else {
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
            }
            $perc = $available_percent;
            if ($cpt_flag == 0) {
                $entities[$i]['percentage'] = $perc;
            } else {
                $entities[$i]['percentage'] = $perc;
            }
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['is_share'] = $row['is_share'];

            $entities[$i]['is_share'] = $row['is_share'];
            $entities[$i]['start_datetime_old'] = date("Y-m-d H:i:s", strtotime($row['start_datetime']));
            $entities[$i]['end_datetime_old'] = date("Y-m-d H:i:s", strtotime($row['end_datetime'])); //$row['end_datetime'];
            $entities[$i]['start_datetime'] = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $entities[$i]['end_datetime'] = (new DateTime($row['end_datetime'], new DateTimeZone('Asia/Kolkata')))
                ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                ->format('Y-m-d H:i:s');
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            // $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            //$session_doc_array = explode(",", $row['session_doctor_id']);
            if ($row['session_doctor_id'] != '') {
                $session_doc_array = explode(",", $row['session_doctor_id']);
            } else {
                //print_r($getchildsession['session_doctors']);
                $session_doc_array = explode(",", $getchildsession['session_doctors'][$row['session_id']]);
                //print_r($session_doc_array); exit;
            }
            $ses_doc_det_array = array();
            $inc_pp = 0;
            $store_total_doctors[$row['session_id']] = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                /*if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
                    $logic_image = base_url() . "uploads/docimg/" . $image;
                } else {
                    $logic_image = base_url() . "uploads/docimg/no-image.png";
                }*/
                if ($image) {
                    // $logic_image_path = "uploads/docimg/" . $image;
                    // $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    // $logic_image = $imgPr;
                    /////============================== updated by  ramanath  14-5-21
                    if (stripos($image, "https://") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg; //"uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                    //=======================================
                } else {
                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
                $store_total_doctors[$row['session_id']] = $inc_pp;
            }
            $k = array_keys((array)$getchildsession['sessioncount']);
            //print_r(in_array($val->session_id,$k));
            if (in_array($row['session_id'], $k)) {
                $total_multiday_session = $getchildsession['sessioncount'][$row['session_id']];
                // print_R($total_multiday_session."/n");
            } else {
                $total_multiday_session = 0;
            }
            //print_r($getchildsession['doctorcount']); exit;
            $keyvalue_1 = array_keys((array)$getchildsession['doctorcount']);
            //print_r($keyvalue_1); exit;
            if (in_array($row['session_id'], $keyvalue_1)) {
                //echo 1;
                $total_doctors = $getchildsession['doctorcount'][$row['session_id']];
            } else {
                //echo 2;
                $total_doctors = $store_total_doctors[$row['session_id']];
            }
            $datetime1 = new DateTime(date('Y-m-d H:i:s'));
            $datetime2 = new DateTime($row['start_datetime']);
            $difference = $datetime1->diff($datetime2);
            $entities[$i]["total_session"] = $total_multiday_session;
            $entities[$i]["total_doctors"] = $total_doctors;
            $entities[$i]["is_locked"] = $key_locked;
            $entities[$i]["price"] = $row['price'];
            $entities[$i]["user_content_payment"] = get_user_content_status($row['session_id'], 2, $user_master_id);
            $entities[$i]["total_days"] = $difference->d; //" days ".$difference->h." hour ".$difference->i." minute ".$difference->s." seconds ";
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $entities[$i]["is_multiday_session"] = $row['is_multiday_session'];
            $entities[$i]['cpddetail'] = $this->getcpddetails($row['session_id']);
            $i++;
        }
        return $entities;
        //print_r($entities); exit();
    }
    /**
     * @param $booked_id
     * @param $user_master_id
     * @param $from
     * @param $to
     * @param $specialty_ids
     * @param $user_client_ids
     * @param $user_group_ids
     * @return array
     */
    public function all_upcomingmastersessionwithspeciality_details(
        $booked_id,
        $user_master_id,
        $from,
        $to,
        $specialty_ids,
        $topic = '',
        $user_client_ids,
        $user_group_ids
    ) {
        $new_arr = array();
        foreach ($booked_id as $sing) {
            $new_arr[] = $sing->knwlg_sessions_id;
        }
        array_push($new_arr, 0);
        $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
            return "FIND_IN_SET('$x', ks.client_id)";
        }, explode(',', $user_client_ids))) . ')';
        $group_list = ' and (' . implode(' OR ', array_map(function ($x) {
            return "FIND_IN_SET('$x', ks.user_group_id)";
        }, explode(',', $user_group_ids))) . ')';
        if ($specialty_ids != "") {
            $speciality_list = ' and (' . implode(' OR ', array_map(function ($x) {
                return "FIND_IN_SET('$x', ks.speciality_id)";
            }, explode(',', $specialty_ids))) . ')';
        } else {
            $speciality_list = "";
        }
        if ($topic != "") {
            $topic = "ks.session_description like '%" . $topic . "%' or ";
        } else {
            $topic = '';
        }
        $res_arr = implode(',', $new_arr);
        $sql = "SELECT
                    ksp.participant_id,
                    ks.session_id,
                    ks.session_doctor_id,
                    ks.session_topic,
                    ks.session_description,
                    ks.sessions_question,
                    ks.master_tag_ids,
                    ks.client_id,
                    ks.sponsor_id,
                    ks.user_group_id,
                    ks.category_id,
                    ks.start_datetime,
                    ks.end_datetime,
                    ks.speciality_id,
                    ks.total_seats,
                    ks.total_buffer,
                    ks.add_question_buffer_days,
                    ks.session_link,
                    ks.master_conf_provider_id,
                    ks.session_access_code,
                    ks.deeplink,
                    ks.in_deeplink,
                    ks.gl_deeplink,
                    ks.template_id,
                    ks.cert_template_id,
                    ks.display_in_dashboard,
                    ks.conf_phone_no,
                    ks.privacy_status,
                    ks.color,
                    ks.added_on,
                    ks.added_by,
                    ks.session_status,
                    ks.cover_image,
                    ks.modified_on,
                    ks.modified_by,
                    ks.is_recommended,
                    ks.is_multiday_session,
                    ks.break_json,
                    ks.status,
                    ks.is_featured,
                    ks.rating_flag,
                    ks.remarks,
                    ks.crm_id,
                    ks.img_credits,
                    ks.session_json,
                    ks.certified,
                    ks.env,
                    ks.notification_template,
                    ks.shortlink,
                    ks.invitefile,
                    ks.exitroute,
                    ks.is_share,
                    ks.is_like,
                    ks.is_comment,
                    sd.knwlg_sessions_docs_id,
                    sd.knwlg_sessions_id,
                    sd.added_on,
                    sd.added_by,
                    sd.modified_on,
                    sd.modified_by,
                    sd.updated_at,
                    sd.updated_by,
                    sd.status,
                    cln.client_name,
                    cln.client_logo,
                    msct.category_name,
                    msct.category_logo,
                    sd.document_path,
                    sd.comment,
                    ksd.knwlg_sessions_docs_id,
                    ksd.document_path,
                    ksd.comment,
                    (ks.total_buffer + ks.total_seats) as tot_seat,
                    -- Aggregated Columns
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names,
                    GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
                    GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
                    GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') as speciality,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                    GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as profile,
                    GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as profile_images,
                    GROUP_CONCAT(ksp.participant_id) as PartName,
                    GROUP_CONCAT(DISTINCT ksp.participant_id) as users,
                    GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED
                FROM knwlg_sessions_V1 as ks
                LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
                LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id
                LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
                LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
                LEFT JOIN knwlg_sessions_participant as ksp ON ksp.knwlg_sessions_id = ks.session_id
                LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id
                WHERE
                    " . $topic . "
                    ks.session_id NOT IN(" . $res_arr . ") " . $client_list . " " . $group_list . " " . $speciality_list . "
                    AND (ks.session_status = 1 OR ks.session_status = 4)
                    AND ks.status != 2
                    AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "'
                GROUP BY
                    ks.session_id, ks.session_doctor_id, ks.session_topic, ks.session_description, ks.sessions_question, ks.master_tag_ids,
                    ks.client_id, ks.sponsor_id, ks.user_group_id, ks.category_id, ks.start_datetime, ks.end_datetime, ks.speciality_id,
                    ks.total_seats, ks.total_buffer, ks.add_question_buffer_days, ks.session_link, ks.master_conf_provider_id, ks.session_access_code,
                    ks.deeplink, ks.in_deeplink, ks.gl_deeplink, ks.template_id, ks.cert_template_id, ks.display_in_dashboard, ks.conf_phone_no,
                    ks.privacy_status, ks.color, ks.added_on, ks.added_by, ks.session_status, ks.cover_image, ks.modified_on, ks.modified_by,
                    ks.is_recommended, ks.is_multiday_session, ks.break_json, ks.status, ks.is_featured, ks.rating_flag, ks.remarks, ks.crm_id,
                    ks.img_credits, ks.session_json, ks.certified, ks.env, ks.notification_template, ks.shortlink, ks.invitefile, ks.exitroute,
                    ks.is_share, ks.is_like, ks.is_comment, sd.knwlg_sessions_docs_id, sd.knwlg_sessions_id, sd.added_on, sd.added_by,
                    sd.modified_on, sd.modified_by, sd.updated_at, sd.updated_by, sd.status, cln.client_name, cln.client_logo,
                    msct.category_name, msct.category_logo, sd.document_path, sd.comment, ksd.knwlg_sessions_docs_id, ksd.document_path,
                    ksd.comment
                ORDER BY ks.start_datetime ASC
                LIMIT " . $from . ", " . $to ."";
        //echo $sql;
        $query = $this->db->query($sql);
        //echo $this->db->last_query(); exit();
        $result = $query->result_array();
        //print_r($result); exit();
        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            //$entities[$i]['participant_id'] = $row['participant_id'];
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $entities[$i]['session_topic'] = $row['session_topic'];
            $entities[$i]['session_description'] = strip_tags($row['session_description']);
            $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            $entities[$i]['client_id'] = $row['client_id'];
            $entities[$i]['client_name'] = $row['client_name'];
            /**
             * new sponsor logic
             */
            $sponsorLogoArry = explode(",", $row['sponsor_logo']);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        // if full path exist
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = base_url('uploads/logo/') . $valueSponor;
                        }
                    }
                }
            } else {
                if ($row['sponsor_logo']) {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($row['document_path'] != "" || $row['document_path'] != null) {
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
            } else {
                $entities[$i]['document_path'] = "";
            }
            if ($row['comment'] != "" || $row['comment'] != null) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['start_datetime'] = $row['start_datetime'];
            $entities[$i]['ms_start_datetime'] = $row['start_datetime'];
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            $post_date = $row['added_on'];
            $start_date = $row['start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $now_str = strtotime("now");
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            $entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            //$user_id = $this->session->userdata['user_master_id'];
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            $entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }
            $entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }
            $cpt_flag = 0;
            $on_of_booking_button = 0;
            $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
            if ($row['users'] == null) {
                $total_original_booking = 0;
            } else {
                $users_array = array();
                $users_array = explode(",", $row['users']);
                $total_original_booking = count($users_array);
            }
            if ($total_original_booking < $row['total_seats']) {
                $total_booking = $total_original_booking;
            }
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $minus_flag = $total_after_buffer - $total_original_booking;
                $total_booking = ($row['total_seats']) - 1;
            }
            if ($total_original_booking >= ($total_after_buffer)) {
                $total_booking = $row['total_seats'];
                $on_of_booking_button = 1;
                $cpt_flag = 1;
            }
            if ($total_booking > 0) {
                $available_percent = ($total_booking / $row['total_seats']) * 100;
            } else {
                $available_percent = 0;
            }
            $available_percent = round($available_percent);
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $total_booking;
            } else {
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
            }
            $perc = $available_percent;
            if ($cpt_flag == 0) {
                $entities[$i]['percentage'] = $perc;
            } else {
                $entities[$i]['percentage'] = $perc;
            }
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['is_share'] = $row['is_share'];

            $entities[$i]['start_datetime'] = date(' jS F y', strtotime($row['start_datetime']));
            $entities[$i]['end_datetime'] = $row['end_datetime'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            $session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if ($image) {
                    // Check if it's already a full URL
                    if (stripos($image, "https://") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                } else {
                    $logic_image = docimg;
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $inc_pp++;
            }
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $i++;
        }
        return $entities;
        //print_r($entities); exit();
    }
    /**
     * @param $booked_id
     * @param $user_master_id
     * @param $from
     * @param $to
     * @param $specialty_ids
     * @param $user_client_ids
     * @param $user_group_ids
     * @return array
     */
    public function all_filter(
        $booked_id,
        $user_master_id,
        $from,
        $to,
        $specialty_ids,
        $topic = '',
        $user_client_ids,
        $user_group_ids,
        $dataArray = array()
    ) {

        // Cache key for this query
        $cacheKey = "all_filter_" . md5(json_encode(func_get_args()));

        // Check if result is cached
        if (isset($this->myredis) && $this->myredis->exists($cacheKey)) {
            return $this->myredis->get($cacheKey);
        }

        // Get environment settings
        $env = get_user_env_id($user_master_id);
        $envStatus = "";
        if ($env) {
            $envStatus = ($env != 2)
                ? "AND (cTenv.env = 2 OR cTenv.env = {$env})"
                : "AND cTenv.env = {$env}";
        }

        // Check user package access
        $key_locked = get_user_package($user_master_id, 'session');
        if (empty($key_locked)) {
            return null;
        }

        // Get child sessions
        $getchildsession = $this->get_all_childsession();
        $childsessionids = "";
        if (!empty($getchildsession['sessions'])) {
            $cids = implode(",", (array)$getchildsession['sessions']);
            $childsessionids = " AND ks.session_id NOT IN ({$cids})";
        }

        // Process sorting options

        // Process sorting options
        $order_by = [];
        if (!empty($dataArray['short_options'])) {
            foreach ($dataArray['short_options'] as $key => $val) {
                switch ($key) {
                    case 'shortBySpeakerName':
                        $order_by[] = ($val == 'z-a') ? 'doc.doctor_name DESC' : 'doc.doctor_name ASC';
                        break;
                    case 'shortByAddOn':
                        if ($val == 'earliest') {
                            $order_by[] = 'ks.added_on DESC';
                        } elseif ($val == 'latest') {
                            $order_by[] = 'ks.added_on ASC';
                        } else {
                            $order_by[] = 'ks.start_datetime ASC';
                        }
                        break;
                    default:
                        $order_by[] = 'ks.start_datetime ASC';
                        break;
                }
            }
        }

        $final_order_by = !empty($order_by)
            ? 'ORDER BY ' . implode(", ", $order_by)
            : 'ORDER BY ks.start_datetime ASC';


        // Process filter options
        $filter_date = [];
        $filter_cat = [];
        $filter_status = [];
        $speciality_list = "";

        if (!empty($dataArray['filter_options'])) {
            foreach ($dataArray['filter_options'] as $key => $val) {
                switch ($key) {
                    case 'dateFrom':
                        if (!empty($val)) {
                            $filter_date[] = "date(ks.start_datetime) >= '" . date("Y-m-d", strtotime($val)) . "'";
                        } else {
                            $filter_date[] = "date(ks.start_datetime) >= '" . date("Y-m-d") . "'";
                        }
                        break;
                    case 'dateTo':
                        if (!empty($val)) {
                            $filter_date[] = "date(ks.end_datetime) <= '" . date("Y-m-d", strtotime($val)) . "'";
                        }
                        break;
                    case 'showMasterCast':
                        if ($val == 1) {
                            $filter_cat[] = "ks.session_status IN(1,2,4,7) AND ks.category_id = 1 AND ks.start_datetime > DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
                        }
                        break;
                    case 'showMasterConsult':
                        if ($val == 1) {
                            $filter_cat[] = "ks.session_status IN(1,2,4,7) AND ks.category_id = 3 AND ks.start_datetime > DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
                        }
                        break;
                    case 'showMasterCircle':
                        if ($val == 1) {
                            $filter_cat[] = "ks.session_status IN(1,2,4,7) AND ks.category_id = 2 AND ks.start_datetime > DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
                        }
                        break;
                    case 'showHotline':
                        if ($val == 1) {
                            $filter_cat[] = "ks.session_status IN(1,2,4,7) AND ks.category_id = 4 AND ks.start_datetime > DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
                        }
                        break;
                    case 'showMultidaysession':
                        if ($val == 1) {
                            $filter_cat[] = "ks.session_status IN(1,2,4,7) AND ks.category_id = 6 AND ks.start_datetime > DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
                        }
                        break;
                    case 'showExpressCast':
                        if ($val == 1) {
                            $filter_cat[] = "ks.session_status IN(1,2,4,7) AND ks.category_id = 7 AND ks.start_datetime > DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
                        }
                        break;
                    case 'showAvailableSession':
                        if ($val == 1) {
                            $filter_status[] = "ks.session_status IN(1,2,4,7) AND ks.status=3 AND ks.start_datetime > DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
                        }
                        break;
                    case 'showUpcomingSession':
                        if ($val == 1) {
                            $filter_status[] = "ks.session_status IN(1,2,4,7) AND ks.status=3 AND ks.start_datetime >= '" . date("Y-m-d H:i:s") . "'";
                        }
                        break;
                    case 'showCompleteSession':
                        if ($val == 1) {
                            $filter_status[] = "ks.session_status = 3 AND ks.status=3 AND ks.start_datetime < '" . date("Y-m-d H:i:s") . "'";
                        }
                        break;
                    case 'specialityIds':
                        if (!empty($val) && $val != '0') {
                            $speciality_list = ' AND (' . implode(' OR ', array_map(function ($x) {
                                return "FIND_IN_SET('{$x}', ks.speciality_id)";
                            }, explode(',', $val))) . ') AND ks.start_datetime > DATE_ADD(CURDATE(), INTERVAL 1 DAY)';
                        }
                        break;
                }
            }
        }

        // Build filter conditions
        $final_filter_cat = !empty($filter_cat)
            ? ' AND (' . implode(" OR ", $filter_cat) . ')'
            : '';

        $final_filter_date = !empty($filter_date)
            ? ' AND (' . implode(" AND ", $filter_date) . ')'
            : '';

        $final_filter_status = !empty($filter_status)
            ? ' AND (' . implode(" OR ", $filter_status) . ')'
            : '';

        // Process booked sessions
        $new_arr = [];
        foreach ($booked_id as $sing) {
            $new_arr[] = $sing->knwlg_sessions_id;
        }
        array_push($new_arr, 0);
        $res_arr = implode(',', $new_arr);

        // Build client and group filters
        // $client_list = ' AND (' . implode(' OR ', array_map(function ($x) {
        //     return "FIND_IN_SET('{$x}', ks.client_id)";
        // }, explode(',', $user_client_ids))) . ')';

        // $group_list = ' AND (' . implode(' OR ', array_map(function ($x) {
        //     return "FIND_IN_SET('{$x}', ks.user_group_id)";
        // }, explode(',', $user_group_ids))) . ')';

        // Process topic filter
        $topic_filter = !empty($topic)
            ? "ks.session_description LIKE '%" . $this->db->escape_like_str($topic) . "%' AND "
            : '';

        // Build optimized query with CTEs
        $sql = "WITH FilteredSessions AS (
                SELECT 
                    ks.session_id
                FROM 
                    knwlg_sessions_V1 AS ks
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
                WHERE 
                    ks.session_status != 3
                    AND ks.status = 3
                    AND ks.privacy_status = 0
                    AND ks.session_status != 6
                    {$childsessionids}
                    AND {$topic_filter}
                    ks.session_id NOT IN({$res_arr})
                    {$speciality_list}
                    {$final_filter_date}
                    {$final_filter_cat}
                    {$final_filter_status}
                    {$envStatus}
            ),
            
            SpecialitiesAgg AS (
                SELECT 
                    fs.session_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                FROM 
                    FilteredSessions fs
                JOIN 
                    knwlg_sessions_V1 ks ON ks.session_id = fs.session_id
                LEFT JOIN 
                    master_specialities_V1 ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
                GROUP BY 
                    fs.session_id
            ),
            
            SponsorsAgg AS (
                SELECT 
                    fs.session_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                FROM 
                    FilteredSessions fs
                JOIN 
                    session_to_sponsor sTspon ON sTspon.session_id = fs.session_id
                JOIN 
                    client_master clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                GROUP BY 
                    fs.session_id
            ),
            
            DoctorsAgg AS (
                SELECT 
                    fs.session_id,
                    GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') AS session_doctor_id,
                    GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') AS doctor_name,
                    GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') AS speciality,
                    GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') AS profile,
                    GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') AS profile_images
                FROM 
                    FilteredSessions fs
                JOIN 
                    knwlg_sessions_V1 ks ON ks.session_id = fs.session_id
                LEFT JOIN 
                    knwlg_sessions_doctors sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                GROUP BY 
                    fs.session_id
            ),
            
            ParticipantsAgg AS (
                SELECT 
                    fs.session_id,
                    GROUP_CONCAT(ksp.participant_id) AS PartName,
                    GROUP_CONCAT(DISTINCT ksp.participant_id) AS users,
                    GROUP_CONCAT(ksp.is_attended) AS IS_ATTENDED
                FROM 
                    FilteredSessions fs
                LEFT JOIN 
                    knwlg_sessions_participant ksp ON ksp.knwlg_sessions_id = fs.session_id
                GROUP BY 
                    fs.session_id
            )
            
            SELECT 
                ksp.participant_id,
                ks.*,
                sd.knwlg_sessions_docs_id,
                sd.knwlg_sessions_id,
                sd.added_on AS sd_added_on,
                sd.added_by AS sd_added_by,
                sd.modified_on AS sd_modified_on,
                sd.modified_by AS sd_modified_by,
                sd.updated_at,
                sd.updated_by,
                sd.status AS sd_status,
                cln.client_name,
                cln.client_logo,
                msct.category_name,
                msct.category_logo,
                cTenv.price,
                uTpyCont.status AS user_content_payment_status,
                sd.document_path,
                sd.comment,
                ksd.knwlg_sessions_docs_id AS ksd_id,
                ksd.document_path AS ksd_document_path,
                ksd.comment AS ksd_comment,
                kv.status AS vault,
                stci.cover_image1,
                stci.cover_image2,
                stci.cover_image3,
                stci.cover_image4,
                stci.cover_image5,
                (ks.total_buffer + ks.total_seats) AS tot_seat,
                spec.specialities_name,
                spec.specialities_ids_and_names,
                spon.sponsor,
                spon.sponsor_logo,
                doc.session_doctor_id,
                doc.doctor_name,
                doc.speciality,
                doc.profile,
                doc.profile_images,
                part.PartName,
                part.users,
                part.IS_ATTENDED
            FROM 
                FilteredSessions fs
            JOIN 
                knwlg_sessions_V1 ks ON ks.session_id = fs.session_id
            LEFT JOIN 
                client_master cln ON cln.client_master_id = ks.client_id
            LEFT JOIN 
                session_to_cover_image stci ON stci.session_id = ks.session_id
            LEFT JOIN 
                knwlg_vault kv ON kv.type_text = 'session' AND kv.post_id = ks.session_id
            LEFT JOIN 
                content_to_env cTenv ON cTenv.type_id = ks.session_id AND cTenv.type = 2
            LEFT JOIN 
                payment_user_to_content uTpyCont ON uTpyCont.type_id = ks.session_id AND uTpyCont.type = 2 AND uTpyCont.user_master_id = ?
            LEFT JOIN 
                master_session_category msct ON msct.mastersession_category_id = ks.category_id
            LEFT JOIN 
                knwlg_sessions_documents sd ON sd.knwlg_sessions_id = ks.session_id
            LEFT JOIN 
                knwlg_sessions_participant ksp ON ksp.knwlg_sessions_id = ks.session_id
            LEFT JOIN 
                knwlg_sessions_documents ksd ON ksd.knwlg_sessions_id = ks.session_id
            LEFT JOIN 
                SpecialitiesAgg spec ON spec.session_id = ks.session_id
            LEFT JOIN 
                SponsorsAgg spon ON spon.session_id = ks.session_id
            LEFT JOIN 
                DoctorsAgg doc ON doc.session_id = ks.session_id
            LEFT JOIN 
                ParticipantsAgg part ON part.session_id = ks.session_id
            {$final_order_by}
            LIMIT ?, ?";

        // Execute query with prepared statements

        // echo $sql;
        // exit;
        $query = $this->db->query($sql, [$user_master_id, (int)$from, (int)$to]);
        $result = $query->result_array();

        if (empty($result)) {
            return [];
        }

        // Process results efficiently

        $entities = [];
        $i = 0;
        $processed_ids = []; // Track processed session IDs to avoid duplicates

        foreach ($result as $row) {
            // Skip if we've already processed this session
            if (in_array($row['session_id'], $processed_ids)) {
                continue;
            }

            $processed_ids[] = $row['session_id'];

            // Process date and time
            $start_time = date("g:i A", strtotime($row['start_datetime']));
            $end_time = date("g:i A", strtotime($row['end_datetime']));

            // Process banner image
            $bannerImage = '';
            $bannerUrl = '';
            if (!empty($row['cover_image1'])) {
                $bannerImage = change_img_src($row['cover_image1']);
                $bannerUrl = base_url() . 'session/details/' . $row['session_id'];
            }

            // Process vxPoll
            $vxPoll = array();
            //$vxPoll = $this->get_session_poll($row['session_id']);

            // Build entity array
            $entities[$i] = [
                'session_id' => (int)$row['session_id'],
                'type_id' => (int)$row['session_id'],
                'type' => 'session',
                'trending_type' => 'session',
                'cover_image' => change_img_src($row['cover_image']),
                'cover_image1' => change_img_src($row['cover_image1']),
                'cover_image2' => change_img_src($row['cover_image2']),
                'cover_image3' => change_img_src($row['cover_image3']),
                'cover_image4' => change_img_src($row['cover_image4']),
                'cover_image5' => change_img_src($row['cover_image5']),
                'session_topic' => $row['session_topic'],
                'session_description' => strip_tags($row['session_description']),
                'master_tag_ids' => $row['master_tag_ids'],
                'vault' => !empty($row['vault']) ? (int)$row['vault'] : 0,
                'is_locked' => $key_locked,
                'price' => $row['price'],
                'user_content_payment' => get_user_content_status($row['session_id'], 2, $user_master_id),
                'category_id' => (int)$row['category_id'],
                'start_datetime' => $row['start_datetime'],
                'ms_start_datetime' => $row['start_datetime'],
                'display_date' => $start_time . "-" . $end_time,
                'deeplink' => ($env == 'GL') ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0),
                'end_datetime' => $row['end_datetime'],
                'specialities_name' => $row['specialities_name'],
                'specialities_ids_and_names' => $this->explode_speciality_string($row['specialities_ids_and_names']),
                'ms_cat_name' => $row['category_name'],
                'category_image' => change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']),
                'client_logo' => change_img_src(base_url() . "uploads/logo/" . $row['client_logo']),
                'url' => base_url() . 'session/popup_with_detail/' . $row['session_id'],
                'ms_cat_logo' => change_img_src($row['category_logo']),
                'doctor_name' => $row['doctor_name'],
                'speciality' => $row['speciality'],
                'cpddetail' => $this->getcpddetails($row['session_id']),
                'color' => !empty($row['color']) ? $row['color'] : '#eb34e5',
                'document_path' => !empty($row['document_path']) ? base_url() . "uploads/mastersession_docs/" . $row['document_path'] : "",
                'comment' => !empty($row['comment']) ? $row['comment'] : "",
            ];

            // Process session doctors
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            //$session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = [];

            if (!empty($row['session_doctor_id'])) {
                $doctor_names = explode("----", $row['doctor_name']);
                $doctor_ids = explode("----", $row['session_doctor_id']);
                $doctor_profiles = !empty($row['profile']) ? explode("----", $row['profile']) : [];
                $doctor_images = !empty($row['profile_images']) ? explode("----", $row['profile_images']) : [];

                for ($j = 0; $j < count($doctor_ids); $j++) {
                    $image = isset($doctor_images[$j]) ? preg_replace('/\s+/', '%20', $doctor_images[$j]) : '';
                    $logic_image = '';

                    if ($image) {
                        if (stripos($image, "https://") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg;
                            $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $imgPr;
                        }
                    } else {
                        $logic_image = docimg;
                    }

                    $ses_doc_det_array[] = [
                        'session_doctor_id' => $doctor_ids[$j],
                        'session_doctor_name' => isset($doctor_names[$j]) ? $doctor_names[$j] : '',
                        'session_doctor_image' => change_img_src($logic_image),
                        'DepartmentName' => '',
                        'profile' => isset($doctor_profiles[$j]) ? $doctor_profiles[$j] : '',
                        'description' => '',
                        'subtitle' => ''
                    ];
                }
            }

            // Process sponsors
            $entities[$i]['sponsor_id'] = $row['sponsor_id'] ?? '';
            $sponsor_array = !empty($row['sponsor_id']) ? explode(",", $row['sponsor_id']) : [];
            $sponsor_det_array = [];

            if (!empty($row['sponsor']) && !empty($row['sponsor_logo'])) {
                $sponsor_names = explode(",", $row['sponsor']);
                $sponsor_logos = explode(",", $row['sponsor_logo']);

                for ($k = 0; $k < count($sponsor_names); $k++) {
                    $image = isset($sponsor_logos[$k]) ? preg_replace('/\s+/', '%20', $sponsor_logos[$k]) : '';
                    $logic_image = '';


                    if ($image) {
                        // Check if it's already a full URL
                        if (stripos($image, "https://") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg;
                            $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $imgPr;
                        }
                    } else {
                        $logic_image = docimg;
                    }


                    $sponsor_det_array[] = [
                        'sponsor_name' => isset($sponsor_names[$k]) ? $sponsor_names[$k] : '',
                        'sponsor_logo' => change_img_src($logic_image),
                        'sponsor_id' => isset($sponsor_array[$k]) ? $sponsor_array[$k] : ''
                    ];
                }
            }

            // Process session queries
            $queries = [];
            if (!empty($row['sessions_question'])) {
                $queries = explode("#", $row['sessions_question']);
            }

            // Add remaining data to entity
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $entities[$i]['sponsor_entities'] = $sponsor_det_array;
            $entities[$i]['session_queries'] = $queries;
            $entities[$i]['banner_image'] = $bannerImage;
            $entities[$i]['banner_url'] = $bannerUrl;
            $entities[$i]['survey'] = $vxPoll;
            $entities[$i]['disclaimer'] = disclaimer('knowledge');

            $i++;
        }

        // Cache the result for 15 minutes
        if (isset($this->myredis)) {
            $this->myredis->set($cacheKey, 900, $entities);
        }

        return $entities;

    }
    public function single_upcomingmastersession_details(
        $user_master_id,
        $session_id,
        $user_client_ids,
        $user_group_ids
    ) {
        // $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
        //     return "FIND_IN_SET('$x', ks.client_id)";
        // }, explode(',', $user_client_ids))) . ')';
        // $group_list = ' and (' . implode(' OR ', array_map(function ($x) {
        //     return "FIND_IN_SET('$x', ks.user_group_id)";
        // }, explode(',', $user_group_ids))) . ')';
        $sql = "SELECT
                    ks.session_id,
                    ks.session_doctor_id,
                    ks.session_topic,
                    ks.session_description,
                    ks.sessions_question,
                    ks.master_tag_ids,
                    ks.client_id,
                    ks.sponsor_id,
                    ks.user_group_id,
                    ks.category_id,
                    ks.start_datetime,
                    ks.end_datetime,
                    ks.speciality_id,
                    ks.total_seats,
                    ks.total_buffer,
                    ks.add_question_buffer_days,
                    ks.session_link,
                    ks.master_conf_provider_id,
                    ks.session_access_code,
                    ks.deeplink,
                    ks.in_deeplink,
                    ks.gl_deeplink,
                    ks.template_id,
                    ks.cert_template_id,
                    ks.display_in_dashboard,
                    ks.conf_phone_no,
                    ks.privacy_status,
                    ks.color,
                    ks.added_on,
                    ks.added_by,
                    ks.session_status,
                    ks.cover_image,
                    ks.modified_on,
                    ks.modified_by,
                    ks.is_recommended,
                    ks.is_multiday_session,
                    ks.break_json,
                    ks.status,
                    ks.is_featured,
                    ks.rating_flag,
                    ks.remarks,
                    ks.crm_id,
                    ks.img_credits,
                    ks.session_json,
                    ks.certified,
                    ks.env,
                    ks.notification_template,
                    ks.shortlink,
                    ks.invitefile,
                    ks.exitroute,
                    ks.is_share,
                    ks.is_like,
                    ks.is_comment,
                    sd.knwlg_sessions_docs_id,
                    sd.knwlg_sessions_id,
                    sd.document_path,
                    sd.comment,
                    sd.added_on,
                    sd.added_by,
                    sd.modified_on,
                    sd.modified_by,
                    sd.updated_at,
                    sd.updated_by,
                    sd.status,
                    cln.client_name,
                    cln.client_logo,
                    msct.category_name,
                    msct.category_logo,
                    ksp.participant_id,
                    ksp.room_link,
                    (ks.total_buffer + ks.total_seats) as tot_seat,
                    ksv.vendor_id,
                    ksv.video_embed_src,
                    ksv.room_id,
                    ksv.vouchpro_url,
                    ksv.go_to_meeting_url,
                    ksv.landing_page_url,
                    ksv.session_cast_type,
                    mst.status_name,
                    -- Aggregated fields
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names,
                    GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_doctor_id,
                    GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
                    GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') as speciality,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                    GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as profile,
                    GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as profile_images,
                    GROUP_CONCAT(ksp.participant_id) as PartName,
                    GROUP_CONCAT(DISTINCT ksp.participant_id) as users,
                    GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED
                FROM knwlg_sessions_V1 as ks
                LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
                LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id
                LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                LEFT JOIN knwlg_sessions_vendor as ksv ON ksv.session_id = ks.session_id
                LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
                LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
                LEFT JOIN knwlg_sessions_participant as ksp ON ksp.knwlg_sessions_id = ks.session_id
                LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id
                LEFT JOIN master_session_status as mst ON mst.master_session_status_id = ks.session_status
                WHERE ks.session_id = " . $session_id . "
                GROUP BY
                    ks.session_id, ks.session_doctor_id, ks.session_topic, ks.session_description, ks.sessions_question,
                    ks.master_tag_ids, ks.client_id, ks.sponsor_id, ks.user_group_id, ks.category_id, ks.start_datetime,
                    ks.end_datetime, ks.speciality_id, ks.total_seats, ks.total_buffer, ks.add_question_buffer_days,
                    ks.session_link, ks.master_conf_provider_id, ks.session_access_code, ks.deeplink, ks.in_deeplink,
                    ks.gl_deeplink, ks.template_id, ks.cert_template_id, ks.display_in_dashboard, ks.conf_phone_no,
                    ks.privacy_status, ks.color, ks.added_on, ks.added_by, ks.session_status, ks.cover_image, ks.modified_on,
                    ks.modified_by, ks.is_recommended, ks.is_multiday_session, ks.break_json, ks.status, ks.is_featured,
                    ks.rating_flag, ks.remarks, ks.crm_id, ks.img_credits, ks.session_json, ks.certified, ks.env,
                    ks.notification_template, ks.shortlink, ks.invitefile, ks.exitroute, ks.is_share, ks.is_like,
                    ks.is_comment, sd.knwlg_sessions_docs_id, sd.knwlg_sessions_id, sd.document_path, sd.comment,
                    sd.added_on, sd.added_by, sd.modified_on, sd.modified_by, sd.updated_at, sd.updated_by, sd.status,
                    cln.client_name, cln.client_logo, msct.category_name, msct.category_logo, ksp.participant_id,
                    ksp.room_link, ks.total_buffer, ks.total_seats, ksv.vendor_id, ksv.video_embed_src, ksv.room_id,
                    ksv.vouchpro_url, ksv.go_to_meeting_url, ksv.landing_page_url, ksv.session_cast_type, mst.status_name
                ORDER BY ks.start_datetime DESC";
        $query = $this->db->query($sql);
        $result = $query->result_array();
        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            $sql_check = "SELECT
            knwlg_sessions_participant_details.question,
            knwlg_sessions_participant_details.upload_documents,
            knwlg_sessions_participant.knwlg_sessions_participant_id,
            knwlg_sessions_participant.room_link
            FROM knwlg_sessions_participant
            JOIN knwlg_sessions_participant_details ON knwlg_sessions_participant_details.sessions_participant_id=knwlg_sessions_participant.knwlg_sessions_participant_id
            WHERE
            knwlg_sessions_participant.status=3
            AND knwlg_sessions_participant.participant_type='member'
            AND knwlg_sessions_participant.knwlg_sessions_id=" . $row['session_id'] . "
            AND participant_id=" . $user_master_id . " ";
            $query_check = $this->db->query($sql_check);
            $result_check = $query_check->row_array();
            if (!empty($result_check)) {
                $entities[$i]['is_booked'] = true;
                $entities[$i]['room_link'] = $result_check['room_link'];
                $entities[$i]['asked_query'] = $result_check['question'];
                $entities[$i]['upload_documents'] = $result_check['upload_documents'];
                $entities[$i]['my_participant_id'] = $result_check['knwlg_sessions_participant_id'];
            } else {
                $entities[$i]['is_booked'] = false;
                $entities[$i]['room_link'] = '';
                $entities[$i]['asked_query'] = "";
                $entities[$i]['my_participant_id'] = "";
            }
            $sqlCompl = "SELECT
                   survey_id
                    FROM
                    survey_user_answer sv
                    WHERE
                    sv.user_master_id = '" . $user_master_id . "'";
            $queryCompl = $this->db->query($sqlCompl);
            $resultCompl = $queryCompl->result();
            $complID = array();
            foreach ($resultCompl as $valCompl) {
                $complID[] = $valCompl->survey_id;
            }
            //print_r($complID); exit;
            $sqlInCompl = "SELECT
                   survey_id
                    FROM
                    survey_user_incomplete_answer sv
                    WHERE
                    sv.status = 3
                    and
                    sv.user_master_id = '" . $user_master_id . "'";
            $queryInCompl = $this->db->query($sqlInCompl);
            $resultInCompl = $queryInCompl->result();
            $incomplID = array();
            foreach ($resultInCompl as $valInCompl) {
                $incomplID[] = $valInCompl->survey_id;
            }
            $arrayFinal = array_unique(array_merge($complID, $incomplID));
            //print_r($arrayFinal); exit;
            $complIDStr = implode(",", (array)$arrayFinal);
            //echo $complIDStr ; exit;
            if ($complIDStr) {
                $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
            } else {
                $qryStr = '';
            }
            $sqlPoll = "SELECT
                            sv.survey_id,
                            sv.category,
                            sv.survey_title,
                            sv.survey_description,
                            sv.image,
                            sv.survey_points,
                            sv.survey_time,
                            sv.question_count,
                            sv.publishing_date,
                            sv.client_id,
                            sv.sponsor_ids,
                            sv.deeplink,
                            sv.gl_deeplink,
                            sv.verified,
                            sv.display_in_dashboard,
                            sv.privacy_status,
                            sv.template_id,
                            sv.color,
                            sv.points_on_approval,
                            sv.added_on,
                            sv.added_by,
                            sv.modified_on,
                            sv.modified_by,
                            sv.status,
                            sv.is_available_survey_portal,
                            sv.available_for_live_session,
                            sv.env,
                            sv.is_share,
                            sv.is_like,
                            sv.is_comment,
                            sv.approved_by,
                            sv.img_credits,
                            svd.data,
                            GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                            GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
                            cln.client_name,
                            cln.client_logo,
                            GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                            GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                        FROM survey sv
                        LEFT JOIN survey_to_speciality svts ON svts.survey_id = sv.survey_id
                        LEFT JOIN master_specialities_V1 ms ON ms.master_specialities_id = svts.speciality_id
                        JOIN client_master cln ON cln.client_master_id = sv.client_id
                        LEFT JOIN survey_to_sponsor suvTspon ON suvTspon.survey_id = sv.survey_id
                        LEFT JOIN client_master clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                        JOIN survey_detail svd ON svd.survey_id = sv.survey_id
                        JOIN survey_to_session stm ON stm.survey_id = sv.survey_id
                        LEFT JOIN survey_user_answer sua ON sua.survey_id = sv.survey_id
                        WHERE sv.status = 3
                        AND stm.session_id = " . $row['session_id'] . "
                        " . $qryStr . "
                        GROUP BY
                            sv.survey_id,
                            sv.category,
                            sv.survey_title,
                            sv.survey_description,
                            sv.image,
                            sv.survey_points,
                            sv.survey_time,
                            sv.question_count,
                            sv.publishing_date,
                            sv.client_id,
                            sv.sponsor_ids,
                            sv.deeplink,
                            sv.gl_deeplink,
                            sv.verified,
                            sv.display_in_dashboard,
                            sv.privacy_status,
                            sv.template_id,
                            sv.color,
                            sv.points_on_approval,
                            sv.added_on,
                            sv.added_by,
                            sv.modified_on,
                            sv.modified_by,
                            sv.status,
                            sv.is_available_survey_portal,
                            sv.available_for_live_session,
                            sv.env,
                            sv.is_share,
                            sv.is_like,
                            sv.is_comment,
                            sv.approved_by,
                            sv.img_credits,
                            svd.data,
                            cln.client_name,
                            cln.client_logo";
            $queryPoll = $this->db->query($sqlPoll);
            $resultPoll = $queryPoll->result();
            //echo $sqlPoll;
            // print_r($resultPoll);
            $vxPoll = array();
            foreach ($resultPoll as $valSurvey) {
                $dataArry = unserialize($valSurvey->data);
                $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                $str = preg_replace('/\\\"/', "\"", $json);
                $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);
                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {
                    if ($valSurvey->sponsor_logo) {
                        $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                if ($valSurvey->survey_id) {
                    $vxPoll[] = array(
                        "survey_id" => $valSurvey->survey_id,
                        "category" => $valSurvey->category,
                        "point" => $valSurvey->survey_points,
                        "json_data" => $str,
                        "survey_title" => $valSurvey->survey_title,
                        "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0),
                        //$valSurvey->deeplink,
                        "survey_description" => substr($valSurvey->survey_description, 0, 150),
                        "image" => change_img_src($valSurvey->image),
                        "specialities_name" => $valSurvey->specialities_name,
                        "specialities_ids_and_names" => $this->explode_speciality_string($valSurvey->specialities_ids_and_names),
                        "client_name" => $valSurvey->client_name,
                        "client_logo" => change_img_src('' . $valSurvey->client_logo),
                        "sponsor_name" => $valSurvey->sponsor,
                        "sponsor_logo" => change_img_src($sponsorLogo),
                        "publishing_date" => $valSurvey->publishing_date,
                    );
                }
            }
            // $entities[$i]['my_participant_id'] = $row['participant_id'];
            $entities[$i]['is_available'] = (strtotime($row['start_datetime']) < time()) ? false : true;
            $entities[$i]['session_id'] = $row['session_id'];
            $entities[$i]['type_id'] = $row['session_id'];
            $entities[$i]['type'] = 'session';
            $entities[$i]['trending_type'] = 'session';
            $coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
            $entities[$i]['cover_image'] = ($row['cover_image'] != '') ? change_img_src($row['cover_image']) : $coverImg;
            $entities[$i]['session_topic'] = $row['session_topic'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['speciality_id'] = $row['speciality_id'];
            $entities[$i]['session_description'] = strip_tags($row['session_description']);
            $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
            $entities[$i]['client_id'] = $row['client_id'];
            $entities[$i]['client_name'] = $row['client_name'];
            $entities[$i]['status_name'] = $row['status_name'];
            $entities[$i]['vendor_id'] = $row['vendor_id'];
            $entities[$i]['room_id'] = $row['room_id'];
            $entities[$i]['vouchpro_url'] = $row['vouchpro_url'];
            $entities[$i]['go_to_meeting_url'] = $row['go_to_meeting_url'];
            $entities[$i]['landing_page_url'] = $row['landing_page_url'];
            $entities[$i]['video_embed_src'] = $row['video_embed_src'];
            $entities[$i]['session_cast_type'] = $row['session_cast_type'];
            /**
             * new sponsor logic
             */
            $sponsorLogoArry = explode(",", $row['sponsor_logo']);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = $valueSponor;
                    }
                }
            } else {
                if ($row['sponsor_logo']) {
                    // if full path exist
                    if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                        $sponsorLogomix[] = $row['sponsor_logo'];
                    } else {
                        $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                    }
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($row['document_path'] != "" || $row['document_path'] != null) {
                $entities[$i]['file_size'] = round((filesize('./uploads/mastersession_docs/' . $row['document_path'] . '') / 1024)) . "Kb";
                $entities[$i]['document_path_exact_file_name'] = $row['document_path'];
                $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
                $entities[$i]['extension_logo_path'] = base_url() . "themes/front/images/" . get_logo_by_file_extension($row['document_path']);
            } else {
                $entities[$i]['document_path_exact_file_name'] = "";
                $entities[$i]['document_path'] = "";
                $entities[$i]['file_size'] = "";
                $entities[$i]['extension_logo_path'] = "";
            }
            if ($row['comment'] != "" || $row['comment'] != null) {
                $entities[$i]['comment'] = $row['comment'];
            } else {
                $entities[$i]['comment'] = "";
            }
            $entities[$i]['category_id'] = $row['category_id'];
            $entities[$i]['category_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['start_datetime'] = $row['start_datetime'];
            $entities[$i]['start_datetimex'] = strtotime($row['start_datetime']);
            $entities[$i]['now_datetimex'] = time();
            $start_time = $row['start_datetime'];
            $date = new DateTime($start_time);
            //$start_time = date("g:i A", strtotime($start_time));
            $now = new DateTime();
            $diff = date_diff($date, $now);
            $entities[$i]['days_remaining'] = abs($diff->format("%R%a")) + 1;
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $entities[$i]['display_time_format'] = $start_time . "-" . $end_time;
            $post_time = $row['start_datetime'];
            $phpdate = strtotime($post_time);
            $mysqldate = date('D,j M y  ', $phpdate);
            $entities[$i]['display_date_format'] = $mysqldate;
            $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
            $start_date = $row['start_datetime'];
            $buffer_day = $row['add_question_buffer_days'];
            $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
            $buffer_str = strtotime($last_display_date);
            $t = time();
            $date = new DateTime($last_display_date);
            $now = new DateTime();
            $diff = date_diff($date, $now);
            //print_r($diff);
            if ($t <= $buffer_str) {
                $dat_diff = abs($diff->format("%R%a"));
            } else {
                $dat_diff = 0;
            }
            $entities[$i]['view_edit_button_text'] = "";
            //echo $dat_diff; exit();
            if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
            }
            if ($question_users->question != "" && $row['session_status'] != 3) {
                $entities[$i]['view_edit_button_text'] = "View Case/Query";
            }
            $is_attended_array = array();
            $is_attended_array = explode(",", $row['IS_ATTENDED']);
            $part_array = array();
            $part_array = explode(",", $row['PartName']);
            $inc = 0;
            foreach ($part_array as $single) {
                if ($single == $user_master_id) {
                    $key_val = $inc;
                }
                $inc++;
            }
            $is_att = $is_attended_array[$key_val];
            $entities[$i]['missed_session_text'] = "";
            if ($is_att == 2) {
                $entities[$i]['missed_session_text'] = "You Missed The Session";
            }
            $entities[$i]['i_cant_attend_button'] = 0;
            $end_time = $row['end_datetime'];
            $end_time = strtotime($end_time);
            $now_time = date('Y-m-d H:i:s');
            $now_time = strtotime($now_time);
            if ($now_time < $end_time) {
                $entities[$i]['i_cant_attend_button'] = 1;
            }
            $cpt_flag = 0;
            $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
            if ($row['users'] == null) {
                $total_original_booking = 0;
            } else {
                $users_array = array();
                $users_array = explode(",", $row['users']);
                $total_original_booking = count($users_array);
            }
            if ($total_original_booking < $row['total_seats']) {
                $total_booking = $total_original_booking;
            }
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $total_booking = ($row['total_seats']) - 1;
            }
            if ($total_original_booking >= ($total_after_buffer)) {
                $total_booking = $row['total_seats'];
                $cpt_flag = 1;
            }
            if ($total_booking > 0) {
                $available_percent = ($total_booking / $row['total_seats']) * 100;
            } else {
                $available_percent = 0;
            }
            $available_percent = round($available_percent);
            if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $total_booking;
            } else {
                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
            }
            $perc = $available_percent;
            if ($cpt_flag == 0) {
                $entities[$i]['percentage'] = ceil($perc);
            } else {
                $entities[$i]['percentage'] = ceil($perc);
            }
            $color = get_progress_color($perc);
            $entities[$i]['color_profress_bar'] = $color;
            $entities[$i]['session_status'] = $row['session_status'];
            $entities[$i]['is_share'] = $row['is_share'];

            $entities[$i]['start_datetime'] = date(' jS F y', strtotime($row['start_datetime']));
            $end_time = $row['end_datetime'];
            $end_time = date("g:i A", strtotime($end_time));
            $start_time = $row['start_datetime'];
            $start_time = date("g:i A", strtotime($start_time));
            $entities[$i]['display_date'] = $start_time . "-" . $end_time;
            $entities[$i]['deeplink'] = ($env == 'GL') ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0);
            //$row['deeplink'];
            $entities[$i]['end_datetime'] = $row['end_datetime'];
            $entities[$i]['specialities_name'] = $row['specialities_name'];
            $entities[$i]['specialities_ids_and_names'] = $this->explode_speciality_string($row['specialities_ids_and_names']);
            $entities[$i]['ms_cat_name'] = $row['category_name'];
            $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
            $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
            $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
            $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
            $entities[$i]['doctor_name'] = $row['doctor_name'];
            $entities[$i]['speciality'] = $row['speciality'];
            $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
            $session_doc_array = explode(",", $row['session_doctor_id']);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                //print_r($var);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                if ($image) {
                    if (stripos($image, "https://") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg; //"uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                } else {
                    $logic_image = docimg;
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $ses_doc_det_array[$inc_pp]['description'] = $var[0]['description'];
                $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                $inc_pp++;
            }
            $entities[$i]['sponsor_id'] = $row['sponsor_id'];
            $sponsor_array = explode(",", $row['sponsor_id']);
            $sponsor_det_array = array();
            if (count($sponsor_array) > 1) {
                $inc_spp = 0;
                foreach ($sponsor_array as $single_sponsor) {
                    $var = sponsor_detail($single_sponsor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image = '';
                    }
                    $sponsor_det_array[$inc_spp]['sponsor_name'] = $var[0]['client_name'];
                    $sponsor_det_array[$inc_spp]['sponsor_logo'] = change_img_src($logic_image);
                    $sponsor_det_array[$inc_spp]['sponsor_id'] = $var[0]['client_master_id'];
                    $inc_spp++;
                }
            } else {
                if ($row['sponsor_id']) {
                    $var = sponsor_detail($row['sponsor_id']);
                    $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image = '';
                    }
                    $sponsor_det_array['sponsor_name'] = $var[0]['client_name'];
                    $sponsor_det_array['sponsor_logo'] = change_img_src($logic_image);
                    $sponsor_det_array['sponsor_id'] = $var[0]['client_master_id'];
                }
            }
            //$row['sessions_question'];
            if ($row['sessions_question'] != '') {
                $qu_val = explode("#", $row['sessions_question']);
                $queries = $qu_val;
            } else {
                $queries = array();
            }
            $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
            $entities[$i]['sponsor_entities'] = $sponsor_det_array;
            $entities[$i]['session_queries'] = $queries;
            $entities[$i]['banner_image'] = $bannerImage;
            $entities[$i]['banner_url'] = $bannerUrl;
            $entities[$i]['survey'] = $vxPoll;
            $entities[$i]['disclaimer'] = disclaimer('knowledge');
            $i++;
        }
        return $entities;
    }
    /**
     * @param $spec_string
     * @param $user_client_ids
     * @param $user_group_ids
     * @return mixed
     */
    public function related_compendium($spec_string, $user_client_ids, $user_group_ids)
    {
        $spec_list = ' and (' . implode(' OR ', array_map(function ($x) {
            return "FIND_IN_SET('$x', comp_qa_speciality_id)";
        }, explode(',', $spec_string))) . ')';
        $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
            return "FIND_IN_SET('$x', client_id)";
        }, explode(',', $user_client_ids))) . ')';
        $group_list = ' and (' . implode(' OR ', array_map(function ($x) {
            return "FIND_IN_SET('$x', user_group_id)";
        }, explode(',', $user_group_ids))) . ')';
        $sql = "SELECT * FROM knwlg_compendium WHERE status=3 " . $spec_list . " " . $client_list . " " . $group_list . "";
        $query = $this->db->query($sql);
        //echo $this->db->last_query(); exit();
        return $result = $query->result_array();
    }
    /**
     * @param $session_id
     * @param $user_id
     * @return mixed
     */
    public function check_duplicate_session_submit(
        $session_id,
        $user_id
    ) {
        $this->db->select(
            'sp.knwlg_sessions_participant_id'
        );
        $query = $this->db->from('knwlg_sessions_participant sp');
        $this->db->where('sp.knwlg_sessions_id', $session_id);
        $this->db->where('sp.participant_id', $user_id);
        $this->db->where('sp.participant_type', 'member');
        //$this->db->where($time_future);
        $query = $this->db->get();
        // echo "<pre>";print_r($query);die;
        return $query->num_rows();

    }
    /**
     * @param $data
     * @return mixed
     */
    public function session_submit_user_participation_request($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            #$this->db->save_queries = TRUE;
            $this->insertdb->insert('knwlg_sessions_participant', $data);
            # print_r($this->db->last_query());
            #die('hie');
            return $this->insertdb->insert_id();
        }
    }
    /**
     * @param $user_master_id
     * @param $session_id
     * @return array
     */
    public function getsessioncomment($user_master_id, $session_id)
    {
        $sql = "SELECT 
        user_detail.first_name,
        user_detail.last_name,
        knwlg_session_qna.* 
        FROM knwlg_session_qna 
        LEFT JOIN user_detail ON user_detail.user_master_id=knwlg_session_qna.user_master_id 
        WHERE knwlg_session_qna.type_id={$session_id} 
        AND knwlg_session_qna.type='session' 
        AND knwlg_session_qna.comment_approve_status = 1";
        $query = $this->db->query($sql);
        //echo $this->db->last_query(); exit();" . $group_list . "
        $result = $query->result_array();
        $i = 0;
        $entities = array();
        foreach ($result as $row) {
            if ($row['user_master_id'] == $user_master_id) {
                $entities[$i]['knwlg_comment_id'] = $row['knwlg_comment_id'];
                $entities[$i]['comment'] = $row['comment'];
                $entities[$i]['added_on'] = $row['added_on'];
                $entities[$i]['first_name'] = $row['first_name'];
                $entities[$i]['last_name'] = $row['last_name'];
            } else {
                if ($row['comment_approve_status'] == 1) {
                    $entities[$i]['knwlg_comment_id'] = $row['knwlg_comment_id'];
                    $entities[$i]['comment'] = $row['comment'];
                    $entities[$i]['added_on'] = $row['added_on'];
                    $entities[$i]['first_name'] = $row['first_name'];
                    $entities[$i]['last_name'] = $row['last_name'];
                }
            }
            $i++;
        }
        return $entities;
    }
    /**
     * @param $data
     * @return mixed
     */
    public function insert_comment_session($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $this->insertdb->insert('knwlg_session_qna', $data);
            return $this->insertdb->insert_id();
        }
    }
    /**
     * @param $data
     * @return mixed
     */
    public function insert_recording_request($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            // print_r($data); exit;
            $this->insertdb->insert('knwlg_session_recording_request', $data);
            // return $this->db->insert_id();
            $session_id = $data['session_id'];
            $user_master_id = $data['user_master_id'];
            // $this->Clirnetnotification_V2->sms($user_master_id,'register',"sessionregister",7549);exit;
            $this->db->select('kva.video_archive_id');
            $this->db->from('knwlg_video_archive kva');
            // $this->db->join('knwlg_sessions_V1 ksv','kva.video_archive_session_id=ksv.session_id');
            $this->db->where(['kva.video_archive_session_id' => $session_id, 'kva.status' => 3]);
            $result = $this->db->get();
            if ($result->num_rows() > 0) {
                $video_archive_id = $result->row()->video_archive_id;
                // echo $video_archive_id; exit;
                if (!empty($video_archive_id)) {
                    // $this->load->library('Clirnetnotification_V2');
                    // $this->Clirnetnotification_V2->send_sms(1,'clinicalvideo',1);
                    #$notify = new Clirnetnotification_V2();
                    // $notify->send_sms2(1,2,3);
                    // exit;
                    #if($notify->send_sms2($video_archive_id,'clinicalvideo',$user_master_id)){
                    $this->insertdb->set('sent_status', '1');
                    $this->insertdb->where(['session_id' => $session_id, 'user_master_id' => $user_master_id]);
                    $this->insertdb->update('knwlg_session_recording_request');
                    #}
                }
            }
            // echo 'not found'; exit;
        }
        return true;
    }
    public function getuserrecorddetails($user_master_id, $session_id)
    {
        //$this->db->save_queries = TRUE;
        $this->db->select('ksv.session_topic,msc.category_name,kva.video_archive_id as videoid,(select first_name from user_detail where user_master_id = ' . $user_master_id . ') as first_name,(select middle_name from user_detail where user_master_id = ' . $user_master_id . ') as middle_name,(select last_name from user_detail where user_master_id = ' . $user_master_id . ') as last_name,(select mobile_primary from user_master where user_master_id = ' . $user_master_id . ') as phone,(select email from user_master where user_master_id = ' . $user_master_id . ') as email,(select sm_token from user_temp_token where user_id = ' . $user_master_id . ' and type = "user_master") as sm_token,(select token from user_temp_token where user_id = ' . $user_master_id . ' and type = "user_master") as long_token');
        $this->db->from('knwlg_sessions_V1 as ksv');
        $this->db->join('knwlg_video_archive as kva', 'kva.video_archive_session_id=ksv.session_id');
        $this->db->join('master_session_category as msc', 'msc.mastersession_category_id=ksv.category_id');
        $this->db->where('kva.video_archive_session_id', $session_id);
        $this->db->limit(1);
        $query = $this->db->get();
        // $this->db->save_queries = TRUE;
        // print_r($this->db->last_query());
        //  exit;
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            // print_r($result);
            //$sid = $result[0]->s_id;
        }
        return $result;
        //exit;
    }
    /**
     * @param $data
     * @return mixed
     */
    public function insert_join_data($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        // print_r($data); exit;
        if (!empty($data)) {
            $this->insertdb->insert('knwlg_session_join_tracking', $data);
            $id = $this->insertdb->insert_id();
            //print_r($id);exit;
            if ((isset($id)) && ($id != 0)) {
                $this->db->select('id,points');
                $this->db->from('content_to_cpd');
                $this->db->where(array('status' => 3, 'type' => 3, 'type_id' => $data['session_id']));
                $query = $this->db->get();
                //print_r($this->db->last_query()); exit;
                if (($query) && ($query->num_rows() > 0)) {
                    $result = $query->result();
                    $array = array(
                        'type' => 3,
                        'type_id' => $data['session_id'],
                        'user_master_id' => $data['user_master_id'],
                        'ctc_id' => $result[0]->id,
                        'points' => $result[0]->points,
                        'status' => 2,
                        'added_by' => 0,
                        'added_on' => date('Y-m-d H:i:s')
                    );
                    $this->db->select('id');
                    $this->db->from('cpd_user_point');
                    $this->db->where(array('ctc_id' => $result[0]->id, 'type' => 3, 'type_id' => $data['session_id'], 'user_master_id' => $data['user_master_id']));
                    $query_check = $this->db->get();
                    //print_r($this->db->last_query());exit;
                    if (($query_check) && ($query_check->num_rows() > 0)) {
                        return $id;
                    } else {
                        $this->insertdb->insert('cpd_user_point', $array);
                        return $id;
                    }
                } else {
                    return $id;
                }
                //$this->db->select()
            }
        }
    }
    /**
     * @param $data
     * @return mixed
     */
    public function insert_rating_review($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $this->insertdb->insert('knwlg_session_rating_reviews', $data);
            return $this->insertdb->insert_id();
        }
    }
    /**
     * @param $data
     * @return mixed
     */
    public function session_submit_user_question($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $this->insertdb->insert('knwlg_sessions_participant_details', $data);
            return $this->insertdb->insert_id();
        }
    }
    /**
     * @param $data
     * @return mixed
     */
    public function session_update_user_question($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            if ($data['upload_documents'] != '') {
                $this->insertdb->set('upload_documents', $data['upload_documents']);
            } else {
                $this->insertdb->set('upload_documents', '');
            }
            $this->insertdb->set('question', $data['question']); //value that used to update column
            $this->insertdb->where('sessions_participant_id', $data['knwlg_sessions_participant_id']); //which row want to upgrade
            $this->insertdb->update('knwlg_sessions_participant_details');
            #print_r($this->insertdb->last_query()); exit;
            return $data['knwlg_sessions_participant_id'];
        }
    }
    /**
     * @param $data
     * @return mixed
     */
    public function cancel_session($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $this->insertdb->insert('knwlg_sessions_cancelletions', $data);
            return $this->insertdb->insert_id();
        }
    }
    /**
     * @param $data
     * @return mixed
     */
    public function delete_session_participant($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $first_del = 0;
            $this->insertdb->where('knwlg_sessions_participant_id', $data);
            $this->insertdb->delete('knwlg_sessions_participant');
            $first_del = $this->insertdb->affected_rows();
            if ($first_del > 0) {
                //$tables1 = array('knwlg_sessions_participant_details');
                $this->insertdb->where('sessions_participant_id', $data);
                $this->insertdb->delete('knwlg_sessions_participant_details');
                $second_del = $this->insertdb->affected_rows();
            }
            return $first_del + $second_del;
            exit();
        }
    }


    /**
     * @param $user_master_id
     * @param $session_id
     * @param $user_client_ids
     * @param $user_group_ids
     * @return array
     */
    public function profile_details($profile_id = '')
    {
        $qry = "SELECT  ksd.doctor_name,ksd.profile_image,ksd.profile,ksd.description,ksd.subtitle,
		GROUP_CONCAT(ms.spec_name ORDER BY ms.master_doc_spec_id) DepartmentName,
		GROUP_CONCAT(mdd.degree_name ORDER BY mdd.degree_name) degreename
		FROM    knwlg_sessions_doctors ksd
		INNER JOIN master_doctor_specialization ms
		ON FIND_IN_SET(ms.master_doc_spec_id, ksd.speciality) > 0
		INNER JOIN master_doctor_degree mdd
		ON FIND_IN_SET(mdd.deg_id, ksd.degree) > 0
		WHERE ksd.sessions_doctors_id=" . $profile_id . "
		";
        $query = $this->db->query($qry);
        $data_row = $query->result_array();
        $i = 0;
        $entities = array();
        foreach ($data_row as $row) {
            /*$h3 =  $this->getTextBetweenH3($row['description']);
            $txt =  $this->getTextBetweenTags('article',$row['description'],0);
             $html_arr_ouptut = str_replace("<p>", "<li>", $row['description']);
             $html_arr_ouptut = str_replace("</p>", "</li>", $html_arr_ouptut);
             $html_arr_ouptut = str_replace("<li>&nbsp;</li>", "", $html_arr_ouptut);
             //echo $row['description'];
             print_r($txt); exit;
             //echo $h3;*/
            $image = preg_replace('/\s+/', '%20', $row['profile_image']);
            // if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
            //     $logic_image = base_url() . "uploads/docimg/" . $image;
            // }
            if (stripos($image, "https://storage.googleapis.com") > -1) {
                $logic_image = $image;
            } else {
                $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
            }
            $entities['doctor_name'] = $row['doctor_name'];
            $entities['profile_image'] = change_img_src($logic_image);
            $entities['profile'] = change_img_src($row['profile']);
            $entities['description'] = $row['description'];
            $entities['subtitle'] = $row['subtitle'];
            $entities['DepartmentName'] = $row['DepartmentName'];
            $entities['degreename'] = $row['degreename'];
        }
        return $entities;
    }
    /**
     * @param $user_master_id
     * @param $session_id
     * @param $user_client_ids
     * @param $user_group_ids
     * @return array
     */
    public function profile_details_full($profile_id = '')
    {
        $qry = "SELECT
        ksd.doctor_name,
        ksd.profile_image,
        ksd.profile,
        ksd.description,
        ksd.subtitle,
        ksd.first_name,
        ksd.middle_name,
        ksd.last_name,
        ksd.link,
        ksd.display_speciality,
        ksd.adv_panel_disp,
        ksd.added_on,
        ksd.added_by,
        ksd.status,
        GROUP_CONCAT(ms.spec_name ORDER BY ms.master_doc_spec_id) DepartmentName,
        GROUP_CONCAT(mdd.degree_name ORDER BY mdd.degree_name) degreename
        FROM    knwlg_sessions_doctors ksd
        INNER JOIN master_doctor_specialization ms
        ON FIND_IN_SET(ms.master_doc_spec_id, ksd.speciality) > 0
        INNER JOIN master_doctor_degree mdd
        ON FIND_IN_SET(mdd.deg_id, ksd.degree) > 0
        WHERE ksd.sessions_doctors_id=" . $profile_id . "";
        $query = $this->db->query($qry);
        $data_row = $query->result_array();
        $i = 0;
        $entities = array();
        foreach ($data_row as $row) {
            $image = preg_replace('/\s+/', '%20', $row['profile_image']);
            // if (@getimagesize(base_url() . "uploads/docimg/" . $image)) {
            //     $logic_image = base_url() . "uploads/docimg/" . $image;
            // }
            if (stripos($image, "https://storage.googleapis.com") > -1) {
                $logic_image = $image;
            } else {
                $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
            }
            $entities['doctor_name'] = $row['doctor_name'];
            $entities['profile_image'] = change_img_src($logic_image);
            $entities['profile'] = $row['profile'];
            $entities['description'] = $row['description'];
            $entities['subtitle'] = $row['subtitle'];
            $entities['DepartmentName'] = $row['DepartmentName'];
            $entities['degreename'] = $row['degreename'];
            $entities['first_name'] = $row['first_name'];
            $entities['middle_name'] = $row['middle_name'];
            $entities['last_name'] = $row['last_name'];
            $entities['link'] = $row['link'];
            $entities['display_speciality'] = $row['display_speciality'];
            $entities['adv_panel_disp'] = $row['adv_panel_disp'];
            $entities['added_on'] = $row['added_on'];
            //  $entities['added_by']   = $row['added_by'];
            $entities['status'] = $row['status'];
        }
        return $entities;
    }
    /**
     * @param $string
     * @return mixed
     */
    public function getTextBetweenH3($string)
    {
        $pattern = "/<h3>(.*?)<\/h3>/";
        preg_match_all($pattern, $string, $matches);
        return $matches[1];
    }
    /**
     * @param $string
     * @param $tagname
     * @return mixed
     */
    public function everything_in_tags($string, $tagname)
    {
        $pattern = "#<\s*?$tagname\b[^>]*>(.*?)</$tagname\b[^>]*>#s";
        preg_match($pattern, $string, $matches);
        return $matches;
    }
    /**
     * @param $tag
     * @param $html
     * @param int $strict
     * @return array
     */
    public function getTextBetweenTags($tag, $html, $strict = 0)
    {
        /*** a new dom object ***/
        $dom = new domDocument();
        /*** load the html into the object ***/
        if ($strict == 1) {
            $dom->loadXML($html);
        } else {
            $dom->loadHTML($html);
        }
        /*** discard white space ***/
        $dom->preserveWhiteSpace = false;
        /*** the tag by its tag name ***/
        $content = $dom->getElementsByTagname($tag);
        /*** the array to return ***/
        $out = array();
        foreach ($content as $item) {
            /*** add node value to the out array ***/
            $out[] = $item->nodeValue;
        }
        /*** return the results ***/
        return $out;
    }
    /**
     * @param $name
     * @return array
     */
    public function getSpecialityIDsByName($name)
    {
        // $sql = "SELECT sp.master_specialities_id  FROM master_specialities_V1 as sp ".
        //      " WHERE sp.specialities_name LIKE  '%" . $name . "%'";
        $sql = "SELECT sp.master_specialities_id  FROM master_specialities_V1 as sp " .
            " WHERE sp.specialities_name LIKE  '%" . ' ' . $name . "%' OR sp.specialities_name LIKE '" . $name . "%'";
        //" OR sp.master_specialities_id IN (22,24,25,26 )" ;
        $query = $this->db->query($sql);
        $result = $query->result_array();
        $inc = 0;
        $output = array();
        foreach ($result as $sing_data) {
            $output[] = $result[$inc]['master_specialities_id'];
            $inc++;
        }
        //  return implode(",",$output);
        return $output;
    }
    /**
     * @param $doc_name
     * @return array
     */
    public function getSessionDoctorIDsByName($doc_name)
    {
        // $sql = "SELECT sd.sessions_doctors_id FROM knwlg_sessions_doctors as sd ".
        //      " WHERE sd.doctor_name LIKE  '%" . $doc_name . "%'" ;
        $sql = "SELECT sd.sessions_doctors_id FROM knwlg_sessions_doctors as sd " .
            " WHERE sd.doctor_name LIKE  '%" . ' ' . $doc_name . "%' OR sd.doctor_name LIKE '" . $doc_name . "%'";
        $query = $this->db->query($sql);
        $result = $query->result_array();
        $inc = 0;
        $output = array();
        foreach ($result as $sing_data) {
            $output[] = ($result[$inc]['sessions_doctors_id']);
            $inc++;
        }
        return $output;
    }
    /**
     * @param $client_id
     * @param $doc_ids_optional
     * @param $sp_ids_optional
     * @param $filter_sp_ids
     * @param $isRequestBlank
     * @return array
     */
    public function sessionsDoctorsListByClientID(
        $user_master_id,
        $client_id,
        $doc_ids_optional,
        $sp_ids_optional,
        $filter_sp_ids,
        $isRequestBlank,
        $from,
        $to
    ) {
        $env = get_user_env($user_master_id);
        if ($env) {
            if ($env != 'GL') {
                $envStatus = "AND (ksd.env ='GL' or ksd.env ='" . $env . "')";
            } else {
                $envStatus = "AND ksd.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        if ($filter_sp_ids != "" && $doc_ids_optional == "" && $sp_ids_optional == "") {
            $sql = "SELECT
                ksd.doctor_name,
                ksd.sessions_doctors_id,
                ksd.profile_image,
                ksd.profile,
                ksd.description,
                ksd.subtitle,
                ksd.first_name,
                ksd.middle_name,
                ksd.last_name,
                ksd.link,
                ksd.adv_panel_disp,
                ksd.added_on
                FROM    knwlg_sessions_doctors ksd
                left join sessiondoc_to_client  stc  on ksd.sessions_doctors_id = stc.session_doc_id
                WHERE
                stc.client_master_id IN (" . $client_id . ")
                " . $envStatus . "
                AND
                ksd.speciality IN (" . $filter_sp_ids . ")  AND ksd.is_featured=1 LIMIT " . $from . "," . $to . "";
        } elseif ($filter_sp_ids != "" && $doc_ids_optional != "") {
            $sql = "SELECT
                ksd.doctor_name,
                ksd.sessions_doctors_id,
                ksd.profile_image,
                ksd.profile,
                ksd.description,
                ksd.subtitle,
                ksd.first_name,
                ksd.middle_name,
                ksd.last_name,
                ksd.link,
                ksd.adv_panel_disp,
                ksd.added_on
                FROM    knwlg_sessions_doctors ksd
                left join sessiondoc_to_client  stc  on ksd.sessions_doctors_id = stc.session_doc_id
                WHERE
                stc.client_master_id IN (" . $client_id . ")
                " . $envStatus . "
                AND
                ksd.speciality IN (" . $filter_sp_ids . ")
                AND ksd.sessions_doctors_id IN (" . $doc_ids_optional . ")  AND ksd.is_featured=1 LIMIT " . $from . "," . $to . " ";
        } elseif ($filter_sp_ids == "" && $doc_ids_optional != "" && $sp_ids_optional != "") {
            $sql = "SELECT
                ksd.doctor_name,
                ksd.sessions_doctors_id,
                ksd.profile_image,
                ksd.profile,
                ksd.description,
                ksd.subtitle,
                ksd.first_name,
                ksd.middle_name,
                ksd.last_name,
                ksd.link,
                ksd.adv_panel_disp,
                ksd.added_on
                FROM    knwlg_sessions_doctors ksd
                left join sessiondoc_to_client  stc  on ksd.sessions_doctors_id = stc.session_doc_id
                WHERE
                stc.client_master_id IN (" . $client_id . ")
                " . $envStatus . "
                AND (ksd.sessions_doctors_id IN (" . $doc_ids_optional . ") OR ksd.speciality IN (" . $sp_ids_optional . "))  AND ksd.is_featured=1 LIMIT " . $from . "," . $to . "";
        } elseif ($filter_sp_ids == "" && $doc_ids_optional != "" && $sp_ids_optional == "") {
            $sql = "SELECT
                ksd.doctor_name,
                ksd.sessions_doctors_id,
                ksd.profile_image,
                ksd.profile,
                ksd.description,
                ksd.subtitle,
                ksd.first_name,
                ksd.middle_name,
                ksd.last_name,
                ksd.link,
                ksd.adv_panel_disp,
                ksd.added_on
                FROM    knwlg_sessions_doctors ksd
                left join sessiondoc_to_client  stc  on ksd.sessions_doctors_id = stc.session_doc_id
                WHERE
                stc.client_master_id IN (" . $client_id . ")
                " . $envStatus . "
                AND ksd.sessions_doctors_id IN (" . $doc_ids_optional . ")  AND ksd.is_featured=1  LIMIT " . $from . "," . $to . "";
        } elseif ($filter_sp_ids == "" && $doc_ids_optional == "" && $sp_ids_optional != "") {
            $sql = "SELECT
                ksd.doctor_name,
                ksd.sessions_doctors_id,
                ksd.profile_image,
                ksd.profile,
                ksd.description,
                ksd.subtitle,
                ksd.first_name,
                ksd.middle_name,
                ksd.last_name,
                ksd.link,
                ksd.adv_panel_disp,
                ksd.added_on
                FROM    knwlg_sessions_doctors ksd
                left join sessiondoc_to_client  stc  on ksd.sessions_doctors_id = stc.session_doc_id
                WHERE
                stc.client_master_id IN (" . $client_id . ")
                " . $envStatus . "
                AND ksd.speciality IN (" . $sp_ids_optional . ")  AND ksd.is_featured=1  LIMIT " . $from . "," . $to . "";
        } elseif ($isRequestBlank) {
            /* $sql = "SELECT
             sd.sessions_doctors_id
             FROM sessiondoc_to_client as stc
             LEFT JOIN knwlg_sessions_doctors as sd
             ON sd.sessions_doctors_id = stc.session_doc_id
             WHERE
             stc.client_master_id IN (" . $client_id . ")";*/
            $sql = "SELECT
                ksd.doctor_name,
                ksd.sessions_doctors_id,
                ksd.profile_image,
                ksd.profile,
                ksd.description,
                ksd.subtitle,
                ksd.first_name,
                ksd.middle_name,
                ksd.last_name,
                ksd.link,
                ksd.adv_panel_disp,
                ksd.added_on
                FROM    knwlg_sessions_doctors ksd
                left join sessiondoc_to_client  stc  on ksd.sessions_doctors_id = stc.session_doc_id
                WHERE
                stc.client_master_id IN (" . $client_id . ")
                 " . $envStatus . "
                 AND ksd.is_featured=1 LIMIT " . $from . "," . $to . "";
        } else {
            if ($sp_ids_optional != '' && !empty($sp_ids_optional)) {
                $spFilter = " AND ksd.speciality IN (" . $sp_ids_optional . ") ";
            } else {
                $spFilter = " ";
            }
            $sql = "SELECT  
                ksd.doctor_name, 
                ksd.sessions_doctors_id, 
                ksd.profile_image, 
                ksd.profile, 
                ksd.description, 
                ksd.subtitle,
                ksd.first_name, 
                ksd.middle_name, 
                ksd.last_name, 
                ksd.link, 
                ksd.adv_panel_disp,
                ksd.added_on
                FROM    knwlg_sessions_doctors ksd
                left join sessiondoc_to_client  stc  on ksd.sessions_doctors_id = stc.session_doc_id
                WHERE 
                    ksd.is_featured = 1 
                    {$spFilter}
                 " . $envStatus . "
                 LIMIT " . $from . "," . $to . "";
        }
        if ($sql != "") {
            // echo $sql;
            // exit;
            $query = $this->db->query($sql);
            $data_row = $query->result_array();
            //print_r($data_row); exit;
            $i = 0;
            $entities = array();
            foreach ($data_row as $row) {
                $image = preg_replace('/\s+/', '%20', $row['profile_image']);
                if ($image) {
                    $logic_image_path = docimg; //"uploads/docimg/" . $image;
                    $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    $logic_image = $imgPr;
                } else {
                    $logic_image = docimg; // base_url() . "uploads/docimg/no-image.png";
                }
                $entities[$i]['sessions_doctors_id'] = $row['sessions_doctors_id'];
                $entities[$i]['doctor_name'] = $row['doctor_name'];
                $entities[$i]['profile_image'] = change_img_src($image);
                $entities[$i]['profile'] = $row['profile'];
                $entities[$i]['description'] = $row['description'];
                $entities[$i]['subtitle'] = $row['subtitle'];
                $entities[$i]['DepartmentName'] = $row['DepartmentName'];
                $entities[$i]['degreename'] = $row['degreename'];
                $entities[$i]['first_name'] = $row['first_name'];
                $entities[$i]['middle_name'] = $row['middle_name'];
                $entities[$i]['last_name'] = $row['last_name'];
                $entities[$i]['link'] = $row['link'];
                $entities[$i]['adv_panel_disp'] = $row['adv_panel_disp'];
                $entities[$i]['added_on'] = $row['added_on'];
                $entities[$i]['type'] = 'Doctor';
                //  $entities['added_by']   = $row['added_by'];
                $i++;
            }
        }
        return $entities;
    }
    /**
     * @param $doctor_id
     * @return array
     */
    public function sessionsDoctorsDetail($doctor_id)
    {
        $sql = "SELECT
        ksd.doctor_name,
        ksd.sessions_doctors_id,
        ksd.profile_image,
        ksd.profile,
        ksd.description,
        ksd.subtitle,
        ksd.first_name,
        ksd.middle_name,
        ksd.last_name,
        ksd.link,
        ksd.adv_panel_disp,
        ksd.added_on
        FROM    knwlg_sessions_doctors ksd
        left join sessiondoc_to_client  stc  on ksd.sessions_doctors_id = stc.session_doc_id
        WHERE ksd.sessions_doctors_id=" . $doctor_id . "";
        if ($sql != "") {
            // echo $sql;
            //exit;
            $query = $this->db->query($sql);
            $data_row = $query->result_array();
            //print_r($data_row); exit;
            $i = 0;
            $entities = array();
            foreach ($data_row as $row) {
                $image = preg_replace('/\s+/', '%20', $row['profile_image']);
                if ($image) {
                    $logic_image_path = docimg; //"uploads/docimg/" . $image;
                    $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                    $logic_image = $imgPr;
                } else {
                    $logic_image = docimg; //base_url() . "uploads/docimg/no-image.png";
                }
                $entities[$i]['sessions_doctors_id'] = $row['sessions_doctors_id'];
                $entities[$i]['doctor_name'] = $row['doctor_name'];
                $entities[$i]['profile_image'] = change_img_src($row['profile_image']);
                $entities[$i]['profile'] = $row['profile'];
                $entities[$i]['description'] = $row['description'];
                $entities[$i]['subtitle'] = $row['subtitle'];
                $entities[$i]['DepartmentName'] = $row['DepartmentName'];
                $entities[$i]['degreename'] = $row['degreename'];
                $entities[$i]['first_name'] = $row['first_name'];
                $entities[$i]['middle_name'] = $row['middle_name'];
                $entities[$i]['last_name'] = $row['last_name'];
                $entities[$i]['link'] = $row['link'];
                $entities[$i]['adv_panel_disp'] = $row['adv_panel_disp'];
                $entities[$i]['added_on'] = $row['added_on'];
                //  $entities['added_by']   = $row['added_by'];
                $i++;
            }
        }
        return $entities;
    }
    /**
     * @param $data
     * @return mixed
     */
    public function session_submit_user_peer_request($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $this->insertdb->insert('user_request_match', $data);
            return $this->insertdb->insert_id();
        }
    }
    /**
     * @param $data
     * @return mixed
     */
    public function get_user_peer_request_by_session_doctor_id($id, $user_id)
    {
        $sql = "SELECT a.* FROM user_request_match a WHERE a.session_doctor_id = '$id'  AND a.user_master_id=" . $user_id . " ";
        $data = array();
        $query = $this->db->query($sql);
        $i = 0;
        if ($query != false && $query->num_rows() > 0) {
            foreach ($query->result() as $record) {
                $data[$i] = array();
                foreach ($record as $key => $val) {
                    $data[$i][$key] = $val;
                }
                $i++;
            }
        }
        return $data;
    }
    /**
     * @param $session_id
     * @return mixed
     */
    public function get_session_detail_by_session_id($session_id)
    {
        $this->db->select(
            'knwlg_sessions_V1.*'
        );
        $query = $this->db->from('knwlg_sessions_V1');
        $this->db->where('knwlg_sessions_V1.status', 3);
        $this->db->where('knwlg_sessions_V1.session_id', $session_id);
        $query = $this->db->get();
        //echo $this->db->last_query(); exit();
        $result = $query->result();
        return $result;
    }
    /**
     * @param $data
     * @return mixed
     */
    public function add_user_tag_details($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        // $staticDB = $this->load->database('static', TRUE);
        if (!empty($data)) {
            $this->insertdb->insert('user_to_tag', $data);
            return $this->insertdb->insert_id();
        }
    }
    /**
     * @param string $id
     * @return array
     */
    public function getMeetingDetailsBySessionID($id = '')
    {
        $entities = array();
        if (!empty($id)) {
            $sql = "SELECT sm.id, sm.session_id, platform_name ,  meeting_login_id ,
                    meeting_login_password,meeting_date, ks.session_status
                    FROM session_meeting as sm
                    LEFT JOIN knwlg_sessions_V1 ks ON ks.session_id = sm.session_id
                    WHERE sm.session_id = '$id' AND sm.status = 3 ";
            // echo $sql; exit;
            //exit;
            $query = $this->db->query($sql);
            $result = $query->row_array();
            if (!empty($result['id'])) {
                $entities['id'] = $result['id'];
                $entities['session_id'] = $result['session_id'];
                $entities['platform_name'] = $result['platform_name'];
                $entities['meeting_login_id'] = $result['meeting_login_id'];
                $entities['meeting_login_password'] = $result['meeting_login_password'];
                $entities['session_status'] = $result['session_status'];
                $entities['meeting_date'] = $result['meeting_date'];
            }
        }
        return $entities;
    }
    public function explode_speciality_string($string)
    {
        $final = array();
        if (!empty($string)) {
            $temp_sp_array = explode(",", $string);
            foreach ($temp_sp_array as $ky => $sp_id_name) {
                $sp_id_name_array = explode("#", $sp_id_name);
                $final[$ky] = array();
                $final[$ky]['id'] = $sp_id_name_array[0];
                $final[$ky]['name'] = $sp_id_name_array[1];
            }
        }
        return $final;
    }
    public function update_participant_name($data, $user_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            // print_r($data);
            // exit;
            $this->insertdb->where('user_master_id', $user_id);
            $this->insertdb->update('user_detail', $data);
            //print_r($this->insertdb);
            //$this->db->save_queries = true;
            //$str = $this->insertdb->last_query();
            //echo 'str ----- '.$str;
            //echo 'hello ...... ';
            // echo 'last query ----'.$this->insertdb->last_query() ;
            // exit();
            // echo 'effected row 2 ----- ' . $this->insertdb->affected_rows();
            // exit;
            return $this->insertdb->affected_rows();

        }
    }
    public function delete_certificate($cert_id, $session_id, $user_master_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        // $sqlSpecialityDel = "DELETE
        // FROM
        // clircert_certificate_master
        // where
        // id = " . $cert_id;
        // $this->insertdb->query($sqlSpecialityDel);
        // if ($this->insertdb->affected_rows() > 0) {
        //     $update['is_certificate_generated'] = 0;
        //     $this->insertdb->where('knwlg_sessions_id', $session_id);
        //     $this->insertdb->where('participant_id', $user_master_id);
        //     $this->insertdb->update('knwlg_sessions_participant', $update);
        // }

        $this->insertdb->set('status', 2);
        $this->insertdb->set('modified_by', $user_master_id);
        $this->insertdb->set('modified_on', date('Y-m-d H:i:s'));
        $this->insertdb->where('id', $cert_id);
        $this->insertdb->update('clircert_certificate_master');

        if ($this->insertdb->affected_rows() > 0) {
            $update['is_certificate_generated'] = 0;
            $this->insertdb->where('knwlg_sessions_id', $session_id);
            $this->insertdb->where('participant_id', $user_master_id);
            $this->insertdb->update('knwlg_sessions_participant', $update);
        }

    }
    public function session_doctor_ids($session_id)
    {
        $sql = "SELECT 
        session_doctor_id  
        FROM knwlg_sessions_V1 
        WHERE session_id = " . $session_id . "";
        $query = $this->db->query($sql);
        $resultSet = $query->row_array();
        return $resultSet['session_doctor_id'];
    }


    public function update_user_certificate($data, $certificate_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $this->insertdb->where('id', $certificate_id);
            $this->insertdb->update('clircert_certificate_master', $data);
            return $this->insertdb->affected_rows();
        }
    }
    public function insert_user_specific_certificate($data)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($data)) {
            $this->insertdb->insert('clircert_certificate_master', $data);
            $insert_id = $this->insertdb->insert_id();
            if ($insert_id > 0) {
                $update['is_certificate_generated'] = 1;
                $this->insertdb->where('knwlg_sessions_id', $data['type_id']);
                $this->insertdb->where('participant_id', $data['user_master_id']);
                $this->insertdb->update('knwlg_sessions_participant', $update);
                //echo $this->db->last_query() ; exit();
                return $insert_id;
            }
        }
    }
    public function getchannel($session_id, $user_master_id)
    {
        $this->db->select('cts.channel_master_id,cm.title,cm.logo,cm.privacy_status,cm.short_description,cTus.status as followed_status');
        $this->db->from('channel_to_session as cts');
        $this->db->join('channel_master as cm', 'cm.channel_master_id = cts.channel_master_id');
        $this->db->join('channel_to_user  as cTus', '(cTus.channel_master_id = cm.channel_master_id and user_master_id = "' . $user_master_id . '"   )');
        $this->db->where('cts.session_id', $session_id);
        $this->db->limit(1);
        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            $result = $query->result();
            $chid = array('channel_id' => $result[0]->channel_master_id, 'title' => $result[0]->title, 'logo' => $result[0]->logo, 'description' => $result[0]->short_description, 'privacy_status' => $result[0]->privacy_status, 'followed_status' => $result[0]->followed_status);
        } else {
            $chid = null; //array();
        }
        return $chid;
    }
    public function getcpddetails($session_id)
    {
        $this->db->select('id');
        $this->db->from('Master_service');
        $this->db->where('name', 'session');
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            $this->db->select('ctc.id,ctc.points,c.name,c.short_name');
            $this->db->from('content_to_cpd as ctc');
            $this->db->join('council as c', 'c.id=ctc.mc_id');
            $this->db->where(array('ctc.type_id' => $session_id, 'ctc.status' => 3, 'ctc.type' => $result[0]->id));
            $query = $this->db->get();
            #print_r($this->db->last_query()); exit;
            if (($query) && ($query->num_rows())) {
                return $query->result();
            } else {
                return array();
            }
        } else {
            return array();
        }
    }
    public function getcertificatetype($certificate_id)
    {
        if ($certificate_id != '') {
            $this->db->select('type');
            $this->db->from('clircert_certificate_master');
            $this->db->where('id', $certificate_id);
            $query = $this->db->get();
            // print_r($this->db->last_query());
            // exit;
            if (($query) && ($query->num_rows() > 0)) {
                $result = $query->result();
                //print_r($result); exit;
                return $result[0]->type;
            } else {
                return '';
            }
        } else {
            return '';
        }
    }
    public function session_user($session_id, $user_master_id)
    {
        $sql = "SELECT
        ud.first_name,
        ud.middle_name,
        ud.last_name,
        ks.session_topic,
        ks.start_datetime,
        cct.*,
        ks.session_id,
        ksp.participant_id,
        -- ks.cert_template_id
        stc.cert_template_id
        FROM knwlg_sessions_participant ksp
        LEFT JOIN user_master um ON um.user_master_id = ksp.participant_id 
        JOIN user_detail ud ON ud.user_master_id = ksp.participant_id 
        LEFT JOIN knwlg_sessions_V1 ks ON ks.session_id = ksp.knwlg_sessions_id
        LEFT JOIN session_to_certificate stc ON stc.session_id = ksp.knwlg_sessions_id
        LEFT JOIN clircert_certificate_templete cct ON cct.id = stc.cert_template_id
        WHERE
        -- ks.cert_template_id IS NOT NULL 
         stc.cert_template_id != 0 
        -- AND ksp.is_certificate_generated = 0  
         AND ksp.knwlg_sessions_id = {$session_id} 
         AND ksp.participant_id = {$user_master_id}";
        // echo ' session_user sql ----'.$sql;
        // exit;
        $query = $this->db->query($sql);
        // print_r($this->db->last_query());
        // exit;
        return $query->result();


    }
    public function is_generated_status($user_master_id, $session_id)
    {
        $this->db->select('is_certificate_generated');
        $this->db->from('knwlg_sessions_participant');
        $this->db->where(array('participant_id' => $user_master_id, 'knwlg_sessions_id' => $session_id));
        $query = $this->db->get();

        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            if ($result[0]->is_certificate_generated == 1) {
                //$array = array('is_certificate_generated'=>0);
                $this->insertdb->set('is_certificate_generated', 0); //value that used to update column
                $this->insertdb->where('participant_id', $user_master_id); //which row want to upgrade
                $this->insertdb->where('knwlg_sessions_id', $session_id);
                $this->insertdb->update('knwlg_sessions_participant');
                //print_r($this->db->last_query()); exit;
            }
        }
        return 0;
    }
    public function getcpdpoints($session_id, $user_master_id)
    {
        $this->db->select('points');
        $this->db->from('cpd_user_point');
        $this->db->where(array('status' => 3, 'user_master_id' => $user_master_id, 'type_id' => $session_id, 'type' => 3));
        $query = $this->db->get();
        //print_R($this->db->last_query()); exit;
        if (($query) && ($query->num_rows() > 0)) {
            $res = $query->result();
            return $res[0]->points;

        } else {
            return 'NA';
        }
    }
    /**
     * Summary of live_sessions
     * @param mixed $user_master_id
     * @return array
     */
    public function live_sessions($user_master_id)
    {
        return live_sessions($user_master_id);
    }
    public function getusersessiondetail($session_id, $user_master_id)
    {
        $result = array();
        //$this->db->save_queries = TRUE;
        $this->db->select('ksv.session_id,ksv.session_topic,ksv.session_description,ksv.start_datetime,ksv.end_datetime,ksv.invitefile,msc.category_name,stc.contentname,stc.schdeduledetail,(select first_name from user_detail where user_master_id=' . $user_master_id . ') as first_name,(select middle_name from user_detail where user_master_id=' . $user_master_id . ') as middle_name,(select last_name from user_detail where user_master_id=' . $user_master_id . ') as last_name,(select mobile_primary from user_master where user_master_id=' . $user_master_id . ') as phone,(select email from user_master where user_master_id=' . $user_master_id . ') as email,(select master_user_type_id from user_master where user_master_id=' . $user_master_id . ') as master_user_type_id,(select sm_token from user_temp_token where user_id=' . $user_master_id . ' and type="user_master" limit 1) as sm_token,(select token from user_temp_token where user_id=' . $user_master_id . ' and type="user_master" limit 1) as long_token');
        $this->db->from('knwlg_sessions_V1 as ksv');
        $this->db->join('master_session_category as msc', 'msc.mastersession_category_id=ksv.category_id', 'left');
        $this->db->join('schedule_to_content as stc', 'stc.content_id=ksv.session_id', 'left');
        $this->db->where('ksv.session_id', $session_id);
        $query = $this->db->get();
        //$this->db->save_queries = TRUE;
        //print_r($this->db->last_query()); exit;
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
        }
        return $result;
    }
    public function getusersessiononesignaldetail($user_master_id)
    {
        $result = array();
        $this->db->select('onesignal');
        $this->db->from('user_notification_communication');
        $this->db->where('user_master_id', $user_master_id);
        $querynotification = $this->db->get();
        if (($querynotification) && ($querynotification->num_rows() > 0)) {
            $result = $querynotification->result();
        }
        return $result;
    }
    public function getcpdstatus($session_id)
    {
        $sql = "select id from content_to_cpd where type_id=$session_id and status = 3";
        $query = $this->db->query($sql);
        if (($query) && ($query->result() > 0)) {
            $result = $query->result();
            if ($result[0]->id != '') {
                return "cpd_points";
            } else {
                return "session";
            }
        } else {
            return "session";
        }
    }
    public function certificate_available($user_master_id, $session_id, $cpdstatus)
    {
        $this->db->select('id');
        $this->db->from('clircert_certificate_master');
        $this->db->where(array('user_master_id' => $user_master_id, 'type' => $cpdstatus, 'type_id' => $session_id));
        $query = $this->db->get();
        // print_R($this->db->last_query()); exit;
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            return $result[0]->id;
        } else {
            return '';
        }
    }
    public function insertcrondetails($array)
    {
        $this->insertdb->insert('cron_notification', $array);
        //print_r($this->db->last_query()); exit;
        return true;
    }
}
