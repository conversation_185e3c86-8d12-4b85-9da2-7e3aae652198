WITH VideoData AS (
                SELECT 
                    cm.video_archive_id,
                    cm.video_archive_question,
                    cm.video_archive_answer,
                    cm.video_archive_question_raw,
                    cm.video_archive_answer_raw,
                    cm.video_archive_file_img,
                    cm.is_share,
                    cm.video_archive_file_img_thumbnail,
                    cm.deeplink,
                    cm.gl_deeplink,
                    cm.added_on,
                    cm.publication_date,
                    cm.duration,
                    cm.type,
                    cm.vendor,
                    cm.src,
                    cm.video_archive_speciality_id,
                    cm.display_in_dashboard
                FROM 
                    knwlg_video_archive AS cm
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = cm.video_archive_id AND cTenv.type = 3

                JOIN knwlg_video_tracking_data as kvtd ON kvtd.content_id=cm.video_archive_id AND kvtd.content_type='video_archive' AND kvtd.user_master_id= 879

                LEFT JOIN video_archive_to_specialities  as cmTs ON cmTs.video_archive_id = cm.video_archive_id   
                WHERE 
                    cm.status = 3
                    AND (cTenv.env = 2 OR cTenv.env = 1)
                ORDER BY 
                    cm.display_in_dashboard DESC,
                    cm.publication_date DESC
                LIMIT 0,
10
            ),
            SpecialitiesData AS (
                SELECT 
                    cmTs.video_archive_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                FROM 
                    VideoData vd
                JOIN 
                    video_archive_to_specialities AS cmTs ON cmTs.video_archive_id = vd.video_archive_id
                JOIN 
                    master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                GROUP BY 
                    cmTs.video_archive_id
            ),
            SponsorsData AS (
                SELECT 
                    cmTspon.video_archive_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_master_id) AS sponsor_ids,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                FROM 
                    VideoData vd
                LEFT JOIN 
                    video_archive_to_sponsor AS cmTspon ON cmTspon.video_archive_id = vd.video_archive_id
                LEFT JOIN 
                    client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                GROUP BY 
                    cmTspon.video_archive_id
            ),
            TrackingData AS (
                SELECT 
                    kvtd.content_id AS video_archive_id,
                    kvtd.play_time
                FROM 
                    VideoData vd
                LEFT JOIN 
                    knwlg_video_tracking_data AS kvtd ON kvtd.content_id = vd.video_archive_id 
                        AND kvtd.content_type = 'video_archive' 
                        AND kvtd.user_master_id = 879
            ),
            SessionData AS (
                SELECT 
                    ks.session_id,
                    ks.session_doctor_id,
                    msct.category_name,
                    msct.category_logo
                FROM 
                    VideoData vd
                LEFT JOIN 
                    knwlg_sessions_V1 AS ks ON ks.session_id = vd.video_archive_id
                LEFT JOIN 
                    master_session_category AS msct ON msct.mastersession_category_id = ks.category_id
            ),
            RatingData AS (
                SELECT 
                    vd.video_archive_id,
                    (SELECT COUNT(rt.rating) FROM knwlg_rating rt 
                     WHERE rt.post_id = vd.video_archive_id AND rt.post_type = 'video_archive' AND rt.rating != 0) AS averageRating,
                    rtmy.rating AS myrating
                FROM 
                    VideoData vd
                LEFT JOIN 
                    knwlg_rating AS rtmy ON rtmy.post_id = vd.video_archive_id 
                        AND rtmy.post_type = 'video_archive' 
                        AND rtmy.rating != 0 
                        AND rtmy.user_master_id = 879
            ),
            VaultData AS (
                SELECT 
                    kv.post_id AS video_archive_id,
                    kv.status AS vault
                FROM 
                    VideoData vd
                LEFT JOIN 
                    knwlg_vault AS kv ON kv.post_id = vd.video_archive_id 
                        AND kv.type_text = 'video_archive' 
                        AND kv.user_id = 879
            ),
            PaymentData AS (
                SELECT 
                    uTpyCont.type_id AS video_archive_id,
                    cTenv.price,
                    uTpyCont.status AS user_contnet_payment_status
                FROM 
                    VideoData vd
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = vd.video_archive_id AND cTenv.type = 3
                LEFT JOIN 
                    payment_user_to_content AS uTpyCont ON uTpyCont.type_id = vd.video_archive_id 
                        AND uTpyCont.type = 3 
                        AND uTpyCont.user_master_id = 879
            ),
            ClientData AS (
                SELECT 
                    cm.video_archive_id,
                    cln.client_name,
                    cln.client_logo
                FROM 
                    VideoData vd
                JOIN 
                    knwlg_video_archive AS cm ON cm.video_archive_id = vd.video_archive_id
                LEFT JOIN 
                    client_master AS cln ON cln.client_master_id = cm.client_id
            )
            SELECT 
                vd.video_archive_id AS type_id,
                vd.video_archive_question,
                vd.video_archive_answer,
                vd.video_archive_question_raw,
                vd.video_archive_answer_raw,
                vd.video_archive_file_img,
                vd.is_share,
                vd.video_archive_file_img_thumbnail,
                vd.deeplink,
                vd.gl_deeplink,
                vd.added_on,
                vd.publication_date,
                vd.duration,
                vd.type,
                vd.vendor,
                vd.src,
                vd.video_archive_speciality_id,
                td.play_time,
                pd.price,
                pd.user_contnet_payment_status,
                cd.client_name,
                cd.client_logo,
                sd.session_doctor_id,
                sd.category_name,
                sd.category_logo,
                sp.sponsor,
                sp.sponsor_ids,
                sp.sponsor_logo,
                spec.specialities_name,
                spec.specialities_ids_and_names,
                vdData.vault,
                rd.averageRating,
                rd.myrating
            FROM 
                VideoData vd
            LEFT JOIN 
                SpecialitiesData spec ON spec.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                SponsorsData sp ON sp.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                TrackingData td ON td.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                SessionData sd ON sd.session_id = vd.video_archive_id
            LEFT JOIN 
                RatingData rd ON rd.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                VaultData vdData ON vdData.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                PaymentData pd ON pd.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                ClientData cd ON cd.video_archive_id = vd.video_archive_id
            ORDER BY 
                vd.display_in_dashboard DESC,
                vd.publication_date DESC