<?php

defined('BASEPATH') or exit('No direct script access allowed');
require dirname(__FILE__) . '/../libraries/NEW_REST_Controller.php';
///require  'vendor/autoload.php';
//use \Firebase\JWT\JWT;
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);
class Cta extends MY_REST_Controller
{
    private $token_data;
    private $logdata = array();

    public function __construct()
    {
        parent::__construct();
        $token = $this->input->get_request_header('Authorization');
        //this->set_response($all_data, REST_Controller::HTTP_OK); //This is the respon if success
        //Validate user
        // echo 'token: '.$token;
        // echo '----';
        $this->validate_token($token);
        $this->token_data = $this->get_token_data($token);
        //print_r($this->token_data);
        $this->load->helper('crmlogin');
        $this->load->library('form_validation');
        //$this->load->library('clirnet_notification');
        $this->load->library('Clirnetnotification_V3');
        $this->load->model('cta_model');
        //$this->load->helper('image');

        /**
         * For api detail table
         */
        $this->logdata['called_on'] = date('Y-m-d H:i:s', time());
        $this->logdata['process_start_time'] = date('Y-m-d H:i:s');
        $this->logdata['version'] = $this->input->get_request_header('version');
        //         ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);

    }

    public function index_get()
    {
        // echo time();
        // echo CI_VERSION;
        echo date('Y-m-d H:i:s');
    }

    public function update_schedule_post()
    {
        // echo "hello"; exit;
        $dataArray = json_decode(file_get_contents('php://input'), true);

        $user_master_id = $this->token_data->userdetail->user_master_id;

        // print_r($dataArray); exit();

        $data = array();
        if (!empty($dataArray['schedule_data'])) {
            $data = json_encode($dataArray['schedule_data']);
        } else {
            $data = json_encode(array());
        }
        // echo $data; exit;

        if ($this->cta_model->update_schedule($user_master_id, $dataArray, $data)) {
            $message = "You Should Get A Call Shortly.";
        } else {
            $message = "Error";
        }

        $output['message'] = $message;
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

        $this->set_response($output, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    public function listing_get()
    {

        $user_master_id = $this->token_data->userdetail->user_master_id;
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $client_ids = $this->token_data->userdetail->client_ids;
        //limit

        if (($this->input->get('type', true)) && ($this->input->get('type', true) != '')) {

            if (($this->input->get('limitTo', true)) && ($this->input->get('limitTo', true) != '')) {
                $limitTo = $this->input->get('limitTo', true);
            } else {
                $limitTo = '';
            }

            if (($this->input->get('limitFrom', true)) && ($this->input->get('limitFrom', true) != '')) {
                $limitFrom = $this->input->get('limitFrom', true);
            } else {
                $limitFrom = '';
            }
            //   echo $limitFrom;
            //   echo $limitTo;
            //echo 'user_master_id_listing: '.$user_master_id;
            $all_data = $this->cta_model->ctalist($this->input->get('type', true), $this->input->get('type_id', true), $user_master_id, $client_ids, $limitTo, $limitFrom);
            if ($all_data != '') {
                $message = "Success";
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                // $resData = (count($all_data) > 0 ) ? $all_data[0] : $all_data;
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
            } else {
                $message = "Failed";
                /**
                 * For api detail table
                 */
                $output['error'] = $message;
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            }
        } else {
            $message = "Failed";
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }
    }

    public function ctareport_post()
    {

        // Takes raw data from the request
        $json = file_get_contents('php://input');

        // Converts it into a PHP object
        $data = json_decode($json);

        if ($data != '') {
            $array = array(
                'user_master_id' => $this->token_data->userdetail->user_master_id,
                'cta_id' => $data->cta_id,
                'cta_type' => $data->cta_type,
                'cta_type_id' => (!empty($data->cta_type_id)) ? $data->cta_type_id : 0,
                'target_type' => $data->target_type,
                'target_type_id' => $data->target_type_id,
                'position' => $data->position,
                'added_on' => date('Y-m-d H:i:s')
            );

            $this->cta_model->ctareport($array);

            // ******* for leads ******* //
            $this->load->library('Background');
            $payloads = array(
                "uid" => $this->token_data->userdetail->user_master_id,
                "project_id" => 0 ,
                "utype" => null,
                "utm_source" => (isset($data->utm_source)) ? $data->utm_source : null,
                "consent_source" => "cta",
                "type_id" => $data->cta_id,
                "ip_address" => getenv('HTTP_CLIENT_IP') ?: getenv('HTTP_X_FORWARDED_FOR') ?: getenv('HTTP_X_FORWARDED') ?: getenv('HTTP_FORWARDED_FOR') ?: getenv('HTTP_FORWARDED') ?: getenv('REMOTE_ADDR'),
                "browser" => (isset($data->browser)) ? $data->browser : null,
                "status" => 3,
                "demo_type" => 0,
                "json_data" => null,
                "timestamp" => date("Y-m-d H:i:s"),
                "created_at" => date("Y-m-d H:i:s")
            );

            $this->background->do_in_background_http_post(LEADS_INSERT_URL, $this->input->get_request_header('Authorization'), $payloads);
            // ******* for leads ******* //


            $message = "Success";
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        } else {
            $message = "Failed";
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }
    }
    public function cta_submitted_forms_post()
    {
        // Takes raw data from the request
        $json = file_get_contents('php://input');

        // Converts it into a PHP object
        $data = json_decode($json);

        if ($data != '') {

            $array = array(
                'cta_id' => $data->cta_id,
                'cta_type' => $data->cta_type,
                'user_id' => $this->token_data->userdetail->user_master_id,
                'added_on' => date('Y-m-d H:i:s'),
                'submitted_form_json' => json_encode($data->submitted_form_json) // Encode the JSON data before insertion
            );

            $insert_id = $this->cta_model->cta_submitted_forms($array);
            //print_r($insert_id);exit;
            $message = "Success";
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

            $this->set_response($insert_id, $message, REST_Controller::HTTP_OK, $this->logdata); // This is the response if success
        } else {
            $message = "Failed";
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }
    }
}
