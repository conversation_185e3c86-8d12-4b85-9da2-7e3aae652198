<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Certificate_model extends CI_Model
{
    public function __construct()
    {
        $ci = get_instance();
        //cc
        $ci->load->helper('image');


    }
    public function all_certificate(
        $user_master_id = '',
        $client_ids = '',
        $limitFrom = '',
        $limitTo = '',
        $type = '',
        $from_date = '',
        $to_date = ''
    ) {

        if ($limitFrom != '' && $limitTo != '') {
            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {

            $limit = "limit 0, 5";
        }



        if (!empty($user_master_id)) {

            //$this->db->cache_on();

            $sql = "SELECT
            *
            FROM clircert_certificate_master 
            WHERE 
            user_master_id=" . $user_master_id . " 
            and status = 3
            order by  id desc " . $limit . "";
            //echo $sql; exit;
            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;
            $query = $this->db->query($sql);
            //$this->db->cache_off();
            $result = $query->result();
            //print_r($result); exit;
            $i = 1;
            $vx = array();
            foreach ($result as $val) {


                $vx[] = array(

                    "slno" => $i,
                    "type_id" => $val->id,
                    "type" => 'certificate',
                    "cert_type" => $val->type,
                    "date" => $val->certificate_issue_date, //date(' jS F y', strtotime($val->certificate_issue_date)),
                    "title" => html_entity_decode(strip_tags($val->certificate_title)),
                    "description" => html_entity_decode(strip_tags(substr($val->certificate_description, 0, 300))),
                    "description_short" => html_entity_decode(strip_tags(substr($val->certificate_description, 0, 300))),
                    "image" => change_img_src($val->image),
                    "file" => $val->file,
                    "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

                );


                $i++;
            }
            //print_r($vx);
            //exit;
            return $vx;
        }
    }
    /**
     * @param string $user_master_id= ''
     * @return array
     */
    public function detail(
        $type_id = '',
        $user_master_id = '',
        $from_type = ''
    ) {

        if (!empty($type_id)) {


            $sql = "SELECT
            *
            FROM clircert_certificate_master 
            
            WHERE 
            user_master_id=" . $user_master_id . " 
            and
            id = " . $type_id . " 
            order by  id desc ";

            //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
            //echo $sql; exit;

            $query = $this->db->query($sql);
            $val = $query->row();
            // print_r($val); exit;
            //poll start

            $vx = array(
                "type_id" => $val->type_id,
                "type" => 'certificate',
                 "cert_type" => $val->type,
                // "type_id" => $val->type_id,
                 "date" => $val->certificate_issue_date,//date(' jS F y', strtotime($val->certificate_issue_date)),
                 "title" => html_entity_decode(strip_tags($val->certificate_title)),
                "description" => html_entity_decode(strip_tags(substr($val->certificate_description, 0, 300))),
                "description_short" => html_entity_decode(strip_tags(substr($val->certificate_description, 0, 300))),
                "image" => change_img_src($val->image),
                "file" => $val->file,
                "year" => $val->year,
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                "disclaimer" => disclaimer('knowledge')
                //"disclaimer" => 'All scientific content on the platform is provided for general medical education purposes meant for registered medical practitioners only. The content is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. In no event will CLIRNET be liable for any decision made or action taken in reliance upon the information provided through this content.',

            );
            // if ($from_type) {}
            // print_r($vx); exit;
            return $vx;
        }
    }

    /**
     * @param $user_master_id
     * @param $acknowledge
     * @param $sync_status
     * @return mixed
     */
    public function add($title, $description, $file, $user_master_id, $year)
    {
        $this->insertdb = $this->load->database('insert', true);
        $data = array(

            "certificate_title" => $title,
            "certificate_description" => $description,
            "file" => $file,
            "type" => 'upload',
            "year" => $year,
            "user_master_id" => $user_master_id,
        );

        //$this->db->where("user_master_id", $user_master_id);
        $this->insertdb->insert("clircert_certificate_master", $data);
        //echo $this->db->last_query(); exit();
        return $this->insertdb->insert_id();
    }

    /**
     * @param $title
     * @param $description
     * @param $user_master_id
     * @return mixed
     */
    public function update($title, $description, $user_master_id, $id, $year)
    {
        $this->insertdb = $this->load->database('insert', true);
        $data = array(

            "certificate_title" => $title,
            "certificate_description" => $description,
            "year" => $year,

        );

        $this->insertdb->where("id", $id);
        $this->insertdb->update("clircert_certificate_master", $data);
        #echo $this->db->last_query(); exit();
        return $this->insertdb->affected_rows();
    }

    /**
     * @param $title
     * @param $description
     * @param $user_master_id
     * @param $id
     * @return mixed
     */
    public function delete($id)
    {
        $this->insertdb = $this->load->database('insert', true);
        $data = array(

            "status" => 2,


        );

        $this->insertdb->where("id", $id);
        $this->insertdb->update("clircert_certificate_master", $data);
        //echo $this->db->last_query(); exit();
        return $this->insertdb->affected_rows();
    }

    /**
     * @param $id
     * @return mixed
     */
    public function countCert($user_master_id)
    {
        $sql1 = "SELECT
        count(*) as total 
        FROM clircert_certificate_master 
        
        WHERE 
        user_master_id=" . $user_master_id . " 
        and status = 3";

        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query1 = $this->db->query($sql1);
        //$this->db->cache_off();
        $result1 = $query1->row_array();



        $sql2 = "SELECT
        count(*) as totalNew 
        FROM clircert_certificate_master 
        
        WHERE 
        user_master_id=" . $user_master_id . " 
        and 
        status = 3
        and 
        added_on >= (NOW() - INTERVAL 1 MONTH)";

        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query2 = $this->db->query($sql2);
        //$this->db->cache_off();
        $result2 = $query2->row_array();







        $sql3 = "SELECT
        count(*) as total 
        FROM clircert_certificate_master 
        
        WHERE 
        user_master_id=" . $user_master_id . " 
        and type = 'upload'
        and status = 3";

        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query3 = $this->db->query($sql3);
        //$this->db->cache_off();
        $result3 = $query3->row_array();






        $sql4 = "SELECT
        count(*) as total 
        FROM clircert_certificate_master 
        
        WHERE 
        user_master_id=" . $user_master_id . " 
        and type != 'upload'
        and status = 3";

        //echo $sql; exit;
        //exit;
        //add child checking in this sql
        //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //exit;
        //echo  $sql; exit;
        $query4 = $this->db->query($sql4);
        //$this->db->cache_off();
        $result4 = $query4->row_array();



        $array['total'] = $result1['total'];
        $array['new'] = $result2['totalNew'];




        $array['uploaded'] = $result3['total'];
        $array['given'] = $result4['total'];


        return $array;





    }
}
