<?php

defined('BASEPATH') || exit('No direct script access allowed');
class Knwlg_model extends CI_Model
{
    public function __construct()
    {
        $ci = get_instance();
        $ci->load->helper('image');
        $ci->load->helper('utility');
        $this->load->library('Myredis');
    }
    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function all_trending(
        $user_master_id,
        $client_ids,
        $group_ids,
        $limitFrom,
        $limitTo,
        $val
    ) {
        if (!empty($user_master_id)) {
            $sqlInt = "select
            specialities_id
            from
            user_to_interest
            where
            user_master_id = " . $user_master_id . "";
            $queryInt = $this->db->query($sqlInt);
            $resultInt = $queryInt->result_array();
            $specialities = array();
            foreach ($resultInt as $val) {
                $specialities[] = $val['specialities_id'];
            }
            if (!empty($specialities)) {
                $specialityIds = implode(",", (array)$specialities);
            }
            if ($specialityIds != '') {
                $specialities_query = ' and (' . implode(' OR ', array_map(function ($x) {
                    return "FIND_IN_SET('$x', fd.speciality_id)";
                }, explode(',', $specialityIds))) . ')';
            } else {
                $specialities = "";
            }
            if ($client_ids) {
                $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                    return "FIND_IN_SET('$x', fd.client_id)";
                }, explode(',', $client_ids))) . ')';
            }
            if ($limitFrom == 0 && $limitTo != '') {
                $limit = "limit " . $limitFrom . " , " . $limitTo;
            } else {
                $limit = "";
            }
            // if ($val != '') {
            //     //$searchQuery = " AND (fd.title like '%" . $val . "%' or fd.description like '%" . $val . "%')";
            // } else {
            //     $searchQuery = "";
            // }
            if (!empty($user_master_id)) {
                $sql = "SELECT
                    fd.type_id,
                    fd.type,
                    fd.title,
                    fd.description,
                    fd.image,
                    fd.session_doctor_id,
                    fd.added_on,
                    fd.publish_date,
                    fd.end_date,
                    ksc.category_name,
                    ksc.category_logo,
                    cln.client_name,
                    cln.client_logo,
                    GROUP_CONCAT(ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                    ks.session_status,
                    cm.comp_qa_file_img,
                    cm.type as con_type,
                    cm.vendor,
                    cm.src,
                    sdoc.doctor_name,
                    sdoc.profile_image
                    FROM feed_V1 as fd
                    LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, fd.speciality_id) > 0
                    LEFT JOIN client_master as cln ON cln.client_master_id = fd.client_id
                    LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, fd.session_doctor_id) > 0
                    LEFT JOIN knwlg_sessions_V1 as ks ON (ks.session_id=fd.type_id)
                    LEFT JOIN knwlg_compendium_V1 as cm ON (cm.comp_qa_id=fd.type_id and fd.type='comp')
                    LEFT JOIN master_session_category as ksc ON(ksc.mastersession_category_id=ks.category_id)
                    where
                    ks.session_status = 1
                    " . $client_list . "
                    and
                    fd.type='session'
                    and
                    (
                    (fd.publish_date_session > DATE_ADD(CURDATE(), INTERVAL 1 DAY ) AND fd.type='session' )
                    OR
                    (fd.publish_date <= CURDATE() AND fd.type='comp')
                    )
                    GROUP BY fd.feed_id
                    order by RAND() limit 0,10";
                $query = $this->db->query($sql);
                $result = $query->result();
                $i = 1;
                $vx = array();
                foreach ($result as $val) {
                    switch ($val->type) {
                        case 'comp':
                            /*if (@getimagesize(base_url() . "uploads/compendium/" . $val->image)) {
                                $img = image_thumb_url('uploads/compendium/' . $val->image, $val->image, 203, 304, '');
                            } else {
                                $img = '';
                            }*/
                            if ($val->comp_qa_file_img) {
                                $img = base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
                            } else {
                                $img = '';
                            }
                            $string = htmlentities($val->title, null, 'utf-8');
                            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                            $main_description = "";
                            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
                            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
                            $vx[] = array(
                                /*"slno" => $i,
                                "type_id" => $val->type_id,
                                "type" => $val->type,
                                "date" => date(' jS F y', strtotime($val->publish_date)),
                                "question" => $string,
                                "image" => $img,
                                "answer" => html_entity_decode( strip_tags(substr($val->description, 0, 300)) ) ,
                                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                                "client_name" => $val->client_name,
                                "client_logo" => base_url('uploads/logo/') . $val->client_logo,
                                "comment_count" => $val->count_comment,
                                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                                "myrating" => ($val->myrating != '') ? true : false,
                                "vault" => ($val->vault != '') ? $val->vault : 0,*/
                                "slno" => $i,
                                "con_type" => $val->con_type,
                                "vendor" => $val->vendor,
                                "src" => $val->src,
                                "type_id" => $val->type_id,
                                "type" => $val->type,
                                "date" => date(' jS F y', strtotime($val->publish_date)),
                                "question" => html_entity_decode(strip_tags($string)),
                                "image" => change_img_src($img),
                                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                                "client_name" => $val->client_name,
                                "client_logo" => change_img_src('' . $val->client_logo),
                                "comment_count" => $val->count_comment,
                                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                                "myrating" => ($val->myrating != '') ? true : false,
                                "vault" => ($val->vault != '') ? $val->vault : 0,
                            );
                            break;
                        case 'survey':
                            if ($val->comp_qa_file_img) {
                                $img = base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
                            } else {
                                $img = '';
                            }
                            $string = htmlentities($val->title, null, 'utf-8');
                            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                            $vx[] = array(
                                "slno" => $i,
                                "type_id" => $val->type_id,
                                "type" => 'survey',
                                "date" => date(' jS F y', strtotime($val->publish_date)),
                                "description" => html_entity_decode(strip_tags($string)),
                                "image" => change_img_src($img),
                            );
                            break;
                        case 'deal':
                            if ($val->comp_qa_file_img) {
                                $img = base_url() . "uploads/compendium/" . $val->comp_qa_file_img;
                            } else {
                                $img = '';
                            }
                            $string = htmlentities($val->title, null, 'utf-8');
                            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                            $vx[] = array(
                                "slno" => $i,
                                "type_id" => $val->type_id,
                                "type" => 'deal',
                                "date" => date(' jS F y', strtotime($val->publish_date)),
                                "description" => html_entity_decode(strip_tags($string)),
                                "product_name" => html_entity_decode(strip_tags($string)),
                                "image" => change_img_src($img),
                                "route" => '/prodcuts/test',
                            );
                            break;
                        case 'session':
                            /*if (@getimagesize(base_url() . "uploads/docimg/" . $val->profile_image)) {
                                $logic_image = base_url() . "uploads/docimg/" . $val->profile_image;
                            } else {
                                $logic_image = base_url() . "uploads/docimg/MConsult.png";
                            }*/
                            $logic_image = docimg; //base_url() . "uploads/docimg/" . $val->profile_image;
                            $start_time = $val->publish_date;
                            $start_time = date("g:i A", strtotime($start_time));
                            $session_doc_array = explode(",", $val->session_doctor_id);
                            $ses_doc_det_array = array();
                            $inc_pp = 0;
                            foreach ($session_doc_array as $single_doctor) {
                                $var = session_doc_detail($single_doctor);
                                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src(base_url() . "uploads/docimg/" . $image);
                                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                                $inc_pp++;
                            }
                            $end_time = $val->end_date;
                            $end_time = date("g:i A", strtotime($end_time));
                            $vx[] = array(
                                "slno" => $i,
                                "type_id" => $val->type_id,
                                "type" => $val->type,
                                "doctor_name" => $val->doctor_name,
                                "date" => date(' jS F y', strtotime($val->publish_date)),
                                "start_datetime" => date(' jS F y', strtotime($val->publish_date)),
                                "display_date" => $start_time . "-" . $end_time,
                                "ms_cat_name" => $val->category_name,
                                "session_status" => $val->session_status,
                                "image" => change_img_src($logic_image),
                                "image_raw_name" => change_img_src($val->profile_image),
                                "seesion_description" => strip_tags(substr($val->description, 0, 300)),
                                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                                "client_name" => $val->client_name,
                                "client_logo" => change_img_src('' . $val->client_logo),
                                "session_doctor_entities" => $ses_doc_det_array,
                            );
                            break;
                        case 'custom':
                            $vx[] = array(
                                "slno" => $i,
                                "type_id" => $val->type_id,
                                "type" => $val->type,
                                "date" => date(' jS F y', strtotime($val->publish_date)),
                                "description" => html_entity_decode(strip_tags($val->description)),
                            );
                            break;
                    }
                    $i++;
                }
                return $vx;
            }
        }
    }
    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function all_feed(
        $user_master_id,
        $client_ids,
        $group_ids,
        $limitFrom,
        $limitTo,
        $val
    ) {
        if ($client_ids) {
            $client_list_kcap = ' and (' . implode(' OR ', array_map(function ($x) {
                return "FIND_IN_SET('$x', kcp.client_id)";
            }, explode(',', $client_ids))) . ')';
            // Removed unused $client_list_comp variable
        }
        if ($limitFrom != '' and $limitTo != '') {
            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {
            $limit = "";
        }
        if ($val != '') {
            $searchQuery = " AND (fd.title like '%" . $val . "%' or fd.description like '%" . $val . "%')";
        } else {
            $searchQuery = "";
        }
        if (!empty($user_master_id)) {
            $sql = "SELECT
            fd.type_id,
            fd.type,
            fd.title,
            fd.description,
            fd.image,
            fd.session_doctor_id,
            fd.added_on,
            fd.publish_date,
            fd.end_date,
            ksc.category_name,
            ksc.category_logo,
            cln.client_name,
            cln.client_logo,
            ms.specialities_name,
            (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = fd.type_id and  rt.post_type=fd.type)as averageRating,
            rtmy.rating  as myrating,
            sdoc.doctor_name,
            sdoc.profile_image,
            (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = fd.type_id and kcm.type = fd.type)as count_comment,
            kv.status as vault
            FROM feed_V1 as fd
            LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = fd.category_id
            LEFT JOIN client_master as cln ON cln.client_master_id = fd.client_id
            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = fd.type_id and  rtmy.post_type=fd.type and rtmy.rating!=0 and  rtmy.user_master_id = " . $user_master_id . "
            LEFT JOIN knwlg_vault as kv ON kv.post_id = fd.type_id and  kv.type_text=fd.type and  kv.user_id = " . $user_master_id . "
            LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, fd.session_doctor_id) > 0
            LEFT JOIN knwlg_sessions as ks ON (ks.session_id=fd.type_id)
            LEFT JOIN master_session_category as ksc ON(ksc.mastersession_category_id=ks.category_id)
            where fd.status = 3
            AND fd.type = 'comp'
            AND DATE(fd.publish_date)<=DATE(NOW())
            " . $searchQuery . "
            order by fd.publish_date desc " . $limit . "";
            $query = $this->db->query($sql);
            $result = $query->result();
            $i = 1;
            $vx = array();
            foreach ($result as $val) {
                switch ($val->type) {
                    case 'comp':
                        if (@getimagesize(base_url() . "uploads/compendium/" . $val->image)) {
                            $img = image_thumb_url('uploads/compendium/' . $val->image, $val->image, 203, 304, '');
                        } else {
                            $img = '';
                        }
                        $string = htmlentities($val->title, null, 'utf-8');
                        $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                        $main_description = "";
                        $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->description);
                        $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                        $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                        $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
                        $vx[] = array(
                            "slno" => $i,
                            "type_id" => $val->type_id,
                            "type" => $val->type,
                            "category_logo" => change_img_src($val->category_logo),
                            "date" => date(' jS F y', strtotime($val->publish_date)),
                            "question" => html_entity_decode(strip_tags($string)),
                            "image" => change_img_src($img),
                            "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                            "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                            "client_name" => $val->client_name,
                            "client_logo" => change_img_src('' . $val->client_logo),
                            "comment_count" => $val->count_comment,
                            "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                            "myrating" => ($val->myrating != '') ? true : false,
                            "vault" => ($val->vault != '') ? $val->vault : 0,
                        );
                        break;
                    case 'kcap':
                        $img = image_thumb_url('uploads/kcap/image/' . $val->image, $val->image, 203, 304, '');
                        $vx[] = array(
                            "slno" => $i,
                            "type_id" => $val->type_id,
                            "type" => $val->type,
                            "date" => date(' jS F y', strtotime($val->added_on)),
                            "category_logo" => change_img_src($val->category_logo),
                            "image" => change_img_src($img),
                            "answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                            "specialities" => $val->specialities_name,
                            "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                            "client_name" => $val->client_name,
                            "client_logo" => change_img_src('' . $val->client_logo),
                            "comment_count" => $val->count_comment,
                            "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                            "myrating" => ($val->myrating != '') ? true : false,
                            "vault" => ($val->vault != '') ? $val->vault : 0,
                        );
                        break;
                    case 'session':
                        if (stripos($val->profile_image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $val->profile_image;
                        } else {
                            $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
                        }
                        $start_time = $val->publish_date;
                        $start_time = date("g:i A", strtotime($start_time));
                        $end_time = $val->end_date;
                        $end_time = date("g:i A", strtotime($end_time));
                        $vx[] = array(
                            "slno" => $i,
                            "type_id" => $val->type_id,
                            "type" => $val->type,
                            "doctor_name" => $val->doctor_name,
                            "date" => date(' jS F y', strtotime($val->publish_date)),
                            "category_logo" => change_img_src($val->category_logo),
                            "start_datetime" => date(' jS F y', strtotime($val->publish_date)),
                            "display_date" => $start_time . "-" . $end_time,
                            "ms_cat_name" => $val->category_name,
                            "image" => change_img_src($logic_image),
                            "image_raw_name" => change_img_src($val->profile_image),
                            "seesion_description" => strip_tags(substr($val->description, 0, 300)),
                            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                            "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                            "client_name" => $val->client_name,
                            "client_logo" => change_img_src('' . $val->client_logo),
                            "comment_count" => $val->count_comment,
                            "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                            "myrating" => ($val->myrating != '') ? true : false,
                            "vault" => ($val->vault != '') ? $val->vault : 0,
                        );
                        break;
                }
                $i++;
            }
            return $vx;
        }
    }
    /**
     * Optimized version of all_compendium with better performance
     *
     * @param string $user_master_id User ID
     * @param string $client_ids Client IDs
     * @param string $group_ids Group IDs
     * @param string $limitFrom Pagination start
     * @param string $limitTo Pagination limit
     * @param string $val Search value
     * @param string $spIds Speciality IDs
     * @param string $type Content type
     * @param string $from_date Start date
     * @param string $to_date End date
     * @return array Formatted results
     */
    public function all_compendium(
        $user_master_id = '',
        $client_ids = '',
        $group_ids = '',
        $limitFrom = '',
        $limitTo = '',
        $val = '',
        $spIds = '',
        $type = '',
        $from_date = '',
        $to_date = ''
    ) {
        if ($limitFrom != '' && $limitTo != '') {
            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {
            $limit = "limit 0, 5";
        }
        if (($spIds != '') && ($spIds != 0)) {
            $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $spIds . ")  and (cm.publication_date <= CURDATE() ) ) ) AND";
        }
        if ($spIds == '' && $spIds == 0) {
            $searchQuery[] = " (cm.publication_date <= CURDATE() ) AND";
        }
        // Date range filter
        if ($from_date != '' && $to_date != '') {
            $searchBYdate = " AND (cm.publication_date BETWEEN '" . $from_date . "' AND '" . $to_date . "' )";
        } else {
            $searchBYdate = " ";
        }
        if ($searchQuery) {
            $searchQueryStr = implode("or", $searchQuery);
        }
        if (empty($user_master_id)) {
            return [];
        }
        // Get environment settings
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $key_locked = get_user_package($user_master_id, 'comp');


        $is_video = $this->input->get('is_video');
        if ($is_video) {
            $is_videoSql = "AND cm.type = 'video'";
        }


        // Set type filters
        if ($type == 'featured') {
            $typeSql = "";
            $typedashboardstatus = "AND cm.display_in_dashboard =1 ";
        } elseif ($type == 1) {
            $typeSql = " AND cm.type = 'video'";
            $typedashboardstatus = "AND cm.display_in_dashboard !=1 ";
        } else {
            $typeSql = "";
            $typedashboardstatus = " ";
        }
        $cacheKey = 'getcompFeed_optimized' .$typedashboardstatus.$spIds.$limit.$env.$type.$limit.$is_video;
        if ($this->myredis->exists($cacheKey)) {
            //uat__getcompFeed_optimizedAND_cmdisplay_in_dashboard_1_limit_0__51featuredlimit_0__5
            $result = $this->myredis->get($cacheKey);
        } else {
            // Use Common Table Expressions for better performance
            $sql = "WITH TopCompendiums AS (
                SELECT cm.comp_qa_id
                FROM knwlg_compendium_V1 as cm
                LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id and cTenv.type = 1
                LEFT JOIN compendium_to_specialities as cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
                WHERE
                    cm.status=3
                    and cm.is_draft = 0
                    {$typeSql}
                    {$typedashboardstatus}
                    {$searchBYdate}
                    and {$searchQueryStr}
                    cm.privacy_status = 0
                    {$envStatus}
                    {$is_videoSql}
                    GROUP BY cm.comp_qa_id
                order by 
                cm.publication_date desc,
                cm.comp_qa_id desc
                {$limit}
            ),
            SpecialitiesAgg AS (
                SELECT
                    cmTs.comp_qa_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                FROM TopCompendiums tc
                JOIN compendium_to_specialities cmTs ON cmTs.comp_qa_id = tc.comp_qa_id
                JOIN master_specialities_V1 ms ON ms.master_specialities_id = cmTs.specialities_id
                GROUP BY cmTs.comp_qa_id
            ),
            SponsorsAgg AS (
                SELECT
                    cmTspon.comp_qa_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                    GROUP_CONCAT(DISTINCT clintspon.client_master_id) as sponsor_ids
                FROM TopCompendiums tc
                JOIN compendium_to_sponsor cmTspon ON cmTspon.comp_qa_id = tc.comp_qa_id
                JOIN client_master clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                GROUP BY cmTspon.comp_qa_id
            )
            SELECT
                cm.comp_qa_id as type_id,
                cm.comp_qa_question,
                cm.comp_qa_answer,
                cm.is_share,
                cm.start_like,
                cm.comp_qa_answer_raw as description,
                cm.comp_qa_question_raw as title,
                cm.comp_qa_file_img,
                cm.comp_qa_file_img_thumbnail,
                cm.added_on,
                cm.publication_date as publish_date,
                cln.client_name,
                cln.client_logo,
                cm.type,
                cm.vendor,
                cm.src,
                cm.deeplink,
                cm.gl_deeplink,
                cm.color,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                rtmy.rating as myrating,
                spec.specialities_name,
                spec.specialities_ids_and_names,
                spon.sponsor,
                spon.sponsor_logo,
                spon.sponsor_ids,
                cm.comp_qa_speciality_id,
                kv.status as vault,
                (select count(rt.rating) from knwlg_rating rt where rt.post_id = cm.comp_qa_id and rt.post_type='comp') as averageRating,
                (select count(kcm.knwlg_comment_id) from knwlg_comment kcm where kcm.type_id = cm.comp_qa_id and kcm.type = 'comp') as count_comment
            FROM TopCompendiums tc
            JOIN knwlg_compendium_V1 as cm ON cm.comp_qa_id = tc.comp_qa_id
            LEFT JOIN client_master as cln ON cln.client_master_id = cm.client_id
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id and cTenv.type = 1
            LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and kv.type_text='comp' and kv.user_id = ?
            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.comp_qa_id and rtmy.post_type='comp' and rtmy.rating!=0 and rtmy.user_master_id = ?
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.comp_qa_id and uTpyCont.type = 1 and uTpyCont.user_master_id = ?
            LEFT JOIN SpecialitiesAgg spec ON spec.comp_qa_id = cm.comp_qa_id
            LEFT JOIN SponsorsAgg spon ON spon.comp_qa_id = cm.comp_qa_id
            order by cm.publication_date desc";

            // echo $sql;
            // exit;

            $result = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id))->result();
            $this->myredis->set($cacheKey, $result);
        }
        // Format results
        $vx = array();
        $i = 1;
        foreach ($result as $val) {
            if ($val->comp_qa_file_img_thumbnail) {
                $logic_image = $val->comp_qa_file_img_thumbnail;
            } else {
                $logic_image = docimg;
            }
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            $sponsorLogomix = array();
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        $sp++;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = '' . $val->sponsor_logo;
                    $allsponsor[] = array('name' => $val->sponsor, "logo" => $val->sponsor_logo);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            $vx[] = array(
                "slno" => $i,
                "con_type" => $val->type,
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => $val->user_contnet_payment_status,
                "env" => $env,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "type_id" => $val->type_id,
                "is_share" => $val->is_share,
                "type" => 'comp',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "question" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($logic_image),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                "answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "sponsor_name" => $val->sponsor,
                "sponsor_id" => $val->sponsor_ids,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "comment_count" => $val->count_comment,
                "rating" => ($val->averageRating != '') ? $val->start_like + $val->averageRating : $val->start_like,
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "deeplink" => ($env == 'GL') ?
                    (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) :
                    (($val->deeplink != '') ? $val->deeplink : 0),
            );
            $i++;
        }
        return $vx;
    }

    public function all_comp()
    {
        $sql = "SELECT * FROM `knwlg_compendium_V1` WHERE `comp_qa_file_img` = ''";
        $query = $this->db->query($sql);
        $result = $query->result();
        foreach ($result as $val) {
            if (@getimagesize(base_url() . "uploads/compendium/" . $val->comp_qa_id . '.jpg')) {
                $img = $val->comp_qa_id . '.jpg';
                $sql = "UPDATE  knwlg_compendium_V1 SET comp_qa_file_img='" . $img . "' WHERE comp_qa_id=? ";
                return $this->db->query($sql, array($val->comp_qa_id));
            }
        }
    }
    /** Get all parents of a speciality
     *
     * @param $id
     * @return object
     */
    public function getAllParents($id)
    {
        $sqlFeed = "SELECT T2.master_specialities_id
            FROM (
                SELECT
                    @r AS _id,
                    (SELECT @r := parent_id FROM master_specialities_V1 WHERE master_specialities_id = _id) AS parent,
                    @l := @l + 1 AS lvl
                FROM
                (SELECT @r := " . $id . ", @l := 0) vars,
                    master_specialities_V1 h
                WHERE @r <> 0) T1
            JOIN master_specialities_V1 T2
            ON T1._id = T2.master_specialities_id
            where T2.master_specialities_id<>" . $id . "
            ORDER BY T1.lvl";
        $query = $this->db->query($sqlFeed);
        return $query->result();
    }

    /**
     * Summary of specialitiesByChannelId
     * @param mixed $channelId
     * @return mixed
     */
    private function specialitiesByChannelId($channelId)
    {
        $sql = "SELECT ms.master_specialities_id, ms.specialities_name FROM master_specialities_V1 as ms
        LEFT JOIN channel_to_specialities as cs ON  cs.specialities_id =  ms.master_specialities_id
        WHERE cs.channel_master_id = {$channelId}";
        return $this->db->query($sql)->result_array();
    }
    /**
     * Summary of totalFollowersCountByChannelId
     * @param mixed $channelId
     * @return mixed
     */
    private function totalFollowersCountByChannelId($channelId)
    {
        $sql =  "select
        count(DISTINCT(user_master_id)) as follower_count
        from
        channel_to_user
        where
        status = 3
        and
        channel_master_id = " . $channelId . "";
        $response = $this->db->query($sql)->row_array();
        return ($this->db->query($sql)->row_array()['follower_count']) ? $this->db->query($sql)->row_array()['follower_count'] : 0;
    }
    private function totalSessionActivityCountByChannelId($channelId)
    {
        #session
        $sessionSql = "SELECT COUNT(*) as total_activity FROM  channel_to_session as a
        WHERE a.channel_master_id = {$channelId}
        GROUP by a.channel_master_id";
        $totalSessionActivity = (int)($this->db->query($sessionSql)->row_array()['total_activity']) ? $this->db->query($sessionSql)->row_array()['total_activity'] : 0;
        return $totalSessionActivity;
    }
    /**
     * Summary of totalActivityCountByChannelId
     * @param mixed $channelId
     * @return int
     */
    public function totalActivityCountByChannelId($channelId, $user_master_id)
    {
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        $compendiumSql = "SELECT
                            COUNT(*) as total_activity
                        FROM
                            channel_to_compendium as a
                        INNER JOIN
                            knwlg_compendium_V1 as cm on cm.comp_qa_id = a.comp_qa_id
                        LEFT JOIN
                            content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id and  cTenv.type = 1
                        WHERE
                            a.channel_master_id = {$channelId}
                            AND cm.status = 3
                            AND cm.privacy_status in (0,1)
                            {$envStatus}
                        ";
        $compActivityResult = $this->db->query($compendiumSql)->row_array()['total_activity'];
        $totalCompendiumActivity = (int)($compActivityResult != '') ? $compActivityResult : 0;
        #survey
        $surveySql = "SELECT
                        COUNT(*) as total_activity
                    FROM
                        channel_to_survey as a
                    INNER JOIN
                        survey as sv on sv.survey_id = a.survey_id
                    INNER JOIN
                        survey_detail as svd ON svd.survey_id = sv.survey_id
                    LEFT JOIN
                        content_to_env as cTenv ON cTenv.type_id = sv.survey_id and cTenv.type = 6
                    WHERE
                        a.channel_master_id = {$channelId}
                        AND sv.status = 3
                        AND sv.privacy_status IN (0, 1)
                        AND sv.survey_id NOT IN (
                                SELECT survey_id FROM survey_user_answer sua WHERE sua.user_master_id = {$user_master_id}
                            )
                        AND sv.survey_id NOT IN (
                                SELECT survey_id FROM survey_user_incomplete_answer suia WHERE suia.user_master_id = {$user_master_id} AND suia.status = 3
                            )
                        {$envStatus}
                    GROUP BY
                        a.channel_master_id";
        $totalSurveyActivityResult = $this->db->query($surveySql)->row_array()['total_activity'];
        $totalSurveyActivity = (int)($totalSurveyActivityResult != '') ? $totalSurveyActivityResult : 0;
        #course
        $courseSql = "SELECT COUNT(*) as total_activity
            FROM
                channel_to_course as a
            INNER JOIN
                training_master as tm ON tm.id = a.course_id
            WHERE
                a.channel_master_id = {$channelId}
                AND tm.privacy_status IN (0,1)
                AND date(tm.published_date) <= CURRENT_DATE()
                AND tm.status = 3
            GROUP by
                a.channel_master_id";
        $totalCourseActivityResult = $this->db->query($courseSql)->row_array()['total_activity'];
        $totalCourseActivity = (int)($totalCourseActivityResult != '') ? $totalCourseActivityResult : 0;
        $sessionSql = "SELECT
                            COUNT(*) as total_activity
                        FROM
                            channel_to_session as a
                        INNER JOIN
                            knwlg_sessions_V1 as ks on ks.session_id = a.session_id
                        LEFT JOIN
                            content_to_env as cTenv ON cTenv.type_id = ks.session_id and  cTenv.type = 2
                        WHERE
                            a.channel_master_id = {$channelId}
                            AND ks.status = 3
                            AND  ks.privacy_status != 2
                            AND  ks.session_status not in (5,6)
                            {$envStatus}
                        GROUP by
                            a.channel_master_id
                        ";
        $totalSessionActivityResult = $this->db->query($sessionSql)->row_array()['total_activity'];
        $totalSessionActivity = (int) ($totalSessionActivityResult != '') ? $totalSessionActivityResult : 0;
        #archive video
        $archiveVideoSql = "SELECT
            COUNT(*) as total_activity
            FROM
                channel_to_video_archive as a
            INNER JOIN
                knwlg_video_archive as kva on kva.video_archive_id = a.video_archive_id
            LEFT JOIN
                content_to_env as cTenv ON cTenv.type_id = kva.video_archive_id and  cTenv.type = 3
            WHERE
                a.channel_master_id = {$channelId}
                AND kva.status = 3
                AND kva.privacy_status != 2
                {$envStatus}
            GROUP by
                a.channel_master_id";
        $totalArchiveVideoActivityResult = $this->db->query($archiveVideoSql)->row_array()['total_activity'];
        $totalArchiveVideoActivity = (int)($totalArchiveVideoActivityResult != '') ? $totalArchiveVideoActivityResult : 0;
        #epub
        #post
        $postSql = "SELECT
                    COUNT(*) as total_activity
                FROM
                    channel_to_post as a
                WHERE
                    a.channel_master_id = {$channelId}
                    AND a.status = 3
                    AND a.post_date <= CURRENT_DATE()
                GROUP by
                    a.channel_master_id";
        //$totalPostActivityResult = $this->db->query($postSql)->row_array()['total_activity'];
        $totalPostActivity = (int)($this->db->query($postSql)->row_array()['total_activity']) ? $this->db->query($postSql)->row_array()['total_activity'] : 0;
        #sdocuemnt
        $documentSql = "SELECT
                            COUNT(*) as total_activity FROM  channel_to_document as a
                        WHERE
                            a.channel_master_id = {$channelId}
                            AND a.status = 3
                        GROUP by
                            a.channel_master_id";
        $totalDocumentActivityResult = $this->db->query($documentSql)->row_array()['total_activity'];
        $totalDocumentActivity = (int)($totalDocumentActivityResult != '') ? $totalDocumentActivityResult : 0;
        $totalActivity = 0;
        $totalActivity += (int)($totalCompendiumActivity + $totalSurveyActivity + $totalCourseActivity + $totalSessionActivity + $totalArchiveVideoActivity + $totalPostActivity + $totalDocumentActivity);
        return $totalActivity;
    }

    /**
     * Get related surveys for a compendium
     *
     * @param int $type_id The compendium ID
     * @param int $user_master_id The user ID
     * @param string $env User environment
     * @return array Array of related surveys
     */
    private function getRelatedSurveys(
        $type_id,
        $user_master_id,
        $env
    ) {
        // Use CTEs for better performance
        $sql = "WITH CompletedSurveys AS (
                SELECT 
                    survey_id
                FROM 
                    survey_user_answer
                WHERE 
                    user_master_id = '{$user_master_id}'
                UNION
                SELECT 
                    survey_id
                FROM 
                    survey_user_incomplete_answer
                WHERE 
                    status = 3
                    AND user_master_id = '{$user_master_id}'
            ),
            FilteredSurveys AS (
                SELECT
                    sv.survey_id
                FROM 
                    survey sv
                JOIN 
                    survey_to_medwiki as stm ON stm.survey_id = sv.survey_id
                WHERE 
                    sv.status = 3
                    AND DATE(sv.publishing_date) <= CURDATE()
                    AND stm.medwiki_id = {$type_id}
                    AND sv.survey_id NOT IN (SELECT survey_id FROM CompletedSurveys)
            ),
            SpecialitiesAgg AS (
                SELECT 
                    svts.survey_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name
                FROM 
                    survey_to_speciality as svts
                JOIN 
                    master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                WHERE 
                    svts.survey_id IN (SELECT survey_id FROM FilteredSurveys)
                GROUP BY 
                    svts.survey_id
            ),
            SponsorsAgg AS (
                SELECT 
                    suvTspon.survey_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM 
                    survey_to_sponsor as suvTspon
                JOIN 
                    client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                WHERE 
                    suvTspon.survey_id IN (SELECT survey_id FROM FilteredSurveys)
                GROUP BY 
                    suvTspon.survey_id
            )
            SELECT
                sv.survey_id,
                sv.category,
                sv.survey_points,
                sv.survey_title,
                sv.deeplink,
                sv.gl_deeplink,
                sv.survey_description,
                sv.image,
                sv.publishing_date,
                svd.data,
                cln.client_name,
                cln.client_logo,
                spec.specialities_name,
                spon.sponsor,
                spon.sponsor_logo
            FROM 
                FilteredSurveys fs
            JOIN 
                survey sv ON sv.survey_id = fs.survey_id
            JOIN 
                survey_detail as svd ON svd.survey_id = sv.survey_id
            JOIN 
                client_master as cln ON cln.client_master_id = sv.client_id
            LEFT JOIN 
                SpecialitiesAgg spec ON spec.survey_id = sv.survey_id
            LEFT JOIN 
                SponsorsAgg spon ON spon.survey_id = sv.survey_id";

        $query = $this->db->query($sql);
        $resultPoll = $query->result();
        $vxPoll = array();

        foreach ($resultPoll as $valSurvey) {
            $dataArry = unserialize($valSurvey->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);

            // Process sponsor data
            $allsponsor = array();
            $sponsorname = explode(",", $valSurvey->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);
            $sponsorLogomix = [];

            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => change_img_src($valueSponor));
                        $sp++;
                    }
                }
            } elseif ($valSurvey->sponsor_logo != '') {
                $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                $allsponsor[] = array('name' => $valSurvey->sponsor, "logo" => change_img_src($valSurvey->sponsor_logo));
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            $vxPoll[] = array(
                "survey_id" => $valSurvey->survey_id,
                "category" => $valSurvey->category,
                "point" => $valSurvey->survey_points,
                "json_data" => $str,
                "survey_title" => $valSurvey->survey_title,
                "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0),
                "survey_description" => substr($valSurvey->survey_description, 0, 150),
                "image" => change_img_src($valSurvey->image),
                "specialities_name" => $valSurvey->specialities_name,
                "sponsor_name" => $valSurvey->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "publishing_date" => $valSurvey->publishing_date,
            );

        }

        return $vxPoll;
    }

    /**
    * Get detailed information for a compendium
    *
    * @param string $type_id Compendium ID
    * @param string $type Content type
    * @param string $user_master_id User ID
    * @param string $from_type Source type
    * @param string $client_ids Client IDs
    * @return array|null Detailed compendium data
    */
    public function detail_feed(
        $type_id = '',
        $type = '',
        $user_master_id = '',
        $from_type = '',
        $client_ids = ''
    ) {

        if (empty($type_id)) {
            return null;
        }

        // Get cache key and user environment
        $cachename = "comp_detail_" . $type_id;
        $user_type = get_master_user_type_id($user_master_id);
        $env = get_user_env_id($user_master_id);

        // echo 'cached user type ----- '.$user_type;
        // exit;

        // Set environment status for query
        if ($env) {
            $envStatus = ($env != 2)
                ? " (cTenv.env = 2 OR cTenv.env = " . $env . ")"
                : " cTenv.env = " . $env;
        } else {
            $envStatus = "";
        }

        // Check user package
        $key_locked = get_user_package($user_master_id, 'comp');

        if (!$user_type) {
            return null;
        }

        $result = null;

        // Check if we can use cache (only for non-internal users)
        $useCache = ($user_type != 5 && $this->myredis->exists($cachename));

        if ($useCache) {
            // Get from cache if available for non-internal users
            $result =  $this->myredis->get($cachename);
            $dataOriginType = 'cached';
            // print_r($result);
            // exit;

        } else {

            $dataOriginType = 'Database';
            // Set status condition based on user type
            $statusCondition = ($user_type == 5)
                ? "cm.status IN (3, 5)"
                : "cm.status = 3";

            // Set publication date condition based on user type
            // $pubDateCondition = ($user_type == 5)
            //     ? ""
            //     : "AND cm.publication_date <= CURDATE()";
            //{$pubDateCondition}

            // SQL with CTEs for better performance - include all needed data in one query
            $sql = "WITH CompData AS (
                SELECT
                    cm.comp_qa_id,
                    cm.comp_qa_question,
                    cm.comp_qa_answer,
                    cm.comp_qa_citation,
                    cm.comp_qa_answer_raw,
                    cm.comp_qa_question_raw,
                    cm.comp_qa_file_img,
                    cm.added_on,
                    cm.env,
                    cm.deeplink,
                    cm.gl_deeplink,
                    cm.comp_qa_tags,
                    cm.privacy_status,
                    cm.publication_date,
                    cm.is_like,
                    cm.is_comment,
                    cm.is_share,
                    cm.status,
                    cm.type,
                    cm.vendor,
                    cm.comment_status,
                    cm.src,
                    cm.start_like,
                    cm.comp_qa_speciality_id,
                    cm.client_id
                FROM knwlg_compendium_V1 as cm
                WHERE {$statusCondition}
                AND cm.comp_qa_id = {$type_id}
                AND cm.privacy_status IN (0, 1, 2)
                
            ),
            SpecialitiesData AS (
                SELECT 
                    cmTs.comp_qa_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                FROM compendium_to_specialities as cmTs
                JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                WHERE cmTs.comp_qa_id = {$type_id}
                GROUP BY cmTs.comp_qa_id
            ),
            SponsorData AS (
                SELECT 
                    cmTspon.comp_qa_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM compendium_to_sponsor as cmTspon
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                WHERE cmTspon.comp_qa_id = {$type_id}
                GROUP BY cmTspon.comp_qa_id
            ),
            -- RatingData AS (
            --     SELECT 
            --         {$type_id} as comp_qa_id,
            --         COUNT(rt.rating) as averageRating
            --     FROM knwlg_rating rt 
            --     WHERE rt.post_id = {$type_id} 
            --     AND rt.post_type = 'comp' 
            --     AND rt.rating != 0
            -- ),
            CommentData AS (
                SELECT 
                    {$type_id} as comp_qa_id,
                    COUNT(kcm.knwlg_comment_id) as count_comment
                FROM knwlg_comment kcm 
                WHERE kcm.type_id = {$type_id} 
                AND kcm.type = 'comp' 
                AND kcm.comment_approve_status = '1'
            )
            SELECT 
                cd.comp_qa_id as type_id,
                cd.comp_qa_question,
                cd.comp_qa_answer,
                cd.comp_qa_citation,
                cd.comp_qa_answer_raw,
                cd.comp_qa_question_raw,
                cd.comp_qa_file_img,
                cd.added_on,
                cd.env,
                cd.deeplink,
                cd.gl_deeplink,
                cd.comp_qa_tags,
                cd.privacy_status,
                cd.publication_date,
                cd.is_like,
                cd.is_comment,
                cd.is_share,
                cd.status,
                cd.type,
                cd.vendor,
                cd.comment_status,
                cd.src,
                cd.start_like,
                cd.comp_qa_speciality_id,
                cln.client_name,
                cln.client_logo,
                sd.specialities_name,
                sd.specialities_ids_and_names,
                sp.sponsor,
                sp.sponsor_logo,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                cmd.count_comment

            FROM CompData cd
            JOIN client_master as cln ON cln.client_master_id = cd.client_id
            LEFT JOIN SpecialitiesData sd ON cd.comp_qa_id = sd.comp_qa_id
            LEFT JOIN SponsorData sp ON cd.comp_qa_id = sp.comp_qa_id
            LEFT JOIN CommentData cmd ON cd.comp_qa_id = cmd.comp_qa_id
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cd.comp_qa_id AND cTenv.type = 1
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cd.comp_qa_id AND uTpyCont.type = 1 AND uTpyCont.user_master_id = {$user_master_id}
            WHERE {$envStatus}";

            // echo $sql;
            // exit;

            $query = $this->db->query($sql);
            $result = $query->row();

            if (empty($result->type_id)) {
                //return null;

                $vx = array(
                    "data_origin" => $dataOriginType,
                    "error" => "Content not found"
                );
                return $vx;

            } elseif ($user_type != 5) {
                // Cache the result for non-internal users
                $this->myredis->set($cachename, $result);
            }
        }
        if (!empty($result->type_id)) {

            // $keyUpdateCompView = "comp_view_" . $type_id . "_" . $user_master_id;
            // $this->myredis->set($keyUpdateCompView, $result);


            $rating_data = get_content_rating_data('comp', $type_id, $user_master_id);
            // Now you can access the data
            $averageRating = $rating_data['averageRating'];
            $hasUserRated = $rating_data['myrating']; // boolean
            $vaultStatus = $rating_data['vault']; // 0 or 1
            $startLike = $rating_data['start_like'];
            // Get related surveys using the optimized function
            $vxPoll = $this->getRelatedSurveys($type_id, $user_master_id, $env);

            // Process image
            $img = $result->comp_qa_file_img ? $result->comp_qa_file_img : '';

            // Process sponsor data
            $allsponsor = array();
            $sponsorname = explode(",", $result->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $result->sponsor_logo);
            $sponsorLogomix = [];

            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        $sp++;
                    }
                }
            } elseif ($result->sponsor_logo) {
                $sponsorLogomix[] = '' . $result->sponsor_logo;
                $allsponsor[] = array('name' => $result->sponsor, "logo" => $result->sponsor_logo);
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);

            // Process question
            $string = $result->comp_qa_question_raw;
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");

            // Process video source
            $vid_src = "";
            $vid_code = null;
            if ($result->vendor == "youtube") {
                $vid_src = "https://www.youtube.com/watch?v=" . $result->src;
                $vid_code = $result->src;
            } elseif ($result->src) {
                $vid_src = "" . $result->src;
            }

            // Determine answer content based on permissions
            if ($key_locked === 1) {
                $anws = '';
                //$anwsHtm = '';
            } else {
                if ($result->price > 0) {
                    if ($result->user_contnet_payment_status == 3) {
                        $anws = html_entity_decode($result->comp_qa_answer_raw);
                        //$anwsHtm = $result->comp_qa_answer;
                    } else {
                        $anws = '';
                        // $anwsHtm = '';
                    }
                } else {
                    $anws = html_entity_decode($result->comp_qa_answer_raw);
                    // $anwsHtm = $result->comp_qa_answer;
                }
            }

            // Check if citation exists
            $citationFlag = (strpos($result->comp_qa_citation, 'id="references"') !== false);

            $vx = array(
                    "data_origin" => $dataOriginType,
                    "type_id" => (string) $result->type_id,
                    "con_type" => $result->type,
                    "is_locked" => $key_locked,
                    "env" => $env,
                    "price" => $result->price,
                    "user_content_payment" => $result->user_contnet_payment_status,
                    "vendor" => $result->vendor,
                    "src" => $vid_src,
                    "src_code" => $vid_code,
                    "type" => 'comp',
                    "date" => date(' jS F y', strtotime($result->publication_date)),
                    "question" => html_entity_decode($string),
                    "answer" => $anws,
                    "question_htm" => $result->comp_qa_question,
                    "answer_htm" => change_img_src($result->comp_qa_answer.$result->comp_qa_citation),
                    "comp_qa_citation" => $result->comp_qa_citation,
                    "isCitationExists" => $citationFlag,
                    "comp_qa_transcript" => $result->comp_qa_transcript,
                    "image" => change_img_src($img),
                    "specialities" => $result->specialities_name,
                    "specialities_id" => $result->comp_qa_speciality_id,
                    "specialities_ids_and_names" => $this->explode_speciality_string($result->specialities_ids_and_names),
                    "privacy_status" => $result->privacy_status,
                    "sponsor_name" => $result->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),
                    "all_sponsor" => $allsponsor,
                    "comment_count" => $result->count_comment ?? 0,
                    // "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
                    "rating" => ($averageRating != '') ? ($averageRating + $startLike) : $startLike,
                    // "myrating" => ($avgrating['myrating'] != '') ? true : false,
                    "myrating" => $hasUserRated,
                    // "vault" => ($result->vault != '') ? $result->vault : 0, //$vault['vault'],//
                    "vault" => $vaultStatus,
                    "deeplink" => ($env == 'GL') ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
                    "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,
                    "disclaimer" => disclaimer('knowledge'),
                    "survey" => $vxPoll,
                    "channel" => $this->getchannel($type_id, $user_master_id, 'comp'),
                    "content_rate" => $this->rate($type_id, $user_master_id, 'comp'),
                    "is_share" => get_a_content_is_share_status($result->type_id, '1'),
                    "is_like" => filter_var($result->is_like, FILTER_VALIDATE_BOOLEAN),
                    "is_comment" => filter_var($result->comment_status, FILTER_VALIDATE_BOOLEAN),
                    "is_commentable" => $result->comment_status
                        );
            $campaign  = getContentCampiagn($user_master_id, $client_ids, $type_id, 'comp');
            $vx['display_banner'] = $campaign['banner_dispaly'];
            $vx['campaign_data'] = $campaign['creative_data'];
            return $vx;
        } else {
            $vx = array(
                "data_origin" => "none",
                "error" => "Content not found"
            );
            return $vx;

        }
    }

    // Add this method to increment the compendium view counter
    // public function increment_user_compendium_view($user_master_id, $type)
    // {
    //     if (!empty($user_master_id)) {

    //         if ($type === 'comp') {
    //             $redis_key = "user_compendium_views_" . $user_master_id;
    //             $this->load->library('Myredis');
    //             $this->myredis->incr($redis_key);
    //             try {
    //                 // Increment the counter
    //                 $count = $this->myredis->incr($redis_key);
    //                 // echo 'count---'.$count;
    //                 // exit;

    //                 // If incr failed, try to get the current value and increment manually
    //                 if ($count === false || $count === null) {
    //                     $current = $this->myredis->get($redis_key);
    //                     $current = is_numeric($current) ? (int)$current : 0;
    //                     $count = $current + 1;
    //                     $this->myredis->set($redis_key, $count);
    //                 }

    //                 // Set expiration if it's a new key
    //                 if ($count == 1) {
    //                     $this->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
    //                 }

    //                 return $count;
    //             } catch (Exception $e) {
    //                 log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
    //                 return 0;
    //             }
    //         }
    //         if ($type === 'archivevideo') {
    //             $redis_key = "user_archivevideo_views_" . $user_master_id;
    //             $this->load->library('Myredis');
    //             $this->myredis->incr($redis_key);
    //             try {
    //                 // Increment the counter
    //                 $count = $this->myredis->incr($redis_key);
    //                 // echo 'count---'.$count;
    //                 // exit;

    //                 // If incr failed, try to get the current value and increment manually
    //                 if ($count === false || $count === null) {
    //                     $current = $this->myredis->get($redis_key);
    //                     $current = is_numeric($current) ? (int)$current : 0;
    //                     $count = $current + 1;
    //                     $this->myredis->set($redis_key, $count);
    //                 }

    //                 // Set expiration if it's a new key
    //                 if ($count == 1) {
    //                     $this->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
    //                 }

    //                 return $count;
    //             } catch (Exception $e) {
    //                 log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
    //                 return 0;
    //             }
    //         }
    //         if ($type === 'session') {
    //             $redis_key = "user_session_views_" . $user_master_id;
    //             $this->load->library('Myredis');
    //             $this->myredis->incr($redis_key);
    //             try {
    //                 // Increment the counter
    //                 $count = $this->myredis->incr($redis_key);
    //                 // echo 'count---'.$count;
    //                 // exit;

    //                 // If incr failed, try to get the current value and increment manually
    //                 if ($count === false || $count === null) {
    //                     $current = $this->myredis->get($redis_key);
    //                     $current = is_numeric($current) ? (int)$current : 0;
    //                     $count = $current + 1;
    //                     $this->myredis->set($redis_key, $count);
    //                 }

    //                 // Set expiration if it's a new key
    //                 if ($count == 1) {
    //                     $this->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
    //                 }

    //                 return $count;
    //             } catch (Exception $e) {
    //                 log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
    //                 return 0;
    //             }
    //         }
    //         if ($type === 'survey') {
    //             $redis_key = "user_survey_views_" . $user_master_id;
    //             $this->load->library('Myredis');
    //             $this->myredis->incr($redis_key);
    //             try {
    //                 // Increment the counter
    //                 $count = $this->myredis->incr($redis_key);
    //                 // echo 'count---'.$count;
    //                 // exit;

    //                 // If incr failed, try to get the current value and increment manually
    //                 if ($count === false || $count === null) {
    //                     $current = $this->myredis->get($redis_key);
    //                     $current = is_numeric($current) ? (int)$current : 0;
    //                     $count = $current + 1;
    //                     $this->myredis->set($redis_key, $count);
    //                 }

    //                 // Set expiration if it's a new key
    //                 if ($count == 1) {
    //                     $this->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
    //                 }

    //                 return $count;
    //             } catch (Exception $e) {
    //                 log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
    //                 return 0;
    //             }
    //         }
    //         if ($type === 'epub') {
    //             $redis_key = "user_epub_views_" . $user_master_id;
    //             $this->load->library('Myredis');
    //             $this->myredis->incr($redis_key);
    //             try {
    //                 // Increment the counter
    //                 $count = $this->myredis->incr($redis_key);
    //                 // echo 'count---'.$count;
    //                 // exit;

    //                 // If incr failed, try to get the current value and increment manually
    //                 if ($count === false || $count === null) {
    //                     $current = $this->myredis->get($redis_key);
    //                     $current = is_numeric($current) ? (int)$current : 0;
    //                     $count = $current + 1;
    //                     $this->myredis->set($redis_key, $count);
    //                 }

    //                 // Set expiration if it's a new key
    //                 if ($count == 1) {
    //                     $this->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
    //                 }

    //                 return $count;
    //             } catch (Exception $e) {
    //                 log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
    //                 return 0;
    //             }
    //         }
    //         if ($type === 'training') {
    //             $redis_key = "user_training_views_" . $user_master_id;
    //             $this->load->library('Myredis');
    //             $this->myredis->incr($redis_key);
    //             try {
    //                 // Increment the counter
    //                 $count = $this->myredis->incr($redis_key);
    //                 // echo 'count---'.$count;
    //                 // exit;

    //                 // If incr failed, try to get the current value and increment manually
    //                 if ($count === false || $count === null) {
    //                     $current = $this->myredis->get($redis_key);
    //                     $current = is_numeric($current) ? (int)$current : 0;
    //                     $count = $current + 1;
    //                     $this->myredis->set($redis_key, $count);
    //                 }

    //                 // Set expiration if it's a new key
    //                 if ($count == 1) {
    //                     $this->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
    //                 }

    //                 return $count;
    //             } catch (Exception $e) {
    //                 log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
    //                 return 0;
    //             }
    //         }

    //         // $redis_key = "user_compendium_views_" . $user_master_id;
    //         // $this->load->library('Myredis');


    //     }
    //     return 0;
    // }
    public function detail_for_related(
        $type_id,
        $type,
        $user_master_id
    ) {


        // echo 'type_id---'.$type_id.'--type---'.$type.'-user_master_id--'.$user_master_id.'-from_type--'.$from_type.'-client_ids--'.$client_ids;
        // exit;

        if (!empty($type_id)) {
            $env = get_user_env($user_master_id);

            $cachename = "comp_detail_" . $type_id . $env . $type;
            if ($this->myredis->exists($cachename)) {
                $result = $this->myredis->get($cachename);
            } else {
                $env = get_user_env_id($user_master_id);
                if ($env) {
                    if ($env != 2) {
                        $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                    } else {
                        $envStatus = "AND cTenv.env =" . $env . "";
                    }
                } else {
                    $envStatus = "";
                }
                $key_locked = get_user_package($user_master_id, 'comp');
                // Refactored SQL using CTE for better performance
                $sql = "WITH CompendiumData AS (
                            SELECT
                                cm.comp_qa_id as type_id,
                                cm.comp_qa_question,
                                cm.comp_qa_answer,
                                cm.comp_qa_answer_raw,
                                cm.comp_qa_question_raw,
                                cm.comp_qa_file_img,
                                cm.added_on,
                                cm.deeplink,
                                cm.gl_deeplink,
                                cm.comp_qa_tags,
                                cm.publication_date,
                                cm.type,
                                cm.vendor,
                                cm.src,
                                cm.start_like,
                                cm.is_share,
                                cm.color,
                                cln.client_name,
                                cln.client_logo,
                                cm.comp_qa_speciality_id,
                                cTenv.price,
                                uTpyCont.status as user_contnet_payment_status,
                                rtmy.rating as myrating,
                                kv.status as vault,
                                kv.timestamp
                            FROM knwlg_compendium_V1 as cm
                            JOIN client_master as cln ON cln.client_master_id = cm.client_id
                            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.comp_qa_id and rtmy.post_type='comp' and rtmy.rating!=0 and rtmy.user_master_id = {$user_master_id}
                            LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id and kv.type_text='comp' and kv.user_id = {$user_master_id}
                            LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id and cTenv.type = 1
                            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.comp_qa_id and uTpyCont.type = 1 and uTpyCont.user_master_id = {$user_master_id}
                            WHERE cm.status = 3
                            {$envStatus}
                            AND cm.comp_qa_id = {$type_id}
                        ),
                        SpecialitiesData AS (
                            SELECT 
                                cmTs.comp_qa_id,
                                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                                GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                            FROM compendium_to_specialities as cmTs
                            JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                            WHERE cmTs.comp_qa_id = {$type_id}
                            GROUP BY cmTs.comp_qa_id
                        ),
                        SponsorData AS (
                            SELECT 
                                cmTspon.comp_qa_id,
                                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                            FROM compendium_to_sponsor as cmTspon
                            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                            WHERE cmTspon.comp_qa_id = {$type_id}
                            GROUP BY cmTspon.comp_qa_id
                        ),
                        MetricsData AS (
                            SELECT 
                                {$type_id} as comp_qa_id,
                                (SELECT COUNT(rt.rating) FROM knwlg_rating rt WHERE rt.post_id = {$type_id} AND rt.post_type='comp' AND rt.rating!=0) as averageRating,
                                (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm WHERE kcm.type_id = {$type_id} AND kcm.type = 'comp' AND kcm.comment_approve_status = '1') as count_comment
                        )
                        SELECT 
                            cd.*,
                            sd.specialities_name,
                            sd.specialities_ids_and_names,
                            sp.sponsor,
                            sp.sponsor_logo,
                            md.averageRating,
                            md.count_comment
                        FROM CompendiumData cd
                        LEFT JOIN SpecialitiesData sd ON cd.type_id = sd.comp_qa_id
                        LEFT JOIN SponsorData sp ON cd.type_id = sp.comp_qa_id
                        LEFT JOIN MetricsData md ON cd.type_id = md.comp_qa_id";
                $query = $this->db->query($sql);
                $result = $query->row();
                $this->myredis->set($cachename, $result);
            }
            // Get rating data
            $avgrating = array('averageRating' => '', 'myrating' => '');
            // Get user rating if not already in result
            if (!isset($result->myrating) || $result->myrating === null) {
                $myratingsql = "SELECT rating as myrating 
                FROM knwlg_rating 
                WHERE post_id = " . $type_id . " 
                AND post_type='comp' 
                AND rating!=0 
                AND user_master_id = " . $user_master_id;
                $querymyratingsql = $this->db->query($myratingsql);
                if (($querymyratingsql) && ($querymyratingsql->num_rows() > 0)) {
                    $myrate = $querymyratingsql->result();
                    $avgrating['myrating'] = $myrate[0]->myrating;
                }
            } else {
                $avgrating['myrating'] = $result->myrating;
            }
            // Process image
            $img = $result->comp_qa_file_img ? $result->comp_qa_file_img : '';
            // Process sponsor data
            $allsponsor = array();
            $sponsorLogomix = array();
            if (!empty($result->sponsor) && !empty($result->sponsor_logo)) {
                $sponsorname = explode(",", $result->sponsor);
                $sponsorLogoArry = explode(",", $result->sponsor_logo);
                for ($sp = 0; $sp < count($sponsorLogoArry); $sp++) {
                    if (!empty($sponsorLogoArry[$sp])) {
                        $sponsorLogomix[] = $sponsorLogoArry[$sp];
                        $allsponsor[] = array(
                            'name' => isset($sponsorname[$sp]) ? $sponsorname[$sp] : '',
                            "logo" => $sponsorLogoArry[$sp]
                        );
                    }
                }
            }
            $sponsorLogo = !empty($sponsorLogomix) ? implode(",", $sponsorLogomix) : '';
            // Process content
            $string = trim(html_entity_decode($result->comp_qa_question_raw), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = str_replace("\n\t", "\n", $result->comp_qa_answer_raw);
            // Process video source
            $vid_src = "";
            $vid_code = "";
            if ($result->vendor == "youtube") {
                $vid_src = "https://www.youtube.com/watch?v=" . $result->src;
                $vid_code = $result->src;
            } elseif (!empty($result->src)) {
                $vid_src = $result->src;
            }
            // Build response array
            $vx = array(
                "type_id" => $result->type_id,
                "trending_type" => 'comp',
                "type" => 'comp',
                "is_locked" => $key_locked,
                "price" => $result->price,
                "user_content_payment" => $result->user_contnet_payment_status,
                "con_type" => $result->type,
                "vendor" => $result->vendor,
                "src" => $vid_src,
                "src_code" => $vid_code,
                "date" => date(' jS F y', strtotime($result->publication_date)),
                "question" => html_entity_decode($string),
                "answer" => html_entity_decode($main_description),
                "question_htm" => $result->comp_qa_question,
                "answer_htm" => $result->comp_qa_answer,
                "image" => change_img_src($img),
                "specialities" => $result->specialities_name,
                "specialities_id" => $result->comp_qa_speciality_id,
                "specialities_ids_and_names" => $this->explode_speciality_string($result->specialities_ids_and_names),
                "sponsor_name" => $result->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "comment_count" => $result->count_comment ?? 0,
                "rating" => (!empty($result->averageRating)) ? ($result->averageRating + $result->start_like) : $result->start_like,
                "myrating" => !empty($avgrating['myrating']),
                "vault" => $result->vault ?? 0,
                "deeplink" => ($result->deeplink != '') ? $result->deeplink : 0,
                "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,
                "disclaimer" => disclaimer('knowledge'),
                "channel" => $this->getchannel($type_id, $user_master_id, 'comp'),
                "content_rate" => $this->rate($type_id, $user_master_id, 'comp'),
            );

            return $vx;
        }
    }
    public function all_vault_feed(
        $user_master_id = '',
        $client_ids = '',
        $group_ids = '',
        $limitFrom = '',
        $limitTo = '',
        $val = '',
        $type = ''
    ) {
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
            } else {
                $envStatus = "AND cTenv.env =" . $env . "";
            }
        } else {
            $envStatus = "";
        }
        if ($limitFrom != '' && $limitTo != '') {
            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {
            $limit = "limit 0,2";
        }
        if ($val != '') {
            $searchQuery = "Where (fd.title like '%" . $val . "%' or fd.description like '%" . $val . "%')";
        } else {
            $searchQuery = "";
        }
        if (!empty($user_master_id)) {
            switch ($type) {
                case "comp":
                    return $this->compdata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    break;
                case "gr":
                    return $this->grdata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    break;
                case "video_archive":
                    return $this->vidarchidata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    break;
                case "survey":
                    return $this->surveydata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    break;
                case "epub":
                    return $this->epubdata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    break;
                case "training":
                    return $this->coursedata($envStatus, $client_list_kcap, $limitTo, $limitFrom, $searchQuery, $user_master_id);
                    //return $finalvary;
                    break;
                case "session":
                    return $this->sessiondata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    //return $finalvary;
                    break;
                default:
                    $finalvary = array();
                    $compdata = $this->compdata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    if (count($compdata) > 0) {
                        $finalvary = array_merge_recursive($finalvary, $compdata);
                    }
                    $grdata = $this->grdata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    if (count($grdata) > 0) {
                        $finalvary = array_merge_recursive($finalvary, $grdata);
                    }
                    $Vidarchdata = $this->vidarchidata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    if (count($Vidarchdata) > 0) {
                        $finalvary = array_merge_recursive($finalvary, $Vidarchdata);
                    }
                    $epubdata = $this->epubdata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    if (count($epubdata) > 0) {
                        $finalvary = array_merge_recursive($finalvary, $epubdata);
                    }
                    $sessiondata = $this->sessiondata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    if (count($sessiondata) > 0) {
                        $finalvary = array_merge_recursive($finalvary, $sessiondata);
                    }
                    $coursedata = $this->coursedata($envStatus, $client_list_kcap, $limitTo, $limitFrom, $searchQuery, $user_master_id);
                    if (count($coursedata) > 0) {
                        $finalvary = array_merge_recursive($finalvary, $coursedata);
                    }
                    $surveydata = $this->surveydata($envStatus, $client_list_kcap, $limit, $searchQuery, $user_master_id);
                    if (count($surveydata) > 0) {
                        $finalvary = array_merge_recursive($finalvary, $surveydata);
                    }
            }
            usort($finalvary, function ($a, $b) {
                return strtotime($b['saved_date']) - strtotime($a['saved_date']);
            });
            return $finalvary;
        }
    }



    /**
     * Fetches saved compendium data from database
     *
     * @param string $envStatus Environment status
     * @param string $client_list_kcap Client list
     * @param string $limit Limit query
     * @param string $searchQuery Search query
     * @param int $user_master_id User master ID
     *
     * @return array
     */
    public function compdata(
        $envStatus,
        $client_list_kcap,
        $limit,
        $searchQuery,
        $user_master_id
    ) {
        // Cache key for this query
        $cacheKey = "vault_compdata_" . md5($envStatus . $limit . $user_master_id);

        if ($this->myredis->exists($cacheKey)) {
            return $this->myredis->get($cacheKey);
        }

        // Get total count using optimized query
        $sqlCount = "SELECT COUNT(DISTINCT cm.comp_qa_id) as total
                    FROM knwlg_compendium_V1 as cm
                    JOIN knwlg_vault as kv ON kv.post_id = cm.comp_qa_id AND kv.type_text='comp' AND kv.user_id = ? AND kv.status = 1
                    LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id AND cTenv.type = 1
                    WHERE cm.status = 3
                    AND cm.privacy_status = 0
                    {$envStatus}";

        // echo $sqlCount;
        // exit;

        $queryCount = $this->db->query($sqlCount, array($user_master_id));
        $total = ($queryCount && $queryCount->num_rows() > 0) ? $queryCount->row()->total : 0;

        // Use CTEs for better performance
        $sql = "WITH SavedCompendiums AS (
                SELECT 
                    cm.comp_qa_id,
                    kv.timestamp as saved_date
                FROM 
                    knwlg_compendium_V1 as cm
                JOIN 
                    knwlg_vault as kv ON kv.post_id = cm.comp_qa_id AND kv.type_text='comp' AND kv.user_id = ? AND kv.status = 1
                LEFT JOIN 
                    content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id AND cTenv.type = 1
                WHERE 
                    cm.status = 3
                    AND cm.privacy_status = 0
                    {$envStatus}
                ORDER BY 
                    kv.timestamp DESC
                {$limit}
            ),
            SpecialitiesData AS (
                SELECT 
                    cmTs.comp_qa_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                FROM 
                    SavedCompendiums sc
                JOIN 
                    compendium_to_specialities as cmTs ON cmTs.comp_qa_id = sc.comp_qa_id
                JOIN 
                    master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                GROUP BY 
                    cmTs.comp_qa_id
            ),
            SponsorsData AS (
                SELECT 
                    cmTspon.comp_qa_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM 
                    SavedCompendiums sc
                LEFT JOIN 
                    compendium_to_sponsor as cmTspon ON cmTspon.comp_qa_id = sc.comp_qa_id
                LEFT JOIN 
                    client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                GROUP BY 
                    cmTspon.comp_qa_id
            ),
            MetricsData AS (
                SELECT 
                    sc.comp_qa_id,
                    (SELECT COUNT(rt.rating) FROM knwlg_rating rt WHERE rt.post_id = sc.comp_qa_id AND rt.post_type='comp' AND rt.rating!=0) as averageRating,
                    (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm WHERE kcm.type_id = sc.comp_qa_id AND kcm.type = 'comp' AND kcm.comment_approve_status = '1') as count_comment
                FROM 
                    SavedCompendiums sc
            )
            SELECT 
                cm.comp_qa_id as type_id,
                cm.comp_qa_question,
                cm.comp_qa_answer,
                cm.is_share,
                cm.comp_qa_answer_raw as description,
                cm.comp_qa_question_raw as title,
                cm.comp_qa_file_img,
                cm.added_on,
                cm.deeplink,
                cm.gl_deeplink,
                cm.publication_date as publish_date,
                cln.client_name,
                cln.client_logo,
                cm.comp_qa_speciality_id,
                cm.type as con_type,
                cm.vendor,
                cm.src,
                cm.color,
                cm.start_like,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                rtmy.rating as myrating,
                kv.status as vault,
                kv.timestamp as saved_date,
                sd.specialities_name,
                sd.specialities_ids_and_names,
                sp.sponsor,
                sp.sponsor_logo,
                md.averageRating,
                md.count_comment
            FROM 
                SavedCompendiums sc
            JOIN 
                knwlg_compendium_V1 as cm ON cm.comp_qa_id = sc.comp_qa_id
            JOIN 
                client_master as cln ON cln.client_master_id = cm.client_id
            LEFT JOIN 
                content_to_env as cTenv ON cTenv.type_id = cm.comp_qa_id AND cTenv.type = 1
            LEFT JOIN 
                payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.comp_qa_id AND uTpyCont.type = 1 AND uTpyCont.user_master_id = ?
            LEFT JOIN 
                knwlg_rating as rtmy ON rtmy.post_id = cm.comp_qa_id AND rtmy.post_type='comp' AND rtmy.rating!=0 AND rtmy.user_master_id = ?
            LEFT JOIN 
                knwlg_vault as kv ON kv.post_id = cm.comp_qa_id AND kv.type_text='comp' AND kv.user_id = ?
            LEFT JOIN 
                SpecialitiesData sd ON sd.comp_qa_id = cm.comp_qa_id
            LEFT JOIN 
                SponsorsData sp ON sp.comp_qa_id = cm.comp_qa_id
            LEFT JOIN 
                MetricsData md ON md.comp_qa_id = cm.comp_qa_id
            ORDER BY 
                sc.saved_date DESC";

        // echo $sql;
        // exit;
        $query = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id, $user_master_id));
        $vx = array();

        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            $key_locked = get_user_package($user_master_id, 'comp');
            $i = 1;

            // print_r($result);
            // exit;

            foreach ($result as $valCm) {
                // Process image
                $img = $valCm->comp_qa_file_img ?: '';

                // Process sponsor logos
                $sponsorLogoArry = explode(",", $valCm->sponsor_logo);
                $sponsorLogomix = [];

                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = $valueSponor;
                        }
                    }
                } elseif ($valCm->sponsor_logo) {
                    $sponsorLogomix[] = $valCm->sponsor_logo;
                }

                $sponsorLogo = implode(",", (array)$sponsorLogomix);

                // Get environment for deeplink selection
                $env = get_user_env($user_master_id);

                // Build result array
                $vx[] = array(
                    "slno" => $i,
                    "type_id" => $valCm->type_id,
                    "is_share" => $valCm->is_share,
                    "is_locked" => $key_locked,
                    "price" => $valCm->price,
                    "user_content_payment" => $valCm->user_contnet_payment_status,
                    //get_user_content_status($valCm->type_id, 1, $user_master_id),
                    "con_type" => $valCm->con_type,
                    "vendor" => $valCm->vendor,
                    "src" => $valCm->src,
                    "deeplink" => ($env == 'GL') ?
                        (($valCm->gl_deeplink != '') ? $valCm->gl_deeplink : 0) :
                        (($valCm->deeplink != '') ? $valCm->deeplink : 0),
                    "image" => change_img_src($img),
                    "type" => 'comp',
                    "date" => date(' jS F y', strtotime($valCm->publish_date)),
                    "question" => str_replace("&nbsp;", " ", strip_tags($valCm->title)),
                    "answer" => str_replace("&nbsp;", " ", strip_tags(substr($valCm->description, 0, 300))),
                    "specialities" => $valCm->specialities_name,
                    "specialities_ids_and_names" => $this->explode_speciality_string($valCm->specialities_ids_and_names),
                    "client_name" => $valCm->client_name,
                    "client_logo" => change_img_src($valCm->client_logo),
                    "color" => ($valCm->color != '') ? $valCm->color : '#eb34e5',
                    "sponsor_name" => $valCm->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),
                    "comment_count" => $valCm->count_comment,
                    "rating" => ($valCm->averageRating != '') ? ($valCm->averageRating + $valCm->start_like) : $valCm->start_like,
                    "myrating" => ($valCm->myrating != '') ? true : false,
                    "vault" => ($valCm->vault != '') ? $valCm->vault : 0,
                    "saved_date" => $valCm->saved_date,
                    "total_records" => $total,
                );
                $i++;
            }

            // Cache the results
            $this->myredis->set($cacheKey, $vx, 60 * 60); // Cache for 1 hour
        }

        return $vx;
    }
    /**
     * Get vaulted guidelines for user
     *
     * @param string $envStatus Environment status
     * @param string $client_list_kcap Client list
     * @param string $limit Limit for query
     * @param string $searchQuery Search query
     * @param int $user_master_id User master ID
     *
     * @return array
     */
    public function grdata(
        $envStatus,
        $client_list_kcap,
        $limit,
        $searchQuery,
        $user_master_id
    ) {
        // Get total count using optimized query
        $sqlcount = "SELECT COUNT(DISTINCT gr.gr_id) as total
                    FROM knwlg_gr_register as gr
                    JOIN knwlg_vault as kv ON kv.post_id = gr.gr_id AND kv.type_text='gr' AND kv.user_id = ? AND kv.status = 1
                    LEFT JOIN content_to_env as cTenv ON cTenv.type_id = gr.gr_id AND cTenv.type = 5
                    WHERE gr.status = 3
                    AND gr.privacy_status = 0
                    {$envStatus}";

        $querycount = $this->db->query($sqlcount, array($user_master_id));
        $total = ($querycount && $querycount->num_rows() > 0) ? $querycount->num_rows() : 0;

        // Get user environment for deeplink selection
        $env = get_user_env($user_master_id);

        // Cache key for this query
        $cacheKey = "vault_grdata_" . md5($envStatus . $limit . $user_master_id);

        if ($this->myredis->exists($cacheKey)) {
            return $this->myredis->get($cacheKey);
        }

        // Use CTEs for better performance
        $sql = "WITH SavedGuidelines AS (
                SELECT 
                    gr.gr_id,
                    kv.timestamp as saved_date
                FROM 
                    knwlg_gr_register as gr
                JOIN 
                    knwlg_vault as kv ON kv.post_id = gr.gr_id AND kv.type_text='gr' AND kv.user_id = ? AND kv.status = 1
                LEFT JOIN 
                    content_to_env as cTenv ON cTenv.type_id = gr.gr_id AND cTenv.type = 5
                WHERE 
                    gr.status = 3
                    AND gr.privacy_status = 0
                    {$envStatus}
                ORDER BY 
                    kv.timestamp DESC
                {$limit}
            ),
            SpecialitiesData AS (
                SELECT 
                    grTs.gr_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                FROM 
                    SavedGuidelines sg
                JOIN 
                    gr_to_specialities as grTs ON grTs.gr_id = sg.gr_id
                JOIN 
                    master_specialities_V1 as ms ON ms.master_specialities_id = grTs.specialities_id
                GROUP BY 
                    grTs.gr_id
            ),
            SponsorsData AS (
                SELECT 
                    grTspon.gr_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM 
                    SavedGuidelines sg
                LEFT JOIN 
                    gr_to_sponsor as grTspon ON grTspon.gr_id = sg.gr_id
                LEFT JOIN 
                    client_master as clintspon ON clintspon.client_master_id = grTspon.sponsor_id
                GROUP BY 
                    grTspon.gr_id
            ),
            DoctorsData AS (
                SELECT 
                    grTsdoc.gr_id,
                    GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id) as session_doctor_id
                FROM 
                    SavedGuidelines sg
                LEFT JOIN 
                    gr_to_session_doctor as grTsdoc ON grTsdoc.gr_id = sg.gr_id
                GROUP BY 
                    grTsdoc.gr_id
            ),
            MetricsData AS (
                SELECT 
                    sg.gr_id,
                    (SELECT COUNT(rt.rating) FROM knwlg_rating rt WHERE rt.post_id = sg.gr_id AND rt.post_type='gr') as averageRating,
                    (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm WHERE kcm.type_id = sg.gr_id AND kcm.type = 'gr' AND kcm.comment_approve_status = '1') as count_comment
                FROM 
                    SavedGuidelines sg
            )
            SELECT 
                gr.gr_id as type_id,
                gr.gr_title as title,
                gr.gr_description as description,
                gr.gr_chief_scientific_editor,
                gr.gr_preview_image,
                gr.added_on,
                gr.gr_date_of_publication as publish_date,
                gr.deeplink,
                gr.gl_deeplink,
                gr.color,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                cln.client_name,
                cln.client_logo,
                sd.specialities_name,
                sd.specialities_ids_and_names,
                sp.sponsor,
                dd.session_doctor_id,
                sp.sponsor_logo,
                md.averageRating,
                md.count_comment,
                rtmy.rating as myrating,
                kv.status as vault,
                kv.timestamp as saved_date
            FROM 
                SavedGuidelines sg
            JOIN 
                knwlg_gr_register as gr ON gr.gr_id = sg.gr_id
            JOIN 
                client_master as cln ON cln.client_master_id = gr.client_id
            LEFT JOIN 
                content_to_env as cTenv ON cTenv.type_id = gr.gr_id AND cTenv.type = 5
            LEFT JOIN 
                payment_user_to_content as uTpyCont ON uTpyCont.type_id = gr.gr_id AND uTpyCont.type = 5 AND uTpyCont.user_master_id = ?
            LEFT JOIN 
                knwlg_rating as rtmy ON rtmy.post_id = gr.gr_id AND rtmy.post_type='gr' AND rtmy.rating!=0 AND rtmy.user_master_id = ?
            LEFT JOIN 
                knwlg_vault as kv ON kv.post_id = gr.gr_id AND kv.type_text='gr' AND kv.user_id = ?
            LEFT JOIN 
                SpecialitiesData sd ON sd.gr_id = gr.gr_id
            LEFT JOIN 
                SponsorsData sp ON sp.gr_id = gr.gr_id
            LEFT JOIN 
                DoctorsData dd ON dd.gr_id = gr.gr_id
            LEFT JOIN 
                MetricsData md ON md.gr_id = gr.gr_id
            ORDER BY 
                sg.saved_date DESC";


        // echo $sql;
        // exit;
        $query = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id, $user_master_id));
        $resultGr = $query->result();
        $vxGr = array();

        // Get user package
        $key_locked_gr = get_user_package($user_master_id, 'gr');

        $i = 1;
        foreach ($resultGr as $val) {
            // Process sponsor logos
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            $sponsorLogomix = [];

            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = $valueSponor;
                    }
                }
            } elseif ($val->sponsor_logo) {
                $sponsorLogomix[] = $val->sponsor_logo;
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);

            // Process session doctor data
            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;

                foreach ($session_doc_array as $single_doctor) {
                    $var = session_doc_detail($single_doctor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);

                    if ($image) {
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } elseif (stripos($image, ".clirnet.com") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg;
                            $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $imgPr;
                        }
                    } else {
                        $logic_image = docimg;
                    }

                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }

            // Build result array
            $vxGr[] = array(
                "slno" => $i,
                "type_id" => $val->type_id,
                "type" => 'gr',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "is_locked" => $key_locked_gr,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->type_id, 4, $user_master_id),
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->gr_preview_image),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src($val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "session_doctor_entities" => $ses_doc_det_array,
                "comment_count" => $val->count_comment,
                "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : $val->start_like,
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "saved_date" => $val->saved_date,
                "deeplink" => ($env == 'GL') ?
                    (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) :
                    (($val->deeplink != '') ? $val->deeplink : 0),
                "total_records" => $total,
            );
            $i++;
        }

        // Cache the results
        $this->myredis->set($cacheKey, $vxGr, 60 * 60); // Cache for 1 hour

        return $vxGr;
    }

    /**
     * @param string $envStatus
     * @param string $client_list_kcap
     * @param string $limit
     * @param string $searchQuery
     * @param int $user_master_id
     * @return array
     *
     * @description
     * Get video archive data for Vault
     * This function also handles the caching of the results
     */
    public function vidarchidata(
        $envStatus,
        $client_list_kcap,
        $limit,
        $searchQuery,
        $user_master_id
    ) {
        // Get user environment for deeplink selection
        $env = get_user_env($user_master_id);

        // Cache key for this query
        $cacheKey = "vault_vidarchidata_" . md5($envStatus . $limit . $user_master_id);

        if ($this->myredis->exists($cacheKey)) {
            return $this->myredis->get($cacheKey);
        }

        // Get total count using optimized query
        $sqlCount = "SELECT COUNT(DISTINCT cm.video_archive_id) as total
                    FROM knwlg_video_archive as cm 
                    JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id AND kv.type_text='video_archive' AND kv.user_id = ? AND kv.status = 1
                    LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.video_archive_id AND cTenv.type = 3 
                    WHERE cm.status = 3
                    {$envStatus}";

        $queryCount = $this->db->query($sqlCount, array($user_master_id));
        $total = ($queryCount && $queryCount->num_rows() > 0) ? $queryCount->row()->total : 0;

        // Use CTEs for better performance
        $sql = "WITH SavedVideos AS (
                SELECT 
                    cm.video_archive_id,
                    kv.timestamp as saved_date
                FROM 
                    knwlg_video_archive as cm
                JOIN 
                    knwlg_vault as kv ON kv.post_id = cm.video_archive_id AND kv.type_text='video_archive' AND kv.user_id = ? AND kv.status = 1
                LEFT JOIN 
                    content_to_env as cTenv ON cTenv.type_id = cm.video_archive_id AND cTenv.type = 3
                WHERE 
                    cm.status = 3
                    {$envStatus}
                ORDER BY 
                    kv.timestamp DESC
                {$limit}
            ),
            SpecialitiesData AS (
                SELECT 
                    cmTs.video_archive_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                FROM 
                    SavedVideos sv
                JOIN 
                    video_archive_to_specialities as cmTs ON cmTs.video_archive_id = sv.video_archive_id
                JOIN 
                    master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                GROUP BY 
                    cmTs.video_archive_id
            ),
            SponsorsData AS (
                SELECT 
                    cmTspon.video_archive_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM 
                    SavedVideos sv
                LEFT JOIN 
                    video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = sv.video_archive_id
                LEFT JOIN 
                    client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                GROUP BY 
                    cmTspon.video_archive_id
            ),
            MetricsData AS (
                SELECT 
                    sv.video_archive_id,
                    (SELECT COUNT(rt.rating) FROM knwlg_rating rt WHERE rt.post_id = sv.video_archive_id AND rt.post_type='video_archive' AND rt.rating!=0) as averageRating,
                    (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm WHERE kcm.type_id = sv.video_archive_id AND kcm.type = 'video_archive' AND kcm.comment_approve_status = '1') as count_comment
                FROM 
                    SavedVideos sv
            )
            SELECT 
                cm.video_archive_id as type_id,
                cm.video_archive_question,
                cm.video_archive_answer,
                cm.video_archive_question_raw,
                cm.video_archive_answer_raw,
                cm.video_archive_file_img,
                cm.video_archive_file_img_thumbnail,
                cm.deeplink,
                cm.gl_deeplink,
                cm.duration,
                cm.added_on,
                cm.publication_date,
                cm.is_share,
                cln.client_name,
                cln.client_logo,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                cm.type,
                cm.vendor,
                cm.src,
                ks.session_doctor_id,
                msct.category_name,
                msct.category_logo,
                cm.video_archive_speciality_id,
                cm.start_like,
                rtmy.rating as myrating,
                kv.status as vault,
                kv.timestamp as saved_date,
                sd.specialities_name,
                sd.specialities_ids_and_names,
                sp.sponsor,
                sp.sponsor_logo,
                md.averageRating,
                md.count_comment
            FROM 
                SavedVideos sv
            JOIN 
                knwlg_video_archive as cm ON cm.video_archive_id = sv.video_archive_id
            JOIN 
                client_master as cln ON cln.client_master_id = cm.client_id
            LEFT JOIN 
                content_to_env as cTenv ON cTenv.type_id = cm.video_archive_id AND cTenv.type = 3
            LEFT JOIN 
                payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.video_archive_id AND uTpyCont.type = 3 AND uTpyCont.user_master_id = ?
            LEFT JOIN 
                knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
            LEFT JOIN 
                master_session_category as msct ON msct.mastersession_category_id = ks.category_id
            LEFT JOIN 
                knwlg_vault as kv ON kv.post_id = cm.video_archive_id AND kv.type_text='video_archive' AND kv.user_id = ?
            LEFT JOIN 
                knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id AND rtmy.post_type='video_archive' AND rtmy.rating!=0 AND rtmy.user_master_id = ?
            LEFT JOIN 
                SpecialitiesData sd ON sd.video_archive_id = cm.video_archive_id
            LEFT JOIN 
                SponsorsData sp ON sp.video_archive_id = cm.video_archive_id
            LEFT JOIN 
                MetricsData md ON md.video_archive_id = cm.video_archive_id
            ORDER BY 
                sv.saved_date DESC";

        $query = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id, $user_master_id));
        $resultVidarch = $query->result();

        // Get user package
        $key_locked_cl = get_user_package($user_master_id, 'video_archived');

        $vxVidarch = array();
        $i = 1;

        foreach ($resultVidarch as $valVidarch) {
            // Process image
            $img = $valVidarch->video_archive_file_img ?: '';

            // Process sponsor logos
            $sponsorLogoArry = explode(",", $valVidarch->sponsor_logo);
            $sponsorLogomix = [];

            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = $valueSponor;
                    }
                }
            } elseif ($valVidarch->sponsor_logo) {
                $sponsorLogomix[] = $valVidarch->sponsor_logo;
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);

            // Process session doctor data
            $ses_doc_det_array = array();

            if ($valVidarch->session_doctor_id) {
                $session_doc_array = explode(",", $valVidarch->session_doctor_id);
                $inc_pp = 0;

                foreach ($session_doc_array as $single_doctor) {
                    $var = session_doc_detail($single_doctor);
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);

                    if ($image) {
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } elseif (stripos($image, ".clirnet.com") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg;
                            $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $imgPr;
                        }
                    } else {
                        $logic_image = docimg;
                    }

                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $inc_pp++;
                }
            }

            // Process description
            $string = htmlentities($valVidarch->video_archive_question_raw, null, 'utf-8');
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");

            $main_description = $valVidarch->video_archive_answer_raw;
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

            // Build result array
            $vxVidarch[] = array(
                "slno" => $i,
                "con_type" => $valVidarch->type,
                "type_id" => $valVidarch->type_id,
                "vendor" => $valVidarch->vendor,
                "src" => $valVidarch->src,
                "deeplink" => ($env == 'GL') ?
                    (($valVidarch->gl_deeplink != '') ? $valVidarch->gl_deeplink : 0) :
                    (($valVidarch->deeplink != '') ? $valVidarch->deeplink : 0),
                "is_share" => $valVidarch->is_share,
                "type" => 'video_archive',
                "is_locked" => $key_locked_cl,
                "price" => $valVidarch->price,
                "user_content_payment" => get_user_content_status($valVidarch->type_id, 2, $user_master_id),
                "date" => date(' jS F y', strtotime($valVidarch->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => ($valVidarch->specialities_name != '') ? $valVidarch->specialities_name : '',
                "speciality_id" => ($valVidarch->video_archive_speciality_id != '') ? $valVidarch->video_archive_speciality_id : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($valVidarch->specialities_ids_and_names),
                "client_name" => $valVidarch->client_name,
                "client_logo" => change_img_src($valVidarch->client_logo),
                "category_logo" => change_img_src($valVidarch->category_logo),
                "category_name" => $valVidarch->category_name,
                "duration" => $valVidarch->duration,
                "sponsor_name" => $valVidarch->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "comment_count" => $valVidarch->count_comment,
                "rating" => ($valVidarch->averageRating != '') ? ($valVidarch->averageRating + $valVidarch->start_like) : $valVidarch->start_like,
                "myrating" => ($valVidarch->myrating != '') ? true : false,
                "vault" => ($valVidarch->vault != '') ? $valVidarch->vault : 0,
                "saved_date" => $valVidarch->saved_date,
                "session_doctor_id" => $valVidarch->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
                "total_count" => $total
            );
            $i++;
        }

        // Cache the results
        $this->myredis->set($cacheKey, $vxVidarch, 60 * 60); // Cache for 1 hour

        return $vxVidarch;
    }

    /**
     * Get all epub data for the user's vault
     *
     * @param string $envStatus Environment status filter
     * @param string $client_list_kcap Client list filter
     * @param string $limit Limit of results
     * @param string $searchQuery Search query filter
     * @param string $user_master_id User id
     *
     * @return array Array of epub data
     */
    public function epubdata(
        $envStatus,
        $client_list_kcap,
        $limit,
        $searchQuery,
        $user_master_id
    ) {
        // Get user environment for deeplink selection
        $env = get_user_env($user_master_id);

        // Cache key for this query
        $cacheKey = "vault_epubdata_" . md5($envStatus . $limit . $user_master_id);

        if ($this->myredis->exists($cacheKey)) {
            return $this->myredis->get($cacheKey);
        }

        // Get total count using optimized query
        $sqlCount = "SELECT COUNT(DISTINCT cm.epub_id) as total
                    FROM epub_master as cm
                    JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id AND kv.type_text='epub' AND kv.user_id = ? AND kv.status = 1
                    LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.epub_id AND cTenv.type = 9
                    WHERE cm.status = 3
                    {$envStatus}";

        $queryCount = $this->db->query($sqlCount, array($user_master_id));
        $total = ($queryCount && $queryCount->num_rows() > 0) ? $queryCount->row()->total : 0;

        // Use CTEs for better performance
        $sql = "WITH SavedEpubs AS (
                SELECT 
                    cm.epub_id,
                    kv.timestamp as saved_date
                FROM 
                    epub_master as cm
                JOIN 
                    knwlg_vault as kv ON kv.post_id = cm.epub_id AND kv.type_text='epub' AND kv.user_id = ? AND kv.status = 1
                LEFT JOIN 
                    content_to_env as cTenv ON cTenv.type_id = cm.epub_id AND cTenv.type = 9
                WHERE 
                    cm.status = 3
                    {$envStatus}
                ORDER BY 
                    kv.timestamp DESC
                {$limit}
            ),
            SpecialitiesData AS (
                SELECT 
                    cmTs.epub_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                FROM 
                    SavedEpubs se
                JOIN 
                    epub_to_specialities as cmTs ON cmTs.epub_id = se.epub_id
                JOIN 
                    master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                GROUP BY 
                    cmTs.epub_id
            ),
            SponsorsData AS (
                SELECT 
                    cmTspon.epub_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM 
                    SavedEpubs se
                LEFT JOIN 
                    epub_to_sponsor as cmTspon ON cmTspon.epub_id = se.epub_id
                LEFT JOIN 
                    client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                GROUP BY 
                    cmTspon.epub_id
            ),
            MetricsData AS (
                SELECT 
                    se.epub_id,
                    (SELECT COUNT(rt.rating) FROM knwlg_rating rt WHERE rt.post_id = se.epub_id AND rt.post_type='epub' AND rt.rating!=0) as averageRating,
                    (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm WHERE kcm.type_id = se.epub_id AND kcm.type = 'epub') as count_comment
                FROM 
                    SavedEpubs se
            )
            SELECT 
                cm.epub_id as type_id,
                cm.epub_description as description,
                cm.epub_title as title,
                cm.epub_img,
                cm.epub_img_thumbnail,
                cm.epub_file,
                cm.author,
                cm.is_share,
                cm.added_on,
                cm.publication_date as publish_date,
                cm.deeplink,
                cm.gl_deeplink,
                cm.color,
                cm.start_like,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                cln.client_name,
                cln.client_logo,
                rtmy.rating as myrating,
                kv.status as vault,
                kv.timestamp as saved_date,
                sd.specialities_name,
                sd.specialities_ids_and_names,
                sp.sponsor,
                sp.sponsor_logo,
                md.averageRating,
                md.count_comment
            FROM 
                SavedEpubs se
            JOIN 
                epub_master as cm ON cm.epub_id = se.epub_id
            JOIN 
                client_master as cln ON cln.client_master_id = cm.client_id
            LEFT JOIN 
                content_to_env as cTenv ON cTenv.type_id = cm.epub_id AND cTenv.type = 9
            LEFT JOIN 
                payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.epub_id AND uTpyCont.type = 9 AND uTpyCont.user_master_id = ?
            LEFT JOIN 
                knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id AND rtmy.post_type='epub' AND rtmy.rating!=0 AND rtmy.user_master_id = ?
            LEFT JOIN 
                knwlg_vault as kv ON kv.post_id = cm.epub_id AND kv.type_text='epub' AND kv.user_id = ?
            LEFT JOIN 
                SpecialitiesData sd ON sd.epub_id = cm.epub_id
            LEFT JOIN 
                SponsorsData sp ON sp.epub_id = cm.epub_id
            LEFT JOIN 
                MetricsData md ON md.epub_id = cm.epub_id
            ORDER BY 
                se.saved_date DESC";

        $queryEpub = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id, $user_master_id));
        $resultEpub = $queryEpub->result();

        // Get user package
        $key_locked_epub = get_user_package($user_master_id, 'epub');

        $vxEpub = array();
        $i = 1;

        foreach ($resultEpub as $val) {
            // Process image
            if ($val->epub_img_thumbnail) {
                $logic_image = $val->epub_img_thumbnail;
            } else {
                $logic_image = docimg;
            }

            // Process sponsor logos
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            $sponsorLogomix = [];

            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = $valueSponor;
                    }
                }
            } elseif ($val->sponsor_logo) {
                $sponsorLogomix[] = $val->sponsor_logo;
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);

            // Build result array
            $vxEpub[] = array(
                "slno" => $i,
                "type_id" => $val->type_id,
                "type" => 'epub',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "description_short" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "author" => $val->author,
                "epub_file" => $val->epub_file,
                "is_share" => $val->is_share,
                "is_locked" => $key_locked_epub,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->type_id, 6, $user_master_id),
                "image" => change_img_src($logic_image),
                "color" => ($val->color != '') ? $val->color : '#918c91',
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "client_name" => $val->client_name,
                "client_logo" => change_img_src($val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "comment_count" => $val->count_comment,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "saved_date" => $val->saved_date,
                "deeplink" => ($env == 'GL') ?
                    (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) :
                    (($val->deeplink != '') ? $val->deeplink : 0),
                "totalcount" => $total
            );
            $i++;
        }

        // Cache the results
        $this->myredis->set($cacheKey, $vxEpub, 60 * 60); // Cache for 1 hour

        return $vxEpub;
    }
    /** epub saved records */
    /**
     * @param $id
     * @param $type
     * @return mixed
     */
    public function get_comment($id, $type)
    {
        $sql = "SELECT
				kcmt.*,
				ud.first_name,
				ud.middle_name,
				ud.profile_image,
				ud.last_name
				FROM knwlg_comment kcmt
				LEFT JOIN user_detail ud on ud.user_master_id = kcmt.user_master_id
				where
				kcmt.type_id = " . $id . "
				and
				kcmt.type = '" . $type . "'
				AND
				kcmt.status = '3'
				group by kcmt.knwlg_comment_id
				order by kcmt.knwlg_comment_id desc ";
        $query = $this->db->query($sql);
        $result = $query->result();
        $i = 1;
        foreach ($result as $val) {
            if (stripos($val->profile_image, "https://storage.googleapis.com") > -1) {
                $logic_image = $val->profile_image;
            } else {
                $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
            }
            $vx[] = array(
                "knwlg_comment_id" => $val->knwlg_comment_id,
                "type_id" => $val->type_id,
                "type" => $val->type,
                "user_master_id" => $val->user_master_id,
                "comment" => $val->comment,
                "added_on" => date(' jS F y', strtotime($val->added_on)),
                "first_name" => $val->first_name,
                "middle_name" => $val->middle_name,
                "last_name" => $val->last_name,
                "profile_image" => change_img_src($logic_image),
            );
            $i++;
        }
        return $vx;
    }
    /**
     * @param $id
     * @param $type
     * @return string
     */
    public function get_sub_comments($parent_id)
    {
        $sql = "SELECT
        kcmt.*,
        ud.first_name,
        ud.middle_name,
        ud.profile_image,
        ud.last_name
        FROM knwlg_comment kcmt
        LEFT JOIN user_detail ud on ud.user_master_id = kcmt.user_master_id
        where
        parent_id = " . $parent_id . "
        AND
        kcmt.status = 3
        and
        kcmt.comment_approve_status = 1
        group by kcmt.knwlg_comment_id
        order by kcmt.knwlg_comment_id asc ";
        $query = $this->db->query($sql);
        $results = $query->result();
        $count = $query->num_rows();
        $array = array();
        if ($count > 0) {
            foreach ($results as $result) {
                $id = $result->knwlg_comment_id;
                if (stripos($result->profile_image, "https://storage.googleapis.com") > -1) {
                    $logic_image = $result->profile_image;
                } else {
                    $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
                }
                if ($result->rating > 0) {
                    $liked = true;
                } else {
                    $liked = false;
                }
                $array[] = array(
                    "commentId" => $id,
                    "parentId" => $parent_id,
                    "type_id" => $result->type_id,
                    "type" => $result->type,
                    "user_master_id" => $result->user_master_id,
                    "body" => $result->comment,
                    "created_at" => $result->added_on,
                    "name" => 'Dr. ' . $result->first_name . ' ' . $result->last_name,
                    "email" => $result->first_name . ' ' . $result->last_name,
                    "first_name" => $result->first_name,
                    "middle_name" => $result->middle_name,
                    "last_name" => $result->last_name,
                    "images" => change_img_src($logic_image),
                    "liked" => $liked,
                    "rating" => $result->rating,
                    "children" => array()
                );
            }
        } else {
            $array[] = null;
        }
        return stripslashes(json_encode($array, JSON_UNESCAPED_SLASHES));
    }
    /**
     * @param $id
     * @param $type
     * @return string
     */
    public function get_comment_nasted($type_id, $type)
    {
        $sql = "SELECT
        kcmt.*,
        ud.first_name,
        ud.middle_name,
        ud.profile_image,
        ud.last_name
        FROM knwlg_comment kcmt
        LEFT JOIN user_detail ud on ud.user_master_id = kcmt.user_master_id
        where
        kcmt.type_id = " . $type_id . "
        and
        kcmt.type = '" . $type . "'
        and
        parent_id = 0
        AND
        kcmt.status = 3
        group by kcmt.knwlg_comment_id
        order by kcmt.knwlg_comment_id asc limit 0,100";
        $query = $this->db->query($sql);
        $results = $query->result();
        foreach ($results as $result) {
            $subcat = array();
            $id = $result->knwlg_comment_id;
            if (stripos($result->profile_image, "https://storage.googleapis.com") > -1) {
                $logic_image = $result->profile_image;
            } else {
                $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
            }
            if ($result->rating > 0) {
                $liked = true;
            } else {
                $liked = false;
            }
            $like_array = array();
            $childs = $this->hasChilds($id);
            $categories[] = array(
                "commentId" => $id,
                "parentId" => null,
                "type_id" => $result->type_id,
                "type" => $result->type,
                "user_master_id" => $result->user_master_id,
                "body" => $result->comment,
                "created_at" => $result->added_on,
                "name" => 'Dr. ' . $result->first_name . ' ' . $result->last_name,
                "email" => $result->first_name . ' ' . $result->last_name,
                "first_name" => $result->first_name,
                "middle_name" => $result->middle_name,
                "last_name" => $result->last_name,
                "images" => change_img_src($logic_image),
                "liked" => $liked,
                "rating" => $result->rating,
                "likes" => $like_array,
                "children" => array_filter($childs)
            );
        }
        /*echo '<pre>';
        print_r($categories);
        echo '</pre>';*/
        return stripslashes(json_encode($categories, JSON_UNESCAPED_SLASHES));
    }
    /**
     * @param $id
     * @return array
     */
    public function hasChilds($parent_id)
    {
        $sql = "SELECT
        kcmt.*,
        ud.first_name,
        ud.middle_name,
        ud.profile_image,
        ud.last_name
        FROM knwlg_comment kcmt
        LEFT JOIN user_detail ud on ud.user_master_id = kcmt.user_master_id
        where
        parent_id = " . $parent_id . "
        AND
        kcmt.status = 3
        and
        kcmt.comment_approve_status = 1
        group by kcmt.knwlg_comment_id
        order by kcmt.knwlg_comment_id asc ";
        $query = $this->db->query($sql);
        $results = $query->result();
        $count = $query->num_rows();
        $array = array();
        if ($count > 0) {
            foreach ($results as $result) {
                $id = $result->knwlg_comment_id;
                if (stripos($result->profile_image, "https://storage.googleapis.com") > -1) {
                    $logic_image = $result->profile_image;
                } else {
                    $logic_image = docimg; //base_url() . "uploads/docimg/MConsult.png";
                }
                if ($result->rating > 0) {
                    $liked = true;
                } else {
                    $liked = false;
                }
                $array[] = array(
                    "commentId" => $id,
                    "parentId" => $parent_id,
                    "type_id" => $result->type_id,
                    "type" => $result->type,
                    "user_master_id" => $result->user_master_id,
                    "body" => $result->comment,
                    "created_at" => $result->added_on,
                    "name" => 'Dr. ' . $result->first_name . ' ' . $result->last_name,
                    "email" => $result->first_name . ' ' . $result->last_name,
                    "first_name" => $result->first_name,
                    "middle_name" => $result->middle_name,
                    "last_name" => $result->last_name,
                    "images" => change_img_src($logic_image),
                    "liked" => $liked,
                    "rating" => $result->rating,
                    "children" => array_filter($this->hasChilds($id))
                );
            }
        } else {
            $array[] = null;
        }
        return $array;
    }
    /**
     * @param array $dataArray
     * @param $user_master_id
     * @return array
     */
    public function nasted_save_comment($dataArray = array(), $user_master_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        $type_id = $dataArray['type_id'];
        $type = $dataArray['type'];
        $comment = $dataArray['comment'];
        $parent_id = $dataArray['parent_id'];
        $data = array(
            'type' => $type,
            'type_id' => $type_id,
            'parent_id' => $parent_id,
            'status' => 3,
            'user_master_id' => $user_master_id,
            'comment' => $comment,
        );
        if ($this->insertdb->insert('knwlg_comment', $data)) {
            $insert_id = $this->insertdb->insert_id();
            $data = array(
                "type" => $type,
                "comment" => $comment,
                "type_id" => $type_id,
                "insert_id" => $insert_id,
            );
        }
        return $data;
    }
    /**
     * @param array $dataArray
     * @return bool
     */
    public function save_comment($dataArray = array(), $user_master_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        $type_id = $dataArray['type_id'];
        $type = $dataArray['type'];
        $comment = $dataArray['comment'];
        $data = array(
            'type' => $type,
            'type_id' => $type_id,
            'status' => 2,
            'user_master_id' => $user_master_id,
            'comment' => $comment,
        );
        if ($this->insertdb->insert('knwlg_comment', $data)) {
            $data = array(
                "type" => $type,
                "comment" => $comment,
                "type_id" => $type_id,
            );
        }
        return $data;
    }
    /**
     * @param array $dataArray
     * @param $user_master_id
     * @return array
     */
    public function save_like($dataArray = array(), $user_master_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        /** clinical video type names send differently */
        $array = array('video', 'archived_video', 'video_archive');
        if (in_array($dataArray['type'], $array)) {
            $type = "video_archive";
        } else {
            $type = $dataArray['type'];
        }
        /** clinical video type names send differntly */
        $type_id = $dataArray['type_id'];
        $sql = "select
        rt.rating_id,
        rt.rating
        from knwlg_rating rt
        where rt.post_id = " . $type_id . "
        and  rt.post_type='" . $type . "'
        and  rt.user_master_id = " . $user_master_id . "";
        $query = $this->db->query($sql);
        $result = $query->row();
        if (!empty($result->rating_id)) {
            if ($result->rating == 0) {
                $sql = "UPDATE  knwlg_rating SET rating=? WHERE post_id=? AND user_master_id=? AND post_type=?";
                $query = $this->insertdb->query($sql, array(1, $type_id, $user_master_id, $type));
                $sql = "select
                count(rt.rating) as averageRating
                from knwlg_rating rt
                where rt.post_id = " . $type_id . "
                and  rt.post_type='" . $type . "'
                 and rt.rating!=0";
                $query = $this->db->query($sql);
                $result = $query->row();
                $datax['rating'] = $result->averageRating;
                $datax['like'] = true;
            } else {
                $sql = "UPDATE  knwlg_rating SET rating=? WHERE post_id=? AND user_master_id=? AND post_type=?";
                $query = $this->insertdb->query($sql, array(0, $type_id, $user_master_id, $type));
                $sql = "select
                count(rt.rating) as averageRating
                from knwlg_rating rt
                where rt.post_id = " . $type_id . "
                and  rt.post_type='" . $type . "'
                and rt.rating!=0 ";
                $query = $this->db->query($sql);
                $result = $query->row();
                $datax['rating'] = $result->averageRating;
                $datax['like'] = false;
            }
        } else {
            $data = array(
                'post_type' => $type,
                'post_id' => $type_id,
                'rating' => 1,
                'user_master_id' => $user_master_id,
            );
            if ($this->insertdb->insert('knwlg_rating', $data)) {
                $sql = "select
                count(rt.rating) as averageRating
                from knwlg_rating rt
                where rt.post_id = " . $type_id . "
                and  rt.post_type='" . $type . "'
                and rt.rating!=0";
                $query = $this->db->query($sql);
                $result = $query->row();
                $datax['rating'] = $result->averageRating;
                $datax['like'] = true;
            }
        }
        return $datax;
    }
    /**
     * @param $postid
     * @param $user_id
     * @param string $type
     * @return mixed
     */
    public function save_vault(
        $postid,
        $user_id,
        $type = ""
    ) {
        $this->insertdb = $this->load->database('insert', true);

        $array = array(
            'video',
            'archived_video',
            'video_archive'
        );
        if (in_array($type, $array)) {
            $type = "video_archive";
        }
        $sql = "select
        vault_id,
        status
        from knwlg_vault
        where post_id = " . $postid . "
        and  type_text='" . $type . "'
        and  user_id = " . $user_id . "";
        $query = $this->db->query($sql);
        $result = $query->row();

        if ($result->vault_id) {
            if ($result->status == 0) {
                $sql = "UPDATE  knwlg_vault SET status=? WHERE post_id=? AND user_id=? AND 	type_text=?";
                $query = $this->insertdb->query($sql, array(1, $postid, $user_id, $type));
                $return = 1;
            } else {
                $sql = "UPDATE  knwlg_vault SET status=? WHERE post_id=? AND user_id=? AND 	type_text=?";
                $query = $this->insertdb->query($sql, array(0, $postid, $user_id, $type));
                $return = 0;
            }
        } else {
            $sql = "INSERT INTO knwlg_vault (status, post_id, user_id,type, type_text) VALUES (?, ?,?,?,?);";
            $query = $this->insertdb->query($sql, array(1, $postid, $user_id, 0, $type));
            $return = 1;
        }
        return $return;
    }
    private function del_valut_cache()
    {
        $all_cahce_keys = $this->myredis->keys('*');
        $my_cache_key = "/" . $this->redis_config['prefix'] . "_" . vault_cache_key_prefix . "/i";
        foreach ($all_cahce_keys as $c_key) {
            if (preg_match($my_cache_key, $c_key)) {
                $this->myredis->del_cache_with_full_key($c_key);
            }
        }
    }
    /**
     * Optimized version of all_archiveVideo with better performance using CTEs
     *
     * @param string $user_master_id User ID
     * @param string $client_ids Client IDs
     * @param string $group_ids Group IDs
     * @param string $spIds Speciality IDs
     * @param string $limitFrom Pagination start
     * @param string $limitTo Pagination limit
     * @param string $type_id Content type ID
     * @param string $type Content type
     * @return array Formatted results
     */
    public function all_archiveVideo(
        $user_master_id = '',
        $client_ids = '',
        $group_ids = '',
        $spIds = '',
        $limitFrom = '',
        $limitTo = '',
        $type_id = '',
        $type = ''
    ) {
        // Set speciality filter
        if (!empty($spIds) && $spIds != '0') {
            $specialities = " AND cmTs.specialities_id IN (" . $spIds . ")";
        } else {
            $specialities = "";
        }
        // Set pagination limit
        $limit = "";
        if ($limitFrom != '' && $limitTo != '') {
            $limit = "LIMIT " . $limitFrom . ", " . $limitTo;
        }
        // Set display filter based on type
        $where = "";
        if ($type == "featured" || $type == "trending") {
            $where .= " AND cm.display_in_dashboard = 1 ";
        } else {
            $where .= " AND cm.display_in_dashboard = 0 ";
        }
        // Set environment filter
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $envStatus = "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")";
            } else {
                $envStatus = "AND cTenv.env = " . $env . "";
            }
        } else {
            $envStatus = "";
        }
        // Check user package
        $key_locked = get_user_package($user_master_id, 'video_archived');
        // Build query with CTEs for better performance
        $sql = "WITH TopVideos AS (
            SELECT 
                cm.video_archive_id
            FROM 
                knwlg_video_archive AS cm
            JOIN 
                video_archive_to_specialities AS cmTs ON cmTs.video_archive_id = cm.video_archive_id
            LEFT JOIN 
                content_to_env AS cTenv ON cTenv.type_id = cm.video_archive_id AND cTenv.type = 3
            WHERE 
                cm.status = 3 
                AND cm.privacy_status = 0
                {$where}
                {$specialities}
                {$envStatus}
                AND DATE(cm.publication_date) <= CURDATE()
            GROUP BY 
                cm.video_archive_id
            ORDER BY 
                cm.display_in_dashboard DESC,
                cm.publication_date DESC
            {$limit}
        ),
        VideoDetails AS (
            SELECT
                cm.video_archive_id AS type_id,
                cm.video_archive_question,
                cm.is_share,
                cm.video_archive_answer,
                cm.video_archive_question_raw,
                cm.video_archive_answer_raw,
                cm.video_archive_file_img,
                cm.video_archive_file_img_thumbnail,
                cm.deeplink,
                cm.gl_deeplink,
                cm.start_like,
                cm.added_on,
                cm.publication_date,
                cm.duration,
                cm.type,
                cm.vendor,
                cm.src,
                cm.video_archive_speciality_id,
                cm.video_archive_session_id,
                cm.display_in_dashboard,
                cln.client_name,
                cln.client_logo,
                cTenv.price,
                uTpyCont.status AS user_contnet_payment_status,
                kv.status AS vault,
                rtmy.rating AS myrating,
                kvtd.play_time
            FROM 
                TopVideos tv
            JOIN 
                knwlg_video_archive AS cm ON cm.video_archive_id = tv.video_archive_id
            LEFT JOIN 
                client_master AS cln ON cln.client_master_id = cm.client_id
            LEFT JOIN 
                content_to_env AS cTenv ON cTenv.type_id = cm.video_archive_id AND cTenv.type = 3
            LEFT JOIN 
                payment_user_to_content AS uTpyCont ON uTpyCont.type_id = cm.video_archive_id 
                AND uTpyCont.type = 3 AND uTpyCont.user_master_id = ?
            LEFT JOIN 
                knwlg_vault AS kv ON kv.post_id = cm.video_archive_id 
                AND kv.type_text = 'video_archive' AND kv.user_id = ?
            LEFT JOIN 
                knwlg_rating AS rtmy ON rtmy.post_id = cm.video_archive_id 
                AND rtmy.post_type = 'video_archive' AND rtmy.rating != 0 AND rtmy.user_master_id = ?
            LEFT JOIN 
                knwlg_video_tracking_data AS kvtd ON kvtd.content_id = cm.video_archive_id 
                AND kvtd.content_type = 'video_archive' AND kvtd.user_master_id = ?
        ),
        SpecialitiesData AS (
            SELECT 
                cmTs.video_archive_id,
                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
            FROM 
                video_archive_to_specialities AS cmTs
            JOIN 
                master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
            WHERE 
                cmTs.video_archive_id IN (SELECT type_id FROM VideoDetails)
            GROUP BY 
                cmTs.video_archive_id
        ),
        SponsorsData AS (
            SELECT 
                cmTspon.video_archive_id,
                GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
                GROUP_CONCAT(DISTINCT clintspon.client_master_id) AS sponsor_ids
            FROM 
                video_archive_to_sponsor AS cmTspon
            JOIN 
                client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            WHERE 
                cmTspon.video_archive_id IN (SELECT type_id FROM VideoDetails)
            GROUP BY 
                cmTspon.video_archive_id
        ),
        SessionData AS (
            SELECT 
                ks.session_id,
                ks.session_doctor_id,
                msct.category_name,
                msct.category_logo
            FROM 
                knwlg_sessions_V1 AS ks
            LEFT JOIN 
                master_session_category AS msct ON msct.mastersession_category_id = ks.category_id
            WHERE 
                ks.session_id IN (SELECT video_archive_session_id FROM VideoDetails)
        ),
        RatingData AS (
            SELECT 
                rt.post_id,
                COUNT(rt.rating) AS averageRating
            FROM 
                knwlg_rating AS rt
            WHERE 
                rt.post_id IN (SELECT type_id FROM VideoDetails)
                AND rt.post_type = 'video_archive'
                AND rt.rating != 0
            GROUP BY 
                rt.post_id
        )
        SELECT 
            vd.*,
            sd.specialities_name,
            sd.specialities_ids_and_names,
            spd.sponsor,
            spd.sponsor_logo,
            spd.sponsor_ids,
            sesd.session_doctor_id,
            sesd.category_name,
            sesd.category_logo,
            rd.averageRating
        FROM 
            VideoDetails vd
        LEFT JOIN 
            SpecialitiesData sd ON vd.type_id = sd.video_archive_id
        LEFT JOIN 
            SponsorsData spd ON vd.type_id = spd.video_archive_id
        LEFT JOIN 
            SessionData sesd ON vd.video_archive_session_id = sesd.session_id
        LEFT JOIN 
            RatingData rd ON vd.type_id = rd.post_id
        ORDER BY 
            vd.display_in_dashboard DESC,
            vd.publication_date DESC";
        // echo $sql;
        // exit;
        $query = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id, $user_master_id));
        $result = $query->result();
        $vx = array();
        $i = 1;
        foreach ($result as $val) {
            // Process image
            $img = !empty($val->video_archive_file_img) ? $val->video_archive_file_img : '';

            // Process sponsor data
            $allsponsor = array();
            $sponsorLogomix = array();
            if (!empty($val->sponsor) && !empty($val->sponsor_logo)) {
                $sponsorname = explode(",", $val->sponsor);
                $sponsorLogoArry = explode(",", $val->sponsor_logo);
                for ($sp = 0; $sp < count($sponsorLogoArry); $sp++) {
                    if (!empty($sponsorLogoArry[$sp])) {
                        if (stripos($sponsorLogoArry[$sp], "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $sponsorLogoArry[$sp];
                        } else {
                            $sponsorLogomix[] = $sponsorLogoArry[$sp];
                        }
                        if (isset($sponsorname[$sp])) {
                            $allsponsor[] = array(
                                'name' => $sponsorname[$sp],
                                "logo" => $sponsorLogoArry[$sp]
                            );
                        }
                    }
                }
            } elseif (!empty($val->sponsor_logo)) {
                if (stripos($val->sponsor_logo, "https://storage.googleapis.com") > -1) {
                    $sponsorLogomix[] = $val->sponsor_logo;
                } else {
                    $sponsorLogomix[] = $val->sponsor_logo;
                }
                $allsponsor[] = array(
                    'name' => $val->sponsor,
                    "logo" => $val->sponsor_logo
                );
            }
            $sponsorLogo = !empty($sponsorLogomix) ? implode(",", $sponsorLogomix) : '';

            // Process session doctor data
            $ses_doc_det_array = array();
            if (!empty($val->session_doctor_id)) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {
                    if (empty($single_doctor)) {
                        continue;
                    }
                    $var = session_doc_detail($single_doctor);
                    if (empty($var) || empty($var[0])) {
                        continue;
                    }
                    $image = !empty($var[0]['profile_image']) ? preg_replace('/\s+/', '%20', $var[0]['profile_image']) : '';
                    $logic_image = '';
                    if (!empty($image)) {
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg;
                            $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $imgPr;
                        }
                    } else {
                        $logic_image = docimg;
                    }
                    $ses_doc_det_array[$inc_pp] = array(
                        'session_doctor_id' => $single_doctor,
                        'session_doctor_name' => $var[0]['doctor_name'],
                        'session_doctor_image' => change_img_src($logic_image),
                        'DepartmentName' => $var[0]['DepartmentName'],
                        'profile' => $var[0]['profile']
                    );
                    $inc_pp++;
                }
            }

            // Process content
            $string = !empty($val->video_archive_question_raw) ?
                trim(html_entity_decode($val->video_archive_question_raw), " \t\n\r\0\x0B\xC2\xA0") : '';
            $main_description = !empty($val->video_archive_answer_raw) ? $val->video_archive_answer_raw : '';
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);


            // Build response array
            $vx[] = array(
                "slno" => $i,
                "con_type" => $val->type,
                "type_id" => $val->type_id,
                "type" => "video",
                "vendor" => $val->vendor,
                "is_share" => $val->is_share,
                "src" => $val->src,
                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => (!empty($val->specialities_name)) ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "speciality_id" => (!empty($val->video_archive_speciality_id)) ? $val->video_archive_speciality_id : '',
                "play_time" => $val->play_time,
                "duration" => $val->duration,
                "category_name" => $val->category_name,
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->type_id, 3, $user_master_id),
                "sponsor_name" => $val->sponsor,
                "sponsor_id" => $val->sponsor_ids,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "deeplink" => ($env != 1) ?
                    ((!empty($val->gl_deeplink)) ? $val->gl_deeplink : 0) :
                    ((!empty($val->deeplink)) ? $val->deeplink : 0),
                "rating" => (!empty($val->averageRating)) ? ($val->averageRating + $val->start_like) : $val->start_like,
                "myrating" => (!empty($val->myrating)),
                "vault" => (!empty($val->vault)) ? $val->vault : 0,
                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
            );
            $i++;
        }
        return $vx;
    }

    public function gethistoryvideo(
        $user_master_id,
        $limitTo,
        $limitFrom
    ) {
        // Prepare limit clause
        $limit = "LIMIT " . $limitFrom . ", " . $limitTo;

        // Get user environment
        $env = get_user_env_id($user_master_id);

        // Set environment status for query
        if ($env) {
            $envStatus = ($env != 2)
                ? "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")"
                : "AND cTenv.env = " . $env;
        } else {
            $envStatus = "";
        }

        // Check user package
        $key_locked = get_user_package($user_master_id, 'video_archived');
        if ($key_locked == '') {
            return null;
        }

        // Cache key for this query
        $cacheKey = "history_video_" . md5($envStatus . $limit . $user_master_id);

        if ($this->myredis->exists($cacheKey)) {
            return $this->myredis->get($cacheKey);
        }

        // Use CTEs for better performance
        $sql = "WITH VideoData AS (
                SELECT 
                    cm.video_archive_id,
                    cm.video_archive_question,
                    cm.video_archive_answer,
                    cm.video_archive_question_raw,
                    cm.video_archive_answer_raw,
                    cm.video_archive_file_img,
                    cm.is_share,
                    cm.video_archive_file_img_thumbnail,
                    cm.deeplink,
                    cm.gl_deeplink,
                    cm.added_on,
                    cm.publication_date,
                    cm.duration,
                    cm.type,
                    cm.vendor,
                    cm.src,
                    cm.video_archive_speciality_id,
                    cm.display_in_dashboard
                FROM 
                    knwlg_video_archive AS cm
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = cm.video_archive_id AND cTenv.type = 3
                WHERE 
                    cm.status = 3
                    {$envStatus}
                ORDER BY 
                    cm.display_in_dashboard DESC,
                    cm.publication_date DESC
                {$limit}
            ),
            SpecialitiesData AS (
                SELECT 
                    cmTs.video_archive_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                FROM 
                    VideoData vd
                JOIN 
                    video_archive_to_specialities AS cmTs ON cmTs.video_archive_id = vd.video_archive_id
                JOIN 
                    master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                GROUP BY 
                    cmTs.video_archive_id
            ),
            SponsorsData AS (
                SELECT 
                    cmTspon.video_archive_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_master_id) AS sponsor_ids,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                FROM 
                    VideoData vd
                LEFT JOIN 
                    video_archive_to_sponsor AS cmTspon ON cmTspon.video_archive_id = vd.video_archive_id
                LEFT JOIN 
                    client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                GROUP BY 
                    cmTspon.video_archive_id
            ),
            TrackingData AS (
                SELECT 
                    kvtd.content_id AS video_archive_id,
                    kvtd.play_time
                FROM 
                    VideoData vd
                LEFT JOIN 
                    knwlg_video_tracking_data AS kvtd ON kvtd.content_id = vd.video_archive_id 
                        AND kvtd.content_type = 'video_archive' 
                        AND kvtd.user_master_id = ?
            ),
            SessionData AS (
                SELECT 
                    ks.session_id,
                    ks.session_doctor_id,
                    msct.category_name,
                    msct.category_logo
                FROM 
                    VideoData vd
                LEFT JOIN 
                    knwlg_sessions_V1 AS ks ON ks.session_id = vd.video_archive_id
                LEFT JOIN 
                    master_session_category AS msct ON msct.mastersession_category_id = ks.category_id
            ),
            RatingData AS (
                SELECT 
                    vd.video_archive_id,
                    (SELECT COUNT(rt.rating) FROM knwlg_rating rt 
                     WHERE rt.post_id = vd.video_archive_id AND rt.post_type = 'video_archive' AND rt.rating != 0) AS averageRating,
                    rtmy.rating AS myrating
                FROM 
                    VideoData vd
                LEFT JOIN 
                    knwlg_rating AS rtmy ON rtmy.post_id = vd.video_archive_id 
                        AND rtmy.post_type = 'video_archive' 
                        AND rtmy.rating != 0 
                        AND rtmy.user_master_id = ?
            ),
            VaultData AS (
                SELECT 
                    kv.post_id AS video_archive_id,
                    kv.status AS vault
                FROM 
                    VideoData vd
                LEFT JOIN 
                    knwlg_vault AS kv ON kv.post_id = vd.video_archive_id 
                        AND kv.type_text = 'video_archive' 
                        AND kv.user_id = ?
            ),
            PaymentData AS (
                SELECT 
                    uTpyCont.type_id AS video_archive_id,
                    cTenv.price,
                    uTpyCont.status AS user_contnet_payment_status
                FROM 
                    VideoData vd
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = vd.video_archive_id AND cTenv.type = 3
                LEFT JOIN 
                    payment_user_to_content AS uTpyCont ON uTpyCont.type_id = vd.video_archive_id 
                        AND uTpyCont.type = 3 
                        AND uTpyCont.user_master_id = ?
            ),
            ClientData AS (
                SELECT 
                    cm.video_archive_id,
                    cln.client_name,
                    cln.client_logo
                FROM 
                    VideoData vd
                JOIN 
                    knwlg_video_archive AS cm ON cm.video_archive_id = vd.video_archive_id
                LEFT JOIN 
                    client_master AS cln ON cln.client_master_id = cm.client_id
            )
            SELECT 
                vd.video_archive_id AS type_id,
                vd.video_archive_question,
                vd.video_archive_answer,
                vd.video_archive_question_raw,
                vd.video_archive_answer_raw,
                vd.video_archive_file_img,
                vd.is_share,
                vd.video_archive_file_img_thumbnail,
                vd.deeplink,
                vd.gl_deeplink,
                vd.added_on,
                vd.publication_date,
                vd.duration,
                vd.type,
                vd.vendor,
                vd.src,
                vd.video_archive_speciality_id,
                td.play_time,
                pd.price,
                pd.user_contnet_payment_status,
                cd.client_name,
                cd.client_logo,
                sd.session_doctor_id,
                sd.category_name,
                sd.category_logo,
                sp.sponsor,
                sp.sponsor_ids,
                sp.sponsor_logo,
                spec.specialities_name,
                spec.specialities_ids_and_names,
                vdData.vault,
                rd.averageRating,
                rd.myrating
            FROM 
                VideoData vd
            LEFT JOIN 
                SpecialitiesData spec ON spec.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                SponsorsData sp ON sp.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                TrackingData td ON td.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                SessionData sd ON sd.session_id = vd.video_archive_id
            LEFT JOIN 
                RatingData rd ON rd.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                VaultData vdData ON vdData.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                PaymentData pd ON pd.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                ClientData cd ON cd.video_archive_id = vd.video_archive_id
            ORDER BY 
                vd.display_in_dashboard DESC,
                vd.publication_date DESC";

        // echo $sql;
        // exit;
        $query = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id, $user_master_id));
        $result = $query->result();

        $vx = array();
        $i = 1;

        foreach ($result as $val) {
            // Process image
            $img = $val->video_archive_file_img ?: '';

            // Process sponsor data
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            $sponsorLogomix = [];

            if (!empty($sponsorLogoArry) && count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = $valueSponor;
                        }
                    }
                }
            } elseif (!empty($val->sponsor_logo)) {
                if (stripos($val->sponsor_logo, "https://storage.googleapis.com") > -1) {
                    $sponsorLogomix[] = $val->sponsor_logo;
                } else {
                    $sponsorLogomix[] = $val->sponsor_logo;
                }
            }

            $sponsorLogo = !empty($sponsorLogomix) ? implode(",", $sponsorLogomix) : '';

            // Process session doctor data
            $ses_doc_det_array = [];

            if (!empty($val->session_doctor_id)) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;

                foreach ($session_doc_array as $single_doctor) {
                    if (empty($single_doctor)) {
                        continue;
                    }

                    $var = session_doc_detail($single_doctor);
                    if (empty($var) || empty($var[0])) {
                        continue;
                    }

                    $image = !empty($var[0]['profile_image']) ? preg_replace('/\s+/', '%20', $var[0]['profile_image']) : '';
                    $logic_image = '';

                    if (!empty($image)) {
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg;
                            $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $imgPr;
                        }
                    } else {
                        $logic_image = docimg;
                    }

                    $ses_doc_det_array[$inc_pp] = [
                        'session_doctor_id' => $single_doctor,
                        'session_doctor_name' => $var[0]['doctor_name'],
                        'session_doctor_image' => change_img_src($logic_image),
                        'DepartmentName' => $var[0]['DepartmentName'],
                        'profile' => $var[0]['profile']
                    ];

                    $inc_pp++;
                }
            }

            // Process content
            $string = !empty($val->video_archive_question_raw) ?
                trim(html_entity_decode($val->video_archive_question_raw), " \t\n\r\0\x0B\xC2\xA0") : '';

            $main_description = !empty($val->video_archive_answer_raw) ? $val->video_archive_answer_raw : '';
            $main_description = str_replace(["\r\n\r\n\r\n\r\n\r\n", "\r\n\r\n\r\n\r\n", "\r\n\r\n\r\n", "\r\n\r\n"], "\r\n", $main_description);

            // Build response array
            $vx[] = [
                "slno" => $i,
                "con_type" => $val->type,
                "type_id" => $val->type_id,
                "type" => "video",
                "is_share" => $val->is_share,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => !empty($val->specialities_name) ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "speciality_id" => !empty($val->video_archive_speciality_id) ? $val->video_archive_speciality_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src($val->client_logo),
                "play_time" => $val->play_time,
                "duration" => $val->duration,
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->type_id, 3, $user_master_id),
                "category_name" => $val->category_name,
                "sponsor_name" => $val->sponsor,
                "sponsor_id" => $val->sponsor_ids,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "comment_count" => $val->count_comment,
                "deeplink" => ($env != 1) ?
                    (!empty($val->gl_deeplink) ? $val->gl_deeplink : 0) :
                    (!empty($val->deeplink) ? $val->deeplink : 0),
                "rating" => !empty($val->averageRating) ? $val->averageRating : '',
                "myrating" => !empty($val->myrating),
                "vault" => !empty($val->vault) ? $val->vault : 0,
                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
            ];

            $i++;
        }

        // Cache the results
        $this->myredis->set($cacheKey, $vx, 60 * 60); // Cache for 1 hour

        return $vx;
    }


    //gethistoryvideoSpeciality

    public function gethistoryvideoSpeciality(
        $user_master_id,
        $limitTo,
        $limitFrom,
        $spIds
    ) {
        // Prepare limit clause
        $limit = "LIMIT " . $limitFrom . ", " . $limitTo;

        // Get user environment
        $env = get_user_env_id($user_master_id);

        // Set environment status for query
        if ($env) {
            $envStatus = ($env != 2)
                ? "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")"
                : "AND cTenv.env = " . $env;
        } else {
            $envStatus = "";
        }

        // Check user package
        $key_locked = get_user_package($user_master_id, 'video_archived');
        if ($key_locked == '') {
            return null;
        }

        $conditionSpeciality = '';
        if (($spIds != '') && ($spIds != 0)) {
            $conditionSpeciality = "  AND cmTs.specialities_id IN (" . $spIds . ")";
        }

        // echo 'hello...';
        // exit;


        // Cache key for this query
        $cacheKey = "history_video_" . md5($envStatus . $limit . $user_master_id.$spIds);

        if ($this->myredis->exists($cacheKey)) {
            return $this->myredis->get($cacheKey);
        }

        // Use CTEs for better performance
        $sql = "WITH VideoData AS (
            SELECT 
                cm.video_archive_id,
                cm.video_archive_question,
                cm.video_archive_answer,
                cm.video_archive_question_raw,
                cm.video_archive_answer_raw,
                cm.video_archive_file_img,
                cm.is_share,
                cm.video_archive_file_img_thumbnail,
                cm.deeplink,
                cm.gl_deeplink,
                cm.added_on,
                cm.publication_date,
                cm.duration,
                cm.type,
                cm.vendor,
                cm.src,
                cm.video_archive_speciality_id,
                cm.display_in_dashboard
            FROM 
                knwlg_video_archive AS cm
            JOIN 
                knwlg_video_tracking_data as kvtd ON kvtd.content_id=cm.video_archive_id AND kvtd.content_type='video_archive' AND kvtd.user_master_id = {$user_master_id}

            LEFT JOIN 
                content_to_env AS cTenv ON cTenv.type_id = cm.video_archive_id AND cTenv.type = 3
            LEFT JOIN 
                video_archive_to_specialities AS cmTs ON cmTs.video_archive_id = cm.video_archive_id
            
            WHERE 
                cm.status = 3
                {$envStatus}
                {$conditionSpeciality} 
            GROUP by cm.video_archive_id
            ORDER BY 
                cm.display_in_dashboard DESC,
                cm.publication_date DESC
            {$limit}
        ),
        SpecialitiesData AS (
            SELECT 
                cmTs.video_archive_id,
                GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
            FROM 
                VideoData vd
            JOIN 
                video_archive_to_specialities AS cmTs ON cmTs.video_archive_id = vd.video_archive_id
            JOIN 
                master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
            GROUP BY 
                cmTs.video_archive_id
        ),
        SponsorsData AS (
            SELECT 
                cmTspon.video_archive_id,
                GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_master_id) AS sponsor_ids,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
            FROM 
                VideoData vd
            LEFT JOIN 
                video_archive_to_sponsor AS cmTspon ON cmTspon.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            GROUP BY 
                cmTspon.video_archive_id
        ),
        TrackingData AS (
            SELECT 
                kvtd.content_id AS video_archive_id,
                kvtd.play_time
            FROM 
                VideoData vd
            LEFT JOIN 
                knwlg_video_tracking_data AS kvtd ON kvtd.content_id = vd.video_archive_id 
                    AND kvtd.content_type = 'video_archive' 
                    AND kvtd.user_master_id = ?
        ),
        SessionData AS (
            SELECT 
                ks.session_id,
                ks.session_doctor_id,
                msct.category_name,
                msct.category_logo
            FROM 
                VideoData vd
            LEFT JOIN 
                knwlg_sessions_V1 AS ks ON ks.session_id = vd.video_archive_id
            LEFT JOIN 
                master_session_category AS msct ON msct.mastersession_category_id = ks.category_id
        ),
        RatingData AS (
            SELECT 
                vd.video_archive_id,
                (SELECT COUNT(rt.rating) FROM knwlg_rating rt 
                 WHERE rt.post_id = vd.video_archive_id AND rt.post_type = 'video_archive' AND rt.rating != 0) AS averageRating,
                rtmy.rating AS myrating
            FROM 
                VideoData vd
            LEFT JOIN 
                knwlg_rating AS rtmy ON rtmy.post_id = vd.video_archive_id 
                    AND rtmy.post_type = 'video_archive' 
                    AND rtmy.rating != 0 
                    AND rtmy.user_master_id = ?
        ),
        VaultData AS (
            SELECT 
                kv.post_id AS video_archive_id,
                kv.status AS vault
            FROM 
                VideoData vd
            LEFT JOIN 
                knwlg_vault AS kv ON kv.post_id = vd.video_archive_id 
                    AND kv.type_text = 'video_archive' 
                    AND kv.user_id = ?
        ),
        PaymentData AS (
            SELECT 
                uTpyCont.type_id AS video_archive_id,
                cTenv.price,
                uTpyCont.status AS user_contnet_payment_status
            FROM 
                VideoData vd
            LEFT JOIN 
                content_to_env AS cTenv ON cTenv.type_id = vd.video_archive_id AND cTenv.type = 3
            LEFT JOIN 
                payment_user_to_content AS uTpyCont ON uTpyCont.type_id = vd.video_archive_id 
                    AND uTpyCont.type = 3 
                    AND uTpyCont.user_master_id = ?
        ),
        ClientData AS (
            SELECT 
                cm.video_archive_id,
                cln.client_name,
                cln.client_logo
            FROM 
                VideoData vd
            JOIN 
                knwlg_video_archive AS cm ON cm.video_archive_id = vd.video_archive_id
            LEFT JOIN 
                client_master AS cln ON cln.client_master_id = cm.client_id
        )
        SELECT 
            vd.video_archive_id AS type_id,
            vd.video_archive_question,
            vd.video_archive_answer,
            vd.video_archive_question_raw,
            vd.video_archive_answer_raw,
            vd.video_archive_file_img,
            vd.is_share,
            vd.video_archive_file_img_thumbnail,
            vd.deeplink,
            vd.gl_deeplink,
            vd.added_on,
            vd.publication_date,
            vd.duration,
            vd.type,
            vd.vendor,
            vd.src,
            vd.video_archive_speciality_id,
            td.play_time,
            pd.price,
            pd.user_contnet_payment_status,
            cd.client_name,
            cd.client_logo,
            sd.session_doctor_id,
            sd.category_name,
            sd.category_logo,
            sp.sponsor,
            sp.sponsor_ids,
            sp.sponsor_logo,
            spec.specialities_name,
            spec.specialities_ids_and_names,
            vdData.vault,
            rd.averageRating,
            rd.myrating
        FROM 
            VideoData vd
        LEFT JOIN 
            SpecialitiesData spec ON spec.video_archive_id = vd.video_archive_id
        LEFT JOIN 
            SponsorsData sp ON sp.video_archive_id = vd.video_archive_id
        LEFT JOIN 
            TrackingData td ON td.video_archive_id = vd.video_archive_id
        LEFT JOIN 
            SessionData sd ON sd.session_id = vd.video_archive_id
        LEFT JOIN 
            RatingData rd ON rd.video_archive_id = vd.video_archive_id
        LEFT JOIN 
            VaultData vdData ON vdData.video_archive_id = vd.video_archive_id
        LEFT JOIN 
            PaymentData pd ON pd.video_archive_id = vd.video_archive_id
        LEFT JOIN 
            ClientData cd ON cd.video_archive_id = vd.video_archive_id
        ORDER BY 
            vd.display_in_dashboard DESC,
            vd.publication_date DESC";
        // echo $sql;
        // exit;
        $query = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id, $user_master_id));
        // echo $this->db->last_query();
        // exit;

        $result = $query->result();

        $vx = array();
        $i = 1;

        foreach ($result as $val) {
            // Process image
            $img = $val->video_archive_file_img ?: '';

            // Process sponsor data
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            $sponsorLogomix = [];

            if (!empty($sponsorLogoArry) && count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        if (stripos($valueSponor, "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $valueSponor;
                        } else {
                            $sponsorLogomix[] = $valueSponor;
                        }
                    }
                }
            } elseif (!empty($val->sponsor_logo)) {
                if (stripos($val->sponsor_logo, "https://storage.googleapis.com") > -1) {
                    $sponsorLogomix[] = $val->sponsor_logo;
                } else {
                    $sponsorLogomix[] = $val->sponsor_logo;
                }
            }

            $sponsorLogo = !empty($sponsorLogomix) ? implode(",", $sponsorLogomix) : '';

            // Process session doctor data
            $ses_doc_det_array = [];

            if (!empty($val->session_doctor_id)) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;

                foreach ($session_doc_array as $single_doctor) {
                    if (empty($single_doctor)) {
                        continue;
                    }

                    $var = session_doc_detail($single_doctor);
                    if (empty($var) || empty($var[0])) {
                        continue;
                    }

                    $image = !empty($var[0]['profile_image']) ? preg_replace('/\s+/', '%20', $var[0]['profile_image']) : '';
                    $logic_image = '';

                    if (!empty($image)) {
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg;
                            $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $imgPr;
                        }
                    } else {
                        $logic_image = docimg;
                    }

                    $ses_doc_det_array[$inc_pp] = [
                        'session_doctor_id' => $single_doctor,
                        'session_doctor_name' => $var[0]['doctor_name'],
                        'session_doctor_image' => change_img_src($logic_image),
                        'DepartmentName' => $var[0]['DepartmentName'],
                        'profile' => $var[0]['profile']
                    ];

                    $inc_pp++;
                }
            }

            // Process content
            $string = !empty($val->video_archive_question_raw) ?
                trim(html_entity_decode($val->video_archive_question_raw), " \t\n\r\0\x0B\xC2\xA0") : '';

            $main_description = !empty($val->video_archive_answer_raw) ? $val->video_archive_answer_raw : '';
            $main_description = str_replace(["\r\n\r\n\r\n\r\n\r\n", "\r\n\r\n\r\n\r\n", "\r\n\r\n\r\n", "\r\n\r\n"], "\r\n", $main_description);

            // Build response array
            $vx[] = [
                "slno" => $i,
                "con_type" => $val->type,
                "type_id" => $val->type_id,
                "type" => "video",
                "is_share" => $val->is_share,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => change_img_src($img),
                "answer" => html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => !empty($val->specialities_name) ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "speciality_id" => !empty($val->video_archive_speciality_id) ? $val->video_archive_speciality_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src($val->client_logo),
                "play_time" => $val->play_time,
                "duration" => $val->duration,
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->type_id, 3, $user_master_id),
                "category_name" => $val->category_name,
                "sponsor_name" => $val->sponsor,
                "sponsor_id" => $val->sponsor_ids,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "comment_count" => $val->count_comment,
                "deeplink" => ($env != 1) ?
                    (!empty($val->gl_deeplink) ? $val->gl_deeplink : 0) :
                    (!empty($val->deeplink) ? $val->deeplink : 0),
                "rating" => !empty($val->averageRating) ? $val->averageRating : '',
                "myrating" => !empty($val->myrating),
                "vault" => !empty($val->vault) ? $val->vault : 0,
                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
            ];

            $i++;
        }

        // Cache the results
        $this->myredis->set($cacheKey, $vx, 60 * 60); // Cache for 1 hour

        return $vx;
    }


    /**
     * Get recording list with optimized performance
     *
     * @param string $user_master_id User ID
     * @param string $limitFrom Starting limit
     * @param string $limitTo Number of records
     * @return array List of recordings
     */
    public function getrecordinglist(
        $user_master_id = '',
        $limitFrom = '',
        $limitTo = ''
    ) {
        if (empty($user_master_id)) {
            return [];
        }

        // Get user environment and type
        $env = get_user_env($user_master_id);
        $user_type = get_master_user_type_id($user_master_id);

        if (!$user_type) {
            return [];
        }

        // Set pagination limit
        $limit = ($limitFrom != '' && $limitTo != '') ? "LIMIT {$limitFrom}, {$limitTo}" : "";

        // Cache key for this query - include user type for proper caching
        $cacheKey = "recording_list_{$user_master_id}_{$user_type}_{$limitFrom}_{$limitTo}";

        // Only use cache for non-internal users
        $useCache = ($user_type != 5 && $this->myredis->exists($cacheKey));

        if ($useCache) {
            return $this->myredis->get($cacheKey);
        }

        // Set status condition based on user type
        $statusCondition = ($user_type == 5)
            ? "cm.status IN (3, 5)"
            : "cm.status = 3";

        // Set publication date condition based on user type
        $pubDateCondition = ($user_type == 5)
            ? ""
            : "AND cm.publication_date <= CURDATE()";

        // Use optimized CTE structure for better performance
        $sql = "WITH RecordingList AS (
            SELECT 
                cm.video_archive_id,
                cm.src,
                cm.video_archive_question_raw,
                cm.video_archive_answer_raw,
                cm.video_archive_file_img,
                cm.video_archive_file_img_thumbnail,
                cm.video_archive_speciality_id,
                cm.deeplink,
                cm.gl_deeplink,
                cm.duration,
                cm.display_in_dashboard,
                cm.publication_date,
                ks.session_doctor_id
            FROM 
                knwlg_video_archive AS cm
            JOIN 
                knwlg_session_recording_request AS ksrr ON ksrr.session_id = cm.video_archive_session_id
            LEFT JOIN
                knwlg_sessions_V1 AS ks ON ks.session_id = cm.video_archive_session_id
            WHERE
                {$statusCondition}
                {$pubDateCondition}
                AND ksrr.user_master_id = ?
            ORDER BY
                cm.display_in_dashboard DESC,
                cm.publication_date DESC
            {$limit}
        )
        SELECT 
            rl.video_archive_id AS type_id,
            rl.src,
            rl.video_archive_question_raw,
            rl.video_archive_answer_raw,
            rl.video_archive_file_img,
            rl.video_archive_speciality_id,
            rl.deeplink,
            rl.gl_deeplink,
            rl.duration,
            rl.session_doctor_id,
            
            /* Specialities data */
            (
                SELECT GROUP_CONCAT(DISTINCT ms.specialities_name)
                FROM video_archive_to_specialities AS cmTs 
                JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                WHERE cmTs.video_archive_id = rl.video_archive_id
            ) AS specialities_name,
            
            (
                SELECT GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name))
                FROM video_archive_to_specialities AS cmTs 
                JOIN master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                WHERE cmTs.video_archive_id = rl.video_archive_id
            ) AS specialities_ids_and_names,
            
            /* Sponsor data */
            (
                SELECT GROUP_CONCAT(DISTINCT clintspon.client_name)
                FROM video_archive_to_sponsor AS cmTspon 
                JOIN client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                WHERE cmTspon.video_archive_id = rl.video_archive_id
            ) AS sponsor,
            
            (
                SELECT GROUP_CONCAT(DISTINCT clintspon.client_logo)
                FROM video_archive_to_sponsor AS cmTspon 
                JOIN client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                WHERE cmTspon.video_archive_id = rl.video_archive_id
            ) AS sponsor_logo,
            
            /* Metrics data */
            (
                SELECT COUNT(rt.rating) 
                FROM knwlg_rating AS rt 
                WHERE rt.post_id = rl.video_archive_id AND rt.post_type = 'video_archive' AND rt.rating != 0
            ) AS averageRating,
            
            (
                SELECT COUNT(kcm.knwlg_comment_id) 
                FROM knwlg_comment AS kcm 
                WHERE kcm.type_id = rl.video_archive_id AND kcm.type = 'video_archive' AND kcm.comment_approve_status = 1
            ) AS count_comment,
            
            /* User-specific data */
            kvtd.play_time,
            rtmy.rating AS myrating,
            kv.status AS vault
            
        FROM 
            RecordingList AS rl
        LEFT JOIN 
            knwlg_video_tracking_data AS kvtd ON kvtd.content_id = rl.video_archive_id 
            AND kvtd.content_type = 'video_archive' 
            AND kvtd.user_master_id = ?
        LEFT JOIN 
            knwlg_rating AS rtmy ON rtmy.post_id = rl.video_archive_id 
            AND rtmy.post_type = 'video_archive' 
            AND rtmy.rating != 0 
            AND rtmy.user_master_id = ?
        LEFT JOIN 
            knwlg_vault AS kv ON kv.post_id = rl.video_archive_id 
            AND kv.type_text = 'video_archive' 
            AND kv.user_id = ?";

        $query = $this->db->query($sql, array($user_master_id, $user_master_id, $user_master_id, $user_master_id));
        $result = $query->result();

        $vx = array();
        $i = 1;

        foreach ($result as $val) {
            // Process image
            $img = !empty($val->video_archive_file_img) ? $val->video_archive_file_img : '';

            // Process sponsor data - optimized to reduce unnecessary operations
            $sponsorLogo = '';
            if (!empty($val->sponsor_logo)) {
                $sponsorLogoArray = explode(",", $val->sponsor_logo);
                $sponsorLogos = array_filter($sponsorLogoArray, function ($logo) {
                    return !empty($logo);
                });
                $sponsorLogo = implode(",", $sponsorLogos);
            }

            // Process session doctor data - optimized to reduce unnecessary operations
            $ses_doc_det_array = [];

            if (!empty($val->session_doctor_id)) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $inc_pp = 0;

                foreach ($session_doc_array as $single_doctor) {
                    if (empty($single_doctor)) {
                        continue;
                    }

                    $var = session_doc_detail($single_doctor);
                    if (empty($var) || empty($var[0])) {
                        continue;
                    }

                    $image = !empty($var[0]['profile_image']) ? preg_replace('/\s+/', '%20', $var[0]['profile_image']) : '';
                    $logic_image = '';

                    if (!empty($image)) {
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg;
                            $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $imgPr;
                        }
                    } else {
                        $logic_image = docimg;
                    }

                    $ses_doc_det_array[$inc_pp] = [
                        'session_doctor_id' => $single_doctor,
                        'session_doctor_name' => $var[0]['doctor_name'],
                        'session_doctor_image' => change_img_src($logic_image),
                        'DepartmentName' => $var[0]['DepartmentName'],
                        'profile' => $var[0]['profile']
                    ];

                    $inc_pp++;
                }
            }

            // Process content - optimized string operations
            $question = '';
            if (!empty($val->video_archive_question_raw)) {
                $question = trim(html_entity_decode($val->video_archive_question_raw), " \t\n\r\0\x0B\xC2\xA0");
            }

            $answer = '';
            if (!empty($val->video_archive_answer_raw)) {
                $answer = $val->video_archive_answer_raw;
                $answer = str_replace(["\r\n\r\n\r\n\r\n\r\n", "\r\n\r\n\r\n\r\n", "\r\n\r\n\r\n", "\r\n\r\n"], "\r\n", $answer);
            }

            // Build response array
            $vx[] = [
                "slno" => $i++,
                "type_id" => $val->type_id,
                "src" => $val->src,
                "question" => html_entity_decode(strip_tags($question)),
                "image" => change_img_src($img),
                "answer" => html_entity_decode(strip_tags(substr($answer, 0, 300))),
                "specialities" => !empty($val->specialities_name) ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                "speciality_id" => !empty($val->video_archive_speciality_id) ? $val->video_archive_speciality_id : '',
                "play_time" => $val->play_time,
                "duration" => $val->duration,
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "deeplink" => ($env != 1) ?
                    (!empty($val->gl_deeplink) ? $val->gl_deeplink : 0) :
                    (!empty($val->deeplink) ? $val->deeplink : 0),
                "rating" => !empty($val->averageRating) ? $val->averageRating : '',
                "comment_count" => !empty($val->count_comment) ? $val->count_comment : 0,
                "myrating" => !empty($val->myrating),
                "vault" => !empty($val->vault) ? $val->vault : 0,
                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
            ];
        }

        // Only cache for non-internal users
        if ($user_type != 5) {
            $this->myredis->set($cacheKey, $vx, 60 * 60); // Cache for 1 hour
        }

        return $vx;
    }
    public function detail_archiveVideo(
        $type_id = '',
        $user_master_id = '',
        $from_type = '',
        $banner_type = ''
    ) {
        if (empty($type_id)) {
            return;
        }

        $this->insertdb = $this->load->database('insert', true);
        $cachename = "archieve_detail_" . $type_id;
        $env = get_user_env_id($user_master_id);
        //$envStatus = "";

        if ($env) {
            $envStatus = ($env != 2) ?
                "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")" :
                "AND cTenv.env = " . $env;
        }

        $key_locked = get_user_package($user_master_id, 'video_archived');
        $user_type = get_master_user_type_id($user_master_id);

        if (!$user_type) {
            return;
        }

        $result = null;

        // For internal users (type 5) or when cache doesn't exist
        if ($user_type == 5 || !$this->myredis->exists($cachename)) {

            $dataOriginType = 'Database';

            // Build status condition based on user type
            $statusCondition = ($user_type == 5) ?
                "cm.status IN (3, 5) AND cm.privacy_status IN (0, 1, 2)" :
                "cm.status = 3  AND cm.publication_date <= CURDATE() AND cm.privacy_status IN (0, 1, 2)";

            // Use CTEs for better performance
            $sql = "WITH VideoData AS (
                SELECT
                    cm.video_archive_id AS type_id,
                    cm.video_archive_question,
                    cm.video_archive_answer,
                    cm.video_archive_citation,
                    cm.video_archive_transcription,
                    
                    cm.env,
                    cm.is_share,
                    cm.is_like,
                    cm.is_comment,
                    cm.video_archive_question_raw,
                    cm.video_archive_answer_raw,
                    cm.video_archive_file_img,
                    cm.video_archive_file_img_thumbnail,
                    cm.start_like,
                    cm.comment_status,
                    cm.added_on,
                    cm.publication_date,
                    cm.privacy_status,
                    cm.type,
                    cm.vendor,
                    cm.src,
                    cm.deeplink,
                    cm.gl_deeplink,
                    cm.video_archive_tags,
                    cm.video_archive_speciality_id
                FROM 
                    knwlg_video_archive AS cm
                WHERE 
                    {$statusCondition}
                    AND cm.video_archive_id = {$type_id}
            ),
            SpecialitiesData AS (
                SELECT 
                    cmTs.video_archive_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                FROM 
                    video_archive_to_specialities AS cmTs
                JOIN 
                    master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                WHERE 
                    cmTs.video_archive_id = {$type_id}
                GROUP BY 
                    cmTs.video_archive_id
            ),
            SponsorData AS (
                SELECT 
                    cmTspon.video_archive_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                FROM 
                    video_archive_to_sponsor AS cmTspon
                LEFT JOIN 
                    client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                WHERE 
                    cmTspon.video_archive_id = {$type_id}
                GROUP BY 
                    cmTspon.video_archive_id
            ),
            ClientData AS (
                SELECT 
                    cm.client_master_id,
                    cm.client_name,
                    cm.client_logo
                FROM 
                    client_master AS cm
                JOIN 
                    knwlg_video_archive AS kva ON kva.client_id = cm.client_master_id
                WHERE 
                    kva.video_archive_id = {$type_id}
            ),
            SessionData AS (
                SELECT 
                    ks.session_id,
                    ks.session_doctor_id
                FROM 
                    knwlg_sessions_V1 AS ks
                JOIN 
                    knwlg_video_archive AS kva ON kva.video_archive_session_id = ks.session_id
                WHERE 
                    kva.video_archive_id = {$type_id}
            ),
            MetricsData AS (
                SELECT 
                    {$type_id} AS video_archive_id,
                    -- (SELECT COUNT(rt.rating) FROM knwlg_rating rt 
                    --  WHERE rt.post_id = {$type_id} AND rt.post_type = 'video_archive' AND rt.rating != 0) AS averageRating,
                    (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm 
                     WHERE kcm.type_id = {$type_id} AND kcm.type = 'video_archive' AND kcm.comment_approve_status = 1) AS count_comment
            ),
            UserData AS (
                SELECT 
                    {$type_id} AS video_archive_id,
                    kvtd.play_time,
                    -- rtmy.rating AS myrating,
                    -- kv.status AS vault,
                    uTpyCont.status AS user_contnet_payment_status,
                    cTenv.price
                FROM 
                    knwlg_video_archive AS kva
                LEFT JOIN 
                    knwlg_video_tracking_data AS kvtd ON kvtd.content_id = kva.video_archive_id 
                        AND kvtd.content_type = 'video_archive' AND kvtd.user_master_id = {$user_master_id}
                -- LEFT JOIN 
                --     knwlg_rating AS rtmy ON rtmy.post_id = kva.video_archive_id 
                --         AND rtmy.post_type = 'video_archive' AND rtmy.rating != 0 AND rtmy.user_master_id = {$user_master_id}
                -- LEFT JOIN 
                --     knwlg_vault AS kv ON kv.post_id = kva.video_archive_id 
                --         AND kv.type_text = 'video_archive' AND kv.user_id = {$user_master_id}
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = kva.video_archive_id AND cTenv.type = 3
                LEFT JOIN 
                    payment_user_to_content AS uTpyCont ON uTpyCont.type_id = kva.video_archive_id 
                        AND uTpyCont.type = 3 AND uTpyCont.user_master_id = {$user_master_id}
                WHERE 
                    kva.video_archive_id = {$type_id}
            )
            SELECT 
                vd.*,
                cd.client_name,
                cd.client_logo,
                sd.session_doctor_id,
                spd.specialities_name,
                spd.specialities_ids_and_names,
                spond.sponsor,
                spond.sponsor_logo,
                ud.play_time,
                -- ud.myrating,
                -- ud.vault,
                ud.user_contnet_payment_status,
                ud.price,
                -- md.averageRating,
                md.count_comment
            FROM 
                VideoData vd
            LEFT JOIN 
                ClientData cd ON 1=1
            LEFT JOIN 
                SessionData sd ON 1=1
            LEFT JOIN 
                SpecialitiesData spd ON vd.type_id = spd.video_archive_id
            LEFT JOIN 
                SponsorData spond ON vd.type_id = spond.video_archive_id
            LEFT JOIN 
                UserData ud ON vd.type_id = ud.video_archive_id
            LEFT JOIN 
                MetricsData md ON vd.type_id = md.video_archive_id";

            // echo $sql;
            // exit;
            $query = $this->db->query($sql);
            $result = $query->row();

            if (empty($result) || $result->type_id == '') {
                return;
            }

            // Cache for non-internal users
            if ($user_type != 5 && !$this->myredis->exists($cachename)) {
                $this->myredis->set($cachename, $result);
            }
        } else {
            // Get from cache
            $dataOriginType = 'cached';
            $result = $this->myredis->get($cachename);
        }

        // Update view count
        $this->insertdb->set('viewed', 'viewed+1', false);
        $this->insertdb->where('video_archive_id', $type_id);
        $this->insertdb->update('knwlg_video_archive');

        if ($result->type_id) {


            $rating_data = get_content_rating_data('video_archive', $type_id, $user_master_id);
            // Now you can access the data
            $averageRating = $rating_data['averageRating'];
            $hasUserRated = $rating_data['myrating']; // boolean
            $vaultStatus = $rating_data['vault']; // 0 or 1
            $startLike = $rating_data['start_like'];


            // Process data for response
            $img = !empty($result->video_archive_file_img) ? $result->video_archive_file_img : '';

            // Process sponsors
            // $allsponsor = [];
            // $sponsorLogomix = [];
            // if (!empty($result->sponsor_logo)) {
            //     $sponsorname = explode(",", $result->sponsor);
            //     $sponsorLogoArry = explode(",", $result->sponsor_logo);

            //     foreach ($sponsorLogoArry as $sp => $valueSponor) {
            //         if (!empty($valueSponor)) {
            //             $sponsorLogomix[] = $valueSponor;
            //             $allsponsor[] = [
            //                 'name' => isset($sponsorname[$sp]) ? $sponsorname[$sp] : '',
            //                 "logo" => $valueSponor
            //             ];
            //         }
            //     }
            // }

            // $sponsorLogo = !empty($sponsorLogomix) ? implode(",", $sponsorLogomix) : null;


            $sponsor_data = get_sponsor_data($result->sponsor, $result->sponsor_logo, true);
            $allsponsor = $sponsor_data['all_sponsors'];
            $sponsorLogo = $sponsor_data['sponsor_logo'];


            // Process session doctors
            $ses_doc_det_array = [];

            if (!empty($result->session_doctor_id)) {
                $session_doc_array = explode(",", $result->session_doctor_id);

                foreach ($session_doc_array as $inc_pp => $single_doctor) {
                    if (empty($single_doctor)) {
                        continue;
                    }

                    $var = session_doc_detail($single_doctor);
                    if (empty($var) || empty($var[0])) {
                        continue;
                    }

                    $image = !empty($var[0]['profile_image']) ? preg_replace('/\s+/', '%20', $var[0]['profile_image']) : '';
                    $logic_image = '';

                    if (!empty($image)) {
                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg;
                            $logic_image = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        }
                    } else {
                        $logic_image = docimg;
                    }

                    $ses_doc_det_array[] = [
                        'session_doctor_id' => $single_doctor,
                        'session_doctor_name' => $var[0]['doctor_name'],
                        'session_doctor_image' => change_img_src($logic_image),
                        'DepartmentName' => $var[0]['DepartmentName'],
                        'profile' => $var[0]['profile'],
                        'subtitle' => $var[0]['subtitle']
                    ];
                }
            }

            // Process content
            $string = !empty($result->video_archive_question_raw) ?
                trim(html_entity_decode($result->video_archive_question_raw), " \t\n\r\0\x0B\xC2\xA0") : '';

            $main_description = !empty($result->video_archive_answer_raw) ?
                str_replace("\n\t", "\n", $result->video_archive_answer_raw) : '';


            $citationFlag = (strpos($result->video_archive_citation, 'id="references"') !== false);
            // Build response array
            $vx = [
                "data_origin" => $dataOriginType,
                "type_id" => (string) $result->type_id,
                "con_type" => $result->type,
                "trending_type" => $result->type,
                "type" => $result->type,
                "is_share" => $result->is_share,
                "is_like" => filter_var($result->is_like, FILTER_VALIDATE_BOOLEAN),
                "is_comment" => filter_var($result->comment_status, FILTER_VALIDATE_BOOLEAN),
                "is_locked" => $key_locked,
                "price" => $result->price,
                "user_content_payment" => get_user_content_status($type_id, 3, $user_master_id),
                "vendor" => $result->vendor,
                "src" => $result->src,
                "privacy_status" => $result->privacy_status,
                "date" => date(' jS F y', strtotime($result->publication_date)),
                "question" => html_entity_decode($string),
                "answer" => html_entity_decode($main_description),
                "video_archive_question" => $result->video_archive_question,
                "video_archive_answer" => $result->video_archive_answer,
                "image" => change_img_src($img),

                "video_archive_citation" => $result->video_archive_citation,
                "isCitationExists" => $citationFlag,

                "video_archive_transcription" => $result->video_archive_transcription,
                "specialities" => $result->specialities_name,
                "specialities_id" => $result->comp_qa_speciality_id,
                "specialities_ids_and_names" => $this->explode_speciality_string($result->specialities_ids_and_names),
                "channel" => $this->getchannel($result->type_id, $user_master_id, 'session'),
                "sponsor_name" => $result->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "play_time" => $result->play_time,
                "comment_count" => $result->count_comment,
                // "rating" => !empty($result->averageRating) ? ($result->averageRating + $result->start_like) : $result->start_like,
                "rating" => !empty($averageRating) ? ($averageRating + $startLike) : $startLike,
                // "myrating" => !empty($result->myrating),
                "myrating" => $hasUserRated,
                "content_rate" => $this->get_content_rating($type_id, $user_master_id),
                // "vault" => !empty($result->vault) ? $result->vault : 0,
                "vault" => $vaultStatus,

                "deeplink" => ($env != 1) ?
                    (!empty($result->gl_deeplink) ? $result->gl_deeplink : 0) :
                    (!empty($result->deeplink) ? $result->deeplink : 0),
                "tags" => !empty($result->comp_qa_tags) ? $result->comp_qa_tags : 0,
                "disclaimer" => disclaimer('knowledge'),
                "env" => $result->env,
                "is_share" => get_a_content_is_share_status($result->type_id, '3'),
                "is_commentable" => filter_var($result->comment_status, FILTER_VALIDATE_BOOLEAN),
                "session_doctor_id" => $result->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
            ];

            // Add campaign data
            $campaign = getContentCampiagn($user_master_id, '', $type_id, 'video_archive');
            $vx['display_banner'] = $campaign['banner_dispaly'];
            $vx['campaign_data'] = $campaign['creative_data'];

        } else {

            $vx = array(
                "data_origin" => "none",
                "error" => "Content not found"
            );
        }



        // Filter by environment for non-internal users
        // if ($user_type != 5) {
        //     if ($env != 1 && $vx['env'] == "IN") {
        //         $vx = [];
        //     }
        // }

        return $vx;
    }

    // Helper method to get content rating
    private function get_content_rating(
        $type_id,
        $user_master_id
    ) {
        $contentRating = "SELECT
                            *
                        FROM
                            `review_comment_rating`
                        WHERE
                            `user_master_id` = {$user_master_id}
                            AND `type` = {$type_id}
                            AND `type_name` LIKE 'video'
                        ORDER BY
                            id DESC
                        LIMIT 1";
        $querycontentRating = $this->db->query($contentRating);

        if (($querycontentRating) && ($querycontentRating->num_rows() > 0)) {
            $contentRate = $querycontentRating->result();
            return $contentRate[0]->rating;
        }

        return 0;
    }
    /**
     * Fetches similar archive video.
     *
     * @param string $type_id
     * @param string $user_master_id
     *
     * @return array
     */
    public function detail_archiveVideo_similar(
        $type_id = '',
        $user_master_id = ''
    ) {
        if (empty($type_id)) {
            return null;
        }
        // echo $type_id;
        // exit;
        $env = get_user_env($user_master_id);
        $cachename = "archieve_detail_similar_" . $type_id . $env;
        if ($this->myredis->exists($cachename)) {
            $result = $this->myredis->get($cachename);
        } else {
            $env_id = get_user_env_id($user_master_id);
            if ($env_id) {
                if ($env_id != 2) {
                    $envStatus = "AND (cTenv.env = 2 OR cTenv.env = " . $env_id . ")";
                } else {
                    $envStatus = "AND cTenv.env = " . $env_id . "";
                }
            } else {
                $envStatus = "";
            }
            $key_locked = get_user_package($user_master_id, 'video_archived');
            // Build optimized query with CTEs
            $sql = "WITH VideoData AS (
                SELECT
                    cm.video_archive_id AS type_id,
                    cm.video_archive_question,
                    cm.video_archive_answer,
                    cm.video_archive_citation,
                    cm.video_archive_transcription,
                    cm.env,
                    cm.is_share,
                    cm.is_like,
                    cm.is_comment,
                    cm.video_archive_question_raw,
                    cm.video_archive_answer_raw,
                    cm.video_archive_file_img,
                    cm.video_archive_file_img_thumbnail,
                    cm.start_like,
                    cm.comment_status,
                    cm.added_on,
                    cm.publication_date,
                    cm.privacy_status,
                    cm.type,
                    cm.vendor,
                    cm.src,
                    cm.deeplink,
                    cm.gl_deeplink,
                    cm.video_archive_tags,
                    cm.video_archive_speciality_id,
                    cm.video_archive_session_id,
                    cln.client_name,
                    cln.client_logo,
                    cTenv.price,
                    uTpyCont.status AS user_contnet_payment_status,
                    rtmy.rating AS myrating,
                    kv.status AS vault
                FROM 
                    knwlg_video_archive AS cm
                LEFT JOIN 
                    client_master AS cln ON cln.client_master_id = cm.client_id
                LEFT JOIN 
                    knwlg_rating AS rtmy ON rtmy.post_id = cm.video_archive_id AND rtmy.post_type = 'video_archive' 
                    AND rtmy.rating != 0 AND rtmy.user_master_id = " . $user_master_id . "
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = cm.video_archive_id AND cTenv.type = 3
                LEFT JOIN 
                    payment_user_to_content AS uTpyCont ON uTpyCont.type_id = cm.video_archive_id 
                    AND uTpyCont.type = 3 AND uTpyCont.user_master_id = " . $user_master_id . "
                LEFT JOIN 
                    knwlg_vault AS kv ON kv.post_id = cm.video_archive_id AND kv.type_text = 'video_archive' 
                    AND kv.user_id = " . $user_master_id . "
                WHERE 
                    cm.status = 3
                    " . $envStatus . "
                    AND cm.publication_date <= CURDATE()
                    AND cm.video_archive_id = " . $type_id . "
            ),
            SpecialitiesData AS (
                SELECT 
                    cmTs.video_archive_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                    GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                FROM 
                    video_archive_to_specialities AS cmTs
                JOIN 
                    master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                WHERE 
                    cmTs.video_archive_id = " . $type_id . "
                GROUP BY 
                    cmTs.video_archive_id
            ),
            SponsorData AS (
                SELECT 
                    cmTspon.video_archive_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                FROM 
                    video_archive_to_sponsor AS cmTspon
                LEFT JOIN 
                    client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                WHERE 
                    cmTspon.video_archive_id = " . $type_id . "
                GROUP BY 
                    cmTspon.video_archive_id
            ),
            SessionDoctorData AS (
                SELECT 
                    ks.session_id,
                    ks.session_doctor_id
                FROM 
                    knwlg_sessions_V1 AS ks
                WHERE 
                    ks.session_id IN (SELECT video_archive_session_id FROM VideoData)
            ),
            TrackingData AS (
                SELECT 
                    kvtd.content_id,
                    kvtd.play_time
                FROM 
                    knwlg_video_tracking_data AS kvtd
                WHERE 
                    kvtd.content_id = " . $type_id . " 
                    AND kvtd.content_type = 'video_archive' 
                    AND kvtd.user_master_id = " . $user_master_id . "
            ),
            MetricsData AS (
                SELECT 
                    " . $type_id . " AS video_archive_id,
                    (SELECT COUNT(rt.rating) FROM knwlg_rating rt 
                     WHERE rt.post_id = " . $type_id . " AND rt.post_type = 'video_archive' AND rt.rating != 0) AS averageRating,
                    (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm 
                     WHERE kcm.type_id = " . $type_id . " AND kcm.type = 'video_archive' AND kcm.comment_approve_status = 1) AS count_comment
            )
            SELECT 
                vd.*,
                sd.specialities_name,
                sd.specialities_ids_and_names,
                sp.sponsor,
                sp.sponsor_logo,
                sdd.session_doctor_id,
                td.play_time,
                md.averageRating,
                md.count_comment
            FROM 
                VideoData vd
            LEFT JOIN 
                SpecialitiesData sd ON vd.type_id = sd.video_archive_id
            LEFT JOIN 
                SponsorData sp ON vd.type_id = sp.video_archive_id
            LEFT JOIN 
                SessionDoctorData sdd ON vd.video_archive_session_id = sdd.session_id
            LEFT JOIN 
                TrackingData td ON vd.type_id = td.content_id
            LEFT JOIN 
                MetricsData md ON vd.type_id = md.video_archive_id";
            $query = $this->db->query($sql);
            $result = $query->row();
            // Cache the result if it's valid
            if ($result && !empty($result->type_id)) {
                $this->myredis->set($cachename, $result);
            }
            // Return early if no result found
            if (empty($result) || empty($result->type_id)) {
                return null;
            }
        }
        // Process image
        $img = !empty($result->video_archive_file_img) ? $result->video_archive_file_img : '';
        // Process sponsor data
        $allsponsor = array();
        $sponsorLogo = '';
        if (!empty($result->sponsor) && !empty($result->sponsor_logo)) {
            $sponsorname = explode(",", $result->sponsor);
            $sponsorLogoArry = explode(",", $result->sponsor_logo);
            $sponsorLogomix = array();
            for ($sp = 0; $sp < count($sponsorLogoArry); $sp++) {
                if (!empty($sponsorLogoArry[$sp])) {
                    $sponsorLogomix[] = $sponsorLogoArry[$sp];
                    $allsponsor[] = array(
                        'name' => isset($sponsorname[$sp]) ? $sponsorname[$sp] : '',
                        "logo" => $sponsorLogoArry[$sp]
                    );
                }
            }
            $sponsorLogo = !empty($sponsorLogomix) ? implode(",", $sponsorLogomix) : '';
        }
        // Process session doctor data
        $ses_doc_det_array = array();
        if (!empty($result->session_doctor_id)) {
            $session_doc_array = explode(",", $result->session_doctor_id);
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                if (empty($single_doctor)) {
                    continue;
                }
                $var = session_doc_detail($single_doctor);
                if (empty($var) || empty($var[0])) {
                    continue;
                }
                $image = !empty($var[0]['profile_image']) ? preg_replace('/\s+/', '%20', $var[0]['profile_image']) : '';
                $logic_image = '';
                if (!empty($image)) {
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }
                } else {
                    $logic_image = docimg;
                }
                $ses_doc_det_array[$inc_pp] = array(
                    'session_doctor_id' => $single_doctor,
                    'session_doctor_name' => $var[0]['doctor_name'],
                    'session_doctor_image' => change_img_src($logic_image),
                    'DepartmentName' => $var[0]['DepartmentName'],
                    'profile' => $var[0]['profile'],
                    'subtitle' => $var[0]['subtitle']
                );
                $inc_pp++;
            }
        }
        // Process content
        $string = !empty($result->video_archive_question_raw) ?
            trim(html_entity_decode($result->video_archive_question_raw), " \t\n\r\0\x0B\xC2\xA0") : '';
        $main_description = !empty($result->video_archive_answer_raw) ?
            str_replace("\n\t", "\n", $result->video_archive_answer_raw) : '';
        // Build response array
        $vx = array(
            "type_id" => (string) $result->type_id,
            "con_type" => $result->type,
            "trending_type" => $result->type,
            "type" => $result->type,
            "is_share" => $result->is_share,
            "is_like" => filter_var($result->is_like, FILTER_VALIDATE_BOOLEAN),
            "is_comment" => filter_var($result->comment_status, FILTER_VALIDATE_BOOLEAN),
            "is_locked" => $key_locked,
            "price" => $result->price,
            "user_content_payment" => get_user_content_status($type_id, 3, $user_master_id),
            "vendor" => $result->vendor,
            "src" => $result->src,
            "privacy_status" => $result->privacy_status,
            "date" => date(' jS F y', strtotime($result->publication_date)),
            "question" => html_entity_decode($string),
            "answer" => html_entity_decode($main_description),
            "video_archive_question" => $result->video_archive_question,
            "video_archive_answer" => $result->video_archive_answer,
            "image" => change_img_src($img),
            "video_archive_citation" => $result->video_archive_citation,
            "video_archive_transcription" => $result->video_archive_transcription,
            "specialities" => $result->specialities_name,
            "specialities_id" => $result->video_archive_speciality_id,
            "specialities_ids_and_names" => $this->explode_speciality_string($result->specialities_ids_and_names),
            "channel" => $this->getchannel($result->type_id, $user_master_id, 'session'),
            "sponsor_name" => $result->sponsor,
            "sponsor_logo" => change_img_src($sponsorLogo),
            "all_sponsor" => $allsponsor,
            "play_time" => $result->play_time,
            "comment_count" => $result->count_comment ?? 0,
            "rating" => (!empty($result->averageRating)) ? ($result->averageRating + $result->start_like) : $result->start_like,
            "myrating" => !empty($result->myrating),
            "content_rate" => $this->rate($type_id, $user_master_id, 'video'),
            "vault" => $result->vault ?? 0,
            "deeplink" => ($env == "GL") ?
                ((!empty($result->gl_deeplink)) ? $result->gl_deeplink : 0) :
                ((!empty($result->deeplink)) ? $result->deeplink : 0),
            "tags" => (!empty($result->video_archive_tags)) ? $result->video_archive_tags : 0,
            "disclaimer" => disclaimer('knowledge'),
            "env" => $result->env,
            "is_share" => get_a_content_is_share_status($result->type_id, '3'),
            "is_commentable" => filter_var($result->comment_status, FILTER_VALIDATE_BOOLEAN),
            "session_doctor_id" => $result->session_doctor_id,
            "session_doctor_entities" => $ses_doc_det_array,
        );
        return $vx;
    }
    public function explode_speciality_string($string)
    {
        $final = array();
        if (!empty($string)) {
            $temp_sp_array = explode(",", $string);
            foreach ($temp_sp_array as $ky => $sp_id_name) {
                $sp_id_name_array = explode("#", $sp_id_name);
                $final[$ky] = array();
                $final[$ky]['id'] = $sp_id_name_array[0];
                $final[$ky]['name'] = $sp_id_name_array[1];
            }
        }
        return $final;
    }
    public function getchannel(
        $type_id,
        $user_master_id,
        $type
    ) {
        //$chid = '';
        switch ($type) {
            case 'session':
                $this->db->select('ctva.channel_master_id,cm.title,cm.follower_count,cm.logo_type,cm.logo,cm.privacy_status,cm.short_description,cm.deeplink,cm.is_followers,cm.is_activities,cm.is_share,cTus.status as followed_status');
                $this->db->from('channel_to_video_archive as ctva');
                $this->db->join('channel_master as cm', 'cm.channel_master_id = ctva.channel_master_id');
                $this->db->join('channel_to_user  as cTus', '(cTus.channel_master_id = cm.channel_master_id and user_master_id = "' . $user_master_id . '"   )');
                $this->db->where('ctva.video_archive_id', $type_id);
                $this->db->limit(1);
                $query = $this->db->get();
                if ($query->num_rows() > 0) {
                    $result = $query->result();
                    $channel = array(
                        'channel_id' => $result[0]->channel_master_id,
                        'title' => $result[0]->title,
                        'is_followers' => filter_var($result[0]->is_followers, FILTER_VALIDATE_BOOLEAN), //$result[0]->is_followers,
                        'is_activities' => filter_var($result[0]->is_activities, FILTER_VALIDATE_BOOLEAN), //$result[0]->is_activities,
                        'is_share' => filter_var($result[0]->is_share, FILTER_VALIDATE_BOOLEAN), //$result[0]->is_share,
                        'logo' => change_img_src($result[0]->logo),
                        'logo_type' => $result[0]->logo_type,
                        'description' => $result[0]->short_description,
                        'privacy_status' => $result[0]->privacy_status,
                        'followed_status' => $result[0]->followed_status,
                        'deeplink' => $result[0]->deeplink,
                    );
                } else {
                    $channel = null; //array();
                }
                if (!empty($channel)) {
                    $channelId = $channel['channel_id'];
                    $channel['speciality'] = $this->specialitiesByChannelId($channelId);
                    $channel['follower_count'] = $this->totalFollowersCountByChannelId($channelId) + $result[0]->follower_count;
                    $channel['total_activity'] = $this->totalSessionActivityCountByChannelId($channelId, $user_master_id);
                }
                break;
            case 'comp':
                $this->db->select('ctc.channel_master_id,cm.title,cm.logo,cm.logo_type,cm.privacy_status,cm.follower_count,cm.short_description,cm.deeplink,cm.is_followers,cm.is_share,cm.is_activities,cTus.status as followed_status');
                $this->db->from('channel_to_compendium as ctc');
                $this->db->join('channel_master as cm', 'cm.channel_master_id = ctc.channel_master_id');
                $this->db->join('channel_to_user  as cTus', '(cTus.channel_master_id = cm.channel_master_id and user_master_id = "' . $user_master_id . '"   )');
                $this->db->where('ctc.comp_qa_id', $type_id);
                $this->db->limit(1);
                $query = $this->db->get();
                if ($query->num_rows() > 0) {
                    $result = $query->result();
                    $channel = array(
                        'channel_id' => $result[0]->channel_master_id,
                        'title' => $result[0]->title,
                        'is_followers' => filter_var($result[0]->is_followers, FILTER_VALIDATE_BOOLEAN), //$result[0]->is_followers,
                        'is_activities' => filter_var($result[0]->is_activities, FILTER_VALIDATE_BOOLEAN), //$result[0]->is_activities,
                        'is_share' => filter_var($result[0]->is_share, FILTER_VALIDATE_BOOLEAN), //$result[0]->is_share,
                        'logo' => change_img_src($result[0]->logo),
                        'logo_type' => $result[0]->logo_type,
                        'description' => $result[0]->short_description,
                        'privacy_status' => $result[0]->privacy_status,
                        'followed_status' => $result[0]->followed_status,
                        'deeplink' => $result[0]->deeplink,
                    );
                } else {
                    $channel = null; ///array();
                }
                if (!empty($channel)) {
                    $channelId = $channel['channel_id'];
                    $channel['speciality'] = $this->specialitiesByChannelId($channelId);
                    $channel['follower_count'] = $this->totalFollowersCountByChannelId($channelId) + $result[0]->follower_count;
                    $channel['total_activity'] = $this->totalActivityCountByChannelId($channelId, $user_master_id);
                }
                break;
        }
        return $channel;
    }
    /**
     * Fetches the top commented compendiums.
     *
     * @param  string $user_master_id    User ID
     * @param  string $client_ids        Client IDs
     * @param  string $group_ids         Group IDs
     * @param  string $limitFrom         Limit from
     * @param  string $limitTo           Limit to
     * @param  string $val               Value
     * @param  string $type              Type
     * @param  string $spIds             Specialities IDs
     *
     * @return array
     */
    public function topcomment(
        $user_master_id = '',
        $client_ids = '',
        $group_ids = '',
        $limitFrom = '',
        $limitTo = '',
        $val = '',
        $type = '',
        $spIds = ''
    ) {
        if ($limitFrom != '' && $limitTo != '') {
            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {
            $limit = "";
        }

        // Build specialities filter
        $specialities = ($spIds != '') ? "AND cmTs.specialities_id IN (" . $spIds . ")" : "";

        if (!empty($user_master_id)) {
            // Get user environment
            $env = get_user_env_id($user_master_id);

            // Set environment filter
            if ($env) {
                if ($env != 2) {
                    $envStatus = "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")";
                } else {
                    $envStatus = "AND cTenv.env = " . $env;
                }
            } else {
                $envStatus = "";
            }

            // Get user package
            $key_locked = get_user_package($user_master_id, 'comp');

            // Set content type filter
            $typeSql = ($type == 1) ? "AND cm.type = 'video'" : "";

            // Use CTEs for better performance
            $sql = "WITH TopCommentCompendiums AS (
                    SELECT 
                        cm.comp_qa_id,
                        (SELECT COUNT(kcm.knwlg_comment_id) 
                         FROM knwlg_comment kcm 
                         WHERE kcm.type_id = cm.comp_qa_id AND kcm.type = 'comp') AS comment_count
                    FROM 
                        knwlg_compendium_V1 AS cm
                    JOIN 
                        compendium_to_specialities AS cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
                    LEFT JOIN 
                        content_to_env AS cTenv ON cTenv.type_id = cm.comp_qa_id AND cTenv.type = 1
                    WHERE 
                        cm.status = 3
                        AND cm.is_draft = 0
                        {$specialities}
                        AND cm.privacy_status = 0
                        {$typeSql}
                        {$envStatus}
                    GROUP BY 
                        cm.comp_qa_id
                    ORDER BY 
                        comment_count DESC
                    {$limit}
                ),
                SpecialitiesAgg AS (
                    SELECT 
                        cmTs.comp_qa_id,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name
                    FROM 
                        TopCommentCompendiums tcc
                    JOIN 
                        compendium_to_specialities AS cmTs ON cmTs.comp_qa_id = tcc.comp_qa_id
                    JOIN 
                        master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                    GROUP BY 
                        cmTs.comp_qa_id
                )
                SELECT 
                    cm.comp_qa_id AS type_id,
                    cm.comp_qa_question,
                    cm.comp_qa_answer,
                    cm.type,
                    cm.is_share,
                    cm.comp_qa_file_img,
                    cm.publication_date AS publish_date,
                    cln.client_name,
                    cln.client_logo,
                    cm.deeplink,
                    cm.gl_deeplink,
                    cTenv.price,
                    uTpyCont.status AS user_contnet_payment_status,
                    sa.specialities_name,
                    tcc.comment_count AS count_comment
                FROM 
                    TopCommentCompendiums tcc
                JOIN 
                    knwlg_compendium_V1 AS cm ON cm.comp_qa_id = tcc.comp_qa_id
                JOIN 
                    client_master AS cln ON cln.client_master_id = cm.client_id
                LEFT JOIN 
                    SpecialitiesAgg sa ON sa.comp_qa_id = cm.comp_qa_id
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = cm.comp_qa_id AND cTenv.type = 1
                LEFT JOIN 
                    payment_user_to_content AS uTpyCont ON uTpyCont.type_id = cm.comp_qa_id AND uTpyCont.type = 1 AND uTpyCont.user_master_id = ?
                ORDER BY 
                    tcc.comment_count DESC";

            $query = $this->db->query($sql, array($user_master_id));
            $i = 1;
            $vx = array();
            if (($query) && ($query->num_rows())) {
                $result = $query->result();
                foreach ($result as $val) {
                    $vx[] = array(
                        "slno" => $i,
                        "con_type" => $val->type,
                        "is_share" => $val->is_share,
                        "is_locked" => $key_locked,
                        "price" => $val->price,
                        "user_content_payment" => $val->user_contnet_payment_status,
                        "type_id" => $val->type_id,
                        "type" => 'comp',
                        "date" => date(' jS F y', strtotime($val->publish_date)),
                        "question" => $val->comp_qa_question,
                        "image" => change_img_src($val->comp_qa_file_img),
                        "color" => ($val->color != '') ? $val->color : '#918c91',
                        "answer" => $val->comp_qa_answer,
                        "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                        "client_name" => $val->client_name,
                        "client_logo" => change_img_src('' . $val->client_logo),
                        "comment_count" => $val->count_comment,
                        "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink
                    );
                    $i++;
                }
            }
            return $vx;
        }
    }
    /**
     * Get top rated compendiums
     *
     * @param string $user_master_id User ID
     * @param string $client_ids Client IDs
     * @param string $group_ids Group IDs
     * @param string $limitFrom Pagination start
     * @param string $limitTo Pagination limit
     * @param string $val Search value
     * @param string $type Content type
     * @param string $spIds Speciality IDs
     * @return array Formatted results
     */
    public function toprated(
        $user_master_id,
        $client_ids,
        $group_ids,
        $limitFrom,
        $limitTo,
        $val,
        $type,
        $spIds
    ) {
        // Set pagination limit
        if ($limitFrom != '' && $limitTo != '') {
            $limit = "LIMIT " . $limitFrom . ", " . $limitTo;
        } else {
            $limit = "LIMIT 0,5";
        }

        // Build client filter
        // $client_list = '';
        // if ($client_ids) {
        //     $client_list = ' AND (' . implode(' OR ', array_map(function ($x) {
        //         return "FIND_IN_SET('$x', cm.client_id)";
        //     }, explode(',', $client_ids))) . ')';
        // }

        // Build speciality filter
        $speciality_filter = '';
        if ($spIds != '') {
            $speciality_filter = "AND cmTs.specialities_id IN (" . $spIds . ")";
        }

        if (!empty($user_master_id)) {
            // Get user environment
            $env = get_user_env($user_master_id);

            // Set environment filter
            if ($env) {
                if ($env != 'GL') {
                    $envStatus = "AND (cm.env = 'GL' OR cm.env = '" . $env . "')";
                } else {
                    $envStatus = "AND cm.env = '" . $env . "'";
                }
            } else {
                $envStatus = "";
            }

            // Set content type filter
            $typeSql = ($type == 1) ? "AND cm.type = 'video'" : "";
            //$typedashboardstatus = ($type != 1) ? "AND cm.display_in_dashboard = 1" : "";

            // Check cache
            $cachename = "toprated_compendium_" . $type . $limitFrom . $limitTo . $spIds;
            if ($this->myredis->exists($cachename)) {
                $result = $this->myredis->get($cachename);
                $cached_status = 1;
            } else {
                // Use CTEs for better performance
                $sql = "WITH TopRatedCompendiums AS (
                        SELECT 
                            cm.comp_qa_id,
                            (SELECT COUNT(rt.rating) 
                             FROM knwlg_rating rt 
                             WHERE rt.post_id = cm.comp_qa_id AND rt.post_type = 'comp') AS rating_count
                        FROM 
                            knwlg_compendium_V1 AS cm
                        JOIN 
                            compendium_to_specialities AS cmTs ON cmTs.comp_qa_id = cm.comp_qa_id
                        WHERE 
                            cm.status = 3
                            AND cm.is_draft = 0
                            AND cm.publication_date <= CURDATE()
                            {$speciality_filter}
                            AND cm.privacy_status = 0
                            {$typeSql}
                            {$envStatus}
                        GROUP BY 
                            cm.comp_qa_id
                        ORDER BY 
                            rating_count DESC
                        {$limit}
                    ),
                    SpecialitiesAgg AS (
                        SELECT 
                            cmTs.comp_qa_id,
                            GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                            GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                        FROM 
                            TopRatedCompendiums trc
                        JOIN 
                            compendium_to_specialities AS cmTs ON cmTs.comp_qa_id = trc.comp_qa_id
                        JOIN 
                            master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                        GROUP BY 
                            cmTs.comp_qa_id
                    ),
                    SponsorsAgg AS (
                        SELECT 
                            cmTspon.comp_qa_id,
                            GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                            GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                        FROM 
                            TopRatedCompendiums trc
                        LEFT JOIN 
                            compendium_to_sponsor AS cmTspon ON cmTspon.comp_qa_id = trc.comp_qa_id
                        LEFT JOIN 
                            client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                        GROUP BY 
                            cmTspon.comp_qa_id
                    ),
                    CommentsCount AS (
                        SELECT 
                            trc.comp_qa_id,
                            COUNT(kcm.knwlg_comment_id) AS count_comment
                        FROM 
                            TopRatedCompendiums trc
                        LEFT JOIN 
                            knwlg_comment kcm ON kcm.type_id = trc.comp_qa_id AND kcm.type = 'comp'
                        GROUP BY 
                            trc.comp_qa_id
                    )
                    SELECT 
                        cm.comp_qa_id AS type_id,
                        cm.comp_qa_question,
                        cm.is_share,
                        cm.comp_qa_answer,
                        cm.comp_qa_answer_raw AS description,
                        cm.comp_qa_question_raw AS title,
                        cm.comp_qa_file_img,
                        cm.comp_qa_file_img_thumbnail,
                        cm.added_on,
                        cm.publication_date AS publish_date,
                        cln.client_name,
                        cln.client_logo,
                        cm.type,
                        cm.vendor,
                        cm.src,
                        cm.deeplink,
                        cm.gl_deeplink,
                        cm.color,
                        sa.specialities_name,
                        sa.specialities_ids_and_names,
                        sp.sponsor,
                        sp.sponsor_logo,
                        cm.comp_qa_speciality_id,
                        trc.rating_count AS averageRating,
                        rtmy.rating AS myrating,
                        cc.count_comment,
                        kv.status AS vault
                    FROM 
                        TopRatedCompendiums trc
                    JOIN 
                        knwlg_compendium_V1 AS cm ON cm.comp_qa_id = trc.comp_qa_id
                    JOIN 
                        client_master AS cln ON cln.client_master_id = cm.client_id
                    LEFT JOIN 
                        SpecialitiesAgg sa ON sa.comp_qa_id = cm.comp_qa_id
                    LEFT JOIN 
                        SponsorsAgg sp ON sp.comp_qa_id = cm.comp_qa_id
                    LEFT JOIN 
                        CommentsCount cc ON cc.comp_qa_id = cm.comp_qa_id
                    LEFT JOIN 
                        knwlg_rating AS rtmy ON rtmy.post_id = cm.comp_qa_id AND rtmy.post_type = 'comp' AND rtmy.rating != 0 AND rtmy.user_master_id = ?
                    LEFT JOIN 
                        knwlg_vault AS kv ON kv.post_id = cm.comp_qa_id AND kv.type_text = 'comp' AND kv.user_id = ?
                    ORDER BY 
                        trc.rating_count DESC";

                $query = $this->db->query($sql, array($user_master_id, $user_master_id));
                $result = $query->result();
                $cached_status = 0;
                $this->myredis->set($cachename, $result, 60 * 60 * 24);
            }

            $i = 1;
            $vx = array();
            foreach ($result as $val) {
                // Process image
                if ($val->comp_qa_file_img) {
                    $logic_image = $val->comp_qa_file_img;
                } else {
                    $logic_image = docimg;
                }

                if ($val->comp_qa_file_img_thumbnail) {
                    $logic_image_thumbnail = $val->comp_qa_file_img_thumbnail;
                } else {
                    $logic_image_thumbnail = docimg;
                }

                // Process sponsor data
                $sponsorLogoArry = explode(",", $val->sponsor_logo);
                $sponsorLogomix = [];

                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {
                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = '' . $val->sponsor_logo;
                    }
                }

                $sponsorLogo = implode(",", (array)$sponsorLogomix);

                $vx[] = array(
                    "slno" => $i,
                    "cached_status" => $cached_status,
                    "con_type" => $val->type,
                    "vendor" => $val->vendor,
                    "src" => $val->src,
                    "type_id" => $val->type_id,
                    "is_share" => $val->is_share,
                    "type" => 'comp',
                    "date" => date(' jS F y', strtotime($val->publish_date)),
                    "question" => html_entity_decode(strip_tags($val->title)),
                    "image" => change_img_src($logic_image),
                    "color" => ($val->color != '') ? $val->color : '#918c91',
                    "answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src('' . $val->client_logo),
                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),
                    "comment_count" => $val->count_comment,
                    "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                    "myrating" => ($val->myrating != '') ? true : false,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                );
                $i++;
            }
            return $vx;
        }
    }
    public function review_insert($array)
    {
        $this->insertdb = $this->load->database('insert', true);
        $this->insertdb->insert('review_comment_rating', $array);
        return true;
    }
    public function rate($type_id, $user_master_id, $type)
    {
        $response = 0;
        $this->db->select('rating');
        $this->db->from('review_comment_rating');
        $this->db->where(array('type' => $type_id, 'type_name' => $type, 'user_master_id' => $user_master_id));
        $this->db->order_by('id', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $res = $query->result();
            $response = $res[0]->rating;
        }
        return $response;
    }
    public function coursedata(
        $envStatus,
        $client_list_kcap,
        $limitTo,
        $limitFrom,
        $searchQuery,
        $user_master_id
    ) {
        $vx = array();
        $env_arr = array();
        $res = array();
        $env = get_user_env_id($user_master_id);
        if ($env) {
            if ($env != 2) {
                $env_arr = array(2, $env);
            } else {
                $env_arr = array($env);
            }
        }
        $key_locked = get_user_package($user_master_id, 'training');
        if ($key_locked == '') {
            return null;
        }
        $this->db->select("
        tm.*,
        cTenv.price,
        uTpyCont.status as user_contnet_payment_status,
        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id)) AS master_spec_id,
        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names,
        MAX(ms.rank) AS maxrank,
        GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
        GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) AS session_doctor_id,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo,
        clintspon.client_name,
        clintspon.client_logo,
        (SELECT COUNT(user_master_id) FROM `payment_user_to_content` WHERE `type_id` = tm.id) AS `active_users`,
        COUNT(rt.rating) AS averageRating,
        COUNT(DISTINCT training_module.id) AS count_module,
        kv.status as vault,
        kv.timestamp as saved_date");
        $this->db->from('training_master as tm');
        $this->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
        $this->db->join('client_master as clintspon', 'ts.sponsor_id = clintspon.client_master_id', 'left');
        $this->db->join('training_to_speciality tts', 'tts.training_id = tm.id', 'left');
        $this->db->join('master_specialities_V1 as ms', 'ms.master_specialities_id = tts.specialities_id', 'left');
        $this->db->join('content_to_env as cTenv', 'cTenv.type_id = tm.id and cTenv.type = 4', 'left');
        $this->db->join('payment_user_to_content as uTpyCont', 'uTpyCont.type_id = tm.id and  uTpyCont.type = 4 and uTpyCont.user_master_id = ' . $user_master_id, 'left');
        $this->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
        $this->db->join('knwlg_rating as rt', "rt.post_id = tm.id AND rt.post_type='training'", 'left');
        $this->db->join('knwlg_vault as kv', "(kv.post_id = tm.id and  kv.type_text='training' and  kv.user_id = " . $user_master_id . " and kv.status =1)");
        $this->db->join('training_module', 'training_module.training_id = tm.id AND training_module.status = 3', 'left');
        $this->db->where('tm.status', 3);
        $this->db->where('tm.privacy_status', 0);
        $this->db->where_in('cTenv.env', $env_arr);
        //$this->db->group_by('tm.id');
        $this->db->group_by(array(
            'tm.id',
            'tm.title',
            'tm.description',
            'cTenv.price',
            'uTpyCont.status',
            'clintspon.client_name',
            'clintspon.client_logo',
            'kv.status',
            'kv.timestamp'
        ));
        $this->db->order_by('kv.timestamp', 'desc');
        if ($limitFrom != '' and $limitTo != '') {
            $this->db->limit($limitTo, $limitFrom);
        }
        $query = $this->db->get();
        if (($query) && ($query->num_rows())) {
            $res = $query->result();
        }
        $i = 1;
        foreach ($res as $key => $val) {
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] =  change_img_src($valueSponor);
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $currentdatetime = date('Y-m-d H:i:s');
            if (isset($val->start_date) && $currentdatetime < $val->start_date && $val->max_participants > 0) {
                if ($val->max_participants > $val->active_users) {
                    $is_registrable = true;
                } else {
                    $is_registrable = false;
                }
            } elseif (isset($val->start_date) && $currentdatetime > $val->start_date) {
                $is_registrable = false;
            } elseif (!isset($val->start_date) && $val->max_participants == 0) {
                $is_registrable = true;
            } elseif (!isset($val->start_date) && $val->max_participants > 0) {
                $is_registrable = false;
            } else {
                $is_registrable = true;
            }
            $temp = array(
                "slno" => $i,
                "id" => $val->id,
                "url" => $val->url,
                "type" => "training",
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "saved_date" => $val->saved_date,
                "is_share" => $val->is_share,
                "title" => html_entity_decode(strip_tags($val->title)),
                "image" => change_img_src($val->preview_image),
                "featured_video" => $val->featured_video,
                "max_participants" => $val->max_participants,
                "start_date" => $val->start_date,
                "active_users" => $val->active_users,
                "is_registrable" => $is_registrable,
                "color" => ($val->color != '') ? $val->color : '#eb34e5',
                "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "module_Count" => $val->count_module, //$this->count_module($val->id),
                "specialities" => $this->explode_speciality_string($val->specialities_ids_and_names, 0), //$this->specialityname($val->master_spec_id), #($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names, 1),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                "client_name" => $val->client_name,
                "client_logo" => change_img_src($val->client_logo),
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "duration" => $val->duration,
                "is_locked" => $key_locked,
                "price" => $val->price,
                "user_content_payment" => get_user_content_status($val->id, 4, $user_master_id),
                "is_completed" => $completestatus,
                "is_certificate" => ($val->cert_template_id != '') ? true : false,
                "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
            );
            $vx[] = $temp;
            $i++;
        }
        return $vx;
    }
    public function livestatus($id)
    {
        $currentdatetime = date('Y-m-d H:i:s');
        $status = 0;
        if ($id != '') {
            $this->db->select('tmc.id,tmc.type_id');
            $this->db->from('training_module_content as tmc');
            $this->db->join('knwlg_sessions_V1 as ks', 'ks.session_id = tmc.type_id');
            $this->db->where(array('tmc.type' => 'session', 'tmc.training_id' => $id, 'ks.session_status' => 2, "tmc.status" => 3));
            $this->db->where("'" . $currentdatetime . "' BETWEEN ks.start_datetime and ks.end_datetime");
            $query = $this->db->get();
            if (($query) && ($query->num_rows() > 0)) {
                $status = 1;
            } else {
                $this->db->select('id');
                $this->db->from('training_module_content');
                $this->db->where(array('type' => 'live_video', 'training_id' => $id));
                $this->db->where("'" . $currentdatetime . "' BETWEEN start_datetime and end_datetime");
                $querylivevideo = $this->db->get();
                if (($querylivevideo) && ($querylivevideo->num_rows() > 0)) {
                    $status = 1;
                }
            }
        }
        return $status;
    }
    public function sessiondata(
        $envStatus,
        $client_list_kcap,
        $limit,
        $searchQuery,
        $user_master_id
    ) {
        $childsessionids = "";
        $specialitiesQy = "";
        $getchildsession = $this->get_all_childsession();
        if (!empty($getchildsession)) {
            $cids = implode(",", (array)$getchildsession['sessions']);
            $childsessionids = " and cm.session_id NOT IN (" . $cids . ")";
        }
        $key_locked = get_user_package($user_master_id, 'session');
        $sql = "SELECT
                cm.*,
                cln.client_name,
                cln.client_logo,
                cln.client_logo as category_logo,
                msct.category_name,
                msct.category_logo,
                kv.status as valutstatus,
                kv.timestamp as saved_date,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
                GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
                GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR '----') as  speciality,
                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as  profile,
                GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as  profile_images,
                stci.cover_image1,stci.cover_image2,stci.cover_image3,stci.cover_image4,stci.cover_image5,
                (cm.total_buffer + cm.total_seats) as tot_seat
                FROM knwlg_sessions_V1 as cm
                LEFT JOIN session_to_specialities as sesTs ON (sesTs.session_id = cm.session_id)
                LEFT JOIN master_specialities_V1 as ms ON (ms.master_specialities_id = sesTs.specialities_id)
                LEFT JOIN client_master as cln ON cln.client_master_id = cm.client_id
                left JOIN session_to_cover_image as stci ON stci.session_id = cm.session_id
                LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = cm.session_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
                JOIN knwlg_vault as kv on (kv.type_text = 'session' and kv.post_id = cm.session_id and kv.user_id = " . $user_master_id . " and kv.status =1)
                LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.session_id and  cTenv.type = 2
                LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.session_id and  uTpyCont.type = 2 and 	uTpyCont.user_master_id = " . $user_master_id . "
                LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = cm.category_id
                LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, cm.session_doctor_id) > 0
                WHERE
                 cm.status=3 " . $childsessionids . " " . $envStatus . $specialitiesQy . "
                and cm.privacy_status = 0";
        $sql .= " GROUP BY
                    cm.session_id,
                    cln.client_name,
                    cln.client_logo,
                    msct.category_name,
                    msct.category_logo,
                    kv.status,
                    kv.timestamp,
                    cTenv.price,
                    uTpyCont.status,
                    stci.cover_image1,
                    stci.cover_image2,
                    stci.cover_image3,
                    stci.cover_image4,
                    stci.cover_image5
                 order by kv.timestamp DESC ";
        if ($limit != '') {
            $sql .= $limit;
        }
        $vx = array();
        $query = $this->db->query($sql);
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            $i = 0;
            foreach ($result as $val) {
                if (@getimagesize(base_url() . "uploads/docimg/" . $val->profile_image)) {
                    $logic_image = '' . $val->profile_image;
                } else {
                    $logic_image = base_url() . "uploads/docimg/MConsult.png";
                }
                $start_time = $val->start_datetime;
                $start_time = date("g:i A", strtotime($start_time));
                $ses_doc_det_array = array();
                if ($val->session_doctor_id != '') {
                    $session_doc_array = explode(",", $val->session_doctor_id);
                } else {
                    $session_doc_array = explode(",", $getchildsession['session_doctors'][$val->session_id]);
                }
                if (!empty($session_doc_array)) {
                    $inc_pp = 0;
                    $store_total_doctors[$val->session_id] = 0;
                    foreach ($session_doc_array as $single_doctor) {
                        $var = session_doc_detail($single_doctor);
                        $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                        $logic_image = $image;
                        $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                        $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
                        $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                        $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                        $inc_pp++;
                        $store_total_doctors[$val->session_id] = $inc_pp;
                    }
                }
                $k = array_keys((array)$getchildsession['sessioncount']);
                if (in_array($val->session_id, $k)) {
                    $total_multiday_session = $getchildsession['sessioncount'][$val->session_id];
                } else {
                    $total_multiday_session = 0;
                }
                $keyvalue_1 = array_keys((array)$getchildsession['doctorcount']);
                if (in_array($val->session_id, $keyvalue_1)) {
                    $total_doctors = $getchildsession['doctorcount'][$val->session_id];
                } else {
                    $total_doctors = $store_total_doctors[$val->session_id];
                }
                $datetime1 = new DateTime(date('Y-m-d H:i:s'));
                $datetime2 = new DateTime($val->start_datetime);
                $difference = $datetime1->diff($datetime2);
                $end_time = $val->end_datetime;
                $end_time = date("g:i A", strtotime($end_time));
                $sponsorSESLogoArry = explode(",", $val->sponsor_logo);
                if (count($sponsorSESLogoArry) > 0) {
                    foreach ($sponsorSESLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorSESLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {
                    if ($val->sponsor_logoSES) {
                        $sponsorSESLogomix[] = '' . $val->sponsor_logoSES;
                    }
                }
                $sponsorLogoSES = implode(",", (array)$sponsorSESLogomix);
                unset($sponsorSESLogomix);
                unset($sponsorSESLogoArry);
                $coverImg = change_img_src(base_url() . "uploads/sessionBgDefault.jpeg");
                $vx[] = array(
                    "slno" => $i,
                    "trending_type" => "session",
                    "type_id" => $val->session_id,
                    "session_id" => $val->session_id,
                    "type" => 'session',
                    "total_session" => $total_multiday_session,
                    "total_doctors" => $total_doctors,
                    "total_days" => $difference->d,
                    "doctor_name" => $val->doctor_name,
                    "cover_image" => ($val->cover_image != '') ? change_img_src($val->cover_image) : $coverImg,
                    "cover_image1" => ($val->cover_image1 != '') ? change_img_src($val->cover_image1) : $coverImg,
                    "cover_image2" => ($val->cover_image2 != '') ? change_img_src($val->cover_image2) : $coverImg,
                    "cover_image3" => ($val->cover_image3 != '') ? change_img_src($val->cover_image3) : $coverImg,
                    "cover_image4" => ($val->cover_image4 != '') ? change_img_src($val->cover_image4) : $coverImg,
                    "cover_image5" => ($val->cover_image5 != '') ? change_img_src($val->cover_image5) : $coverImg,
                    "session_doctor_id" => $val->session_doctor_id,
                    "date" => date(' jS F y', strtotime($val->start_datetime)),
                    "is_multiday_session" => $val->is_multiday_session,
                    "start_datetime" => $val->start_datetime, //date(' jS F y', strtotime($val->start_datetime)),
                    "display_date" => $start_time . "-" . $end_time,
                    "ms_cat_name" => $val->category_name,
                    "category_image" => change_img_src(base_url() . "/themes/front/images/session/" . $val->category_logo),
                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogoSES),
                    "image" => change_img_src($logic_image),
                    "image_raw_name" => change_img_src($val->profile_image),
                    "session_status" => $val->session_status,
                    "status_name" => $val->status_name,
                    "seesion_description" => html_entity_decode(strip_tags((substr($val->description, 0, 300)))),
                    "session_topic" => html_entity_decode(strip_tags((substr($val->session_topic, 0, 300)))),
                    "seesion_title" => html_entity_decode(strip_tags((substr($val->title, 0, 300)))),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "specialities_ids_and_names" =>  $this->explode_speciality_string($val->specialities_ids_and_names),
                    "color" => ($val->color != '') ? $val->color : '#08cc9e',
                    "client_name" => $val->client_name,
                    "is_share" => $val->is_share,
                    "client_logo" => change_img_src('' . $val->client_logo),
                    "is_locked" => $key_locked,
                    "price" => $val->price,
                    "user_content_payment" => $val->user_contnet_payment_status,
                    "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                    "session_doctor_entities" => $ses_doc_det_array,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "saved_date" => $val->saved_date
                );
            }
        }
        return $vx;
    }
    public function get_all_childsession()
    {
        $ids = array();
        $this->db->select("kstc.multidaysession_id,GROUP_CONCAT(DISTINCT kstc.childsession_id SEPARATOR ',') as childsession_id,GROUP_CONCAT(DISTINCT sts.sessions_doctors_id SEPARATOR ',') as session_soctor_id");
        $this->db->from('knwlg_session_to_child as kstc');
        $this->db->join('session_to_sessiondoctor as sts', 'sts.session_id = kstc.childsession_id', 'left');
        $this->db->group_by('kstc.multidaysession_id');
        $query = $this->db->get();
        $ids = array();
        if (($query) && ($query->num_rows() > 0)) {
            $session_ids = $query->result();
            foreach ($session_ids as $key => $value) {
                $totalids = explode(',', $value->childsession_id);
                $getids[$value->multidaysession_id] = count(explode(',', $value->childsession_id)); //$value->childsession_id;
                $getdoctorcount[$value->multidaysession_id] = count(explode(',', $value->session_soctor_id));
                $ids = array_merge($ids, $totalids); //$value->childsession_id;
            }
        }
        $response['doctorcount'] = $getdoctorcount;
        $response['sessioncount'] = $getids; //$countmultidayminisession;
        $response['sessions'] = $ids;
        return $response;
    }
    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function all_bookedmastersession($user_master_id = '')
    {
        if (!empty($user_master_id)) {
            $this->db->select('knwlg_sessions_id');
            $this->db->where('participant_id', $user_master_id);
            $this->db->where('participant_type', 'member');
            $this->db->where_not_in('session_approval_status', 3);
            $query = $this->db->get('knwlg_sessions_participant');
            $result = $query->result();
            return $result;
        }
    }
    public function surveydata(
        $envStatus,
        $client_list_kcap,
        $limit,
        $searchQuery,
        $user_master_id
    ) {
        $env = get_user_env_id($user_master_id);
        $key_locked = get_user_package($user_master_id, 'survey');
        if ($key_locked == '') {
            return null;
        }
        $sql = "SELECT
            cm.* ,
            svd.data,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
            cln.client_name,
            cln.client_logo,
            kv.status as vault,
            kv.timestamp as saved_date,
            cTenv.price,
            uTpyCont.status as user_contnet_payment_status,
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
            FROM
            survey cm
            left JOIN survey_to_speciality as svts ON svts.survey_id = cm.survey_id
            left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
            JOIN client_master as cln ON cln.client_master_id = cm.client_id
            LEFT JOIN content_to_env as cTenv ON cTenv.type_id =cm.survey_id and  cTenv.type = 6
            LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.survey_id and  uTpyCont.type = 6 and 	uTpyCont.user_master_id = " . $user_master_id . "
            LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = cm.survey_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
            JOIN knwlg_vault as kv on (kv.type_text = 'survey' and kv.post_id = cm.survey_id and kv.user_id = " . $user_master_id . " and kv.status =1)
            JOIN survey_detail as svd ON svd.survey_id = cm.survey_id
            left JOIN survey_user_answer as sua ON sua.survey_id = cm.survey_id
            WHERE
            cm.status = 3
            " . $envStatus . "
            and
            cm.privacy_status = 0
            and date(publishing_date) BETWEEN CURDATE() - INTERVAL 1 YEAR AND CURDATE() and date(publishing_date)<=CURDATE()
            group by
                cm.survey_id,
                svd.data,
                cln.client_name,
                cln.client_logo,
                cTenv.price,
                uTpyCont.status,
                kv.timestamp
            order by kv.timestamp DESC ";
        if ($limit != '') {
            $sql .= $limit;
        }
        $vx = array();
        $query = $this->db->query($sql);
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            foreach ($result as $val) {
                $dataArry = unserialize($val->data);
                $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                $str = preg_replace('/\\\"/', "\"", $json);
                $sponsorLogoArry = explode(",", $val->sponsor_logo);
                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = change_img_src($valueSponor);
                        }
                    }
                } else {
                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = change_img_src($val->sponsor_logo);
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                $vx[] = array(
                    "survey_id" => $val->survey_id,
                    "category" => $val->category,
                    "is_share" => $val->is_share,
                    "type" => "survey",
                    "question_count" => $val->question_count,
                    "survey_time" => $val->survey_time,
                    "display_in_dashboard" => $val->display_in_dashboard,
                    "point" => $val->survey_points,
                    "points_on_approval" => $val->points_on_approval,
                    "json_data" => $str,
                    "survey_title" => $val->survey_title,
                    "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                    "survey_description_short" => html_entity_decode(substr($val->survey_description, 0, 150)),
                    "survey_description" => str_replace('&#39;', "'", html_entity_decode($val->survey_description)),
                    "image" => change_img_src($val->image),
                    "specialities_name" => $val->specialities_name,
                    "specialities_ids_and_names" => $this->explode_speciality_string($val->specialities_ids_and_names),
                    "is_locked" => $key_locked,
                    "price" => $val->price,
                    "user_content_payment" => get_user_content_status($val->survey_id, 6, $user_master_id),
                    "client_name" => $val->client_name,
                    "client_logo" => change_img_src($val->client_logo),
                    "sponsor_name" => change_img_src($val->sponsor),
                    "sponsor_logo" => $sponsorLogo,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "saved_date" => $val->saved_date,
                    "publishing_date" => $val->publishing_date,
                );
            }
        }
        return $vx;
    }
}
