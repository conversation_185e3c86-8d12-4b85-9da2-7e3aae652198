<?php

defined('BASEPATH') or exit('No direct script access allowed');

class User_model extends CI_Model
{
    public function __construct()
    {
        $ci = get_instance();
        $this->load->library('Myredis');
        //$this->reportdb = $this->load->database('report', TRUE);
        //$this->insertdb = $this->load->database('insert', TRUE);
    }

    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function detail($user_master_id = '', $type = '')
    {

        if (!empty($user_master_id)) {

            /**
             * Building User Filler Queue
             */
            $this->buildUserFillers($user_master_id);

            $cachee_key = "user_details_".$user_master_id;
            $env = get_user_env($user_master_id);

            if ($this->myredis->get($cachee_key)) {
                // echo 'hey';die;
                $result = $this->myredis->get($cachee_key);
            } else {

                $sql = "SELECT
                um.user_master_id as user_master_id,
                um.user_mem_id,
                um.user_name,
                um.country_code,
                um.email,
                um.mobile_primary,
                um.master_user_type_id,
                um.password,
                um.status,
                um.company_id,
                ud.specility_update_status,
                ud.doctor_voice_eligibility_status,
                clm.client_logo,
                clm.banner,
                
                up.payment_package_id,
                mm.sessions_doctors_id as mentor_id,

                utt.sm_token, 

                ud.first_name,
                ud.menu,
                ud.middle_name,
                ud.last_name,
                ud.profile_image,
                ud.description,
                ud.spec_dashboard_eligibility_status,
                utp.parent as parent_id,
                cg.data as user_goal,
                IF(utp.parent=um.user_master_id, \"user\", \"staff\") as profile_type
            
                
                FROM user_master as um
                LEFT JOIN user_detail as ud ON  um.user_master_id = ud.user_master_id
                LEFT JOIN knwlg_sessions_doctors as mm ON  mm.user_master_id = um.user_master_id

                LEFT JOIN user_temp_token as utt ON  utt.user_id = um.user_master_id

                LEFT JOIN user_to_parent as utp ON  um.user_master_id = utp.user_master_id
                LEFT JOIN client_master clm on clm.client_master_id = um.company_id
                LEFT JOIN cpd_goal as cg on cg.user_master_id = um.user_master_id
                left join payment_user_to_package up on up.user_master_id = um.user_master_id

                WHERE 
                um.status=3 
                AND 
                um.user_master_id = " . $user_master_id . " ";

                //echo $sql; exit;
                // exit;
                $query = $this->db->query($sql);
                $result = $query->row_array();
                $this->myredis->set($cachee_key, $result, 60 * 60 * 4);

            }



            //$result->primary_client_logo = 'https://doctor.clirnet.com/knowledge/uploads/logo/' . $result->client_logo;
            //$result->campaign = FALSE;
            //$result->permission = array('knowledge' => 'yes', 'concierge' => 'no');

            /*"user_master_id": "155",
            "user_mem_id": "5E9C8218-003E-2ED0-85B4-4854A52A7988",
            "user_name": "ajit.ray",
            "email": "<EMAIL>",
            "mobile_primary": "7980854661",
            "password": "827ccb0eea8a706c4c34a16891f84e7b",
            "company_id": "2",
            "client_logo": "DocAssist.png",
            "first_name": "Testdeb",
            "middle_name": "",
            "last_name": "ray",
            "profile_image": "",
            "parent_id": "155",
            "profile_type": "user",
            "primary_client_logo": "https://doctor.clirnet.com/knowledge/uploads/logo/DocAssist.png",
            "campaign": false,
            "permission": {
                    "knowledge": "yes",
                "concierge": "no"
            }*/

            $speciality = $this->get_profile_speciality($result['user_master_id']);
            // print_r($speciality);
            // exit;
            $search_values = array(5, 12);
            // $entities['spec_dashboard_eligibility_status'] = false;

            $speciality_ids = [];
            if (!empty($speciality)) {
                foreach ($speciality as $key => $value) {
                    $speciality_ids[] = $value['master_specialities_id'];
                }

                $spc = $speciality;

            } else {


                $spc = [
                    [
                        'master_specialities_id' => 1252,
                        'specialities_name' => 'General Medicine'
                    ]
                ];


            }
            //print_r($result['payment_package_id']); exit;
            $env_id = get_user_env_id($result['user_master_id']);

            if ($result['payment_package_id'] != '') {

                if ($env_id != 2) {
                    $package = ($type) ? 6 : $result['payment_package_id'];

                } else {
                    $package = ($type) ? 7 : $result['payment_package_id'];
                }

                //print_r($package); exit;
                $qryPkg = "SELECT
                id,
                package_setting,
                package_setting_old_menu
                FROM payment_package 
                WHERE 
                id = " . $package . "
                
                ";
                //echo $qryPkg; exit;
                $queryPkg = $this->db->query($qryPkg);
                $rowPkg = $queryPkg->row_array();

                //echo $rowPkg['package_setting'];
                //and
                //                country_id = " . $result['country_code'] . "
                //echo '<xxxxxxxxxxxxxxxxxxxxxxx<br>';
                //$jsonData = stripslashes(html_entity_decode($rowPkg['package_setting']));

                $pckgStting = json_decode($rowPkg['package_setting'], true);
                //var_dump($jsonData);
                //var_dump(json_decode($rowPkg['package_setting'],true));
                //var_dump(json_last_error());
                //var_dump(json_last_error_msg());
                //print_r($pckgStting);
                //echo '>xxxxxxxxxxxxxxxxxxxxxxx';
                $class = $this->router->fetch_class();
                //print_r($pckgStting['menu']);
                //exit;



                //exit;


            } else {
                //print_R($env_id); exit;
                if ($env_id != 2) {
                    $package = ($type) ? 6 : 4;

                    $qryPkg = "SELECT
                    id,
                package_setting,
                package_setting_old_menu,
                package_setting_new
                FROM payment_package 
                WHERE 
                id =".$package;
                    $queryPkg = $this->db->query($qryPkg);
                    $rowPkg = $queryPkg->row_array();

                } else {

                    $package = ($type) ? 7 : 5;


                    $qryPkg = "SELECT
                    id,
                package_setting,
                package_setting_old_menu,
                package_setting_new
                FROM payment_package 
                WHERE 
                id =".$package;
                    $queryPkg = $this->db->query($qryPkg);
                    $rowPkg = $queryPkg->row_array();

                }


            }





            $entities = array();


            ///$logic_image = base_url() . "uploads/docimg/MConsult.png";

            ($result['user_master_id'] != '') ? $entities['user_master_id'] = $result['user_master_id'] : '';
            ($result['user_mem_id'] != '') ? $entities['user_mem_id'] = $result['user_mem_id'] : '';
            ($result['user_name'] != '') ? $entities['user_name'] = $result['user_name'] : '';
            ($result['email'] != '') ? $entities['email'] = $result['email'] : '';
            // ($result['password'] != '') ? $entities['password'] = $result['password'] : '';
            ($result['mobile_primary'] != '') ? $entities['mobile_primary'] = $result['mobile_primary'] : '';
            ($result['company_id'] != '') ? $entities['company_id'] = $result['company_id'] : '';
            ($result['client_logo'] != '') ? $entities['client_logo'] = change_img_src($result['client_logo']) : '';
            ($result['first_name'] != '') ? $entities['first_name'] = $result['first_name'] : '';
            ($result['master_user_type_id'] != '') ? $entities['master_user_type_id'] = $result['master_user_type_id'] : '';
            //($result['mentor_user_master_id'] != '') ? $entities['mentor_user_master_id'] = $result['mentor_id'] : '';
            $entities['mentor_user_master_id'] = $result['mentor_id'];
            $entities['env'] = $env;
            $entities['sm_token'] = $result['sm_token'];
            $entities['status'] = $result['status'];

            //$entities['middle_name'] = 'dsfgdsfdsf';
            ($result['middle_name'] != '') ? $entities['middle_name'] = $result['middle_name'] : $entities['middle_name'] = '';

            ($result['last_name'] != '') ? $entities['last_name'] = $result['last_name'] : '';
            ($result['description'] != '') ? $entities['description'] = $result['description'] : '';

            $entities['speciality'] = stripslashes(json_encode($spc));

            (!empty($speciality)) ? $entities['speciality'] = json_encode($speciality, JSON_UNESCAPED_SLASHES) : json_encode($spc, JSON_UNESCAPED_SLASHES);

            ($result['profile_image'] != '') ? $entities['profile_image'] = change_img_src($result['profile_image']) : $entities['profile_image'] = change_img_src('https://firebasestorage.googleapis.com/v0/b/clirnetapp.appspot.com/o/doctor%2F0E57DB94-9662-11F6-3541-148D6F42BDC8%2F0E57DB94-9662-11F6-3541-148D6F42BDC8167242955049480.png?alt=media&token=1754cd39-f142-4ad9-aca8-8cc102dfe88f');

            ($result['parent_id'] != '') ? $entities['parent_id'] = $result['parent_id'] : '';
            ($result['profile_type'] != '') ? $entities['profile_type'] = $result['profile_type'] : '';
            ($result['primary_client_logo'] != '') ? $entities['primary_client_logo'] = change_img_src($result['primary_client_logo']) : '';
            ($result['banner'] != '') ? $entities['banner'] = change_img_src(base_url() . 'uploads/banner_images/' . $result['banner'])
                : '';
            ($result['campaign'] != '') ? $entities['campaign'] = $result['campaign'] : '';









            if (array_intersect($speciality_ids, $search_values)) {
                //$entities['spec_dashboard_eligibility_status'] = true;
            } else {
                //$entities['spec_dashboard_eligibility_status'] = false;
            }

            // ($result['spec_dashboard_eligibility_status'] == 1) ? $entities['spec_dashboard_eligibility_status'] = true : false;

            $spec_dashboard_eligibility_status = ($type) ? 1 : $result['spec_dashboard_eligibility_status'];
            // if($result['spec_dashboard_eligibility_status'] == 1) {
            $header_data = array_change_key_case($this->input->request_headers(), CASE_LOWER);
            $substrings = ['rjsa', 'rjsw'];

            if ((isset($header_data['version'])) && ($header_data['version'] != '')) {
                $containsAny = array_filter($substrings, fn ($substring) => str_contains($header_data['version'], $substring));
            } else {
                $containsAny = array();

            }
            // Check if the string contains any of the substrings

            if ($spec_dashboard_eligibility_status == 1) {

                $entities['spec_dashboard_eligibility_status'] = true;
                // print_r(json_decode($rowPkg['package_setting_new']));die;

                if ((isset($header_data['version'])) && ($header_data['version'] != '') && (empty($containsAny))) {
                    $menu_data = (json_decode($rowPkg['package_setting_new'], true));
                    $menu = (popKeys($menu_data, 'Community'));
                    if ($env_id == 2) {
                        $menu = (popKeys($menu, 'Epaper'));
                    }
                    $entities['menu'] = ($type) ? json_encode($menu) : $rowPkg['package_setting_new'];
                    // print_R(menu_pop_internal($rowPkg['package_setting'])); exit;
                } else {

                    $entities['menu'] = $rowPkg['package_setting_new'];

                }
            } else {
                $entities['spec_dashboard_eligibility_status'] = false;
                $entities['menu'] = $rowPkg['package_setting_old_menu'];
            }

            $entities['spec_dashboard_eligibility_status-2'] = $result['spec_dashboard_eligibility_status'] ;


            ($result['specility_update_status'] != '') ? $entities['specility_update_status'] = $result['specility_update_status'] : false;
            ($result['doctor_voice_eligibility_status'] != '') ? $entities['doctor_voice_eligibility_status'] = (string)$result['doctor_voice_eligibility_status'] : "0";
            $entities['user_goal'] = $result['user_goal'];

            $entities['user_speaker_invitation_status'] = $this->checkinvitationstatus($user_master_id);
            return $entities;
        }

    }


    public function buildUserFillers($user_master_id)
    {
        $fillerQueueName = "fillerQueue_".$user_master_id;

        if ($this->myredis->exists($fillerQueueName)) {
            return true;
        } else {
            $this->myredis->populateFillers($fillerQueueName);
        }
        return true;
    }

    public function getUserActivityData($user_master_id, $filters)
    {
        $this->crmDb = $this->load->database('crm', true);

        if (isset($filters['month']) && $filters['month'] != '' && $filters['month'] != 0) {
            $monthClause = " AND MONTH(ua.activity_date_time) = {$filters['month']}";
        }

        if (isset($filters['year']) && $filters['year'] != '' && $filters['year'] != 0) {
            $yearClause = " AND YEAR(ua.activity_date_time) = {$filters['year']}";
        }

        $sql = "SELECT
                    base.service_code as type,
                    GROUP_CONCAT(DISTINCT(base.activity_page_id)) as ids
                FROM (
                    SELECT
                        svc.service_code,
                        ua.activity_page_id
                    FROM
                        user_activities as ua
                    JOIN
                        services as svc ON svc.id = ua.activity_service_type_id
                    WHERE
                        ua.user_data_id = {$user_master_id}
                        {$monthClause}
                        {$yearClause}
                        AND svc.service_code IN('medwiki','session','survey','channel' , 'archived_video', 'epub' , 'training') 
                    
                    ORDER BY
                        ua.activity_date_time DESC
                    
                    LIMIT
                        {$filters['from']}, {$filters['to']}
                ) as base

            GROUP BY
                base.service_code
        ";

        $query = $this->crmDb->query($sql);
        $result = $query->result_array();

        return $result;
    }

    public function cme_points($user_master_id = '')
    {

        if (!empty($user_master_id)) {


            $sql = "SELECT
            sum(points) as value
            from
            cpd_user_point
            where
            user_master_id = " . $user_master_id . "
            and
            status =3
             ";

            //echo $sql; exit;
            //exit;
            $query = $this->db->query($sql);
            $result = $query->row_array();

            // $entities['value'] = $result['value'];


            return $result['value'];
        }

    }


    /**
     * @param string $user_master_id
     * @return reponse
     */
    public function checkinvitationstatus($user_master_id)
    {
        if (!empty($user_master_id)) {
            $this->db->select('acceptance_status');
            $this->db->from('speaker_invitation_master');
            $this->db->where('user_master_id', $user_master_id);
            $query = $this->db->get();

            if ($query->num_rows() > 0) {
                $result = $query->result();
                $status_message = $result[0]->acceptance_status;

            } else {
                $status_message = 0;
            }

        } else {
            $status_message = 0;
        }

        return $status_message;
    }

    /**
     * @param string $user_master_id
     * @return array
     */
    public function sponsor($user_master_id = '')
    {

        if (!empty($user_master_id)) {

            $sql = "SELECT
            um.user_master_id as user_master_id,
            um.user_mem_id,
           
            um.company_id,
            
            clm.client_logo,
            clm.banner,
            clm.client_name
            
            FROM user_master as um
            LEFT JOIN user_detail as ud ON  um.user_master_id = ud.user_master_id
            LEFT JOIN client_master clm on clm.client_master_id = um.company_id
            
            WHERE um.status=3 
            AND 
            um.user_master_id = " . $user_master_id . " ";

            //echo $sql; exit;
            //exit;
            $query = $this->db->query($sql);
            $result = $query->row_array();

            $entities = array();

            ($result['user_master_id'] != '') ? $entities['user_master_id'] = $result['user_master_id'] : '';
            ($result['user_mem_id'] != '') ? $entities['user_mem_id'] = $result['user_mem_id'] : '';
            ($result['client_logo'] != '') ? $entities['client_logo'] = change_img_src(base_url() . 'uploads/logo/' . $result['client_logo']) : '';
            ($result['client_name'] != '') ? $entities['client_name'] = $result['client_name'] : '';
            ($result['primary_client_logo'] != '') ? $entities['primary_client_logo'] = change_img_src($result['primary_client_logo']) : '';
            ($result['banner'] != '') ? $entities['banner'] = change_img_src(base_url() . 'uploads/banner_images/' . $result['banner'])
                : '';
            //($result['campaign'] != '') ? $entities['campaign'] = $result['campaign'] : '';
            $entities['campaign'] = 1;


            return $entities;
        }

    }

    /**
     * @param string $user_master_id
     * @return arrayx
     */
    public function getVersion($user_master_id = '')
    {

        if (!empty($user_master_id)) {


            $sql = "SELECT
            *
            from
            app_version
             ";

            //echo $sql; exit;
            //exit;
            $query = $this->db->query($sql);
            $result = $query->row_array();

            $entities['andriod_version'] = $result['andriod_version'];
            $entities['ios_version'] = $result['ios_version'];
            $entities['ios_flag'] = $result['ios_flag'];
            $entities['andriod_flag'] = $result['andriod_flag'];

            return $entities;
        }

    }

    /**
     * @param string $user_master_id
     * @return mixed
     */
    public function get_mem_id($user_master_id = '')
    {

        if (!empty($user_master_id)) {

            $sql = "SELECT
            user_mem_id
            FROM user_master 
            WHERE 
            user_master_id = " . $user_master_id . " ";
            //exit;
            $query = $this->db->query($sql);
            $result = $query->row();
            return $result->user_mem_id;
        }

    }

    /**
     * @param $user_master_id
     * @param $acknowledge
     * @param $sync_status
     * @return mixed
     */
    public function insert_check_user_present($user_master_id, $acknowledge, $sync_status)
    {
        $this->insertdb = $this->load->database('insert', true);
        $data['user_master_id'] = $user_master_id;
        $data['acknowledge'] = $acknowledge;
        $data['sync_status'] = $sync_status;


        $this->insertdb->insert("check_user_present", $data);
        //echo $this->db->last_query(); exit();
        return $this->insertdb->insert_id();
    }

    /**
     * @param $user_id
     * @return mixed
     */
    public function get_profile_speciality($user_id)
    {
        $sql = "SELECT 
        usp.specialities_id as master_specialities_id,
        ms.specialities_name 
        FROM  user_to_specialities usp 
        left join master_specialities_V1 ms on ms.master_specialities_id = usp.specialities_id
        WHERE usp.user_master_id=? ";
        $query = $this->db->query($sql, array($user_id));

        $results = $query->result_array();
        $data = $results;
        return $data;

    }

    /**
     * @param $user_id
     * @return mixed
     */
    public function update_mobile_number($user_id, $mobile_number, $country_code)
    {
        $this->insertdb = $this->load->database('insert', true);
        $sql = "UPDATE user_master SET country_code = '$country_code' ,  mobile_primary = '$mobile_number' WHERE user_master_id = ?";
        $query = $this->insertdb->query($sql, array($user_id));

        return $this->insertdb->affected_rows();
    }

    public function find_record($user_id, $mobile_number)
    {

        $sql = "SELECT user_master_id FROM user_master WHERE user_master_id  = ? and  mobile_primary = ? ";

        $query = $this->db->query($sql, array($user_id, $mobile_number));

        $results = $query->result_array();
        $data = $results;
        return $data;

    }


    /**
     * @param $user_master_id
     * @param $acknowledge
     * @param $sync_status
     * @return mixed
     */
    public function udateMenu($user_master_id, $data)
    {
        $this->insertdb = $this->load->database('insert', true);
        // $data['user_master_id'] = $user_master_id;
        $dataupdate['menu'] = json_encode($data);
        //$data['sync_status'] = $sync_status;

        $this->insertdb->where("user_master_id", $user_master_id);
        $this->insertdb->update("user_detail", $dataupdate);
        //echo $this->db->last_query(); exit();
        return $this->insertdb->affected_rows();
    }

    public function getuserpersonaldetail($user_master_id)
    {

        $sql = "SELECT
        
        um.email,
        um.mobile_primary,
        ud.first_name,
        ud.profile_image,
        ud.description
       
        FROM user_master as um
        LEFT JOIN user_detail as ud ON  um.user_master_id = ud.user_master_id
        
        WHERE um.status=3 
        AND 
        um.user_master_id = " . $user_master_id . " ";

        //echo $sql; exit;
        //exit;
        $query = $this->db->query($sql);
        $count = 0;
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $value) {

                if ($value->email != '') {
                    $count = $count + 1;
                }

                if ($value->mobile_primary != '') {
                    $count = $count + 1;
                }

                if ($value->profile_image != '') {
                    $count = $count + 1;
                }

                if ($value->description != '') {
                    $count = $count + 1;
                }
            }
        }
        $speciality = $this->get_profile_speciality($user_master_id);

        if ($speciality) {
            // list is empty.
            $count = $count + 1;
        }

        return $count;
    }

    public function user_detail($user_master_id)
    {
        $response = array();
        $sql = "SELECT
        ud.first_name,
        ud.middle_name,
        ud.last_name,
        ud.city
        FROM user_master as um
        LEFT JOIN user_detail as ud ON  um.user_master_id = ud.user_master_id
        
        WHERE um.status=3 
        AND 
        um.user_master_id = " . $user_master_id . " ";

        //echo $sql; exit;
        //exit;
        $query = $this->db->query($sql);
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $value) {
                if ($value->middle_name == '') {
                    $response['name'] = $value->first_name . ' ' . $value->last_name;

                } else {
                    $response['name'] = $value->first_name . ' ' . $value->middle_name . ' ' . $value->last_name;
                }

                $response['city'] = $value->city;

            }

        }
        $response['speciality'] = $this->get_profile_speciality($user_master_id);
        return $response;
    }

    public function updatestatus($status, $user_master_id)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (($user_master_id != '') && ($status != '')) {
            if ($status == 3) {
                $data = array('master_user_type_id' => 11);

                $this->insertdb->where("user_master_id", $user_master_id);
                $this->insertdb->update("user_master", $data);

            }

            $acceptstatus = array('acceptance_status' => $status);
            $this->insertdb->where("user_master_id", $user_master_id);
            $this->insertdb->update("speaker_invitation_master", $acceptstatus);
        }
        return true;
    }

    /**
     * @param $user_master_id
     * @param $model
     * @return bollean true or false
     */
    public function update_specility_update_status_attribute($user_master_id, $model)
    {
        $this->insertdb = $this->load->database('insert', true);
        $this->insertdb->where("user_master_id", $user_master_id);
        return $this->insertdb->update("user_detail", $model);
    }

    /*
    public function insert_user_completed_goal($table, $model) {
        return $this->db->insert($table, $model);
    }
    */

    // public function get_user_goal_by_user_master_id($user_master_id = '')
    // {

    //     if (!empty($user_master_id)) {

    //         $sql = "SELECT
    //         um.user_master_id as user_master_id,
    //         cg.data as user_goal
    //         FROM user_master as um
    //         LEFT JOIN cpd_goal as cg on cg.user_master_id = um.user_master_id
    //         WHERE um.status=3
    //         AND
    //         um.user_master_id = " . $user_master_id . "
    //         order by updated_on desc limit 1";


    //         $query = $this->db->query($sql);
    //         $result = $query->row_array();
    //         $entities = array();
    //         // ($result['user_master_id'] != '') ? $entities['user_master_id'] = $result['user_master_id'] : '';
    //         return ($result['user_goal'] != '') ? $result['user_goal'] : json_encode(array(
    //             'read' => 10,
    //             'watch' => 10,
    //             'discuss' => 10,
    //             'cme_point' => 10
    //         ));
    //         // return $entities;
    //     }
    // }

    public function get_user_goal_by_user_master_id($user_master_id = '')
    {
        if (!empty($user_master_id)) {
            $sql = "SELECT
            um.user_master_id as user_master_id,
            cg.data as user_goal
            FROM user_master as um
            LEFT JOIN cpd_goal as cg on cg.user_master_id = um.user_master_id
            WHERE um.status=3 
            AND 
            um.user_master_id = " . $user_master_id . " 
            order by updated_on desc limit 1";

            $query = $this->db->query($sql);
            $result = $query->row_array();

            // Default goal data with numeric values
            $default_goals = array(
                'read' => 10,
                'watch' => 10,
                'discuss' => 10,
                'cme_point' => 10
            );

            // Get base goal data
            $goal_data = ($result['user_goal'] != '') ? json_decode($result['user_goal'], true) : $default_goals;

            // Ensure all values are numeric
            foreach ($goal_data as $key => $value) {
                // Convert any non-numeric values to default
                if (!is_numeric($value) || $value === '<string>') {
                    $goal_data[$key] = $default_goals[$key] ?? 0;
                } else {
                    $goal_data[$key] = (int)$value;
                }
            }

            // Get Redis counter for compendium views
            $redis_key = "user_read_views_" . $user_master_id;
            $this->load->library('Myredis');
            $comp_view_count = $this->myredis->get($redis_key);

            // Add Redis counter to existing read count if available
            if ($comp_view_count !== false && $comp_view_count !== null && is_numeric($comp_view_count)) {
                // Get the base read value
                $base_read_count = isset($goal_data['read']) ? (int)$goal_data['read'] : 0;

                // Add the Redis counter to the base value
                $goal_data['read'] = $base_read_count + (int)$comp_view_count;
            }



            // Get Redis counter for compendium views
            $redis_key = "user_video_archive_views_" . $user_master_id;
            $this->load->library('Myredis');
            $comp_view_count = $this->myredis->get($redis_key);

            // Add Redis counter to existing read count if available
            if ($comp_view_count !== false && $comp_view_count !== null && is_numeric($comp_view_count)) {
                // Get the base read value
                $base_read_count = isset($goal_data['read']) ? (int)$goal_data['read'] : 0;

                // Add the Redis counter to the base value
                $goal_data['watch'] = $base_read_count + (int)$comp_view_count;
            }






            // Ensure all required keys exist
            foreach ($default_goals as $key => $value) {
                if (!isset($goal_data[$key])) {
                    $goal_data[$key] = $value;
                }
            }

            return json_encode($goal_data);
        }
    }

    /**
     * Summary of get_total_points_by_type_ids_from_content_to_cpd
     * @param mixed $content_ids
     * @param mixed $type_id
     * @return mixed
     */
    public function get_total_points_by_type_ids_from_content_to_cpd($content_ids = array(), $type_id)
    {
        $sql = "SELECT 
        sum(points) as points 
        FROM `content_to_cpd` 
        where type_id IN($content_ids) 
        and type = '" . $type_id . "'";

        $row = $this->db->query($sql)->row_array();
        if (!empty($row)) {
            return $row['points'];
        }
    }
}
