fastcgi_cache_path /etc/nginx/cache levels=1:2 keys_zone=MYAPP:100m inactive=60m;
fastcgi_cache_key "$scheme$request_method$host$request_uri";
server {
    listen 80 default_server;

    root /var/www/public;
    index index.php index.html;

    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET,PUT,POST,DELETE' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, version' always;
    # add_header 'Cache-Control' 'public, max-age=3600'; # 1 hour
    server_name _;
    

    location / {
        try_files $uri $uri/ /index.php?q=$uri&$args;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }

    location ~ \.php$ {
        try_files $uri = 404;
        fastcgi_pass unix:/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_buffering off;
        include fastcgi_params;
        fastcgi_cache MYAPP;
	    fastcgi_cache_valid 200 60m;
    }
}
