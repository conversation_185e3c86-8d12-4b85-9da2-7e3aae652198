<?php

defined('BASEPATH') or exit('No direct script access allowed');
require dirname(__FILE__) . '/../libraries/NEW_REST_Controller.php';

use Google\Cloud\BigQuery\BigQueryClient;

///require  'vendor/autoload.php';
//use \Firebase\JWT\JWT;
// Add these lines somewhere on top of your PHP file:
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);
class User extends MY_REST_Controller
{
    private $token_data;
    private $logdata = array();
    private $message;
    private $json_data = array();
    private $payload = array();
    private $array = array();
    private $is_row_affected = false;

    private $user_master_id;
    private $params = array();
    private $rest_api_url;
    private $crm_url;
    private $crm_email;
    private $crm_password;
    private $access_token;
    private $headers = array();


    private static $keyFileData = [
        "type" => GCP_JSON_TYPE,
        "project_id" => GCP_JSON_PROJECT_ID,
        "private_key_id" => GCP_JSON_PRIVATE_KEY_ID,
        "private_key" => GCP_JSON_PRIVATE_KEY,
        "client_email" => GCP_JSON_CLIENT_EMAIL,
        "client_id" => GCP_JSON_CLIENT_ID,
        "auth_uri" => GCP_JSON_AUTH_URI,
        "token_uri" => GCP_JSON_TOKEN_URI,
        "auth_provider_x509_cert_url" => GCP_JSON_AUTH_PROVIDER_X509_CERT_URL,
        "client_x509_cert_url" => GCP_JSON_CLIENT_X509_CERT_URL
    ];


    public function __construct()
    {
        /*ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);*/


        parent::__construct();
        $token = $this->input->get_request_header('Authorization');
        // print_r($token);
        // exit;
        //this->set_response($all_data, REST_Controller::HTTP_OK); //This is the respon if success
        //Validate user
        $this->validate_token($token);
        $this->token_data = $this->get_token_data($token);

        //print_r($token); exit;


        $this->load->library('form_validation');
        $this->load->library('Background');
        $this->load->model('user_model');
        $this->load->model('search_model');
        $this->load->helper('crmlogin');
        $this->load->helper('image');
        $this->load->model('Service_model', 'service_model');
        /**
         * For api detail table
         */
        $this->logdata['called_on'] = date('Y-m-d H:i:s', time());
        $this->logdata['process_start_time'] = date('Y-m-d H:i:s');
        $this->logdata['version'] = $this->input->get_request_header('version');
        /**
         * For api detail table
         */
    }

    /**
     * Get user detail
     * start
     */
    public function detail_get()
    {
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $type = ($this->input->get('type')) ? $this->input->get('type') : "";
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $this->background->do_in_background_http(BUILD_CAMPAIGN_URL, $this->input->get_request_header('Authorization'));
        $all_data = $this->user_model->detail($user_master_id, $type);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    /**
     *
     */
    public function checkversion_get()
    {
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $all_data['sponsor'] = $this->user_model->sponsor($user_master_id);
        $all_data['version'] = $this->user_model->getVersion($user_master_id);
        $all_data['deal_url'] = '/ecom/wp.php';

        $hlightArry = array(
            'highlight_type' => 'gr',
            'data' =>
            array(
                'type_id' => '10',
            ),
        );
        $chattArry = array(
            'chat_type' => 0,
            'data' =>
            array(

                array(
                    'path' => 'gr',
                    'id' => 10,
                ),
                array(
                    'path' => 'session',
                    'id' => 10,
                ),
            ),

        );

        $all_data['highlight'] = 1;
        $all_data['highlight_path'] = json_encode($hlightArry, true);
        $all_data['chat'] = json_encode($chattArry, true);
        $all_data['highlight_alt_text'] = "COVID Peer Hotline";
        $all_data['highlight_image'] = "https://doctor.clirnet.com/knowledge/themes/front/images/320X100.jpg";
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    /**
     *
     */
    public function present_post()
    {
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $acknowledge = $this->input->post('status');
        $sync_status = $this->input->post('sync_status');

        $add_present = $this->user_model->insert_check_user_present($user_master_id, $acknowledge, $sync_status);
        $all_data = array();
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    /**
     * Get user detail
     * start
     */
    public function updatemobilenumber_post()
    {

        $dataArray = json_decode(file_get_contents('php://input'), true);
        $mobile_number = $dataArray['mobilenumber'];
        $country_code = $dataArray['countrycode'];

        $error_flag = 0;

        if ($mobile_number == "" || $country_code == "") {

            $message = 'Error in fields value';
            $output['msg'] = "Mobile Number or Country Code not found!";
            $error_flag = 1;

            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }


        if ($error_flag == 0) {

            $user_master_id = $this->token_data->userdetail->user_master_id;

            //$data['added_on']= $user_id = date('Y-m-d H:i:s', time());

            // check for similar record exist or not


            $result = $this->user_model->find_record($user_master_id, $mobile_number);

            if (count($result) > 0) {

                $message = 'mobile number already exist.';
                $output['msg'] = "number exist";


                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($output, $message, REST_Controller::HTTP_OK, $this->logdata);
                exit;
            }


            $row_effected = $this->user_model->update_mobile_number($user_master_id, $mobile_number, $country_code);

            if ($row_effected > 0) {

                $all_data = array();
                $message = 'Mobile Number Updated Successfully.';

                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
            } else {

                $message = 'User ID not found!';
                $output['msg'] = "zero row effected!";

                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */

                $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
            }
        }
    }

    /**
     *
     */
    public function updateMenu_post()
    {
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $dataArray = json_decode(file_get_contents('php://input'), true);
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $data = $dataArray['data'];



        //print_r($data); exit;


        $add_menu = $this->user_model->udateMenu($user_master_id, $data);
        $all_data = array(

            "return" => $add_menu,
        );
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    public function profilecompeletionpercentage_get()
    {

        $this->load->model('Profile_model');
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);

        $user_master_id = $this->token_data->userdetail->user_master_id;

        $getpersonaldata = $this->user_model->getuserpersonaldetail($user_master_id);

        $geteducationaldata = $this->Profile_model->get_data_all($user_master_id, 'user_education');

        print_r($geteducationaldata);

        // //    // $data = $dataArray['data'];



        // //     //print_r($data); exit;


        // //     $add_menu = $this->user_model->udateMenu($user_master_id, $data);
        // //     $all_data = array(

        // //         "return" => $add_menu,
        // //     );
        //     $message = 'Success';
        //     /**
        //      * For api detail table
        //      */
        //     $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        //     $this->logdata['response'] = REST_Controller::HTTP_OK;
        //     $this->logdata['message'] = $message;
        //     $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        //     /**
        //      * For api detail table
        //      */
        //     $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    public function invitation_acceptance_post()
    {


        //  echo $this->input->post('status',TRUE) ;
        //  exit;

        //acceptance 3,4
        if (($this->input->post('status', true)) && ($this->input->post('status', true) != '')) {



            $all_data = array();
            $data_update = $this->user_model->updatestatus($this->input->post('status', true), $this->token_data->userdetail->user_master_id);


            $message = 'invitation status Updated Successfully.';

            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata);
        } else {
            $message = 'Status not available';
            $output['msg'] = "zero row effected!";

            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */

            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }
    }
    /**
     * Write by rakesh maity
     */
    private function set_user_master_id($user_master_id = null)
    {
        if (!empty($user_master_id)) {
            $this->user_master_id = $user_master_id;
        } else {
            $this->user_master_id = (int) $this->token_data->userdetail->user_master_id; //290
        }
    }

    /**
     * Get user detail
     * start
     */
    public function user_goal_get()
    {
        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id

        $user_master_id = $this->token_data->userdetail->user_master_id;

        $all_data['user_goal'] = $this->user_model->get_user_goal_by_user_master_id($user_master_id);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    /**
     * Summary of env_config
     * @return void
     */
    private function env_config()
    {
        switch ($this->config->item('environment_status')) {
            case 1:
                ## crm ##

                $this->crm_url = crm_url;

                $this->crm_email = crm_email;

                $this->crm_password = crm_password;

                ## crm ##

                break;

            case 2:
                ## crm ##

                $this->crm_url = crm_url;
                $this->crm_email = crm_email;
                $this->crm_password = crm_password;

                ## crm ##

                /*uat mode for notification ends */
                break;

            case 3:
                /* live mode for notification start */


                ## crm ##

                $this->crm_url = crm_url;
                $this->crm_email = crm_email;
                $this->crm_password = crm_password;

                ## crm ##

                /*live mode for notification ends */
                break;
        }
    }
    /**
     * Summary of fetch
     * @param mixed $method
     * @param mixed $url
     * @param mixed $data
     * @return bool|string
     */
    public function fetch($method, $url, $data = false, $headers = null)
    {

        $curl = curl_init();

        switch ($method) {
            case "POST":
                curl_setopt($curl, CURLOPT_POST, 1);

                if ($data) {
                    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
                }
                break;
            case "PUT":
                curl_setopt($curl, CURLOPT_PUT, 1);
                break;
            default:
                if ($data) {
                    $url = sprintf("%s?%s", $url, http_build_query($data));
                }
        }

        //echo $url;
        //exit;

        // Optional Authentication:
        curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($curl, CURLOPT_USERPWD, "username:password");

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($curl);

        curl_close($curl);

        //echo $result;
        //exit;



        return json_decode($result, true);
    }
    private function set_headers()
    {
        $this->headers  = [
            'Authorization: Bearer ' . $this->access_token,
            'Content-Type: application/json',
            'Accept:application/json',
        ];
    }
    /**
     * Summary of get_achieved_goal
     * @param mixed $user_master_id
     * @param mixed $service_type
     * @return void
     */
    public function goal_achieved_get()
    {
        $this->env_config();
        $this->access_token = logincrm($this->crm_url, $this->crm_email, $this->crm_password);
        $this->set_headers();
        $this->set_user_master_id();

        $this->params = array();
        $this->params['services']        = array('medwiki', 'archived_video', 'session');
        $this->params['user_master_id']  = $this->user_master_id;


        $key = 'goal_achieved_' . implode('_', $this->params['services']) . '_' . $this->params['user_master_id'];
        $this->load->library('Myredis');

        // Load redis library and check if it exists then fetch data from redis cache
        if ($this->myredis->exists($key)) {
            $this->payload = array();
            $this->payload  = $this->myredis->get($key);
            $this->message = 'Data has been successfully fetched from redis';
            $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata);
        }

        $this->rest_api_url = $this->crm_url . 'get_useractivities_count';
        $this->json_data = $this->fetch('GET', $this->rest_api_url, $this->params, $this->headers);

        $this->message = '500 internal server error';

        $this->payload['goal_achieved'] = array();

        if (!empty($this->json_data['data'])) {
            foreach ($this->json_data['data'] as $content_type => $total_goal) {

                if ($content_type == 'medwiki') {
                    $this->payload['goal_achieved']['article_read'] = array(
                        'total_goal' => $total_goal,
                        'content_type' => $content_type,
                    );
                }
                if ($content_type == 'archived_video') {
                    $this->payload['goal_achieved']['video_watched'] = array(
                        'total_goal' => $total_goal,
                        'content_type' => $content_type,
                    );
                }
                if ($content_type == 'session') {
                    $this->payload['goal_achieved']['attended_cme'] = array(
                        'total_goal' => $total_goal,
                        'content_type' => $content_type,
                    );
                }
            }
            // Set redis key value pair
            $this->myredis->set($key, $this->payload);
            $this->message = 'Retrieved successfully';
            $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
        $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
    /**
     * Summary of cme_points_get
     * @return void
     */
    public function cme_points_get()
    {



        $user_master_id = $this->token_data->userdetail->user_master_id;
        $all_data = $this->user_model->cme_points($user_master_id);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success




    }

    public function user_activities_duplicate_get()
    {
        $this->env_config();
        $this->access_token = logincrm($this->crm_url, $this->crm_email, $this->crm_password);
        $this->set_headers();

        // $this->load->library('json');
        // $this->json_data = $this->json->get_php_input();

        if ($this->input->get('user_master_id')) {
            $this->set_user_master_id($this->input->get('user_master_id'));
        } else {
            $this->set_user_master_id();
        }

        // echo "<pre>";
        // print_r($this->user_master_id);
        // exit;

        $this->params = array();
        $this->params['services']        = array('medwiki', 'archived_video', 'session');
        $this->params['user_master_id']  = $this->user_master_id;
        // $this->params['offset']          = ($this->input->get('from')) ? $this->input->get('from') : 0;
        // $this->params['limit']           = ($this->input->get('to')) ? $this->input->get('to') : 10;
        $this->params['date']            = ($this->input->get('date')) ? $this->input->get('date') : null;

        $key = 'user_activities_' . implode('_', $this->params['services']) . '_' . $this->params['user_master_id'] . '_' . $this->params['date'];
        $this->load->library('Myredis');
        // echo $key;
        // exit;
        // Load redis library and check if it exists then fetch data from redis cache
        if ($this->myredis->exists($key)) {

            $this->payload = array();
            $this->payload  = $this->myredis->get($key);
            $this->message = 'Data has been successfully fetched from redis';
            $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata);
        }

        $this->json_data = [];
        $this->rest_api_url = $this->crm_url . 'get_useractivities_details';
        $this->json_data = $this->fetch('GET', $this->rest_api_url, $this->params, $this->headers);

        $this->payload = array();
        $this->message = '500 internal server error';

        $this->payload['date'] = $this->params['date'];
        $this->payload['data'] = [];
        $attributes = [];
        // print_r($this->json_data['data']);
        // exit;
        if (!empty($this->json_data['data'])) {

            $this->json_data['user_master_id'] = $this->user_master_id;
            $this->payload['data'] = user_activities_productdetail($this->json_data);


            // Set redis key value pair
            $this->myredis->set($key, $this->payload, 600);

            $this->message = 'Retrieved successfully';
            $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
        $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }


    /**
     * Summary of user_activities_get
     * @return void
     */

    public function user_activities_get()
    {

        $this->params = array();
        $this->params['user_master_id']  = $this->token_data->userdetail->user_master_id;
        $this->params['from']          = ($this->input->get('from')) ? $this->input->get('from') : 0;
        $this->params['to']           = ($this->input->get('to')) ? $this->input->get('to') : 10;
        $this->params['month'] = $this->input->get('month');
        $this->params['year'] = $this->input->get('year');
        $cachekey = 'user_activities_' . $this->params['month'] . '_' . $this->params['year'] . '_' . $this->params['user_master_id'] . '_' . $this->params['from'] . '_' . $this->params['to'];
        //print_r($cachekey); exit;
        $this->load->library('Myredis');
        $this->params['month'] = $this->input->get('month');
        //print_r($this->myredis->exists($cachekey)); exit;
        if ($this->myredis->exists($cachekey)) {
            // echo "hel"; exit;
            $result = $this->myredis->get($cachekey);
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = 'Retrieved successfully from cache';
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            $this->set_response($result, $this->logdata['message'], REST_Controller::HTTP_OK, $this->logdata);
        } else {
            //echo "hel1"; //exit;
            $channelDetail = array();
            $compDetail = array();
            $archiveVideoDetail = array();
            $sessiondetail = array();
            $data = $this->user_model->getUserActivityData($this->token_data->userdetail->user_master_id, $this->params);
            // $data = $this->bigqueryUser_activitiesnew($this->params);


            $epubDetail = array();
            $trainingDetail = array();
            $surveyDetail = array();
            $grDetail = array();

            if (!empty($data)) {
                $env = get_user_env($this->params['user_master_id']);
                foreach ($data as $key => $value) {

                    switch ($value['type']) {
                        case 'medwiki':
                            if (isset($value['ids']) && !empty($value['ids'])) {
                                $compDetail = useractivitydetail('comp', $value['ids'], $env, '');
                            }
                            break;

                        case 'channelnew':
                            if (isset($value['ids']) && !empty($value['ids'])) {
                                $channelDetail = useractivitydetail("channel", $value['ids'], $env, '');
                            }

                            break;

                        case 'archived_video':
                            if (isset($value['ids']) && !empty($value['ids'])) {
                                $archiveVideoDetail = useractivitydetail('archivevideo', $value['ids'], $env, '');
                            }
                            break;

                        case 'session':
                            if (isset($value['ids']) && !empty($value['ids'])) {
                                $sessiondetail =  useractivitydetail('session', $value['ids'], $env, '');
                            }
                            break;

                        case 'epub':
                            if (isset($value['ids']) && !empty($value['ids'])) {
                                $epubDetail = useractivitydetail('epub', $value['ids'], $env, '');
                            }
                            break;

                        case 'training':
                            if (isset($value['ids']) && !empty($value['ids'])) {
                                $trainingDetail = useractivitydetail('training', $value['ids'], $env, '');
                            }
                            break;

                        case 'survey':
                            if (isset($value['ids']) && !empty($value['ids'])) {
                                $surveyDetail = useractivitydetail('survey', $value['ids'], $env, $this->params['user_master_id']);
                            }
                            break;

                            // case 'gr':
                            //     if (isset($value['detail'])) {
                            //         $grDetail = useractivitydetail('gr', $value['detail'], $env, '');
                            //     }
                            // break;

                        default:
                            break;
                    }
                }
                //echo "hello"; exit;
                $result = array_filter(array_merge_recursive($sessiondetail, $archiveVideoDetail, $compDetail, $surveyDetail, $grDetail, $trainingDetail, $epubDetail, $channelDetail));

                if (!empty($payload)) {
                    $this->myredis->set($cachekey, $payload, 60 * 60 * 48); // 2 days
                }

                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = 'Retrieved successfully';
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                $this->set_response($result, $this->logdata['message'], REST_Controller::HTTP_OK, $this->logdata);
            } else {

                $result = "";
                $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = 'Data not available';
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                //print_r($this->logdata); exit;
                $this->set_response($result, $this->logdata['message'], REST_Controller::HTTP_OK, $this->logdata);
            }
        }

        //This is the respon if success
    }

    /**
     * Summary of user_activities_get
     * @return void
     */
    public function user_activities_old_get()
    {
        //echo "baaler developer code"; exit;
        $this->env_config();
        //$this->access_token = logincrm($this->crm_url, $this->crm_email, $this->crm_password);
        $this->set_headers();

        // $this->load->library('json');
        // $this->json_data = $this->json->get_php_input();

        if ($this->input->get('user_master_id')) {
            $this->set_user_master_id($this->input->get('user_master_id'));
        } else {
            $this->set_user_master_id();
        }

        // echo "<pre>";
        // print_r($this->user_master_id);
        // exit;

        $this->params = array();
        $this->params['services']        = array('medwiki', 'archived_video', 'session');
        $this->params['user_master_id']  = $this->user_master_id;

        $this->params['offset']          = ($this->input->get('from')) ? $this->input->get('from') : 0;
        $this->params['limit']           = ($this->input->get('to')) ? $this->input->get('to') : 10;

        $this->params['date']            = ($this->input->get('date')) ? $this->input->get('date') : null;

        $cachekey = 'user_activities_' . implode('_', $this->params['services']) . '_' . $this->params['user_master_id'] . '_' . $this->params['offset'] . '_' . $this->params['limit'];
        $this->load->library('Myredis');
        // echo $key;
        //  exit;
        // Load redis library and check if it exists then fetch data from redis cache
        //print_r($this->myredis->get($cachekey)); //exit;
        // if ($this->myredis->get($cachekey)) {

        //     $this->payload = array();
        //     $this->payload  = $this->myredis->get($cachekey);
        //     $this->message = 'Data has been successfully fetched from redis';
        //     $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata);
        // }
        //
        //$this->json_data = [];
        //$this->rest_api_url = $this->crm_url . 'get_useractivities_details';
        //$this->json_data = $this->fetch('GET', $this->rest_api_url, $this->params, $this->headers);

        $payload = array();
        $this->message = '500 internal server error';

        //$this->payload['date'] = $this->params['date'];
        $payload['data'] = [];
        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');

        $data = $this->bigqueryUser_activities($limitFrom, $limitTo);
        // print_r($data);
        // exit;

        if (!empty($data)) {

            foreach ($data as $key => $value) {
                // print_r($value['feedDetail']);
                switch ($key) {

                    case 'Channelnew':
                        //detail id respect data fetch
                        if (isset($value['detail'])) {

                            // $send_content_type = 'comp';

                            // $compDetail = useractivitydetail($send_content_type, $value['type_id'], $this->user_master_id);
                            // print_r($compDetail); exit;
                        }
                        break;

                    case 'knwlg':
                        if (isset($value['feedDetail'])) {

                            $send_content_type = 'comp';

                            $compDetail = useractivitydetail('comp', $value['feedDetail'], $user_master_id, $env);
                            // print_r($compDetail); exit;
                        }
                        if (isset($value['archiveVideoDetail'])) {
                            $send_content_type = 'archivevideo';

                            $archiveVideoDetail = useractivitydetail($send_content_type, $value['type_id'], $this->user_master_id);
                        }
                        break;
                    case 'knwlgmastersessionnew':
                        if (isset($value['sessiondetail_new'])) {
                            $send_content_type = 'session';
                            $sessiondetail =  useractivitydetail($send_content_type, $value['type_id'], $this->user_master_id);
                        }
                        break;
                    default:
                        break;
                }
                // if ($key == 'feedDetail') {

                //     $send_content_type = 'comp';

                //     $compDetail = useractivitydetail($send_content_type, $value['type_id'], $this->user_master_id);
                //    // print_r($compDetail); exit;
                // }

                // elseif ($key == 'archiveVideoDetail') {
                //     $send_content_type = 'archivevideo';

                //     $archiveVideoDetail = useractivitydetail($send_content_type, $value['type_id'], $this->user_master_id);

                //     //print_r($archiveVideoDetail); exit;
                // } elseif ($key == 'sessiondetail_new') {
                //     //echo  $key;
                //     $send_content_type = 'session';

                //     $sessiondetail =  useractivitydetail($send_content_type,$value['type_id'], $this->user_master_id);

                // }


                // exit;
                //  print_R($payload); exit;
                // print_r(date('d-m-Y H:i:s')."     "); exit; //exit;exit;
                // Set redis key value pair
                //print_r($cachekey); exit;

            }
            //print_r($compDetail); exit;

            $payload = array_filter(array_merge_recursive($sessiondetail, $archiveVideoDetail, $compDetail));
            //print_r($payload);
            //$this->myredis->set($cachekey, $payload);

            //print_R($this->myredis->get($cachekey)); exit;
            $this->message = 'Retrieved successfully';
            $this->set_response($payload, $this->message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

        }
    }
    /**
     * get all dates of user activities
     * Code end here written by rakesh maity
     */
    public function user_activities_date_get()
    {
        $this->env_config();
        $this->access_token = logincrm($this->crm_url, $this->crm_email, $this->crm_password);
        $this->set_headers();

        // $this->load->library('json');
        // $this->json_data = $this->json->get_php_input();

        if ($this->input->get('user_master_id')) {
            $this->set_user_master_id($this->input->get('user_master_id'));
        } else {
            $this->set_user_master_id();
        }

        // echo "<pre>";
        // print_r($this->user_master_id);
        // exit;

        $this->params = array();
        $this->params['services']        = array('medwiki', 'archived_video', 'session', 'dashboard');
        $this->params['user_master_id']  = $this->user_master_id;
        //$this->params['date']            = ($this->input->get('date')) ? $this->input->get('date') : null;

        $key = 'user_activities_date' . implode('_', $this->params['services']) . '_' . $this->params['user_master_id'];
        $this->load->library('Myredis');
        // echo $key;
        // exit;
        // Load redis library and check if it exists then fetch data from redis cache
        if ($this->myredis->exists($key)) {

            $this->payload = array();
            $this->payload  = $this->myredis->get($key);
            $this->message = 'Data has been successfully fetched from redis';
            $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata);
        }

        $this->json_data = [];
        $this->rest_api_url = $this->crm_url . 'get_useractivities_datetime';
        $this->json_data = $this->fetch('GET', $this->rest_api_url, $this->params, $this->headers);

        $this->payload = array();
        $this->message = '500 internal server error';
        // echo "<pre>";
        // print_r($this->json_data);
        // exit;
        if (!empty($this->json_data)) {


            // print_r($this->json_data['date']);
            // exit;



            foreach ($this->json_data['date'] as $date) {
                // if ($content_type == 'medwiki') {
                //     $content_type = 'comp';
                // } else if ($content_type == 'archived_video') {
                //     $content_type = 'archivevideo';
                // }
                // if (!empty($content_ids) && is_array($content_ids)) {
                //     foreach ($content_ids as $content_id) {
                //         $this->payload[] = productdetail($content_type, $content_id, $this->user_master_id);
                //     }
                // }
                $this->payload[] = $date;
            }
            // Set redis key value pair
            $this->myredis->set($key, $this->payload, 600);

            $this->message = 'Retrieved successfully';
            $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
        $this->set_response($this->payload, $this->message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    public function bigqueryUser_activitiesnew($array)
    {

        if (!empty($array['month'])) {
            $month = str_pad($array['month'], 2, '0', STR_PAD_LEFT);
            $year = $array['year'];

            $start_date = "$year-$month-01 00:00:00";
            $end_date = date("Y-m-t 23:59:59", strtotime($start_date)); // Get last day of the month
            //$dateSql = "TIMESTAMP_TRUNC(called_on, DAY) BETWEEN TIMESTAMP('".$limitFrom."') AND TIMESTAMP('".$limitTo."') AND";
            // $dateSql = "EXTRACT(MONTH FROM called_on) = " . $array['month'] . " and EXTRACT(YEAR FROM called_on) = " . $array['year'] . "  AND user_master_id = " . $array['user_master_id'];
        } else {
            $dateSql = "";
        }
        // $bigQuery = new BigQueryClient([
        //     'projectId' => 'clirnet-dev',
        //     'keyFile' => json_decode(LOG_WRITER_GCP_JSON)
        //     //'keyFilePath' => APPPATH . '/libraries/ImageGCP/clirnet-dev-c1c87c7d7226.json'
        // ]);

        $bigQuery = new BigQueryClient([
            'projectId' => self::$keyFileData['project_id'],
            'keyFile' => self::$keyFileData
            //'keyFilePath' => APPPATH . '/libraries/ImageGCP/clirnet-dev-c1c87c7d7226.json'
        ]);


        $queryJobConfig = $bigQuery->query("SELECT method, controller, type, type_id, FORMAT_TIMESTAMP('%Y-%m-%d', called_on) AS called_on
        FROM `".GCP_JSON_PROJECT_ID.".api_call_data.user_activities`
        WHERE called_on BETWEEN '".$start_date."' AND '".$end_date."'
        AND user_master_id = ".$array['user_master_id']."
        ORDER BY called_on DESC
    ");




        // print_r($queryJobConfig); exit;
        $queryResults = $bigQuery->runQuery($queryJobConfig);
        // print_r($queryResults);
        // exit;
        $message = 'Succeess';
        //$data[] = array();
        foreach ($queryResults as $row) {

            if (($row['type_id'] != '') && ($row['type_id'] != 'undefined')) {
                //$data[$row['controller']][$row['method']]['type_id'][] = $row['type_id'];
                $data[$row['controller']][$row['method']]['called_on'][$row['type_id']] = $row['called_on'];
            }
        }
        // print_R($data); exit;

        return $data;
        //exit;
        //$this->set_response($data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success


    }
}
