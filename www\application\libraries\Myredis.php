<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * CodeIgniter Redis
 *
 * A CodeIgniter library to interact with Redis
 *
 * @package            CodeIgniter
 * @category        Libraries
 * <AUTHOR>
 * @version            v0.4
 * @link            https://github.com/joelcox/codeigniter-redis
 * @link            http://joelcox.nl
 * @license            http://www.opensource.org/licenses/mit-license.html
 */
class Myredis
{
    /**
     * CI
     *
     * CodeIgniter instance
     * @var    object
     */
    private $_ci;

    /**
     * Connection
     *
     * Socket handle to the Redis server
     * @var        handle
     */
    private $_connection;

    /**
     * Debug
     *
     * Whether we're in debug mode
     * @var        bool
     */
    public $debug = false;


    private $redis_config;

    /**
     * CRLF
     *
     * User to delimiter arguments in the Redis unified request protocol
     * @var        string
     */
    //public const CRLF = "\r\n";

    /**
     * Constructor
     */
    public function __construct($params = array())
    {

        log_message('debug', 'Redis Class Initialized');

        $this->_ci = get_instance();
        $this->_ci->load->config('redis');

        // Check for the different styles of configs
        if (isset($params['connection_group'])) {
            // Specific connection group
            $this->redis_config = $this->_ci->config->item('redis_' . $params['connection_group']);
        } elseif (is_array($this->_ci->config->item('redis_default'))) {
            // Default connection group
            $this->redis_config = $this->_ci->config->item('redis_default');
        } else {
            // Original config style
            $this->redis_config = array(
                'host' => $this->_ci->config->item('redis_host'),
                'port' => $this->_ci->config->item('redis_port'),
                'password' => $this->_ci->config->item('redis_password'),
            );
        }
        //print_r($this->redis_config);
        //exit;
        // Connect to Redis

        //$this->_connection = @fsockopen($this->redis_config['host'], $this->redis_config['port'], $errno, $errstr, 3);
        /*$this->_connection = new Redis();
        $this->_connection->connect($this->redis_config['host'], $this->redis_config['port']);*/


        //$this->_connection = new Redis();
        //$this->_connection->connect($this->redis_config['host'], $this->redis_config['port']);
        //echo "Connection to server sucessfully";
        //set the data in redis string
        //$this->_connection->set("tutorial-namexxxx", "Redis tutorial");
        //$this->_connection->set("tutorial-name", "Redis ");
        // Get the stored data and print it
        //echo "Stored string in redis:: " .$this->_connection->get("tutorial-namexxxx");


        //print_r($this->_connection);
        //exit;

        if ($this->redis_config['enable']) {


            $this->_Status = 0;
            try {

                //echo $this->redis_config['host'];
                //exit;

                $this->_connection = new Redis();
                //$this->_connection->setOption(Redis:: OPT_READ_TIMEOUT, -1);
                $this->_connection->connect($this->redis_config['host'], $this->redis_config['port'], $this->redis_config['timeout']);
                if ($this->redis_config['password'] != '') {
                    $this->_connection->auth($this->redis_config['password']);
                }
                $this->_Status = 1;
            } catch (RedisException $ex) {

                show_error('Could not connect to Redis at ' . $this->redis_config['host'] . ':' . $this->redis_config['port']);

            } finally {

            }





            // Display an error message if connection failed
            if (!$this->_connection) {
                //show_error('Could not connect to Redis at ' . $this->redis_config['host'] . ':' . $this->redis_config['port']);
            } else {
                //$this->_connection->select(0);
            }

            // Authenticate when needed
            //$this->_auth($this->redis_config['password']);








        } else {

            $this->_Status = 0;
        }
    }

    public function formatDbKey($key)
    {
        $key = trim($key);
        $key = preg_replace('/[^a-zA-Z0-9_\s]/', '', $key);
        return str_replace(' ', '_', $key);
    }

    public function exists($key)
    {
        $key =  $this->formatDbKey($key);
        if ($this->redis_config['enable']) {
            return $this->_connection->exists($this->redis_config['prefix'].'_'.$key);
        } else {
            return false;
        }
    }

    public function get($key)
    {
        $key =  $this->formatDbKey($key);
        if ($this->redis_config['enable'] and $this->_Status == 1) {
            // echo $this->redis_config['prefix'] . '_' . $key.' -----';
            return unserialize($this->_connection->get($this->redis_config['prefix'] . '_' . $key));
        } else {
            return false;
        }
    }



    public function populateFillersCopy($queueName)
    {


        if ($this->redis_config['enable'] and $this->_Status == 1) {

            $queueName = $this->redis_config['prefix'] . '_' . $queueName;
            $globalFillerQueueName =  $this->redis_config['prefix'] . '_' .GLOBAL_FILLER_QUEUE_NAME;

            $lua = <<<LUA
            local source = KEYS[1]
            local destination = KEYS[2]
            local items = redis.call("LRANGE", source, 0, -1)
            for i = 1, #items do
                redis.call("RPUSH", destination, items[i])
            end
            redis.call("EXPIRE", destination, 3600)
            return #items
            LUA;

            /**
             * copying $globalFillerQueueName data into $queueName
             *  source is $globalFillerQueueName and destination is $queueName
             */
            $itemCount = $this->_connection->eval($lua, [$globalFillerQueueName, $queueName], 2);

            return $itemCount;
        } else {
            return false;
        }
    }

    public function populateFillers($queueName)
    {
        if ($this->redis_config['enable'] and $this->_Status == 1) {

            $queueName = $this->redis_config['prefix'] . '_' . $queueName;
            $globalFillerQueueName =  $this->redis_config['prefix'] . '_' .GLOBAL_FILLER_QUEUE_NAME;

            /**
             * copying $globalFillerQueueName data into $queueName
             *  source is $globalFillerQueueName and destination is $queueName
             */
            $this->_connection->copy($globalFillerQueueName, $queueName);
            $this->_connection->expire($queueName, 3600);
            return true;
        } else {
            return false;
        }
    }



    public function set($key, $value, $time = 0)
    {
        $key =  $this->formatDbKey($key);
        if ($this->redis_config['enable'] and $this->_Status == 1) {
            if ($time) {
                return $this->_connection->set($this->redis_config['prefix'] . '_' . $key, serialize($value), $time);
            } else {
                return $this->_connection->set($this->redis_config['prefix'] . '_' . $key, serialize($value), $this->redis_config['timeout']);
            }
        } else {
            return false;
        }
    }
    public function keys($key)
    {
        $key =  $this->formatDbKey($key);
        if ($this->redis_config['enable'] and $this->_Status == 1) {
            return $this->_connection->keys($this->redis_config['prefix'] . '_' . $key);
        } else {
            return false;
        }
    }
    public function del($key)
    {
        $key =  $this->formatDbKey($key);
        if ($this->redis_config['enable'] and $this->_Status == 1) {
            return $this->_connection->del($this->redis_config['prefix'] . '_' . $key);
        } else {
            return false;
        }
    }

    /**
     * @param $key
     * @return bool|int
     */
    public function del_cache_with_full_key($key)
    {
        $key =  $this->formatDbKey($key);
        if ($this->redis_config['enable'] and $this->_Status == 1) {
            return $this->_connection->del($key);
        } else {
            return false;
        }
    }
    /**
     *
     */
    public function delall()
    {
        $this->_connection->delete($this->_connection->keys('*'));
    }

    /**
     *
     */
    public function getlall()
    {
        $this->_connection->get($this->_connection->keys($this->redis_config['prefix'] . '_' . '*'));
    }

    public function getAllKeys($prefix)
    {
        $all_keys = $this->_connection->keys($this->redis_config['prefix'] . '_' . '*');
        $all_key = 0;
        $total_deleted_key = 0;

        foreach ($all_keys as $key => $val) {
            $all_key++;
            $match_string_len = strlen($this->redis_config['prefix'] . "_" . $prefix);
            if (substr($val, 0, $match_string_len)  == $this->redis_config['prefix'] . "_" . $prefix) {
                $this->del_cache_with_full_key($val);
                $total_deleted_key++;
            }
        }

        $data = array(
            "total_keys" => $all_key,
            "total_deleted_keys" => $total_deleted_key,
            "message" => ($total_deleted_key == 0) ? "no keys present!" : "success",
        );

        return  $data;
    }

    public function findAllKeysByPrefix($prefix)
    {
        $all_keys = $this->_connection->keys($this->redis_config['prefix'] . '_' . '*');
        $all_key = 0;
        $matched_key_by_prefix = 0;
        $all_key_arr = [];
        foreach ($all_keys as $key => $val) {
            $all_key++;
            $match_string_len = strlen($this->redis_config['prefix'] . "_" . $prefix);
            if (substr($val, 0, $match_string_len)  == $this->redis_config['prefix'] . "_" . $prefix) {
                $matched_key_by_prefix++;
                $all_key_arr['key_' . $key][] = $val;
            }
        }

        $data = array(
            "total_keys" => $all_key,
            "total_matched_keys" => $matched_key_by_prefix,
            "all_keys" => $all_key_arr,
            "message" => ($matched_key_by_prefix == 0) ? "no keys present!" : "success",
        );

        return  $data;
    }

    /**
     * @param $channel
     * @param $logJsondata
     * @return bool
     */
    public function publish($channel, $logJsondata)
    {
        //echo 'bc';
        //exit;
        //echo $this->_Status;
        if ($this->redis_config['enable'] and $this->_Status == 1) {

            if ($this->_connection->ping()) {
                if ($this->_connection->publish($channel, $logJsondata)) {

                    return true;
                } else {

                    return false;
                }
            } else {

                return false;
            }
        } else {

            return false;
            //echo $this->_Status;


        }
        //$this->_connection->delete($this->_connection->keys('*'));

    }

    /**
     * @return handle|Redis
     */
    public function subscribe()
    {
        //$this->_connection->delete($this->_connection->keys('*'));
        return $this->_connection;
    }

    public function hIncrBy($key, $data, $incrment)
    {

        $this->_connection->hIncrBy($key, $data, $incrment);
    }

    public function incr($key)
    {

        $this->_connection->incr($key);
    }


    // public function incr($key)
    // {
    //     $key = $this->formatDbKey($key);
    //     // echo 'key---'.$key;
    //     // exit;
    //     if ($this->redis_config['enable'] and $this->_Status == 1) {
    //         try {
    //             // Format the key with prefix
    //             $prefixed_key = $this->redis_config['prefix'] . '_' . $key;

    //             // echo 'prefixed_key---'.$prefixed_key;
    //             //     exit;

    //             // Check connection and reconnect if needed
    //             if (!$this->_connection->ping()) {
    //                 $this->_connection = new Redis();
    //                 $this->_connection->connect($this->redis_config['host'], $this->redis_config['port'], $this->redis_config['timeout']);
    //                 if ($this->redis_config['password'] != '') {
    //                     $this->_connection->auth($this->redis_config['password']);
    //                 }
    //                 if ($this->redis_config['database'] != '') {
    //                     $this->_connection->select($this->redis_config['database']);
    //                 }
    //             }

    //             // Perform the increment
    //             return $this->_connection->incr($prefixed_key);
    //         } catch (Exception $e) {
    //             log_message('error', 'Redis incr error: ' . $e->getMessage());
    //             return false;
    //         }
    //     } else {
    //         return false;
    //     }
    // }


    public function expire($key, $seconds)
    {
        if ($this->redis_config['enable'] and $this->_Status == 1) {
            try {
                $prefixed_key = $this->redis_config['prefix'] . '_' . $key;
                return $this->_connection->expire($prefixed_key, $seconds);
            } catch (Exception $e) {
                log_message('error', 'Redis expire error: ' . $e->getMessage());
                return false;
            }
        } else {
            return false;
        }
    }



    public function streamAdd($transactions, $logdata)
    {

        $this->_connection->xAdd($transactions, '*', $logdata);
    }

    public function streamGet($stream_name, $group_name, $consumer_name)
    {
        $this->_connection->xGroup('CREATE', $stream_name, $group_name, '0');
        $stream_messages = $this->_connection->xReadGroup(
            $group_name,
            $consumer_name,
            [$stream_name => '>']
        );

        if (!$stream_messages) {
            die('No messages' . PHP_EOL);
        }



        $transactions = $stream_messages[$stream_name] ?? null;

        if (!$transactions) {
            die('No transactions' . PHP_EOL);
        }
    }

    public function subscribe_pubsub($channel)
    {

        if ($this->redis_config['enable'] and $this->_Status == 1) {

            if ($this->_connection->ping()) {

                //                $redis = new Redis();
                //                $redis->connect($this->redis_config['host'], 6379);
                //
                //                $response = $redis->subscribe(array('apicalldata'), function($redis, $channel, $stdout) {
                //                    if ($stdout === 'quit') {
                //                        $redis->close();
                //                    }
                //                    return array('result' => $stdout);
                //                });

                $instance = $this->_connection;

                $this->_connection->subscribe(['api-call-data'], function ($instance, $channel, $message) {

                    die($message);
                    //return array('result' => $message);
                    // $instance->getLastError();
                });

                //$this->_connection->unsubscribe($subscribe_items);
                //$this->_connection->close();

                //echo 'fffff'; exit;




            } else {

                return false;
            }
        } else {

            return false;
            //echo $this->_Status;


        }
    }




    public function subscribeCallback($redis, $channel, $message)
    {
        echo "$channel => $message\n";
    }

    /**
     * @param $queueName
     * @param $jobData
     * @return bool|string
     */
    public function enqueueJob($queueName, $jobData)
    {
        //global $redis;

        $jobId = uniqid(); // Generate a unique job ID
        $jobData = json_encode($jobData);

        //$redis->rpush($queueName, $jobData);


        if ($this->redis_config['enable'] and $this->_Status == 1) {

            if ($this->_connection->ping()) {
                if ($this->_connection->rpush($queueName, $jobData)) {

                    return true;
                } else {

                    return false;
                }
            } else {

                return false;
            }
        } else {

            return false;
            //echo $this->_Status;


        }

        return $jobId;
    }

    /**
     * @param $queueName
     * @return bool|mixed|null
     */
    public function dequeueJob($queueName)
    {


        if ($this->redis_config['enable'] and $this->_Status == 1) {

            if ($this->_connection->ping()) {
                if ($jobData = $this->_connection->lpop($queueName)) {

                    if ($jobData === null) {
                        return null;
                    }

                    return json_decode($jobData, true);
                } else {

                    return false;
                }
            } else {

                return false;
            }
        } else {

            return false;
            //echo $this->_Status;


        }
    }
    /**
     * Destructor
     *
     * Kill the connection
     * @return    void
     */
    public function __destruct()
    {
        //if ($this->_connection) fclose($this->_connection);
    }
}
