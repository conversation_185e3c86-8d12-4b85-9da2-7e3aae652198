<?php

/*uat-cms*/

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}
/*
|--------------------------------------------------------------------------
| File and Directory Modes
|--------------------------------------------------------------------------
|
| These prefs are used when checking and setting modes when working
| with the file system.  The defaults are fine on servers with proper
| security, but you may wish (or even need) to change the values in
| certain environments (Apache running a separate process for each
| user, PHP under CGI with Apache suEXEC, etc.).  Octal values should
| always be used to set the mode correctly.
|
*/
define('FILE_READ_MODE', 0644);
define('FILE_WRITE_MODE', 0666);
define('DIR_READ_MODE', 0755);
define('DIR_WRITE_MODE', 0777);
/*
|--------------------------------------------------------------------------
| File Stream Modes
|--------------------------------------------------------------------------
|
| These modes are used when working with fopen()/popen()
|
*/

define('FOPEN_READ', 'rb');
define('FOPEN_READ_WRITE', 'r+b');
define('FOPEN_WRITE_CREATE_DESTRUCTIVE', 'wb'); // truncates existing file data, use with care
define('FOPEN_READ_WRITE_CREATE_DESTRUCTIVE', 'w+b'); // truncates existing file data, use with care
define('FOPEN_WRITE_CREATE', 'ab');
define('FOPEN_READ_WRITE_CREATE', 'a+b');
define('FOPEN_WRITE_CREATE_STRICT', 'xb');
define('FOPEN_READ_WRITE_CREATE_STRICT', 'x+b');


define('PROFILE_IMAGE_PATH', FCPATH . 'assets/users/profile/');
define('PROFILE_IMAGE_FOLDER', 'assets/user/profile/');

define('PROFILE_IMAGE_THUMB_WIDTH', '');
define('PROFILE_IMAGE_THUMB_HIGHT', '');
define('PROFILE_IMAGE_THUMB_SMALL_WIDTH', 150);
define('PROFILE_IMAGE_THUMB_SMALL_HIGHT', 150);
/*
|--------------------------------------------------------------------------
| File Upload paths
|--------------------------------------------------------------------------
*/
define("ABS_PATH", $_SERVER['DOCUMENT_ROOT'] . "/");
define('IMAGE_UPLOAD_PATH', ABS_PATH . 'assets/');
define('BANNER_IMAGE_UPLOAD_PATH', IMAGE_UPLOAD_PATH . 'banner/');
define('IMAGE_TYPE_ALLOWED', 'gif|jpg|png|jpeg');
define('EMAIL_FILE_TYPE_ALLOWED', 'jpg|png|jpeg|zip|pdf');
define('VOICE_FILE_TYPE_ALLOWED', 'avi|mp4|mp3');
define('ENCRYPTION', false);


/*
|--------------------------------------------------------------------------
| Date Time
|--------------------------------------------------------------------------
*/
define('VIEW_DATE_FORMATE', 'd/m/Y');

/*
|--------------------------------------------------------------------------
| Custom Constants (origin)
|--------------------------------------------------------------------------
*/
/*
|--------------------------------------------------------------------------
| Title & metadata Constants on the functions page
|--------------------------------------------------------------------------
*/

define('BITLAY_APIKEY', 'R_d4a7791e4b784883b56b1a69859fa4c6');
define('BITLAY_USERID', 'ashu004');

define('HTTP_OK', '200');
define('HTTP_FALSE', '400');
define('HTTP_FAILED', '500');

define('HTTP_ERROR', '403');

define('APPOINTMENT_STATUS', ['0' => "Acceptance Pending", '1' => "Accepted But Payment Verifivation Pending", '2' => "Verified But Consultation Pending", '3' => "WhatsApp Called But not Marked Completed", '4' => "WhatsApp ReCalled But not Marked Completed", '5' => "Consultation Completed", '6' => "Request Rejected"]);

define('PAYMENT_STATUS', ['0' => "Pending", '1' => "Accepted", '2' => "Rejected"]);


define('RAZORPAY_LIVE_KEY_ID', '***********************');
define('RAZORPAY_LIVE_KEY_SECRET', 'SSJlakZUAFqq13pdXyoL5qgd');

define('RAZORPAY_DEV_KEY_ID', 'rzp_test_v0XPwVfvZ00HLh');
define('RAZORPAY_DEV_KEY_SECRET', 'oxVajNLGCUEpITmZ0u2rIvSY');


define('DLT_FLOW_TELEMED', [
    'APT_NEW' => ['ID' => "609936d265bcea76750019f2", 'DVP1' => 'Request_No', 'DVP2' => 'Patient_Name', 'DVP3' => 'Patient_Age', 'DVP4' => 'Patient_Gender', 'DVP5' => 'Schedule_Day', 'DVP6' => 'aptSlotStartTime', 'DVP7' => 'aptSlotEndTime', 'DVP8' => 'Manage_Location'],
    'APT_ACCEPT' => ['ID' => "60992d653e3caa223e33c43a", 'DVP2' => 'Appointment_Fees', 'DVP3' => 'Payment_Method_Name', 'DVP4' => 'Instruction_Of_Payment_Or_Link_Phone_Number_With_details', 'DVP5' => 'Payment_Proof_Upload_Link', 'DVP6' => 'Doctor_Full_Name'],
    'APT_REJECT' => ['ID' => "609932374af69d0c065dd7ec", 'DVP1' => 'Request_No', 'DVP2' => 'Doctor_Profile_link', 'DVP3' => 'Doctor_Full_Name'],
    'APT_FREE' => ['ID' => "6099302c784a8f4f037e98d7", 'DVP1' => 'Request_No', 'DVP2' => 'Appointment_Request_Date', 'DVP3' => 'Appointment_Request_Time_Slot', 'DVP4' => 'Doctor_Full_Name'],
    'PROOF_UPLOADED' => ['ID' => "60993421c359f463696a5143", 'DVP1' => 'Request_No', 'DVP2' => 'Econsultation_Location'],
    'PROFILE_SETUP' => ['ID' => "6026164ab7589a5f1e2143a0", 'DVP1' => 'Doctor_Full_Name', 'DVP2' => 'Doctor_Profile_link']
]);


define('REFERRAL_TYPE', ['1' => 'Discussion', '2' => 'Referral', '3' => 'Discussed & Referral']);
define('CASE_URGENCY', ['0' => 'General', '1' => 'General', '2' => 'Critical', '3' => 'Advance']);
define('REFERRAL_DISCUSSION_STATUS', ['1' => 'Request Initiated', '2' => 'Request Accepted', '3' => 'In-Progress/Ongoing', '4' => 'Completed', '5' => 'Re Initiated', '6' => 'Patient Visited', '7' => 'Patient Not Visited', '8' => 'Rejected by Client', '9' => 'Rejected by Clirnet', '10' => 'Rejected by Clirnet', '11' => 'Case Closed', '12' => 'Deleted']);

/* message environment */


define('sms_url', 'https://msg.clirnet.com/api/');
define('sms_email', '<EMAIL>');
define('sms_password', '4efb6941082101abd22c268969f5bb4bb');



define('shortlink_url', 'https://admin.clrn.in/api/');
define('shortlink_email', '<EMAIL>');
define('shortlink_password', '4efb6941082101abd22c268969f5bb4bb');



define('crm_url', 'https://crm.clirnet.com/api/');
define('crm_email', '<EMAIL>');
define('crm_password', '4efb6941082101abd22c268969f5bb4bb');

define("USER_DETAIL_CACHE_REBUILD_CRM_URL", "https://crm.clirnet.com/api/user-data-update/");


define('doctube_url', 'https://api.doctube.com/apiV3/');
define('doctube_email', '<EMAIL>');
define('doctube_password', '221c1a241c5164dc8af9632012d299e9');
/* doctube environment */



define('clirnet_url', 'https://api.clirnet.com/rnv8/');



define('session_reserve_email', 43);
define('session_selfie_email', 18);
define('session_selfie_whatsapp', 163);
/* session notification templates live */

define('otp_template_id', "6752a964d6fc054cc57062b2");

define('DB_INSTANCE_SELECT', 'clirnetapp:asia-south1:clirnet-db-mysql8');
define('DB_SELECT_NAME', 'clirnetDBmain');
define('DB_SELECT_USER', 'uat_cms');
define('DB_SELECT_PASS', '44sGIeTHJskMebWx');

define('DB_INSTANCE_INSERT', 'clirnetapp:asia-south1:clirnet-db-mysql8');
define('DB_INSERT_NAME', 'clirnetDBmain');
define('DB_INSERT_USER', 'uat_cms');
define('DB_INSERT_PASS', '44sGIeTHJskMebWx');

define('DB_INSTANCE_REPORT', 'clirnetapp:asia-south1:clirnet-db-mysql8');
define('DB_REPORT_NAME', 'clirnetDBmain');
define('DB_REPORT_USER', 'uat_cms');
define('DB_REPORT_PASS', '44sGIeTHJskMebWx');

define('DB_INSTANCE_CRM', 'clirnetdev.mysql.database.azure.com');
define('DB_CRM_NAME', 'crm-db');
define('DB_CRM_USER', 'gk7zy7ku8rxpw5ovv0ku');
define('DB_CRM_PASS', 'pscale_pw_2Iij80D4FxnTRDhAVH8zpTqVODHRrZu8iYMtHm4ki68');


// define('DSN_SELECT', 'mysql:unix_socket=/cloudsql/clirnetapp:asia-south1:clirnet-db-mysql8;dbname=clirnetDBmain');
// define('DSN_INSERT', 'mysql:unix_socket=/cloudsql/clirnetapp:asia-south1:clirnet-db-mysql8;dbname=clirnetDBmain');
// define('DSN_REPORT', 'mysql:unix_socket=/cloudsql/clirnetapp:asia-south1:clirnet-db-mysql8;dbname=clirnetDBmain');

define('DSN_SELECT', 'mysql:host=*************;dbname=clirnetDBmain');
define('DSN_INSERT', 'mysql:host=*************;dbname=clirnetDBmain');
define('DSN_REPORT', 'mysql:host=*************;dbname=clirnetDBmain');
define('DSN_CRM', 'mysql:host=aws.connect.psdb.cloud;dbname=crm-db');

define('BUILD_CAMPAIGN_URL', 'https://uat-apigl.clirnet.com/campaign/generateCampaignQueue');
define('GLOBAL_FILLER_QUEUE_NAME', 'globalFillerQueue');

// define('REDIS_INSTANCE', '********'); //*************  //********
// define('REDIS_PORT', '6379');
// define('REDIS_PASS', '');
// define('REDIS_TIMEOUT', '2628000');
// define('REDIS_PREFIX', 'production');
// define('REDIS_ENABLE', TRUE);

define('REDIS_INSTANCE', 'redis-17530.fcrce171.ap-south-1-1.ec2.redns.redis-cloud.com'); //*************  //********
define('REDIS_PORT', '17530');
define('REDIS_PASS', 'xomhSsrKaDY1AJ3udDhyWwQVUKtKLwzE');
define('REDIS_TIMEOUT', '2628000');
define('REDIS_PREFIX', 'uat_');
define('REDIS_ENABLE', false);

define('GCP_JSON_TYPE', 'service_account');
define('GCP_JSON_PROJECT_ID', 'clirnetapp');
define('GCP_JSON_PRIVATE_KEY_ID', '9e142af7a77480b45081f7d5141327ab04e3b9af');
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
define('GCP_JSON_CLIENT_EMAIL', '<EMAIL>');
define('GCP_JSON_CLIENT_ID', '117224492478485157949');
define('GCP_JSON_AUTH_URI', 'https://accounts.google.com/o/oauth2/auth');
define('GCP_JSON_TOKEN_URI', 'https://oauth2.googleapis.com/token');
define('GCP_JSON_AUTH_PROVIDER_X509_CERT_URL', 'https://www.googleapis.com/oauth2/v1/certs');
define('GCP_JSON_CLIENT_X509_CERT_URL', 'https://www.googleapis.com/robot/v1/metadata/x509/clirnetmedwiki%40clirnetapp.iam.gserviceaccount.com');


define("LOG_WRITER_GCP_JSON", '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');

define('STORAGE_IMG_SRC', 'https://storage.googleapis.com/');
define('CDN_IMG_SRC', 'https://clirnet-cms.b-cdn.net/');

define('monolog_projectId', 'clirnetapp');
define('monolog_auth_json', 'clirnetapp-E2lwAOz5LRw1Tq7.json');

define('bigquery-api-call-data', 'clirnetapp.api_call_data.api_call_data');
//clirnet-dev.api_call_logs.api_call_data
define('bigquery-analytics-call-data', 'clirnetapp.analytics_call_data.api_call_data');
define('bigquery-banner-call-data', 'clirnetapp.banner_call_data.api_call_data');

/* End of file constants.php *//* Location: ./application/config/constants.php */

/* session notification templates dev */
define('session_reserve_sms_dev', 24);
define('book_slot_email', 48);
define('book_mentor_join_email', 49);
/* session notification templates uat */
define('session_reserve_sms_uat', 24);
define('session_reserve_sms', 25);
define('session_reserve_email_onesignal', 18);
define('session_recording_request_sms', 8);
define('session_recording_request_email', 48);

define('manualloginsms', 11);
define('autologinsms', 12);
define('manualloginemail', 22);
define('autologinemail', 21);
/* session notification templates live */
define('session_certificate', 9);
define('session_certificate_whatsapp', 174);
/*SESSION CERTIFICATE */

/** static image  */
define('docimg', 'https://storage.googleapis.com/medwiki/43_server/images/doctor.svg'); //'https://storage.googleapis.com/medwiki/43_server/images/images.png');
/** static image*/
define('rest_email_dev', 49);
define('rest_email_uat', 49);
define('rest_email_live', 42);
define('rest_email', 42);
/* clirnet Data ML environment */
define('dataML1_url_dev', 'https://asia-south1-clirnet-dev.cloudfunctions.net/personalize_v2');
define('dataML2_url_dev', 'https://asia-south1-clirnet-dev.cloudfunctions.net/user_recommendation_v4');
define('dataML1_url_uat', 'https://asia-south1-clirnetapp.cloudfunctions.net/clirnet-python-personalize_v2');
define('dataML2_url_uat', 'https://us-central1-clirnetapp.cloudfunctions.net/clirnet-python-user-recommendation-v4');
define('dataML1_url', 'https://asia-south1-clirnetapp.cloudfunctions.net/clirnet-python-personalize_v2');
define('dataML2_url', 'https://us-central1-clirnetapp.cloudfunctions.net/clirnet-python-user-recommendation-v4');
define('dev_training_cert_api_url', 'https://admin.clirdev.com/clirnet-admin-ci/admin/generatecertificate/training');
define('uat_training_cert_api_url', 'https://admin.clirdev.com/clirnet-admin-ci/admin/generatecertificate/training');
define('training_cert_api_url', 'https://admin.clirnet.com/admin/generatecertificate/training');
define('prod_search', 'https://asia-south1-clirnetapp.cloudfunctions.net/cl-suggest-search-prod/clirnet_search/clirnet_search');
define('LEADS_INSERT_URL', 'https://uat-apigl.clirnet.com/insertLeads');

define('jwt_key', 'this@.0is@.1secret@.2key@.3for@.4jwt@.5');
define('version_check', json_encode(['rjsa', 'rjsw']));
define('COMMUNITY_ENABLED', true);
define('GCP_UPLOAD_IMAGE_BUCKET_NAME', 'web-uat-image');

/* clirnet Data ML environment */
// =========================== for crm redis =========================== //
define('CRM_REDIS_INSTANCE', 'redis-14423.fcrce171.ap-south-1-1.ec2.redns.redis-cloud.com'); //*************  //********
define('CRM_REDIS_PORT', '14423');
define('CRM_REDIS_PASS', '8GAoVpxy49E3BRjpUUkJpABOtqYuoidB');
define('CRM_REDIS_TIMEOUT', '2628000');
define('CRM_REDIS_PREFIX', 'crm_prod:');
define('CRM_REDIS_ENABLE', true);

define("USER_DETAIL_CACHE_REBUILD_CRM_URL", "https://crm.clirnet.com/api/user-data-update/");

define("CIPHERING_VALUE", "AES-128-CTR");
define("ENCRYPTION_KEY", "Clirnet12345@Aa@");

define("USER_INFO_CACHE_KEY", "user_info_");
// =========================== /for crm redis =========================== //

define('siam_test', 'siam_reactnative_image');
define('default_reactnative_image_folder', 'default_reactnative_image');
