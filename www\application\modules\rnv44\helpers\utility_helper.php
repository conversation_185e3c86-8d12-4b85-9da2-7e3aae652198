<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}
$CI = &get_instance();

if (!function_exists('sponsor_detail')) {
    function sponsor_detail($sponsor_id)
    {
        $CI = &get_instance();
        $CI->load->library('Myredis');
        $cachename = "sponsor_detail_" . $sponsor_id;
        if ($CI->myredis->get($cachename)) {
            $data_row = $CI->myredis->get($cachename);
        } else {
            $qry = "SELECT
            client_master_id,
            client_name,
            client_logo
            FROM  client_master
            WHERE
            client_master_id=" . $sponsor_id . "
            ";
            //echo $qry;
            $query = $CI->db->query($qry);
            $data_row = $query->result_array();
            $CI->myredis->set($cachename, $data_row);
        }
        return $data_row;
    }
}

if (!function_exists('get_user_env_cached')) {
    function get_user_env_cached($user_master_id)
    {
        $CI = &get_instance();
        $CI->load->library('Myredis');
        $cachename = "user_env_" . $user_master_id;
        if ($CI->myredis->get($cachename)) {
            $data_row = $CI->myredis->get($cachename);
        } else {
            $qry = "SELECT
            country_code
            FROM `user_master`
            WHERE `user_master_id` = " . $user_master_id . "";
            $query = $CI->db->query($qry);
            $data_row = $query->row();
            $CI->myredis->set($cachename, $data_row);
        }
        if ($data_row->country_code == 99) {
            $env = 'IN';
        } else {
            $env = 'GL';
        }
        return $env;
    }
}
if (!function_exists('get_user_env')) {
    function get_user_env($user_master_id)
    {
        //        $CI =& get_instance();
        //
        //        $qry = "SELECT
        //        country_code
        //        FROM `user_master`
        //        WHERE `user_master_id` = " . $user_master_id . "";
        //
        //        $query = $CI->db->query($qry);
        //        $data_row = $query->row();
        //
        //        if ($data_row->country_code == 99) {
        //            $env = 'IN';
        //        } else {
        //            $env = 'GL';
        //        }
        //        return $env;
        $CI = &get_instance();
        //echo 'user_master_id: '.$user_master_id;
        // $CI->load->library('Myredis');
        // $cachename = "user_env_" . $user_master_id;
        $CI->load->library('CrmMyredis');
        $cachename = USER_INFO_CACHE_KEY . $user_master_id;
        // echo $cachename;
        // exit;
        if ($CI->crmmyredis->exists($cachename)) {
            $locData = $CI->crmmyredis->get($cachename)["loc"];
            $country_code =  getUserLocData($locData)["country_code"];
            // echo 'country_code: '.$country_code;
        } else {
            $qry = "SELECT
            country_code
            FROM `user_master`
            WHERE `user_master_id` = " . $user_master_id . "";
            $query = $CI->db->query($qry);
            $data_row = $query->row();
            $country_code = $data_row->country_code;
            // $CI->myredis->set($cachename, $data_row);
        }
        if ($country_code == 99) {
            $env = 'IN';
        } else {
            $env = 'GL';
        }
        return $env;
    }
}
if (!function_exists('get_user_env_by_ch_region')) {
    function getUserLocData($locString)
    {
        $locData = array(
            "country_code" => null,
            "zone_id" => null,
            "state_id" => null,
            "city_id" => null
        );
        if (!empty($locString) && $locString != '') {
            $locDataArr = explode(",", $locString);
            if (isset($locDataArr[0]) && $locDataArr[0] != '') {
                $locData["country_code"] = $locDataArr[0];
                if ($locData["country_code"] == 0) {
                    $locData["country_code"] = 99;
                }
            }
            if (isset($locDataArr[1]) && $locDataArr[1] != '') {
                $locData["zone_id"] = $locDataArr[1];
            }
            if (isset($locDataArr[2]) && $locDataArr[2] != '') {
                $locData["state_id"] = $locDataArr[2];
            }
            if (isset($locDataArr[3]) && $locDataArr[3] != '') {
                $locData["city_id"] = $locDataArr[3];
            }
        }
        return $locData;
    }
}
if (!function_exists('get_user_env_by_ch_region')) {
    function get_user_env_by_ch_region()
    {
        $CI = &get_instance();
        if ($CI->input->get_request_header('X-Ch-Region') == 'IN') {
            $env = 'IN';
        } else {
            $env = 'GL';
        }
        return $env;
    }
}

if (!function_exists('getContentCampiagn')) {
    function getContentCampiagn(
        $user_master_id = '',
        $client_ids = '',
        $content_type_id = '',
        $content_type = ''
    ) {
        $CI = &get_instance();
        $CI->load->library('CrmMyredis');
        $returnData = array(
            "banner_dispaly" => true,
            "creative_data" => array()
        );
        // checking exclude campaign
        $excludeSql = "SELECT
                            exl.ids
                        FROM
                            clirbanner_exclude_list exl
                        WHERE
                            exl.type = '{$content_type}'
        ";
        $excludeCampaign = $CI->db->query($excludeSql);
        $excludeResult = $excludeCampaign->row();
        if (!empty($excludeResult->ids)) {
            $excludeIds = explode(',', $excludeResult->ids);
            $excludeIdsAssoc = array_combine($excludeIds, $excludeIds);
            if (array_key_exists($content_type_id, $excludeIdsAssoc)) {
                $returnData['banner_dispaly'] = false;
                return $returnData;
            }
        }
        if ($client_ids) {
            $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                return "FIND_IN_SET('$x', nb.client_id)";
            }, explode(',', $client_ids))) . ')';
        }
        $cachename = USER_INFO_CACHE_KEY . $user_master_id;
        $cacheExists = false;
        $userDetail = null;
        if ($CI->crmmyredis->exists($cachename)) {
            $cacheExists = true;
            $userDetail = $CI->crmmyredis->get($cachename);
        }
        $user_type_id = get_master_user_type_id($user_master_id);
        //get user speciality
        $resultInt = null;
        if ($cacheExists) {
            if (!empty($userDetail) && $userDetail["is_id"] != '') {
                $interest_spec_ids = explode(",", $userDetail["is_id"]);
                $resultInt = array();
                foreach ($interest_spec_ids as $key => $val) {
                    $resultInt[] = array("specialities_id" => $val);
                }
            }
        } else {
            $sqlInt = "SELECT
                        specialities_id
                    FROM
                        user_to_interest
                    WHERE
                        user_master_id = {$user_master_id}";
            //echo $sqlInt; exit;
            $queryInt = $CI->db->query($sqlInt);
            $resultInt = $queryInt->result_array();
        }
        if (!empty($resultInt)) {
            $spIdsArray = array_unique(array_column($resultInt, 'specialities_id'));
            $specialityIds = implode(",", $spIdsArray);
            $specialities = "  AND nbtsp.speciality_id IN (" . $specialityIds . ")";
        } else {
            $specialityIds = '';
            $specialities = "";
        }
        // getting user group
        $resultUserGroup = null;
        if ($cacheExists) {
            if (!empty($userDetail) && $userDetail["grp"] != '') {
                $group_ids = explode(",", $userDetail["grp"]);
                $resultUserGroup = array();
                foreach ($group_ids as $key => $val) {
                    $resultUserGroup[] = array("group_id" => $val);
                }
            }
        } else {
            $sqlUserGroup = "SELECT
                                *
                            FROM
                                user_to_usergroup
                            WHERE
                                user_master_id = {$user_master_id}";
            $queryUserGroup = $CI->db->query($sqlUserGroup);
            $resultUserGroup = $queryUserGroup->result_array();
        }
        if (!empty($resultUserGroup)) {
            $userGroupIds = array_unique(array_column($resultUserGroup, 'group_id'));
            $userGroups = implode(",", $userGroupIds);
        } else {
            $userGroups = "";
        }
        if ($userGroups) {
            $sql = "SELECT
                        nb.campaign_id
                    FROM
                        clirbanner_master_campaign nb
                    LEFT JOIN clirbanner_campaign_to_speciality nbtsp on nb.campaign_id = nbtsp.campaign_id
                    LEFT JOIN clirbanner_campaign_to_user_type nbtutyp on nb.campaign_id = nbtutyp.campaign_id
                    LEFT JOIN clirbanner_campaign_to_content nbtc on nbtc.campaign_id = nb.campaign_id
                    LEFT JOIN campaing_to_group cmTgr on ( cmTgr.campaign_id = nb.campaign_id )
                    WHERE
                        nb.status = 3
                        and nbtc.type_id = {$content_type_id}
                        and nbtc.type = '{$content_type}'
                        and nbtutyp.user_type_id = {$user_type_id}
                        AND (cmTgr.group_id in (" . $userGroups . ") or cmTgr.campaign_id is null)
               ";
        } else {
            $sql = "SELECT
                        nb.campaign_id
                    FROM
                        clirbanner_master_campaign nb
                    LEFT JOIN clirbanner_campaign_to_speciality nbtsp on nb.campaign_id = nbtsp.campaign_id
                    LEFT JOIN clirbanner_campaign_to_user_type nbtutyp on nb.campaign_id = nbtutyp.campaign_id
                    LEFT JOIN clirbanner_campaign_to_content nbtc on nbtc.campaign_id = nb.campaign_id
                    LEFT JOIN campaing_to_group cmTgr on ( cmTgr.campaign_id = nb.campaign_id )
                    WHERE
                        nb.status = 3
                        and nbtc.type_id = {$content_type_id}
                        and nbtc.type = '{$content_type}'
                        and nbtutyp.user_type_id = {$user_type_id}
                        AND (cmTgr.campaign_id is NULL)
                ";
        }
        $queryBanner = $CI->db->query($sql);
        $resultBanner = $queryBanner->result_array();
        if (!empty($resultBanner)) {
            foreach ($resultBanner as $key => $campaigns) {
                foreach ($campaigns as $campaignId) {
                    $result[] = buildCampaignJSON($campaignId);
                }
            }
            $returnData['banner_dispaly'] = true;
            $returnData['creative_data'] = $result;
            return $returnData;
        } else {




            if ($content_type == 'video_archive') {


                $jayParsedAry = [
                                    [
                                          "Type" => "video",
                                          "300x200" => [
                                             "CrId" => 3592,
                                             "Dur" => "20",
                                             "CrUrl" => "https://clirnet-cms.b-cdn.net/medwiki/43_server/video/SunpharmaEnglish.mp4"
                                          ],
                                          "CampaignId" => 6075,
                                          "position" => "video",
                                          "CTA" => [
                                                "Type" => "ext",
                                                "Behave" => "pop",
                                                "ActionPath" => "https://doctor.clirnet.com/#/clinical-video/detail/8482/",
                                                "Title" => "Watch Again",
                                                "ButtonText" => "Watch Again"
                                             ]
                                       ]
                                 ];


            } else {

                $jayParsedAry = [];
            }



            $returnData['banner_dispaly'] = true;
            $returnData['creative_data'] = $jayParsedAry;
            //return $returnData;
        }
        return $returnData;
    }
}
if (!function_exists('buildCampaignJSON')) {
    function buildCampaignJSON($campaignId)
    {
        $CI = &get_instance();
        // $cacke_key = "campaign_{$campaignId}";
        // if($CI->myredis->get( $cacke_key )) {
        //     return true;
        // }
        $creativeSql = "SELECT
                            CONCAT(cmc.creative_width, 'x', cmc.creative_height) as creative_dimention,
                            cmc.creative_id as CrId,
                            cmc.file as CrUrl,
                            cmc.type as Type,
                            cmc.video_duration as Dur,
                            cmc.internal_type_id AS ActionID,
                            CASE
                                WHEN mc.is_consent = 1 THEN 'con'
                                WHEN cmc.CTA = 'external_url' THEN 'ext'
                                WHEN cmc.CTA = 'internal_url' THEN 'int'
                                ELSE 'default'
                            END AS CTA_Type,
                            CASE
                                WHEN mc.is_consent = 1
                                	THEN mc.consent_value
                                ELSE NULL
                            END AS ConVal,
                            CASE
                                WHEN cmc.CTA = 'internal_url' THEN 'base'
                                WHEN cmc.CTA = 'external_url' OR cmc.CTA = 'pdf_open' THEN 'pop'
                                WHEN cmc.CTA = 'file_download' THEN 'download'
                                ELSE NULL
                            END AS Behave,
                            CASE
                                WHEN cmc.CTA = 'external_url' THEN cmc.external_URL
                                WHEN cmc.CTA = 'internal_url' THEN cmc.internal_type
                                WHEN cmc.CTA = 'file_download' OR cmc.CTA = 'pdf_open' THEN cmc.CTAupload_file
                                ELSE NULL
                            END AS ActionPath,
                            cmc.CTA_button AS Title,
                            cmc.CTA_text AS ButtonText
                        FROM
                            clirbanner_master_creative cmc
                        LEFT JOIN
                            clirbanner_campaign_to_creative cTc on cTc.creative_id = cmc.creative_id
                        LEFT JOIN
                            clirbanner_master_campaign AS mc ON mc.campaign_id = cTc.campaign_id
                        WHERE
                            cTc.campaign_id = {$campaignId}
                        GROUP BY
	                        creative_dimention,
                            cmc.creative_id,
                            cmc.file,
                            cmc.type,
                            cmc.video_duration,
                            cmc.internal_type_id,
                            mc.is_consent,
                            mc.consent_value,
                            cmc.CTA,
                            cmc.external_URL,
                            cmc.internal_type,
                            cmc.CTAupload_file,
                            cmc.CTA_button,
                            cmc.CTA_text
                        ORDER BY
                            cmc.creative_id asc
        ";
        $queryCreative = $CI->db->query($creativeSql);
        $creativeResult = $queryCreative->result_array();
        $keyByCreativeDimention = null;
        if (!empty($creativeResult)) {
            if (!empty($creativeResult[0]['CTA_Type'])) {
                $CTAData["Type"] = $creativeResult[0]['CTA_Type'];
            }
            if (!empty($creativeResult[0]['Behave'])) {
                $CTAData["Behave"] = $creativeResult[0]['Behave'];
            }
            if (!empty($creativeResult[0]['ActionPath'])) {
                $CTAData["ActionPath"] = $creativeResult[0]['ActionPath'];
            }
            if (!empty($creativeResult[0]['Title'])) {
                $CTAData["Title"] = $creativeResult[0]['Title'];
            }
            if (!empty($creativeResult[0]['ButtonText'])) {
                $CTAData["ButtonText"] = $creativeResult[0]['ButtonText'];
            }
            if ($creativeResult[0]['ActionID'] != '') {
                $CTAData["ActionID"] = $creativeResult[0]['ActionID'];
            }
            if (!empty($creativeResult[0]['ConVal'])) {
                $CTAData["ConVal"] = $creativeResult[0]['ConVal'];
            }
            foreach ($creativeResult as $key => $value) {
                $keyByCreativeDimention["Type"] = $value['Type'];
                $keyByCreativeDimention[$value['creative_dimention']]["CrId"] = $value['CrId'];
                $keyByCreativeDimention[$value['creative_dimention']]["Dur"] = $value['Dur'];
                $keyByCreativeDimention[$value['creative_dimention']]["CrUrl"] = change_img_src($value['CrUrl']);
            }
            $keyByCreativeDimention["CampaignId"] = (int)$campaignId;
            $keyByCreativeDimention["CTA"] = $CTAData;
            // $this->myredis->set( $cacke_key , $keyByCreativeDimention);
        }
        // echo "<pre>";print_r(json_encode($keyByCreativeDimention));die;
        return $keyByCreativeDimention;
    }
}

if (!function_exists('get_user_env_id_by_ch_region')) {
    function get_user_env_id_by_ch_region()
    {
        $CI = &get_instance();
        if ($CI->input->get_request_header('X-Ch-Region') == 'IN') {
            $env = 1;
        } else {
            $env = 2;
        }
        return $env;
    }
}
if (!function_exists('get_user_content_status')) {
    function get_user_content_status($content_id, $content_type, $user_master_id)
    {
        $CI = &get_instance();
        if (($content_id != '') && ($content_type != '') && ($user_master_id != '')) {
            $sql = "SELECT
                CASE
                    WHEN
                        puc.status IS NULL THEN (select payment_status FROM order_master WHERE user_master_id = {$user_master_id} and type_id = {$content_id} and type = {$content_type} order by id DESC limit 1)
                    ELSE
                        puc.status
                    END AS
                        status,puc.type_id
                FROM
                    payment_user_to_content puc
                WHERE
                    puc.status = 3
                AND
                    puc.type_id = {$content_id}
                AND
                    puc.type = {$content_type}
                AND
                    puc.user_master_id = {$user_master_id}";
            // echo $sql; exit;
            $query = $CI->db->query($sql);
            //print_r($query); exit;
            if ($query) {
                $row = $query->row();
                if ($row->status) {
                    return (string)$row->status;
                } else {
                    return null;
                }
            } else {
                return null;
            }
            // echo "<pre>";print_r($query->row());die;
            //return (!empty($query->row()) && $query->num_rows() == 1) ? 3 : null;
        } else {
            return null;
        }
    }
}
if (!function_exists('get_a_content_price')) {
    function get_a_content_price($content_id, $content_type, $env_id)
    {
        $CI = &get_instance();
        if (($content_id != '') && ($content_type != '') && ($env_id != '')) {
            $sql = "SELECT
                   cTenv.price
                FROM
                    content_to_env cTenv
                WHERE
                    cTenv.type_id = {$content_id}
                AND
                    cTenv.type = {$content_type}
                AND
                    cTenv.env = {$env_id}
               ";
            // echo $sql; exit;
            $query = $CI->db->query($sql);
            return ($query->num_rows() == 1) ? $query->row()->price : null;
        } else {
            return null;
        }
    }
}
if (!function_exists('get_user_env_id')) {
    function get_user_env_id($user_master_id)
    {
        $CI = &get_instance();
        $CI->load->library('CrmMyredis');
        // $cachename = "user_env_id_" . $user_master_id;
        $cachename = USER_INFO_CACHE_KEY . $user_master_id;
        if ($CI->crmmyredis->exists($cachename)) {
            $env_id = $CI->crmmyredis->get($cachename)["env"];
        } else {
            $qry = "SELECT
            um.country_code ,
            etc.env_id
            FROM user_master um
            left  join env_to_country as etc on etc.country_id = um.country_code
            WHERE um.user_master_id = " . $user_master_id . "";
            //echo $qry; exit;
            $query = $CI->db->query($qry);
            $data_row = $query->row();
            $env_id = $data_row->env_id;
            // $CI->myredis->set($cachename, $data_row);
        }
        return $env_id;
    }
}
if (!function_exists('change_img_src')) {
    function change_img_src($image)
    {
        if (is_string($image)) {
            $ts = strpos($image, STORAGE_IMG_SRC);
            if (isset($ts)) {
                $CI = &get_instance();
                $image = str_replace(STORAGE_IMG_SRC, CDN_IMG_SRC, $image);
                return $image;
            } else {
                return $image;
            }
        } elseif (is_array($image)) {
            $returnArray = null;
            foreach ($image as $key => $value) {
                if (!empty($value)) {
                    $ts = strpos($value, STORAGE_IMG_SRC);
                    if (isset($ts)) {
                        $CI = &get_instance();
                        $image = str_replace(STORAGE_IMG_SRC, CDN_IMG_SRC, $value);
                        $returnArray[] =  $image;
                    } else {
                        $returnArray[] =  $image;
                    }
                }
            }
            return $returnArray;
        } else {
            return $image;
        }
    }
}
if (!function_exists('get_a_content_is_share_status')) {
    function get_a_content_is_share_status($content_id, $content_type)
    {
        $CI = &get_instance();
        // $CI->load->library('Myredis');
        // $cachename = "user_env_id_" . $user_master_id;
        if (empty($content_id) || empty($content_type)) {
            return null;
        }
        switch ($content_type) {
            case "1":
                $table_name = "knwlg_compendium_V1";
                $primary_key = "comp_qa_id";
                break;
            case "2":
                $table_name = "knwlg_sessions_V1";
                $primary_key = "session_id";
                break;
            case "3":
                $table_name = "knwlg_video_archive";
                $primary_key = "video_archive_id";
                break;
            case "4":
                $table_name = "training_master";
                $primary_key = "id";
                break;
            case "5":
                $table_name = "knwlg_gr_register";
                $primary_key = "gr_id";
                break;
            case "6":
                $table_name = "survey";
                $primary_key = "survey_id";
                break;
            case "9":
                $table_name = "epub_master";
                $primary_key = "epub_id";
                break;
            case "11":
                $table_name = "channel_master";
                $primary_key = "channel_master_id";
                break;
        }
        $qry = "SELECT
                is_share
            FROM {$table_name}
            WHERE  {$primary_key} = {$content_id}
            ";
        // echo $qry; exit;
        $query = $CI->db->query($qry);
        $data_row = $query->row();
        $share_status =  (!empty($data_row)) ? ($data_row->is_share) ? true : false : false;
        // echo "<pre>";print_r($d );die;
        return $share_status;
    }
}
if (!function_exists('get_user_package')) {
    function get_user_package($user_master_id, $module)
    {
        $CI = &get_instance();
        // =============== currency =================//
        $user_env = get_user_env_id($user_master_id);
        $key_value['currency'] = ($user_env == 1) ? "INR" : "USD";
        // =============== currency =================//
        $CI->load->library('CrmMyredis');
        $cachename = USER_INFO_CACHE_KEY . $user_master_id;
        if ($CI->crmmyredis->exists($cachename)) {
            $userData = $CI->crmmyredis->get($cachename);
            $locData = $userData["loc"];
            $country_code =  getUserLocData($locData)["country_code"];
            $row = array(
                "country_code" => $country_code,
                "status" => $userData["status"],
                "payment_package_id" => $userData["pkg_id"],
            );
        } else {
            $qry = "SELECT
                        um.status,
                        um.country_code,
                        up.payment_package_id
                    FROM user_master um
                    left join payment_user_to_package up on up.user_master_id = um.user_master_id
                    WHERE
                        um.user_master_id = " . $user_master_id . "";
            $query = $CI->db->query($qry);
            $row = $query->row_array();
        }
        if ($row['payment_package_id'] != '') {
            $qryPkg = "SELECT
            package_setting
            FROM payment_package
            WHERE
            id = " . $row['payment_package_id'] . "
            ";
            $queryPkg = $CI->db->query($qryPkg);
            $rowPkg = $queryPkg->row_array();
            $user_package_setting_array = (array)json_decode($rowPkg['package_setting'], true);
            foreach ($user_package_setting_array['menu'] as $value) {
                if ($value['module_name'] == 'explore_more') {
                    foreach ($value['sub_menu'] as $val) {
                        if ($val['module_name'] == $module) {
                            //                            if($val['module_name'] == 'training'){
                            //                                print_r($val); //exit;
                            //
                            //                            }
                            if ($val['premium'] == 1) {
                                // echo 'xxxxx'; exit;
                                $key_locked = 1;
                                $key_value['display_icon'] = $val['displayIcon'];
                                $key_value['content_access'] = false;
                            } else {
                                $key_locked = 0;
                                $key_value['display_icon'] = $val['displayIcon'];
                                $key_value['content_access'] = true;
                            }
                        }
                    }
                } else {
                    if ($value['module_name'] == $module) {
                        if ($value['premium'] == 1) {
                            $key_locked = 1;
                            $key_value['display_icon'] = $value['displayIcon'];
                            $key_value['content_access'] = true;
                        } else {
                            $key_locked = 0;
                            $key_value['display_icon'] = $value['displayIcon'];
                            $key_value['content_access'] = false;
                        }
                    }
                }
            }
            //echo $key_locked; exit;
            return $key_value;
        } else {
            $key_value['display_icon'] = false;
            $key_value['content_access'] = true;
            return $key_value;
        }
    }
}
if (!function_exists('get_open_user_package')) {
    function get_open_user_package($module)
    {
        $CI = &get_instance();
        // =============== currency =================//
        $user_env = get_user_env_id_by_ch_region();
        $key_value['currency'] = ($user_env == 1) ? "INR" : "USD";
        // =============== currency =================//
        $qry = "SELECT
        pp.id as payment_package_id
        FROM payment_package pp
        WHERE
        pp.package_name = 'standard-free'
        AND pp.env_id = {$user_env}
        ";
        $query = $CI->db->query($qry);
        $row = $query->row_array();
        if ($row['payment_package_id'] != '') {
            $qryPkg = "SELECT
            package_setting
            FROM payment_package
            WHERE
            id = " . $row['payment_package_id'] . "
            ";
            $queryPkg = $CI->db->query($qryPkg);
            $rowPkg = $queryPkg->row_array();
            $user_package_setting_array = (array)json_decode($rowPkg['package_setting'], true);
            foreach ($user_package_setting_array['menu'] as $value) {
                if ($value['module_name'] == 'explore_more') {
                    foreach ($value['sub_menu'] as $val) {
                        if ($val['module_name'] == $module) {
                            //                            if($val['module_name'] == 'training'){
                            //                                print_r($val); //exit;
                            //
                            //                            }
                            if ($val['premium'] == 1) {
                                // echo 'xxxxx'; exit;
                                $key_locked = 1;
                                $key_value['display_icon'] = $val['displayIcon'];
                                $key_value['content_access'] = false;
                            } else {
                                $key_locked = 0;
                                $key_value['display_icon'] = $val['displayIcon'];
                                $key_value['content_access'] = true;
                            }
                        }
                    }
                } else {
                    if ($value['module_name'] == $module) {
                        if ($value['premium'] == 1) {
                            $key_locked = 1;
                            $key_value['display_icon'] = $value['displayIcon'];
                            $key_value['content_access'] = true;
                        } else {
                            $key_locked = 0;
                            $key_value['display_icon'] = $value['displayIcon'];
                            $key_value['content_access'] = false;
                        }
                    }
                }
            }
            //echo $key_locked; exit;
            return $key_value;
        } else {
            $key_value['display_icon'] = false;
            $key_value['content_access'] = true;
            return $key_value;
        }
    }
}
if (!function_exists('get_master_user_type_id')) {
    function get_master_user_type_id($user_master_id)
    {
        $CI = &get_instance();
        $CI->load->library('CrmMyredis');
        // $CI->load->library('Myredis');
        // $cachename = "user_master_type_" . $user_master_id;
        $cachename = USER_INFO_CACHE_KEY . $user_master_id;
        //echo 'cache name: '.$cachename;
        if ($CI->crmmyredis->exists($cachename)) {
            $master_user_type_id = $CI->crmmyredis->get($cachename)["type"];
        } else {
            $CI->db->select('master_user_type_id');
            $CI->db->where('user_master_id', $user_master_id);
            $sql = $CI->db->get('user_master');
            $result = $sql->result();
            if (!empty($result)) {
                $master_user_type_id = $result[0]->master_user_type_id;
            } else {
                $master_user_type_id = false;
            }
            // $CI->myredis->set($cachename, $result);
        }
        return $master_user_type_id;
    }
}
if (!function_exists('build_internal_and_is_draft_query')) {
    function build_internal_and_is_draft_query($user_master_id, $db_alies, $db_field_name)
    {
        $CI = &get_instance();
        $CI->db->select('master_user_type_id');
        $CI->db->where('user_master_id', $user_master_id);
        $sql = $CI->db->get('user_master');
        // echo "<pre>";print_r($sql->result());die();
        if ($sql->num_rows() == 1) {
            $master_user_type_id = $sql->result()[0]->master_user_type_id;
        } else {
            $master_user_type_id = false;
        }
        // check for user_type inetrnal or not
        if ($master_user_type_id == false) {
            $query = $db_alies . '.' . $db_field_name . ' = ';
        } elseif ($master_user_type_id == 5) { //internal, then fetch data what is active(==3) and draft(==5) no needmof cache
            $query = $db_alies . '.' . $db_field_name . ' in(3,5)';
        } else {
            $query = $db_alies . '.' . $db_field_name . ' = 3';
        }
        return $query;
    }
}

// if (!function_exists('is_logged_in_profile')) {
//     function is_logged_in_profile()
//     {
//         $CI = &get_instance();
//         $is_logged_in = $CI->session->userdata('is_logged_in');
//         //print_r($CI->session);
//         //echo $is_logged_in;
//         //exit;
//         if ($is_logged_in == true) {
//             redirect('home');
//         }
//     }
// }

// if (!function_exists('is_register_profile')) {
//     function is_register_profile()
//     {
//         $CI = &get_instance();
//         $is_register_in = $CI->session->userdata('is_register_in');
//         //print_r($CI->session);
//         //echo $is_register_in;
//         //exit;
//         if ($is_register_in == true) {
//             redirect('registration/terms');
//         } else {
//         }
//     }
// }

// if (!function_exists('is_register_in')) {
//     function is_register_in()
//     {
//         $CI = &get_instance();
//         $is_register_in = $CI->session->userdata('is_register_in');
//         if (!isset($is_register_in) || $is_register_in == false) {
//             //redirect('home');
//             redirect('registration');
//         }
//     }
// }
// if (!function_exists('is_logged_in')) {
//     function is_logged_in()
//     {
//         $CI = &get_instance();
//         $is_logged_in = $CI->session->userdata('is_logged_in');
//         if (!isset($is_logged_in) || $is_logged_in == false) {
//             //redirect('home');
//             redirect(base_url());
//         } else {
//             $CI->session->set_userdata('is_setting_in', false);
//         }
//     }
// }
// if (!function_exists('is_logged_in_setting')) {
//     function is_logged_in_setting()
//     {
//         $CI = &get_instance();
//         $is_logged_in_setting = $CI->session->userdata('is_setting_in');
//         $is_logged_in = $CI->session->userdata('is_logged_in');
//         if (!isset($is_logged_in) || $is_logged_in == false) {
//             //redirect('home');
//             redirect(base_url(''));
//         } else {
//             if (!isset($is_logged_in_setting) || $is_logged_in_setting == false) {
//                 //redirect('home');
//                 redirect(base_url('settings_login'));
//             }
//         }
//     }
// }
if (!function_exists('decrypt_cache_data')) {
    function decrypt_cache_data($encryption_value)
    {
        $ciphering_value = CIPHERING_VALUE;
        $encryption_key = ENCRYPTION_KEY;
        $decryption_value = openssl_decrypt($encryption_value, $ciphering_value, $encryption_key, 0, $encryption_key);
        return $decryption_value;
    }
}
if (!function_exists('session_doc_detail')) {
    function session_doc_detail($doctor_id = '')
    {
        $CI = &get_instance();
        if ($doctor_id) {
            $cache_key = "session_doctor_details_" . $doctor_id;
            if ($CI->myredis->get($cache_key)) {
                $data_row = $CI->myredis->get($cache_key);
            } else {
                $qry = "SELECT
                            ksd.sessions_doctors_id,
                            ksd.doctor_name,
                            ksd.profile_image,
                            ksd.profile,
                            ksd.subtitle,
                            ksd.display_speciality AS DepartmentName,
                            GROUP_CONCAT(ms.spec_name ORDER BY ms.master_doc_spec_id) AS DepartmentName2
                        FROM knwlg_sessions_doctors ksd
                        LEFT JOIN master_doctor_specialization ms
                            ON FIND_IN_SET(ms.master_doc_spec_id, ksd.speciality) > 0
                        WHERE ksd.sessions_doctors_id = " . $doctor_id . "
                        GROUP BY
                            ksd.sessions_doctors_id,
                            ksd.doctor_name,
                            ksd.profile_image,
                            ksd.profile,
                            ksd.subtitle,
                            ksd.display_speciality";


                // echo $qry; exit;
                $query = $CI->db->query($qry);
                $data_row = $query->result_array();
                $CI->myredis->set($cache_key, $data_row);
            }
        }
        return $data_row;
    }
}
if (!function_exists('session_doc_detail_by_env')) {
    function session_doc_detail_by_env($doctor_id = '', $user_master_id)
    {
        $CI = &get_instance();
        $env = get_user_env($user_master_id);
        if ($env) {
            if ($env != 'GL') {
                $envStatus = "AND (ksd.env ='GL' or ksd.env ='" . $env . "')";
            } else {
                $envStatus = "AND ksd.env ='" . $env . "'";
            }
        } else {
            $envStatus = "";
        }
        if ($doctor_id) {
            $qry = "SELECT
                        ksd.sessions_doctors_id,
                        ksd.doctor_name,
                        ksd.profile_image,
                        ksd.profile,
                        ksd.subtitle,
                        ksd.adv_panel_disp AS DepartmentName,
                        GROUP_CONCAT(ms.spec_name ORDER BY ms.master_doc_spec_id) AS DepartmentName2
                    FROM knwlg_sessions_doctors ksd
                    LEFT JOIN master_doctor_specialization ms
                        ON FIND_IN_SET(ms.master_doc_spec_id, ksd.speciality) > 0
                    WHERE
                        ksd.sessions_doctors_id = " . $doctor_id . "
                        " . $envStatus . "
                    GROUP BY
                        ksd.sessions_doctors_id,
                        ksd.doctor_name,
                        ksd.profile_image,
                        ksd.profile,
                        ksd.subtitle,
                        ksd.adv_panel_disp";
            $query = $CI->db->query($qry);
            $data_row = $query->result_array();
        }
        return $data_row;
    }
}
if (!function_exists('session_doc_detail_v1')) {
    function session_doc_detail_v1($doctor_id = '')
    {
        $CI = &get_instance();
        if ($doctor_id) {
            $qry = "SELECT
                        ksd.sessions_doctors_id,
                        ksd.doctor_name,
                        ksd.profile_image,
                        ksd.profile,
                        ksd.subtitle,
                        ksd.adv_panel_disp AS DepartmentName,
                        GROUP_CONCAT(ms.spec_name ORDER BY ms.master_doc_spec_id) AS DepartmentName2
                    FROM knwlg_sessions_doctors ksd
                    LEFT JOIN master_doctor_specialization ms
                        ON FIND_IN_SET(ms.master_doc_spec_id, ksd.speciality) > 0
                    WHERE
                        ksd.sessions_doctors_id = " . $doctor_id . "
                        AND ksd.status = 3
                    GROUP BY
                        ksd.sessions_doctors_id,
                        ksd.doctor_name,
                        ksd.profile_image,
                        ksd.profile,
                        ksd.subtitle,
                        ksd.adv_panel_disp";
            $query = $CI->db->query($qry);
            //echo $qry; exit;
            $data_row = $query->result_array();
        }
        return $data_row;
    }
}
if (!function_exists('get_progress_color')) {
    function get_progress_color($count)
    {
        $CI = &get_instance();
        $CI->db->select(
            '
		'
        );
        $qry = "SELECT color from knwlg_session_progress_color WHERE from_percent<=" . $count . " AND to_percent>=" . $count . "";
        $query = $CI->db->query($qry);
        $data_row = $query->result_array();
        return $data_row[0]['color'];
    }
}
if (!function_exists('user_questions_by_mastersession_id')) {
    function user_questions_by_mastersession_id($session_id, $user_id)
    {
        $CI = &get_instance();
        $CI->db->select(
            '
		'
        );
        $qry = "SELECT  kspd.question,kspd.upload_documents,kspd.sessions_participant_id
		FROM    knwlg_sessions_participant ksp
		INNER JOIN knwlg_sessions_participant_details kspd
		ON ksp.knwlg_sessions_participant_id=kspd.sessions_participant_id
		WHERE ksp.participant_id=" . $user_id . " AND
		ksp.knwlg_sessions_id=" . $session_id . "";
        //echo $qry; exit;
        $query = $CI->db->query($qry);
        if ($query) {
            $data_row = $query->row();
        } else {
            $data_row = array();
        }
        return $data_row;
    }
}
if (!function_exists('get_logo_by_file_extension')) {
    function get_logo_by_file_extension($image_name)
    {
        $CI = &get_instance();
        $file_name_split = explode(".", $image_name);
        $spl_count = count($file_name_split);
        $spl_count = $spl_count - 1;
        $file_type = $file_name_split[$spl_count];
        if (strtolower($file_type) == "jpg" || strtolower($file_type) == "jpeg" || strtolower($file_type) == "gif" || strtolower($file_type) == "png") {
            $image_name_logo = "image_down.png";
        }
        if ($file_type == "doc" || $file_type == "docx") {
            $image_name_logo = "word_down.png";
        }
        if ($file_type == "pdf") {
            $image_name_logo = "pdf_down.png";
        }
        return $image_name_logo;
    }
}
if (!function_exists('download_zip')) {
    function download_zip()
    {
        $files = array('http://**************/~devappv1clirnet/final/knowledge/themes/front/uploads/askclir_docs/pdf2.pdf');
        $zipname = 'file.zip';
        $zip = new ZipArchive();
        $zip->open($zipname, ZipArchive::CREATE);
        foreach ($files as $file) {
            $zip->addFile($file);
        }
        $zip->close();
        header('Content-Type: application/zip');
        header('Content-disposition: attachment; filename=' . $zipname);
        header('Content-Length: ' . filesize($zipname));
        readfile($zipname);
    }
}
if (!function_exists('get_list_rating')) {
    function get_list_rating($postid = '', $rating = '', $myrating = '', $type = '')
    {
        if ($myrating) {
            $rateValue = $myrating;
            $readOnly = 'false';
        } else {
            $rateValue = 4;
            $readOnly = 'false';
        }
        if ($rating) {
            $avrate = $rating;
        } else {
            $avrate = 4.0;
        }
        $str = '<div class="avgrateme">Avg. Rating: <span>' . $avrate . '</span></div><div class="retme_container"><div class="retme_text">Rate Me : </div> <div id="rateYo' . $postid . '"></div>
		<div class="counter">' . $myrating . '</div></div>
		<script>
		$(function () {
			$("#rateYo' . $postid . '").rateYo({
			starWidth: "20px",
			rating: ' . $rateValue . ',
			fullStar: true,
			readOnly: ' . $readOnly . ',
			onChange: function (rating, rateYoInstance) {
				$(this).next().text(rating);
				},
			onSet: function (rating, rateYoInstance) {
				//alert("Rating is set to: " + rating);
				var postid = ' . $postid . ';
				var type = "' . $type . '";
				var rating = rating;
				$.ajax({
					url: "' . base_url() . 'kcap/rating",
					type: "POST",
					data: {postid:postid, type:type, rating:rating},
				success: function(msg)
				{
				  //alert(msg);
				},
				});
				}
			});
		});
		</script>';
        return $str;
    }
}
if (!function_exists('get_list_rating1')) {
    function get_list_rating1($postid = '', $rating = '', $myrating = '', $type = '')
    {
        if ($myrating) {
            $rateValue = $myrating;
            $readOnly = 'false';
        } else {
            $rateValue = 4;
            $readOnly = 'false';
        }
        if ($rating) {
            $avrate = $rating;
        } else {
            $avrate = 4.0;
        }
        $str = '<div class="avgrateme">Avg. Rating: <span>' . $avrate . '</span></div><div class="retme_container"><div class="retme_text">Rate Me : </div> <div id="rateYo1' . $postid . '"></div>
		<div class="counter">' . $myrating . '</div></div>
		<script>
		$(function () {
			$("#rateYo1' . $postid . '").rateYo({
			starWidth: "20px",
			rating: ' . $rateValue . ',
			fullStar: true,
			readOnly: ' . $readOnly . ',
			onChange: function (rating, rateYoInstance) {
				$(this).next().text(rating);
				},
			onSet: function (rating, rateYoInstance) {
				//alert("Rating is set to: " + rating);
				var postid = ' . $postid . ';
				var type = "' . $type . '";
				var rating = rating;
				$.ajax({
					url: "' . base_url() . 'kcap/rating",
					type: "POST",
					data: {postid:postid, type:type, rating:rating},
				success: function(msg)
				{
				  //alert(msg);
				},
				});
				}
			});
		});
		</script>';
        return $str;
    }
}
// if (!function_exists('add_to_vault')) {
//     function add_to_vault($postid, $type = '', $user_id)
//     {
//         $CI = &get_instance();
//         $CI->db->select(
//             '
// 		'
//         );
//         $qry = "SELECT * FROM knwlg_vault WHERE user_id=" . $user_id . " AND type='" . $type . "' AND post_id=" . $postid . " AND status=1";
//         $query = $CI->db->query($qry);
//         $data_row = $query->row();
//         if (empty($data_row)) {
//             $str = '<span class="vault_span_' . $postid . '"><a class="text-uppercase font_12px addValue_link" href="javascript:void(0)" onclick="switch_vault(' . $postid . ',' . $user_id . ',1);"><img src="' . base_url() . 'themes/front/images/addVault.png" alt="icon" title="icon" class="value_img_add" width="14" height="18">
//                    <img src="' . base_url() . 'themes/front/images/addVault-hover.png" alt="icon" title="icon" class="value_hover_img_add" width="16" height="19">
//                    Add to vault</a></span>
// 				   ';
//         } else {
//             $str = '<span class="vault_span_' . $postid . '"><a class="text-uppercase font_12px addValue_link active" href="javascript:void(0)" onclick="switch_vault(' . $postid . ',' . $user_id . ',0);">
//                    <img src="' . base_url() . 'themes/front/images/addVault.png" alt="icon" title="icon" class="value_img_add" width="14" height="18">
//                    <img src="' . base_url() . 'themes/front/images/addVault-hover.png" alt="icon" title="icon" class="value_hover_img_add" width="16" height="19">
//                    Remove From Vault</a></span>
// 		';
//         }
//         return $str;
//     }
// }
// if (!function_exists('add_to_vault_for_my_vault')) {
//     function add_to_vault_for_my_vault($postid, $type = '', $user_id)
//     {
//         $CI = &get_instance();
//         $CI->db->select(
//             '
// 		'
//         );
//         $qry = "SELECT * FROM knwlg_vault WHERE user_id=" . $user_id . " AND type='" . $type . "' AND post_id=" . $postid . " AND status=1";
//         $query = $CI->db->query($qry);
//         $data_row = $query->row();
//         if (empty($data_row)) {
//             $str = '<span class="vault_span_' . $postid . '"><a class="text-uppercase font_12px addValue_link" href="javascript:void(0)" onclick="switch_vault(' . $postid . ',' . $user_id . ',1);"><img src="' . base_url() . 'themes/front/images/addVault.png" alt="icon" title="icon" class="value_img_add" width="14" height="18">
//                    <img src="' . base_url() . 'themes/front/images/addVault-hover.png" alt="icon" title="icon" class="value_hover_img_add" width="16" height="19">
//                    Add to vault</a></span>
// 				   ';
//         } else {
//             $str = '<span class="vault_span_' . $postid . '"><a class="text-uppercase font_12px addValue_link active" href="javascript:void(0)" onclick="switch_vault(' . $postid . ',' . $user_id . ',0,' . $type . ');">
//                    <img src="' . base_url() . 'themes/front/images/addVault.png" alt="icon" title="icon" class="value_img_add" width="14" height="18">
//                    <img src="' . base_url() . 'themes/front/images/addVault-hover.png" alt="icon" title="icon" class="value_hover_img_add" width="16" height="19">
//                    Remove From Vault</a></span>
// 		';
//         }
//         return $str;
//     }
// }
if (!function_exists('get_background_box_img')) {
    function get_background_box_img($category_id)
    {
        if ($category_id == 1) {
            $classValue = " masterCast ";
        }
        if ($category_id == 2) {
            $classValue = " masterConsult ";
        }
        if ($category_id == 3) {
            $classValue = " masterCircle ";
        }
        return $classValue;
    }
}
if (!function_exists('get_image_by_ext')) {
    function get_image_by_ext($image_name, $class_name, $height, $width)
    {
        if ($image_name != "") {
            if (file_exists("" . FCPATH . "uploads/kcap/image/" . $image_name . "")) {
                $html_return = '<img class="' . $class_name . '" height="' . $height . 'px" width="' . $width . 'px" src="' . base_url() . 'uploads/kcap/image/' . $image_name . '" title="' . $image_name . '">';
            } else {
                //echo $image_name;
                $image_temp_name_array = explode(".", $image_name);
                $image_name_count = count($image_temp_name_array);
                $image_name_str = "";
                for ($i = 0; $i <= $image_name_count - 2; $i++) {
                    if (isset($image_temp_name_array[$i])) {
                        $image_name_str .= $image_temp_name_array[$i] . ".";
                    }
                }
                $temp_image = $image_name_str . "jpg";
                if (file_exists("" . FCPATH . "uploads/kcap/image/" . $temp_image . "")) {
                    //$html_return='<img class="'.$class_name.'" height="'.$height.'px" width="'.$width.'px" src="'.base_url().'uploads/kcap/image/'.$temp_image.'" title="'.$image_temp_name_array[0].'">';
                    $html_return = image_thumb('uploads/kcap/image/' . $temp_image . '', 50, 50) . 'uploads/kcap/image/' . $temp_image . '';
                } else {
                    $html_return = '<img class="' . $class_name . '" height="' . $height . 'px" width="' . $width . 'px" src="' . base_url() . 'uploads/kcap/image/no_image.jpg" title="No Image">';
                }
            }
        } else {
            $html_return = '<img class="' . $class_name . '" height="' . $height . 'px" width="' . $width . 'px" src="' . base_url() . 'uploads/kcap/image/no_image.jpg" title="No Image">';
        }
        return $html_return;
    }
}
if (!function_exists('get_doctor_image')) {
    function get_doctor_image($image_name, $class_name, $height, $width)
    {
        $CI = &get_instance();
        if ($image_name != "") {
            if (file_exists("" . FCPATH . "uploads/docimg/" . $image_name . "")) {
                $html_return = '<img class="' . $class_name . '" height="' . $height . 'px" width="' . $width . 'px" src="' . base_url() . 'uploads/docimg/' . $image_name . '" title="doc_pr_image">';
            } else {
                $color_array = ["1E90FF"];
                $fr_name = $CI->session->userdata['first_name'];
                $firstCharacter = substr($fr_name, 0, 1);
                $html_return = '<div class="name_usr_icon" style="background-color:#' . $color_array[array_rand($color_array)] . '"><span class="translate_both">' . $firstCharacter . '</span></div>';
            }
        } else {
            $color_array = ["1E90FF"];
            $fr_name = $CI->session->userdata['first_name'];
            $firstCharacter = substr($fr_name, 0, 1);
            $html_return = '<div class="name_usr_icon" style="background-color:#' . $color_array[array_rand($color_array)] . '"><span class="translate_both">' . $firstCharacter . '</span></div>';
        }
        return $html_return;
    }
}
if (!function_exists('get_doctor_image_email')) {
    function get_doctor_image_email($image_name, $class_name, $height, $width)
    {
        $CI = &get_instance();
        if ($image_name != "") {
            if (file_exists("" . FCPATH . "uploads/docimg/" . $image_name . "")) {
                $html_return = '<img style="float:left; object-fit:cover; -webkit-object-fit:cover;"  class="' . $class_name . '" height="' . $height . '%" width="' . $width . '%" src="' . base_url() . 'uploads/docimg/' . $image_name . '" title="doc_pr_image">';
            } else {
                $color_array = ["1E90FF"];
                $fr_name = $CI->session->userdata['first_name'];
                $firstCharacter = substr($fr_name, 0, 1);
                $html_return = '<div class="name_usr_icon" style="background-color:#' . $color_array[array_rand($color_array)] . '"><span class="translate_both">' . $firstCharacter . '</span></div>';
            }
        } else {
            $color_array = ["1E90FF"];
            $fr_name = $CI->session->userdata['first_name'];
            $firstCharacter = substr($fr_name, 0, 1);
            $html_return = '<div class="name_usr_icon" style="background-color:#' . $color_array[array_rand($color_array)] . '"><span class="translate_both">' . $firstCharacter . '</span></div>';
        }
        return $html_return;
    }
}
if (!function_exists('get_profile_image')) {
    function get_profile_image($image_name = '', $user_name = '', $class_name = '', $height = '', $width = '')
    {
        $CI = &get_instance();
        if ($image_name != "") {
            if (file_exists("" . FCPATH . "uploads/docimg/" . $image_name . "")) {
                $html_return = '<img class="' . $class_name . '" height="' . $height . 'px" width="' . $width . 'px" src="' . base_url() . 'uploads/docimg/' . $image_name . '" title="doc_pr_image">';
            } else {
                $color_array = ["21ad66"];
                $fr_name = $user_name;
                $firstCharacter = substr($fr_name, 0, 1);
                $html_return = '<div class="name_usr_icon" style="background-color:#' . $color_array[array_rand($color_array)] . '"><span class="translate_both">' . $firstCharacter . '</span></div>';
            }
        } else {
            $color_array = ["21ad66"];
            $fr_name = $user_name;
            $firstCharacter = substr($fr_name, 0, 1);
            $html_return = '<div class="name_usr_icon" style="background-color:#' . $color_array[array_rand($color_array)] . '"><span class="translate_both">' . $firstCharacter . '</span></div>';
        }
        return $html_return;
    }
}
if (!function_exists('get_session_doctor_image')) {
    function get_session_doctor_image($image_name, $class_name, $height, $width, $doctor_name)
    {
        $CI = &get_instance();
        if ($image_name != "") {
            if (file_exists("" . FCPATH . "uploads/docimg/" . $image_name . "")) {
                $html_return = '<img class="' . $class_name . '" height="' . $height . 'px" width="' . $width . 'px" src="' . base_url() . 'uploads/docimg/' . $image_name . '" title="doc_pr_image">';
            } else {
                $color_array = ["1E90FF"];
                $fr_name = $doctor_name;
                $firstCharacter = substr($fr_name, 0, 1);
                $html_return = '<div class="name_usr_icon" style="background-color:#' . $color_array[array_rand($color_array)] . '"><span class="translate_both">' . $firstCharacter . '</span></div>';
            }
        } else {
            $color_array = ["1E90FF"];
            $fr_name = $doctor_name;
            $firstCharacter = substr($fr_name, 0, 1);
            $html_return = '<div class="name_usr_icon" style="background-color:#' . $color_array[array_rand($color_array)] . '"><span class="translate_both">' . $firstCharacter . '</span></div>';
        }
        return $html_return;
    }
}
if (!function_exists('get_image_path_by_ext')) {
    function get_image_path_by_ext($image_name)
    {
        if ($image_name != "") {
            if (file_exists("" . FCPATH . "uploads/kcap/image/" . $image_name . "")) {
                $html_return = '' . base_url() . 'uploads/kcap/image/' . $image_name . '';
            } else {
                //echo $image_name;
                $image_temp_name_array = explode(".", $image_name);
                $image_name_count = count($image_temp_name_array);
                $image_name_str = "";
                for ($i = 0; $i <= $image_name_count - 2; $i++) {
                    if (isset($image_temp_name_array[$i])) {
                        $image_name_str .= $image_temp_name_array[$i] . ".";
                    }
                }
                $temp_image = $image_name_str . "jpg";
                if (file_exists("" . FCPATH . "uploads/kcap/image/" . $temp_image . "")) {
                    $html_return = '' . base_url() . 'uploads/kcap/image/' . $temp_image . '';
                } else {
                    $html_return = '' . base_url() . 'uploads/kcap/image/no_image.jpg';
                }
            }
        } else {
            $html_return = '' . base_url() . 'uploads/kcap/image/no_image.jpg';
        }
        return $html_return;
    }
}
if (!function_exists('is_manual_seen')) {
    function is_manual_seen()
    {
        $CI = &get_instance();
        $module_name = $CI->router->fetch_class();
        $user_id = $CI->session->userdata['user_master_id'];
        $qry = "SELECT * FROM master_manual_view WHERE user_id=" . $user_id . " AND type='" . $module_name . "'";
        $query = $CI->db->query($qry);
        $data_result = $query->result();
        if (empty($data_result)) {
            redirect('howtouse/openmanual/' . $module_name . '');
        }
        return $output;
    }
}
// Generate membership uid
if (!function_exists('create_membership_id')) {
    function create_membership_id()
    {
        $s = strtoupper(md5(uniqid(rand(), true)));
        $guidText =
            substr($s, 0, 8) . '-' .
            substr($s, 8, 4) . '-' .
            substr($s, 12, 4) . '-' .
            substr($s, 16, 4) . '-' .
            substr($s, 20);
        return $guidText;
    }
}

function jTraceEx($e, $seen = null)
{
    $starter = $seen ? 'Caused by: ' : '';
    $result = array();
    if (!$seen) {
        $seen = array();
    }
    $trace = $e->getTrace();
    $prev = $e->getPrevious();
    $result[] = sprintf('%s%s: %s', $starter, get_class($e), $e->getMessage());
    $file = $e->getFile();
    $line = $e->getLine();
    while (true) {
        $current = "$file:$line";
        if (is_array($seen) && in_array($current, $seen)) {
            $result[] = sprintf(' ... %d more', count($trace) + 1);
            break;
        }
        $result[] = sprintf(
            ' at %s%s%s(%s%s%s)',
            count($trace) && array_key_exists('class', $trace[0]) ? str_replace('\\', '.', $trace[0]['class']) : '',
            count($trace) && array_key_exists('class', $trace[0]) && array_key_exists('function', $trace[0]) ? '.' : '',
            count($trace) && array_key_exists('function', $trace[0]) ? str_replace('\\', '.', $trace[0]['function']) : '(main)',
            $line === null ? $file : basename($file),
            $line === null ? '' : ':',
            $line === null ? '' : $line
        );
        if (is_array($seen)) {
            $seen[] = "$file:$line";
        }
        if (!count($trace)) {
            break;
        }
        $file = array_key_exists('file', $trace[0]) ? $trace[0]['file'] : 'Unknown Source';
        $line = array_key_exists('file', $trace[0]) && array_key_exists('line', $trace[0]) && $trace[0]['line'] ? $trace[0]['line'] : null;
        array_shift($trace);
    }
    $result = join("\n", $result);
    if ($prev) {
        $result .= "\n" . jTraceEx($prev, $seen);
    }
    return $result;
}

function get_ip_detail($ip = false)
{
    $CI = &get_instance();
    if (empty($ip)) {
        $ip = $CI->input->ip_address();
    }
    $ip_response = file_get_contents('http://ip-api.com/json/' . $ip);
    $ip_array = json_decode($ip_response, true);
    return $ip_array;
}
/**
 * @param $type
 * @return string
 */
function disclaimer($module_type)
{
    switch ($module_type) {
        case 'knowledge':
            return "All scientific content on the platform is provided for general medical " .
                "education purposes meant for registered medical practitioners only. " .
                "The content is not meant to substitute for the independent medical " .
                "judgment of a physician relative to diagnostic and treatment options " .
                "of a specific patient's medical condition. In no event will CLIRNET " .
                "be liable for any decision made or action taken in reliance upon the " .
                "information provided through this content.";
            break;
        case 'session':
            return "The information in this educational activity is provided for general medical education purposes meant for registered medical practitioners only. The activity is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. The viewpoints expressed in this CME activity are those of the authors/faculty. They do not represent an endorsement by CLIRNET or the Sponsor. In no event will CLIRNET, the Sponsor or, the authors/faculty be liable for any decision made or action taken in reliance upon the information provided through this CME activity. All CMEs are recorded to be used for research & information purposes only. Clirnet at the request of Sponsor, may share your details such as name, location, recording of the session and session feedback for information purposes only.";
            break ;

        case 'channel':
            return "All scientific content on the platform is provided for general medical education purposes meant for registered medical practitioners only. The content is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient's medical condition. In no event will CLIRNET be liable for any decision made or action taken in reliance upon the information provided through this content.";
            break ;

        default:
            return '';
            break;
    }
}
function cta(
    $ctype,
    $type_id,
    $user_master_id,
    $client_ids
) {
    $CI = &get_instance();
    // echo '<br> ctype: '.$ctype.' <br> --- type_id: '.$type_id.'<br> --- user_master_id: '.$user_master_id.' <br>---- client_ids: '.$client_ids;
    // return array();
    //echo 'test--' . $ctype . '-----' . $type_id;
    //print_r($type_id);
    //exit;
    $env = get_user_env($user_master_id);
    //$responses = array();
    switch ($ctype) {
        case 'comp':
            $responses = productdetail('comp', $type_id, $user_master_id, $client_ids);
            // print_r($responses);
            // exit;
            return $responses;
            break;
        case 'gr':
            $responses = productdetail('gr', $type_id, $user_master_id, $client_ids);
            return $responses;
            break;
        case 'clinical_video':
            $responses = productdetail('archivevideo', $type_id, $user_master_id, $client_ids);
            return $responses;
            break;
        case 'session':
            $responses = productdetail('session', $type_id, $user_master_id, $client_ids);
            return $responses;
            break;
        case 'channel':
            $responses = productdetail('channel', $type_id, $user_master_id, $client_ids);
            return $responses;
            break;
        case 'survey':
            $responses = productdetail('survey', $type_id, $user_master_id, $client_ids);
            return $responses;
            break;
        case 'epub':
            $responses = productdetail('epub', $type_id, $user_master_id, $client_ids);
            return $responses;
            break;
        case 'training':
            $responses = productdetail('training', $type_id, $user_master_id, $client_ids);
            return $responses;
            break;
        default:
            $responses =  array();
            break;
    }

    // print_r($responses);
    // exit;

    return $responses;



}

function callApi($method, $url, $data = [], $accessToken = '')
{
    $ch = curl_init();
    // Set headers
    $headers = [
        'Content-Type: application/json',
    ];
    if (!empty($accessToken)) {
        $headers[] = 'Authorization: Bearer ' . $accessToken;
    }
    // Configure cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    if (strtoupper($method) === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    // Execute request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    if ($error) {
        return ['error' => true, 'message' => $error];
    }
    return ['status_code' => $httpCode, 'response' => json_decode($response, true)];
}
function updateUserDetailCache($id)
{

    if (empty($id) || $id == '') {
        return false;
    }
    $CI = &get_instance();
    $CI->load->helper('crmlogin_helper');
    $token = logincrm(crm_url, crm_email, crm_password);
    $response = callApi("GET", USER_DETAIL_CACHE_REBUILD_CRM_URL . $id, null, $token);
    if ($response["status_code"] == 200) {
        return true;
    } else {
        return false;
    }
}
function useractivitydetail(
    $ctype,
    $ids,
    $env,
    $user_master_id
) {

    $type_id = $ids;
    $vx = array();
    //$env = get_user_env($user_master_id);
    $CI = &get_instance();
    switch ($ctype) {
        case 'comp':
            $sql = "SELECT
            cm.comp_qa_id as type_id,
            cm.comp_qa_question,
            cm.comp_qa_question_raw,
            cm.comp_qa_file_img,
            cm.added_on,
            cm.deeplink,
            cm.gl_deeplink,
            cm.type
            FROM knwlg_compendium_V1 as cm
            WHERE cm.status=3
            AND
            cm.comp_qa_id  in (" . $type_id . ")";
            //echo $sql; exit;
            $query = $CI->db->query($sql);
            $vx = array();
            if (($query) && ($query->num_rows() > 0)) {
                $value = $query->result();
                //print_r($value); exit;
                foreach ($value as $key => $result) {
                    //print_r($result);
                    if ($result->comp_qa_file_img) {
                        $img = $result->comp_qa_file_img; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;
                    } else {
                        $img = '';
                    }
                    if ($result->type_id != '') {
                        $vx[] = array(
                            "type_id" => $result->type_id,
                            "con_type" => $result->type,
                            "type" => 'comp',
                            "question" => $result->comp_qa_question,
                            "image" => $img,
                            "specialities" => $result->specialities_name,
                            "deeplink" => ($env == 'GL') ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
                            "called_on" => $extracted_id[$result->type_id]
                        );
                    }
                }
            }
            return $vx;
            break;
        case 'gr':
            $sql = "SELECT
            gr.gr_id as type_id,
            gr.gr_title as title,
            gr.gr_description as description,
            gr.gr_preview_image,
            gr.gr_type,
            gr.start_like,
            gr.gr_date_of_publication as publish_date,
            gr.deeplink,
            gr.gl_deeplink
            FROM knwlg_gr_register as gr
            WHERE
            gr.status=3
            and
            gr.gr_id in (" . $type_id . ")";
            //echo $sql; exit;
            $query = $CI->db->query($sql);
            if (($query) && ($query->num_rows() > 0)) {
                $vx = array();
                foreach ($query->result() as $val) {
                    if ($val->gr_preview_image) {
                        $gr_logic_image = $val->gr_preview_image;
                    } else {
                        $gr_logic_image = '';
                    }
                    if ($val->type_id != '') {
                        $vx[] = array(
                            "type_id" => $val->type_id,
                            "type" => 'gr',
                            "date" => date(' jS F y', strtotime($val->publish_date)),
                            "title" => html_entity_decode(strip_tags($val->title)),
                            "media_type" => $val->gr_type,
                            "image" => $gr_logic_image,
                            "description" => html_entity_decode(strip_tags($val->description)),
                            "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                            "called_on" => $extracted_id[$val->type_id]
                        );
                    }
                }
            }
            return $vx;
            break;
        case 'session':
            // $CI->db->save_queries = TRUE;
            $sql = "SELECT
                    ks.session_id,
                    ks.session_topic,
                    ks.cover_image,
                    ks.start_datetime,
                    ks.deeplink,
                    ks.gl_deeplink,
                    ks.end_datetime,
                    msct.category_name,
                    msct.category_logo,
                    mst.status_name,
                    GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
                    GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
                    GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as  profile,
                    GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as  profile_images
                    FROM knwlg_sessions_V1 as ks
                    LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
                    LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
                    LEFT JOIN knwlg_sessions_participant as ksp ON ksp.knwlg_sessions_id = ks.session_id
                    LEFT JOIN master_session_status as mst ON mst.master_session_status_id = ks.session_status
                    WHERE
                    ks.session_id in (" . $type_id . ")   and ks.session_status not in (5,6)
                    GROUP BY
                        ks.session_id,
                        ks.session_topic,
                        ks.cover_image,
                        ks.start_datetime,
                        ks.deeplink,
                        ks.gl_deeplink,
                        ks.end_datetime,
                        msct.category_name,
                        msct.category_logo,
                        mst.status_name

                    ORDER BY
                        ks.start_datetime DESC ";
            $query = $CI->db->query($sql);
            //echo $sql; exit;
            $i = 0;
            $entities = array();
            if (($query) && ($query->num_rows() > 0)) {
                foreach ($query->result() as $result) {
                    //print_r($result); exit;
                    $entities[$i]['type_id'] = $result->session_id;
                    $entities[$i]['type'] = 'session';
                    $entities[$i]['called_on'] = $extracted_id[$result->session_id];
                    // $entities[$i]['my_participant_id'] = $row['participant_id'];
                    $entities[$i]['is_available'] = (strtotime($result->start_datetime) < time()) ? false : true;
                    $tempcover = $result->cover_image;
                    $coverimageArry = explode(",", $tempcover);
                    $entities[$i]['cover_image'] = $coverimageArry;
                    //$entities[$i]['cover_image'] = $row['cover_image'];
                    $entities[$i]['session_topic'] = $result->session_topic;
                    $entities[$i]['category_id'] = $result->category_id;
                    $entities[$i]['category_name'] = $result->category_name;
                    $entities[$i]['category_image'] = base_url() . "/themes/front/images/session/" . $result->category_logo;
                    $entities[$i]['start_datetime'] = $result->start_datetime;
                    $start_time = $result->start_datetime;
                    $date = new DateTime($start_time);
                    //$start_time = date("g:i A", strtotime($start_time));
                    $now = new DateTime();
                    $diff = date_diff($date, $now);
                    $entities[$i]['days_remaining'] = abs($diff->format("%R%a")) + 1;
                    $end_time = $result->end_datetime;
                    $end_time = date("g:i A", strtotime($end_time));
                    $entities[$i]['display_time_format'] = $start_time . "-" . $end_time;
                    $post_time = $result->start_datetime;
                    $phpdate = strtotime($post_time);
                    $mysqldate = date('D, j M `y  ', $phpdate);
                    $entities[$i]['display_date_format'] = $mysqldate;
                    $entities[$i]['session_status'] = $result->session_status;
                    $entities[$i]['start_datetime'] = $result->start_datetime;
                    $end_time = $result->end_datetime;
                    $end_time = date("g:i A", strtotime($end_time));
                    $start_time = $result->start_datetime;
                    $start_time = date("g:i A", strtotime($start_time));
                    $entities[$i]['display_date'] = $start_time . "-" . $end_time;
                    $entities[$i]['deeplink'] = ($env == 'GL') ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0); //$row['deeplink'];
                    $entities[$i]['end_datetime'] = $result->end_datetime;
                    $entities[$i]["called_on"] = $extracted_id[$result->session_id];
                    if ($result->session_doctor_id != '') {
                        $session_doc_array = explode(",", $result->session_doctor_id);
                    } else {
                        $session_doc_array = array();
                    }
                    $ses_doc_det_array = array();
                    $inc_pp = 0;
                    if (!empty($session_doc_array)) {
                        foreach ($session_doc_array as $single_doctor) {
                            $var = session_doc_detail($single_doctor);
                            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            if ($image) {
                                if (stripos($image, "https://storage.googleapis.com") > -1) {
                                    $logic_image = $image;
                                } else {
                                    $logic_image = docimg;
                                }
                            } else {
                                $logic_image = docimg;
                            }
                            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
                            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                            $ses_doc_det_array[$inc_pp]['description'] = $var[0]['description'];
                            $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                            $inc_pp++;
                            $store_total_doctors[$result->session_id] = $inc_pp;
                        }
                        $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
                        $i++;
                    }
                }
            }
            return $entities;
            break;
        case 'epub':
            $sql = "SELECT
                cm.epub_id as type_id,
                cm.epub_title as title,
                cm.epub_description,
                cm.epub_img,
                cm.epub_img_thumbnail,
                cm.publication_date as publish_date,
                cm.deeplink,
                cm.gl_deeplink
                FROM epub_master as cm
                WHERE
                cm.status=3
                AND
                cm.epub_id in (" . $type_id . ")";
            $query = $CI->db->query($sql);
            if (($query) && ($query->num_rows() > 0)) {
                foreach ($query->result() as $row) {
                    if ($row->epub_img_thumbnail) {
                        $img = $row->epub_img_thumbnail; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;
                    } else {
                        $img = '';
                    }
                    $string = $row->comp_qa_question_raw;
                    $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                    $main_description = "";
                    $main_description = str_replace("\n\t", "\n", $row->comp_qa_answer_raw);
                    $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                    $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
                    $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
                    $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
                    //print_r(html_entity_decode(strip_tags($row->epub_description))); exit;
                    if ($row->type_id != '') {
                        $vx[] = array(
                            "type_id" => $row->type_id,
                            "type" => 'epub',
                            "title" => html_entity_decode($row->title),
                            "description" => html_entity_decode(strip_tags($row->epub_description)),
                            // "description" => html_entity_decode($row->description),//strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),
                            "image" => $img,
                            "deeplink" => ($env == 'GL') ? (($row->gl_deeplink != '') ? $row->gl_deeplink : 0) : (($row->deeplink != '') ? $row->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
                            "called_on" => $extracted_id[$row->type_id]
                        );
                    }
                }
            }
            return $vx;
            break;
        case 'channel':
            $sql = "SELECT
                ch.channel_master_id as type_id,
                ch.title as title,
                ch.short_description as short_description,
                ch.cover_image,
                ch.logo,
                ch.privacy_status,
                ch.deeplink,
                ch.gl_deeplink
                FROM channel_master as ch
                WHERE
                ch.status=3
                and
                ch.channel_master_id in (" . $type_id . ")";
            //echo $sql; exit;
            $query = $CI->db->query($sql);
            //print_R($query->num_rows()); exit;
            if (($query) && ($query->num_rows() > 0)) {
                $vx = array();
                foreach ($query->result() as $val) {
                    //print_r($val);
                    $vx[] = array(
                        "type_id" => $val->type_id,
                        "type" => 'channel',
                        "title" => html_entity_decode(strip_tags($val->title)),
                        "cover_image" => $val->cover_image,
                        "logo" => $val->client_logo,
                        "description" => $val->description,
                        "short_description" => html_entity_decode(strip_tags(substr($val->short_description, 0, 300))),
                        "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                        "called_on" => $extracted_id[$val->type_id],
                    );
                }
            }
            //print_r($vx); exit;
            return $vx;
            break;
        case "survey":
            $sql = "SELECT sv.*
                    FROM
                    survey sv
                    JOIN survey_detail as sd ON   sd.survey_id = sv.survey_id
                    WHERE
                    sv.status = 3  and sd.is_draft = 0
                    AND
                    sv.survey_id in (" . $type_id . ") ";
            // echo $sql; exit;
            $query = $CI->db->query($sql);
            if (($query) && ($query->num_rows() > 0)) {
                foreach ($query->result() as $val) {
                    $user_survey_status = detail_user_status($type_id, $user_master_id);
                    if ($val->survey_id != '') {
                        $vx[] = array(
                            "survey_id" => $val->survey_id,
                            "survey_title" => $val->survey_title,
                            "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                            "survey_description" => $val->survey_description,
                            "survey_points" => $val->survey_points,
                            "point" => $val->survey_points,
                            "survey_time" => $val->survey_time,
                            "question_count" => $val->question_count,
                            "image" => $val->image,
                            "publishing_date" => $val->publishing_date,
                            "user_survey_status" => $user_survey_status,
                            "called_on" => $extracted_id[$val->survey_id]
                        );
                    }
                }
            }
            return $vx;
            break;
        case "archivevideo":
            $sql = "SELECT
                        cm.video_archive_id as type_id,
                        cm.video_archive_question_raw,
                        cm.video_archive_file_img,
                        cm.deeplink,
                        cm.gl_deeplink,
                        cm.src,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name
                    FROM knwlg_video_archive as cm
                    JOIN video_archive_to_specialities as cmTs ON cmTs.video_archive_id = cm.video_archive_id
                    JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                    LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                    WHERE
                        cm.status = 3
                        AND cm.video_archive_id IN (" . $type_id . ")
                    GROUP BY
                        cm.video_archive_id,
                        cm.video_archive_question_raw,
                        cm.video_archive_file_img,
                        cm.deeplink,
                        cm.gl_deeplink,
                        cm.src
                    ORDER BY
                        cm.publication_date DESC";
            $query = $CI->db->query($sql);
            $i = 1;
            $vx = array();
            if (($query) && ($query->num_rows() > 0)) {
                $value = $query->result();
                foreach ($value as $key => $val) {
                    if ($val->video_archive_file_img) {
                        $img = $val->video_archive_file_img;
                    } else {
                        $img = '';
                    }
                    $sponsorLogoArry = explode(",", $val->sponsor_logo);
                    if (count($sponsorLogoArry) > 0) {
                        foreach ($sponsorLogoArry as $valueSponor) {
                            if ($valueSponor) {
                                $sponsorLogomix[] = '' . $valueSponor;
                            }
                        }
                    } else {
                        if ($val->sponsor_logo) {
                            $sponsorLogomix[] = '' . $val->sponsor_logo;
                        }
                    }
                    if (!empty($sponsorLogomix)) {
                        $sponsorLogo = implode(",", $sponsorLogomix);
                    } else {
                        $sponsorLogo = null;
                    }
                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);
                    if ($val->type_id != '') {
                        $vx[] = array(
                            "type" => 'archived_video',
                            "type_id" => $val->type_id,
                            "src" => $val->src,
                            "question" => $val->video_archive_question_raw, //html_entity_decode(strip_tags($string)),
                            "image" => $img,
                            "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                            "sponsor_logo" => $sponsorLogo,
                            "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                            "called_on" => $extracted_id[$val->type_id]
                        );
                    }
                }
            }
            return $vx;
            break;
        case 'training':
            $tid = explode(",", $type_id);
            $CI->db->save_queries = true;
            $CI->db->select("tm.id,
            tm.title,
            tm.description,
            tm.preview_image,
            tm.client_id,
            tm.channel_id,
            tm.template_id,
            tm.color,
            tm.display_in_dashboard,
            tm.featured_video,
            tm.deeplink,
            tm.in_deeplink,
            tm.gl_deeplink,
            tm.start_like,
            tm.start_date,
            tm.url,
            tm.max_participants,
            tm.added_on,
            tm.added_by,
            tm.modified_on,
            tm.modified_by,
            tm.status,
            tm.cert_template_id,
            tm.duration,
            tm.privacy_status,
            tm.img_credits,
            tm.published_date,
            tm.env,
            tm.is_share,
            tm.is_like,
            tm.is_comment,
            tm.enable_maxparticipants,
            tm.allow_postStart,
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT Tdoc.session_doctor_id) as session_doctor_id,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
            clintspon.client_name,
            clintspon.client_logo,
            (SELECT count(rt.rating) as averageRating
             FROM knwlg_rating rt
             WHERE rt.post_id = tm.id AND rt.post_type='training') as averageRating,
            (SELECT count(id)
             FROM training_module
             WHERE training_module.training_id=tm.id AND training_module.status=3) as count_module,
            (SELECT count(id)
             FROM training_module_content
             WHERE training_module_content.type='comp'
             AND status=3 AND training_module_content.training_id=tm.id) as total_medwiki,
            (SELECT count(id)
             FROM training_module_content
             WHERE training_module_content.type='session'
             AND status=3 AND training_module_content.training_id=tm.id) as total_session,
            (SELECT count(id)
             FROM training_module_content
             WHERE training_module_content.type='survey'
             AND status=3 AND training_module_content.training_id=tm.id) as total_survey,
            (SELECT count(id)
             FROM training_module_content
             WHERE training_module_content.type='live_video'
             AND status=3 AND training_module_content.training_id=tm.id) as total_live_training,
            (SELECT count(id)
             FROM training_module_content
             WHERE training_module_content.type='clinical_video'
             AND status=3 AND training_module_content.training_id=tm.id) as total_clinical_video");
            $CI->db->from('training_master as tm');
            $CI->db->join('training_to_sponsor as ts', 'tm.id = ts.training_id', 'left');
            $CI->db->join('client_master as clintspon', 'ts.sponsor_id=clintspon.client_master_id', 'left');
            $CI->db->join('training_to_session_doctor as Tdoc', 'Tdoc.training_id = tm.id', 'left');
            $CI->db->where('tm.status', 3);
            $CI->db->where_in('tm.id', $tid);
            $CI->db->where('tm.privacy_status', 0);
            $CI->db->group_by('tm.id,
                        tm.title,
                        tm.description,
                        tm.preview_image,
                        tm.client_id,
                        tm.channel_id,
                        tm.template_id,
                        tm.color,
                        tm.display_in_dashboard,
                        tm.featured_video,
                        tm.deeplink,
                        tm.in_deeplink,
                        tm.gl_deeplink,
                        tm.start_like,
                        tm.start_date,
                        tm.url,
                        tm.max_participants,
                        tm.added_on,
                        tm.added_by,
                        tm.modified_on,
                        tm.modified_by,
                        tm.status,
                        tm.cert_template_id,
                        tm.duration,
                        tm.privacy_status,
                        tm.img_credits,
                        tm.published_date,
                        tm.env,
                        tm.is_share,
                        tm.is_like,
                        tm.is_comment,
                        tm.enable_maxparticipants,
                        tm.allow_postStart,
                        clintspon.client_name,
                        clintspon.client_logo');
            $CI->db->order_by('total', 'DESC');
            $CI->db->limit(40);

            $query = $CI->db->get();
            // $CI->db->save_queries = TRUE;
            // $str = $CI->db->last_query();
            // echo $str; exit;
            $i = 1;
            if (($query) && ($query->num_rows())) {
                foreach ($query->result() as $key => $val) {
                    $content_count['comp'] = $val->total_medwiki;
                    $content_count['session'] = $val->total_session;
                    $content_count['survey'] = $val->total_survey;
                    $content_count['video_archieve'] = $val->total_clinical_video;
                    $content_count['live_training'] = $val->total_live_training;
                    $sponsorLogoArry = explode(",", $val->sponsor_logo);
                    if (count($sponsorLogoArry) > 0) {
                        foreach ($sponsorLogoArry as $valueSponor) {
                            if ($valueSponor) {
                                $sponsorLogomix[] = $valueSponor;
                            }
                        }
                    } else {
                        if ($val->sponsor_logo) {
                            $sponsorLogomix[] = $val->sponsor_logo;
                        }
                    }
                    if (!empty($sponsorLogomix)) {
                        $sponsorLogo = implode(",", $sponsorLogomix);
                    } else {
                        $sponsorLogo = null;
                    }
                    unset($sponsorLogomix);
                    unset($sponsorLogoArry);
                    $ses_doc_det_array = array();
                    if ($val->session_doctor_id) {
                        $session_doc_array = explode(",", $val->session_doctor_id);
                        $ses_doc_det_array = array();
                        $inc_pp = 0;
                        foreach ($session_doc_array as $single_doctor) {
                            $var = session_doc_detail($single_doctor);
                            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                            if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                                $logic_image = $var[0]['profile_image'];
                            } else {
                                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                                $logic_image_path = "uploads/docimg/" . $image;
                                $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                                $logic_image = $imgPr;
                                //$logic_image = $var[0]['profile_image'];
                            }
                            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
                            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                            $inc_pp++;
                        }
                    }
                    $completestatus = complete_status($val->id, $user_master_id);
                    $temp = array(
                        "slno" => $i,
                        "id" => $val->id,
                        "title" => html_entity_decode(strip_tags($val->title)),
                        "image" => $val->preview_image,
                        "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                        "module_Count" => $val->count_module, //$this->count_module($val->id),
                        "training_module_content" => $content_count, //$this->content_count($val->id),
                        "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                        "client_name" => $val->client_name,
                        "client_logo" => $val->client_logo,
                        "sponsor_name" => $val->sponsor,
                        "sponsor_logo" => $sponsorLogo,
                        "session_doctor_entities" => $ses_doc_det_array,
                        "duration" => $val->duration,
                        "is_completed" => $completestatus, #$this->complete_status($val->id,$user_master_id),
                        "is_certificate" => ($val->cert_template_id != '') ? true : false,
                        "rating" => ($val->averageRating != '') ? $val->averageRating : '',
                        "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                        "called_on" => $extracted_id[$val->id]
                    );
                    $vx[] = $temp;
                    $i++;
                }
            }
            return $vx;
            break;
        default:
            return '';
            break;
    }
}
/**
 * Summary of productdetail
 * @param mixed $ctype
 * @param mixed $type_id
 * @param mixed $user_master_id
 * @param mixed $client_ids
 * @return array|string
 */
function productdetail(
    $ctype,
    $type_id,
    $user_master_id,
    $client_ids = null
) {
    $CI = &get_instance();
    $env = get_user_env_id($user_master_id);
    if ($env) {
        if ($env != 2) {
            $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
        } else {
            $envStatus = "AND cTenv.env =" . $env . "";
        }
    } else {
        $envStatus = "";
    }

    switch ($ctype) {
        case 'comp':
            //MEDWIKI
            $cachename = "comp_detail_" . $type_id;

            // echo $cachename;
            // exit;
            $key_locked = get_user_package($user_master_id, 'comp');
            if ($key_locked == '') {
                return null;
            }

            if ($CI->myredis->get($cachename)) {
                $result = $CI->myredis->get($cachename);
                // print_r($result);
                // exit;
            } else {


                $sql = "WITH CompData AS (
                    SELECT
                        cm.comp_qa_id,
                        cm.comp_qa_question,
                        cm.comp_qa_answer,
                        cm.comp_qa_citation,
                        cm.comp_qa_answer_raw,
                        cm.comp_qa_question_raw,
                        cm.comp_qa_file_img,
                        cm.added_on,
                        cm.env,
                        cm.deeplink,
                        cm.gl_deeplink,
                        cm.comp_qa_tags,
                        cm.privacy_status,
                        cm.publication_date,
                        cm.is_like,
                        cm.is_comment,
                        cm.is_share,
                        cm.status,
                        cm.type,
                        cm.vendor,
                        cm.comment_status,
                        cm.src,
                        cm.start_like,
                        cm.comp_qa_speciality_id,
                        cm.client_id
                    FROM knwlg_compendium_V1 as cm
                    WHERE 
                    cm.comp_qa_id = {$type_id}
                    AND cm.privacy_status IN (0, 1, 2)
                    
                ),
                SpecialitiesData AS (
                    SELECT 
                        cmTs.comp_qa_id,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                    FROM compendium_to_specialities as cmTs
                    JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                    WHERE cmTs.comp_qa_id = {$type_id}
                    GROUP BY cmTs.comp_qa_id
                ),
                SponsorData AS (
                    SELECT 
                        cmTspon.comp_qa_id,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                    FROM compendium_to_sponsor as cmTspon
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                    WHERE cmTspon.comp_qa_id = {$type_id}
                    GROUP BY cmTspon.comp_qa_id
                ),
                RatingData AS (
                    SELECT 
                        {$type_id} as comp_qa_id,
                        COUNT(rt.rating) as averageRating
                    FROM knwlg_rating rt 
                    WHERE rt.post_id = {$type_id} 
                    AND rt.post_type = 'comp' 
                    AND rt.rating != 0
                ),
                CommentData AS (
                    SELECT 
                        {$type_id} as comp_qa_id,
                        COUNT(kcm.knwlg_comment_id) as count_comment
                    FROM knwlg_comment kcm 
                    WHERE kcm.type_id = {$type_id} 
                    AND kcm.type = 'comp' 
                    AND kcm.comment_approve_status = '1'
                )
                SELECT 
                    cd.comp_qa_id as type_id,
                    cd.comp_qa_question,
                    cd.comp_qa_answer,
                    cd.comp_qa_citation,
                    cd.comp_qa_answer_raw,
                    cd.comp_qa_question_raw,
                    cd.comp_qa_file_img,
                    cd.added_on,
                    cd.env,
                    cd.deeplink,
                    cd.gl_deeplink,
                    cd.comp_qa_tags,
                    cd.privacy_status,
                    cd.publication_date,
                    cd.is_like,
                    cd.is_comment,
                    cd.is_share,
                    cd.status,
                    cd.type,
                    cd.vendor,
                    cd.comment_status,
                    cd.src,
                    cd.start_like,
                    cd.comp_qa_speciality_id,
                    cln.client_name,
                    cln.client_logo,
                    sd.specialities_name,
                    sd.specialities_ids_and_names,
                    sp.sponsor,
                    sp.sponsor_logo,
                    rtmy.rating as myrating,
                    kv.status as vault,
                    cTenv.price,
                    uTpyCont.status as user_contnet_payment_status,
                    rd.averageRating,
                    cmd.count_comment
                FROM CompData cd
                JOIN client_master as cln ON cln.client_master_id = cd.client_id
                LEFT JOIN SpecialitiesData sd ON cd.comp_qa_id = sd.comp_qa_id
                LEFT JOIN SponsorData sp ON cd.comp_qa_id = sp.comp_qa_id
                LEFT JOIN RatingData rd ON cd.comp_qa_id = rd.comp_qa_id
                LEFT JOIN CommentData cmd ON cd.comp_qa_id = cmd.comp_qa_id
                LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cd.comp_qa_id AND rtmy.post_type='comp' AND rtmy.rating!=0 AND rtmy.user_master_id = {$user_master_id}
                LEFT JOIN knwlg_vault as kv ON kv.post_id = cd.comp_qa_id AND kv.type_text='comp' AND kv.user_id = {$user_master_id}
                LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cd.comp_qa_id AND cTenv.type = 1
                LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cd.comp_qa_id AND uTpyCont.type = 1 AND uTpyCont.user_master_id = {$user_master_id}";

                //$query = $this->db->query($sql);

                // echo $sql;
                // exit;

                $query = $CI->db->query($sql);
                $result = $query->row();
                $CI->myredis->set($cachename, $result);
            }
            if (isset($result) || ($query) && ($query->num_rows() > 0)) {

                if ($result->comp_qa_file_img) {
                    $img = $result->comp_qa_file_img;
                } else {
                    $img = '';
                }
                $allsponsor = array();
                $sponsorname = explode(",", $result->sponsor);
                $sp = 0;
                $sponsorLogoArry = explode(",", $result->sponsor_logo);
                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                            $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                            $sp++;
                        }
                    }
                } else {
                    if ($result->sponsor_logo) {
                        $sponsorLogomix[] = '' . $result->sponsor_logo;
                        $allsponsor[] = array('name' => $result->sponsor, "logo" => $result->sponsor_logo);
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                //$string = htmlentities($result->comp_qa_question_raw, null, 'utf-8');
                $question = $result->comp_qa_question_raw;
                $answer = $result->comp_qa_answer_raw;

                if ($result->vendor == "youtube") {
                    $vid_src = "https://www.youtube.com/watch?v=" . $result->src;
                    $vid_code = $result->src;
                } else {
                    $vid_src = "" . $result->src;
                }



                $citationFlag = (strpos($result->comp_qa_citation, 'id="references"') !== false);
                $vxPoll = array();
                $vx = array(
                    "content_id" => $result->type_id,
                    "type_id" => $result->type_id,
                    "con_type" => $result->type,
                    "vendor" => $result->vendor,
                    "src" => $vid_src,
                    "src_code" => $vid_code,
                    "type" => 'comp',
                    "date" => date(' jS F y', strtotime($result->publication_date)),
                    "question" => html_entity_decode($question),
                    "answer" => html_entity_decode(strip_tags(substr($answer, 0, 100))),
                    "question_htm" => $result->comp_qa_question,
                    "answer_htm" => $result->comp_qa_answer . $result->comp_qa_citation,
                    "image" => $img,
                    "comp_qa_citation" => $result->comp_qa_citation,
                    "isCitationExists" => $citationFlag,
                    "banner_image" => null,
                    "banner_url" => null,
                    "specialities" => $result->specialities_name,
                    "specialities_id" => $result->comp_qa_speciality_id,
                    "specialities_ids_and_names" => explode_speciality_string($result->specialities_ids_and_names),
                    // "client_name" => $result->client_name,
                    // "client_logo" => '' . $result->client_logo,
                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "price" => $result->price,//get_a_content_price($result->type_id, 1, $env),
                    "user_content_payment" => $result->user_contnet_payment_status,//get_user_content_status($result->type_id, 1, $user_master_id),
                    //============ integrated for subscription ============//
                    "sponsor_name" => $result->sponsor,
                    "sponsor_logo" => $sponsorLogo,
                    "all_sponsor" => $allsponsor,
                    "comment_count" => $result->count_comment,//$comnt['count_comment'],
                    "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
                    "myrating" => ($result->myrating != '') ? true : false,
                    "vault" => $result->vault,//$vault['vault'], //($result->vault != '') ? $result->vault : 0,
                    "is_share" => $result->is_share,
                    "deeplink" => ($env != 1) ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
                    "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,

                    "disclaimer" => disclaimer('knowledge'),
                    "survey" => $vxPoll,
                    "channel" => getchannel($type_id, $user_master_id, 'comp'),
                    "content_rate" => rate($type_id, $user_master_id, 'comp'),
                );

                // print_r($vx);
                // exit;
            }

            // print_r($vx);
            // exit;

            // if ($from_type) {}
            return $vx;
            break;
        case 'gr':
            $key_locked = get_user_package($user_master_id, 'gr');
            if ($key_locked == '') {
                return null;
            }

            $sql = "SELECT
                gr.gr_id as type_id,
                gr.gr_title as title,
                gr.title_video as title_video,
                gr.gr_description as description,
                gr.gr_chief_scientific_editor ,
                gr.gr_preview_image,
                gr.gr_type,
                gr.gr_video_source,
                gr.vendor,
                gr.start_like,
                gr.live_video,
                gr.hotline_status,
                gr.qn_status,
                gr.start_datetime,
                gr.end_datetime,
                gr.added_on,
                gr.gr_date_of_publication as publish_date,
                gr.deeplink,
                gr.gl_deeplink,
                gr.association_status,
                gr.association_setting,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                cln.client_name,
                cln.client_logo,
                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                max( ms.rank) as maxrank,
                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT grTsdoc.session_doctor_id  ORDER BY  grTsdoc.session_doctor_id  ASC ) as session_doctor_id,
                GROUP_CONCAT(DISTINCT grTsdoc.description  ORDER BY  grTsdoc.session_doctor_id  ASC SEPARATOR '----') as gr_doc_description,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = gr.gr_id and  rt.post_type='gr')as averageRating,
                rtmy.rating  as myrating,
                (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = gr.gr_id and kcm.type = 'gr' and kcm.comment_approve_status = 1)as count_comment,
                kv.status as vault
                FROM knwlg_gr_register as gr
                JOIN gr_to_specialities  as grTs ON grTs.gr_id = gr.gr_id
                JOIN master_specialities_V1 as ms ON ms.master_specialities_id = grTs.specialities_id
                JOIN client_master as cln ON cln.client_master_id = gr.client_id
                LEFT JOIN gr_to_sponsor as grTspon ON grTspon.gr_id = gr.gr_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = grTspon.sponsor_id
                LEFT JOIN gr_to_session_doctor as grTsdoc ON grTsdoc.gr_id = gr.gr_id
                LEFT JOIN content_to_env as cTenv ON cTenv.type_id = gr.gr_id and  cTenv.type = 5
                LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = gr.gr_id and  uTpyCont.type = 5 and 	uTpyCont.user_master_id = " . $user_master_id . "
                LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = gr.gr_id and  rtmy.post_type='gr' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                LEFT JOIN knwlg_vault as kv ON kv.post_id = gr.gr_id and  kv.type_text='gr' and  kv.user_id = " . $user_master_id . "
                LEFT JOIN knwlg_rating as rt ON rt.post_id = gr.gr_id and  rt.post_type='gr'
                WHERE
                gr.status=3 
                and gr.privacy_status = 0 
                and date(gr.gr_date_of_publication)<=CURDATE()
                " . $envStatus . "
                and
                gr.gr_id = " . $type_id . "";
            // echo $sql;
            // exit;

            $query = $CI->db->query($sql);
            //$CI->db->cache_off();
            $val = $query->row();
            //print_r($val); exit;
            //echo $val->gr_preview_image;
            $vx = array();
            if ($val->gr_preview_image) {
                $gr_logic_image = $val->gr_preview_image;
            } else {
                $gr_logic_image = '';
            }
            $allsponsor = array();
            $sponsorname = explode(",", $val->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $val->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = $valueSponor;
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        $sp++;
                    }
                }
            } else {
                if ($val->sponsor_logo) {
                    $sponsorLogomix[] = $val->sponsor_logo;
                    $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $session_gr_doc_description_array = explode("----", $val->gr_doc_description);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {
                    $var = session_doc_detail($single_doctor);
                    if ($var[0]['profile_image']) {
                        $logic_image = $var[0]['profile_image'];
                    } else {
                        $logic_image = docimg;
                        //$logic_image = $var[0]['profile_image'];
                    }
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                    $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $session_gr_doc_description_array[$inc_pp];
                    $inc_pp++;
                }
            }
            //print_r($ses_doc_det_array);
            //gr.association_status,
            //gr.association_setting,
            $fileArray = array();
            if ($val->type_id) {
                $fileArray = grFile($val->type_id);
            }
            if ($val->vendor == "youtube") {
                $vid_src = "https://www.youtube.com/watch?v=" . $val->gr_video_source;
                $vid_code = $val->gr_video_source;
            } else {
                $vid_src = "" . $val->gr_video_source;
            }
            $vx[] = array(
                "content_id" => $val->type_id,
                "type_id" => $val->type_id,
                "type" => 'gr',
                "date" => date(' jS F y', strtotime($val->publish_date)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "title_video" => html_entity_decode(strip_tags($val->title_video)),
                "media_type" => $val->gr_type,
                "image" => $gr_logic_image,
                "video" => $vid_src,
                "src_code" => $vid_code,
                "vendor" => $val->vendor,
                "live_video" => $val->live_video,
                "hotline_status" => $val->hotline_status,
                "qn_status" => $val->qn_status,
                "association_status" => $val->association_status,
                "association_setting" => $val->association_setting,
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => get_a_content_price($val->type_id, 5, $env),
                "user_content_payment" => get_user_content_status($val->type_id, 5, $user_master_id),
                //============ integrated for subscription ============//
                "start_datetime" => $val->start_datetime,
                "end_datetime" => $val->end_datetime,
                "description" => html_entity_decode(strip_tags($val->description)),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "specialities_ids_and_names" => explode_speciality_string($val->specialities_ids_and_names),
                "session_doctor_id" => ($val->session_doctor_id != '') ? $val->session_doctor_id : '',
                // "client_name" => $val->client_name,
                // "client_logo" => $val->client_logo,
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "all_sponsor" => $allsponsor,
                "session_doctor_entities" => $ses_doc_det_array,
                "gr_files" => $fileArray,
                "comment_count" => $val->count_comment,
                "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : $val->start_like,
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "deeplink" => ($env != 1) ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                "disclaimer" => disclaimer('knowledge'),
            );
            //    print_r($vx);
            //    die;
            return $vx;
            break;
        case 'session':
            $key_locked = get_user_package($user_master_id, 'session');
            if ($key_locked == '') {
                return null;
            }
            $cachename = "session_detail_" . $type_id;
            if ($CI->myredis->get($cachename)) {
                $result = $CI->myredis->get($cachename);
                //print_r($result); exit;
            } else {

                $sql = "WITH 
                    -- Base session information
                    session_base AS (
                        SELECT
                            ks.session_id,
                            ks.is_multiday_session,
                            ks.session_topic,
                            ks.session_description,
                            ks.sessions_question,
                            ks.master_tag_ids,
                            ks.client_id,
                            ks.sponsor_id,
                            ks.category_id,
                            ks.start_datetime,
                            ks.end_datetime,
                            ks.speciality_id,
                            ks.total_seats,
                            ks.total_buffer,
                            ks.add_question_buffer_days,
                            ks.session_doctor_id,
                            ks.deeplink,
                            ks.is_share,
                            ks.gl_deeplink,
                            ks.cover_image,
                            ks.session_status,
                            cln.client_name,
                            cln.client_logo,
                            msct.category_name,
                            msct.category_logo,
                            kstc.multidaysession_id as parent_session,
                            mst.status_name
                        FROM knwlg_sessions_V1 as ks
                        LEFT JOIN client_master as cln 
                            ON cln.client_master_id = ks.client_id
                        LEFT JOIN master_session_category as msct 
                            ON msct.mastersession_category_id = ks.category_id
                        LEFT JOIN knwlg_session_to_child as kstc 
                            ON kstc.childsession_id = ks.session_id
                        LEFT JOIN master_session_status as mst 
                            ON mst.master_session_status_id = ks.session_status
                        LEFT JOIN content_to_env as cTenv 
                            ON cTenv.type_id = ks.session_id 
                            AND cTenv.type = 2
                        WHERE ks.session_id = {$type_id}
                            AND ks.status = 3
                            {$envStatus}
                            AND ks.session_status IN (1,2,3,7)
                    ),

                    -- Cover images
                    cover_images AS (
                        SELECT
                            session_id,
                            cover_image1,
                            cover_image2,
                            cover_image3,
                            cover_image4,
                            cover_image5
                        FROM session_to_cover_image
                        WHERE session_id = {$type_id}
                    ),

                    -- Vendor information
                    vendor_info AS (
                        SELECT
                            ksv.session_id,
                            ksv.vendor_id,
                            ksv.video_embed_src,
                            ksv.room_id,
                            ksv.vouchpro_url,
                            ksv.go_to_meeting_url,
                            ksv.landing_page_url,
                            ksv.session_cast_type,
                            kv.meta_data
                        FROM knwlg_sessions_vendor as ksv
                        LEFT JOIN master_vendor as kv 
                            ON kv.vendor_id = ksv.vendor_id
                        WHERE ksv.session_id = {$type_id}
                    ),

                    -- Document information (NEEDED - confirmed)
                    -- docs AS (
                    --     SELECT
                    --         knwlg_sessions_id,
                    --         document_path,
                    --         comment
                    --     FROM knwlg_sessions_documents
                    --     WHERE knwlg_sessions_id = {$type_id}
                    --     LIMIT 1
                    -- ),

                    -- Specialties information
                    specialties AS (
                        SELECT
                            sb.session_id,
                            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                            GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                        FROM session_base sb
                        JOIN master_specialities_V1 as ms 
                            ON FIND_IN_SET(ms.master_specialities_id, sb.speciality_id) > 0
                        GROUP BY sb.session_id
                    ),

                    -- Sponsor information
                    sponsors AS (
                        SELECT
                            sTspon.session_id,
                            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                        FROM session_to_sponsor as sTspon
                        JOIN client_master as clintspon 
                            ON clintspon.client_master_id = sTspon.sponsor_id
                        WHERE sTspon.session_id = {$type_id}
                        GROUP BY sTspon.session_id
                    ),

                    -- Doctor information
                    doctors AS (
                        SELECT
                            sb.session_id,
                            GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
                            GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') as speciality
                        FROM session_base sb
                        JOIN knwlg_sessions_doctors as sdoc 
                            ON FIND_IN_SET(sdoc.sessions_doctors_id, sb.session_doctor_id) > 0
                        GROUP BY sb.session_id
                    )

                    -- Participant information (NEEDED - confirmed)
                    -- participants AS (
                    --     SELECT
                    --         ksp.knwlg_sessions_id,
                    --         GROUP_CONCAT(ksp.participant_id) as PartName,
                    --         GROUP_CONCAT(DISTINCT ksp.participant_id) as users,
                    --         GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED
                    --     FROM knwlg_sessions_participant as ksp
                    --     WHERE ksp.knwlg_sessions_id = :type_id
                    --     GROUP BY ksp.knwlg_sessions_id
                    -- )

                    -- Main query combining all the data
                    SELECT
                        sb.*,
                        ci.cover_image1,
                        ci.cover_image2,
                        ci.cover_image3,
                        ci.cover_image4,
                        ci.cover_image5,
                        v.vendor_id,
                        v.video_embed_src,
                        v.room_id,
                        v.vouchpro_url,
                        v.go_to_meeting_url,
                        v.landing_page_url,
                        v.session_cast_type,
                        v.meta_data,
                        -- d.document_path,
                        -- d.comment,
                        s.specialities_name,
                        s.specialities_ids_and_names,
                        dr.doctor_name,
                        dr.speciality,
                        sp.sponsor,
                        sp.sponsor_logo
                        -- p.PartName,
                        -- p.users,
                        -- p.IS_ATTENDED
                    FROM session_base sb
                    LEFT JOIN cover_images ci ON ci.session_id = sb.session_id
                    LEFT JOIN vendor_info v ON v.session_id = sb.session_id
                    -- LEFT JOIN docs d ON d.knwlg_sessions_id = sb.session_id
                    LEFT JOIN specialties s ON s.session_id = sb.session_id
                    LEFT JOIN doctors dr ON dr.session_id = sb.session_id
                    LEFT JOIN sponsors sp ON sp.session_id = sb.session_id
                    -- LEFT JOIN participants p ON p.knwlg_sessions_id = sb.session_id
                    ORDER BY sb.start_datetime DESC";

                // echo $sql;
                // exit;
                $query = $CI->db->query($sql);
                $result = $query->result_array();
                $CI->myredis->set($cachename, $result);
            }
            $vxPoll = array();
            $i = 0;
            $entities = array();
            foreach ($result as $row) {
                $entities[$i]['is_multiday_session'] = $row['is_multiday_session'];
                $entities[$i]['parent_session'] = $row['parent_session'];
                $entities[$i]['is_booked'] = false;
                $entities[$i]['room_link'] = '';
                $entities[$i]['asked_query'] = "";
                $entities[$i]['my_participant_id'] = "";
                $entities[$i]['last_join_date'] = "";
                $entities[$i]['recorded_video_id'] = '';
                $entities[$i]['channel_details'] = [];
                $entities[$i]['is_rating_review'] = false;
                $entities[$i]['is_sent_recording'] = false;
                $entities[$i]['is_available'] = (strtotime($row['start_datetime']) < time()) ? false : true;
                $entities[$i]['session_id'] = $row['session_id'];
                $entities[$i]['content_id'] = $row['session_id'];
                $entities[$i]['type_id'] = $row['session_id'];
                $entities[$i]['type'] = 'session';
                $entities[$i]['trending_type'] = 'session';
                $entities[$i]['cover_image1'] = $row['cover_image1'];
                $entities[$i]['cover_image2'] = $row['cover_image2'];
                $entities[$i]['cover_image3'] = $row['cover_image3'];
                $entities[$i]['cover_image4'] = $row['cover_image4'];
                $entities[$i]['cover_image5'] = $row['cover_image5'];
                $coverImg = base_url() . "uploads/sessionBgDefault.jpeg";
                $tempcover = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
                $coverimageArry = explode(",", $tempcover);
                $entities[$i]['cover_image'] = $coverimageArry;
                //$entities[$i]['cover_image'] = $row['cover_image'];
                $entities[$i]['session_topic'] = $row['session_topic'];
                $entities[$i]['specialities_name'] = $row['specialities_name'];
                $entities[$i]['specialities_ids_and_names'] = explode_speciality_string($row['specialities_ids_and_names']);
                $entities[$i]['speciality_id'] = $row['speciality_id'];
                $entities[$i]['session_description'] = $row['session_description'];
                $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
                $entities[$i]['client_id'] = $row['client_id'];
                $entities[$i]['vendor_meta_data'] = $row['meta_data'];
                $entities[$i]['client_name'] = $row['client_name'];
                $entities[$i]['status_name'] = $row['status_name'];
                $entities[$i]['vendor_id'] = $row['vendor_id'];
                $entities[$i]['room_id'] = $row['room_id'];
                $entities[$i]['vouchpro_url'] = $row['vouchpro_url'];
                $entities[$i]['go_to_meeting_url'] = $row['go_to_meeting_url'];
                $entities[$i]['landing_page_url'] = $row['landing_page_url'];
                $entities[$i]['video_embed_src'] = $row['video_embed_src'];
                $entities[$i]['session_cast_type'] = $row['session_cast_type'];
                $entities[$i]['is_share'] = $row['is_share'];
                $sponserentity = array();
                $sponsorLogoArry = explode(",", $row['sponsor']);
                $sponsorNameArry = explode(",", $row['sponsor_logo']);
                $ii = 0;
                foreach ($sponsorLogoArry as $singlelogo) {
                    if ($singlelogo != "") {
                        $sponserentity[$ii]['sponsor_name'] = $singlelogo;
                        $sponserentity[$ii]['sponsor_logo'] = $sponsorNameArry[$ii];
                    }
                    $ii++;
                }
                $entities[$i]['sponsor_entity'] = $sponserentity;
                $entities[$i]['all_sponsor'] =  $sponserentity;
                /**
                 * new sponsor logic
                 */
                $allsponsor = array();
                $sponsorname = explode(",", $row['sponsor']);
                $sp = 0;
                $sponsorLogoArry = explode(",", $row['sponsor_logo']);
                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = $valueSponor;
                            $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                            $sp++;
                        }
                    }
                } else {
                    if ($row['sponsor_logo']) {
                        // if full path exist
                        if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                            $sponsorLogomix[] = $row['sponsor_logo'];
                        } else {
                            $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                        }
                        $allsponsor[] = array('name' => $row['sponsor'], "logo" => $row['sponsor_logo']);
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                /**
                 * new sponsor logic
                 */
                $entities[$i]['sponsor_name'] = $row['sponsor'];
                $entities[$i]['sponsor_logo'] = $sponsorLogo;
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                // if ($row['document_path'] != "" || $row['document_path'] != null) {
                //     $entities[$i]['file_size'] = round((filesize('./uploads/mastersession_docs/' . $row['document_path'] . '') / 1024)) . "Kb";
                //     $entities[$i]['document_path_exact_file_name'] = $row['document_path'];
                //     $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
                //     $entities[$i]['extension_logo_path'] = base_url() . "themes/front/images/" . get_logo_by_file_extension($row['document_path']);
                // } else {
                //     $entities[$i]['document_path_exact_file_name'] = "";
                //     $entities[$i]['document_path'] = "";
                //     $entities[$i]['file_size'] = "";
                //     $entities[$i]['extension_logo_path'] = "";
                // }


                $entities[$i]['document_path_exact_file_name'] = "";
                $entities[$i]['document_path'] = "";
                $entities[$i]['file_size'] = "";
                $entities[$i]['extension_logo_path'] = "";


                // if ($row['comment'] != "" || $row['comment'] != null) {
                //     $entities[$i]['comment'] = $row['comment'];
                // } else {
                //     $entities[$i]['comment'] = "";
                // }


                $entities[$i]['comment'] = "";

                $entities[$i]['category_id'] = $row['category_id'];
                $entities[$i]['category_name'] = $row['category_name'];
                $entities[$i]['category_image'] = base_url() . "/themes/front/images/session/" . $row['category_logo'];
                $entities[$i]['start_datetime_old'] = $row['start_datetime'];
                $entities[$i]['start_datetime'] = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                    ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                    ->format('Y-m-d H:i:s');
                $entities[$i]['start_datetimex'] = strtotime($row['start_datetime']);
                $entities[$i]['now_datetimex'] = time();
                $start_time = $row['start_datetime'];
                $date = new DateTime($start_time);
                //$start_time = date("g:i A", strtotime($start_time));
                $now = new DateTime();
                $diff = date_diff($date, $now);
                $entities[$i]['days_remaining'] = abs($diff->format("%R%a")) + 1;
                $end_time = $row['end_datetime'];
                $end_time = date("g:i A", strtotime($end_time));
                $entities[$i]['display_time_format_old'] = $start_time . "-" . $end_time;
                $start_time_f = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                    ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                    ->format('Y-m-d H:i:s');
                $end_time_f = (new DateTime($row['end_datetime'], new DateTimeZone('Asia/Kolkata')))
                    ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                    ->format('g:i A');
                $entities[$i]['display_time_format'] = $start_time_f . "-" . $end_time_f;
                $post_time = $row['start_datetime'];
                $phpdate = strtotime($post_time);
                $mysqldate = date('D, j M `y  ', $phpdate);
                $entities[$i]['display_date_format'] = $mysqldate;
                $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);

                $start_date = $row['start_datetime'];
                $buffer_day = $row['add_question_buffer_days'];
                $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
                $buffer_str = strtotime($last_display_date);
                $t = time();
                $date = new DateTime($last_display_date);
                $now = new DateTime();
                //$now_str = strtotime("now");
                $diff = date_diff($date, $now);
                //print_r($diff);
                if ($t <= $buffer_str) {
                    $dat_diff = abs($diff->format("%R%a"));
                } else {
                    $dat_diff = 0;
                }
                $entities[$i]['view_edit_button_text'] = "";
                //echo $dat_diff; exit();
                if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
                    $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
                }
                if ($question_users->question != "" && $row['session_status'] != 3) {
                    $entities[$i]['view_edit_button_text'] = "View Case/Query";
                }
                // $is_attended_array = array();
                // $is_attended_array = explode(",", $row['IS_ATTENDED']);
                // $part_array = array();
                // $part_array = explode(",", $row['PartName']);
                // //$user_id = $CI->session->userdata['user_master_id'];
                // $inc = 0;
                // foreach ($part_array as $single) {
                //     if ($single == $user_master_id) {
                //         $key_val = $inc;
                //     }
                //     $inc++;
                // }
                // $is_att = $is_attended_array[$key_val];
                $entities[$i]['missed_session_text'] = "";
                if ($is_att == 2) {
                    $entities[$i]['missed_session_text'] = "You Missed The Session";
                }
                $entities[$i]['i_cant_attend_button'] = 0;
                $end_time = $row['end_datetime'];
                $end_time = strtotime($end_time);
                $now_time = date('Y-m-d H:i:s');
                $now_time = strtotime($now_time);
                if ($now_time < $end_time) {
                    $entities[$i]['i_cant_attend_button'] = 1;
                }
                // $cpt_flag = 0;
                // //$on_of_booking_button = 0;
                // $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
                // if ($row['users'] == null) {
                //     $total_original_booking = 0;
                // } else {
                //     $users_array = array();
                //     $users_array = explode(",", $row['users']);
                //     $total_original_booking = count($users_array);
                // }
                // if ($total_original_booking < $row['total_seats']) {
                //     $total_booking = $total_original_booking;
                // }
                // if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                //     //$minus_flag = $total_after_buffer - $total_original_booking;
                //     $total_booking = ($row['total_seats']) - 1;
                // }
                // if ($total_original_booking >= ($total_after_buffer)) {
                //     $total_booking = $row['total_seats'];
                //     // $on_of_booking_button = 1;
                //     $cpt_flag = 1;
                // }
                // if ($total_booking > 0) {
                //     $available_percent = ($total_booking / $row['total_seats']) * 100;
                // } else {
                //     $available_percent = 0;
                // }
                // $available_percent = round($available_percent);
                // if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
                //     $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
                //     $entities[$i]['total_seat'] = $row['total_seats'];
                //     $entities[$i]['total_booking_left'] = $total_booking;
                // } else {
                //     $entities[$i]['total_seat'] = $row['total_seats'];
                //     $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
                // }
                // $perc = $available_percent;
                // if ($cpt_flag == 0) {
                //     $entities[$i]['percentage'] = ceil($perc);
                // } else {
                //     $entities[$i]['percentage'] = ceil($perc);
                // }

                $entities[$i]['total_seat'] = $row['total_seats'];
                $entities[$i]['total_booking_left'] = null;
                $entities[$i]['percentage'] = null;

                $color = null;//get_progress_color($perc);
                $entities[$i]['color_profress_bar'] = $color;
                $entities[$i]['session_status'] = $row['session_status'];
                //$entities[$i]['start_datetime'] = date(' jS F y', strtotime($row['start_datetime']));
                $end_time = $row['end_datetime'];
                $end_time = date("g:i A", strtotime($end_time));
                $start_time = $row['start_datetime'];
                $start_time = date("g:i A", strtotime($start_time));
                $entities[$i]['display_date_old'] = $start_time . "-" . $end_time;
                $start_time_x = (new DateTime($row['start_datetime'], new DateTimeZone('Asia/Kolkata')))
                    ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                    ->format('g:i A');
                $end_time_x = (new DateTime($row['end_datetime'], new DateTimeZone('Asia/Kolkata')))
                    ->setTimezone(new DateTimeZone(date_default_timezone_get()))
                    ->format('g:i A');
                $entities[$i]['display_date'] = $start_time_x . "-" . $end_time_x;
                $entities[$i]['deeplink'] = ($env != 1) ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0); //$row['deeplink'];
                $entities[$i]['end_datetime'] = $row['end_datetime'];
                $entities[$i]['specialities_name'] = $row['specialities_name'];
                $entities[$i]['specialities_ids_and_names'] = explode_speciality_string($row['specialities_ids_and_names']);
                $entities[$i]['ms_cat_name'] = $row['category_name'];
                $entities[$i]['category_image'] = base_url() . "/themes/front/images/session/" . $row['category_logo'];
                // $entities[$i]['client_logo'] = base_url() . "uploads/logo/" . $row['client_logo'];
                $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
                $entities[$i]['ms_cat_logo'] = $row['category_logo'];
                $entities[$i]['doctor_name'] = $row['doctor_name'];
                $entities[$i]['speciality'] = $row['speciality'];
                $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
                $session_doc_array = explode(",", $row['session_doctor_id']);
                $ses_doc_det_array = array();
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {
                    #print_r($single_doctor);
                    $var = session_doc_detail($single_doctor);
                    # print_r($var);
                    $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
                    if ($image) {

                        if (stripos($image, "https://") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image_path = docimg; //"uploads/docimg/" . $image;
                            $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                            $logic_image = $imgPr;
                        }

                    } else {
                        $logic_image = docimg;
                    }
                    #print_r($var);
                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $var[0]['sessions_doctors_id'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $ses_doc_det_array[$inc_pp]['description'] = $var[0]['description'];
                    $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                    $inc_pp++;
                }
                $entities[$i]['sponsor_id'] = $row['sponsor_id'];
                $sponsor_array = explode(",", $row['sponsor_id']);
                $sponsor_det_array = array();
                if (count($sponsor_array) > 1) {
                    $inc_spp = 0;
                    foreach ($sponsor_array as $single_sponsor) {
                        $var = sponsor_detail($single_sponsor);
                        $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);

                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image = '';
                        }
                        $sponsor_det_array[$inc_spp]['sponsor_name'] = $var[0]['client_name'];
                        $sponsor_det_array[$inc_spp]['sponsor_logo'] = $logic_image;
                        $sponsor_det_array[$inc_spp]['sponsor_id'] = $var[0]['client_master_id'];
                        $inc_spp++;
                    }
                } else {
                    if ($row['sponsor_id']) {
                        $var = sponsor_detail($row['sponsor_id']);
                        $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);

                        if (stripos($image, "https://storage.googleapis.com") > -1) {
                            $logic_image = $image;
                        } else {
                            $logic_image = ''; //base_url() . "uploads/docimg/MConsult.png";
                        }
                        $sponsor_det_array['sponsor_name'] = $var[0]['client_name'];
                        $sponsor_det_array['sponsor_logo'] = $logic_image;
                        $sponsor_det_array['sponsor_id'] = $var[0]['client_master_id'];
                    }
                }
                //$row['sessions_question'];
                if ($row['sessions_question'] != '') {
                    $qu_val = explode("#", $row['sessions_question']);
                    $queries = $qu_val;
                } else {
                    $queries = array();
                }

                $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
                //$entities[$i]['sponsor_entities'] = $sponsor_det_array;
                $entities[$i]['sponsor_entities'] = $sponserentity;
                $entities[$i]['all_sponsor'] = $sponserentity;
                $entities[$i]['session_queries'] = $queries;
                $entities[$i]['cpddetail'] = getcpddetails($row['session_id']);
                $entities[$i]["channel"] = getchannel($result->type_id, $user_master_id, 'session');
                $entities[$i]['survey'] = $vxPoll;
                $entities[$i]['locked'] = $key_locked;
                $entities[$i]['price'] = get_a_content_price($type_id, 2, $env);
                $entities[$i]['user_content_payment'] = get_user_content_status($row['session_id'], 2, $user_master_id);
                $entities[$i]['disclaimer'] = disclaimer('session');
                $i++;
            }

            return $entities;
            break;
        case 'epub':
            $cachename = "epub_detail_" . $type_id;
            $key_locked = get_user_package($user_master_id, 'epub');
            if ($key_locked == '') {
                return null;
            }
            if (!empty($type_id)) {
                if ($CI->myredis->get($cachename)) {
                    $result = $CI->myredis->get($cachename);
                    // print_r($res); exit;
                } else {



                    $sql = "WITH 
                        -- Base ePub data
                        epub_base AS (
                            SELECT
                                cm.epub_id as type_id,
                                cm.epub_description as description,
                                cm.epub_title as title,
                                cm.epub_img,
                                cm.epub_img_thumbnail,
                                cm.epub_file,
                                cm.start_like,
                                cm.added_on,
                                cm.publication_date as publish_date,
                                cm.deeplink,
                                cm.gl_deeplink,
                                cm.color,
                                cm.author,
                                cln.client_name,
                                cln.client_logo,
                                cTenv.price,
                                uTpyCont.status as user_contnet_payment_status,
                                rtmy.rating as myrating,
                                kv.status as vault
                            FROM epub_master as cm
                            JOIN client_master as cln 
                                ON cln.client_master_id = cm.client_id
                            LEFT JOIN content_to_env as cTenv 
                                ON cTenv.type_id = cm.epub_id AND cTenv.type = 9
                            LEFT JOIN payment_user_to_content as uTpyCont 
                                ON uTpyCont.type_id = cm.epub_id AND uTpyCont.type = 9 AND uTpyCont.user_master_id = {$user_master_id}
                            LEFT JOIN knwlg_rating as rtmy 
                                ON rtmy.post_id = cm.epub_id AND rtmy.post_type = 'epub' AND rtmy.rating != 0 AND rtmy.user_master_id = {$user_master_id}
                            LEFT JOIN knwlg_vault as kv 
                                ON kv.post_id = cm.epub_id AND kv.type_text = 'epub' AND kv.user_id = {$user_master_id}
                            WHERE cm.status = 3 
                                AND date(cm.publication_date) <= CURDATE()
                                
                                AND cm.epub_id = {$type_id}
                        ),

                        -- Specialties data
                        specialties AS (
                            SELECT
                                cmTs.epub_id,
                                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name
                            FROM epub_to_specialities as cmTs
                            JOIN master_specialities_V1 as ms 
                                ON ms.master_specialities_id = cmTs.specialities_id
                            WHERE cmTs.epub_id = {$type_id}
                            GROUP BY cmTs.epub_id
                        ),

                        -- Sponsors data
                        sponsors AS (
                            SELECT
                                cmTspon.epub_id,
                                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                            FROM epub_to_sponsor as cmTspon
                            JOIN client_master as clintspon 
                                ON clintspon.client_master_id = cmTspon.sponsor_id
                            WHERE cmTspon.epub_id = {$type_id}
                            GROUP BY cmTspon.epub_id
                        ),

                        -- Rating count
                        ratings AS (
                            SELECT 
                                post_id, 
                                COUNT(rating) as averageRating
                            FROM knwlg_rating
                            WHERE post_id = {$type_id} AND post_type = 'epub'
                            GROUP BY post_id
                        ),

                        -- Comment count
                        comments AS (
                            SELECT 
                                type_id, 
                                COUNT(knwlg_comment_id) as count_comment
                            FROM knwlg_comment
                            WHERE type_id = {$type_id} AND type = 'epub'
                            GROUP BY type_id
                        )

                        -- Main query joining all CTEs
                        SELECT
                            eb.*,
                            s.specialities_name,
                            sp.sponsor,
                            sp.sponsor_logo,
                            r.averageRating,
                            c.count_comment
                        FROM epub_base eb
                        LEFT JOIN specialties s ON s.epub_id = eb.type_id
                        LEFT JOIN sponsors sp ON sp.epub_id = eb.type_id
                        LEFT JOIN ratings r ON r.post_id = eb.type_id
                        LEFT JOIN comments c ON c.type_id = eb.type_id";


                    $query = $CI->db->query($sql);
                    $result = $query->row();
                    $CI->myredis->set($cachename, $result);
                }

                $cachename_v1 = "epub_detail_author" . $type_id;
                if ($CI->myredis->get($cachename_v1)) {
                    $resultAuther = $CI->myredis->get($cachename_v1);
                    // print_r($res); exit;
                } else {
                    $sqlAuther = "SELECT * FROM epub_to_author WHERE epub_id = $type_id ";
                    $queryAuther = $CI->db->query($sqlAuther);
                    $resultAuther = $queryAuther->result();
                    $CI->myredis->set($cachename_v1, $result);
                }
                $autherArray = array();
                $autherIndex = 0;
                foreach ($resultAuther as $rw) {
                    $autherArray[$autherIndex] = array();
                    $autherArray[$autherIndex]['author_name'] = $rw->author_name;
                    $autherArray[$autherIndex]['author_image'] = $rw->author_image;
                    $autherArray[$autherIndex]['author_description'] = $rw->author_description;
                    $autherIndex++;
                }


                if ($result->epub_img_thumbnail) {
                    $img = $result->epub_img_thumbnail; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;
                } else {
                    $img = '';
                }
                $allsponsor = array();
                $sponsorname = explode(",", $result->sponsor);
                $sp = 0;
                $sponsorLogoArry = explode(",", $result->sponsor_logo);
                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                            $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        }
                        $sp++;
                    }
                } else {
                    if ($result->sponsor_logo) {
                        $sponsorLogomix[] = '' . $result->sponsor_logo;
                        $allsponsor[] = array('name' => $result->sponsor, "logo" => $valueSponor);
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);

                // $ratingsql = "select
                // count(rt.rating) as averageRating
                // from knwlg_rating rt
                // where rt.post_id = " . $type_id . "
                // and  rt.post_type='epub'";
                // $queryratingsql = $CI->db->query($ratingsql);

                // if (($queryratingsql) && ($queryratingsql->num_rows() > 0)) {
                //     $rate = $queryratingsql->result();
                //     $avgrating['averageRating'] = $rate[0]->averageRating;
                // }

                //print_r($avgrating); exit;
                // $myratingsql = "select
                // rating  as myrating
                // from knwlg_rating where post_id = " . $type_id . "
                // and  post_type='epub'
                // and rating!=0 and user_master_id =" . $user_master_id;

                // $querymyratingsql = $CI->db->query($myratingsql);
                // //print_r($CI->db->last_query()); exit;
                // if (($querymyratingsql) && ($querymyratingsql->num_rows() > 0)) {
                //     $myrate = $querymyratingsql->result();
                //     $avgrating['myrating'] = $myrate[0]->myrating;
                // } else {
                //     $avgrating['myrating'] = "";
                // }

                // $vaultstatus = "select
                // status from knwlg_vault
                // where post_id =" . $type_id . " and
                // type_text='epub' " . $user_master_id;

                // $queryvault = $CI->db->query($vaultstatus);
                // if (($queryvault) && ($queryvault->num_rows() > 0)) {
                //     $vaultres = $queryvault->result();
                //     $vault['vault'] = $vaultres[0]->vaultres;
                // } else {
                //     $vault['vault'] = 0;
                // }

                $string = $result->comp_qa_question_raw;
                $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
                $main_description = "";
                $main_description = str_replace("\n\t", "\n", $result->comp_qa_answer_raw);
                $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);

                // print_r($result->publish_date);
                // exit;
                $vxPoll = array();
                $vx = array(
                    "content_id" => $result->type_id,
                    "type_id" => $result->type_id,
                    "type" => 'epub',
                    "date" => date(' jS F y', strtotime($result->publish_date)),
                    "title" => html_entity_decode($result->title),
                    "description" => html_entity_decode($result->description),
                    //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),
                    "epub_file" => $result->epub_file,
                    "author" => $result->author,
                    "image" => $img,
                    "author_entities" => $autherArray,
                    "specialities" => $result->specialities_name,
                    "specialities_id" => $result->comp_qa_speciality_id,
                    // "client_name" => $result->client_name,
                    // "client_logo" => '' . $result->client_logo,
                    //============ integrated for subscription ============//
                    "is_locked" => $key_locked,
                    "price" => get_a_content_price($type_id, 9, $env),
                    "user_content_payment" => $result->user_contnet_payment_status,
                    //get_user_content_status($result->type_id, 9, $user_master_id),
                    //============ integrated for subscription ============//
                    "sponsor_name" => $result->sponsor,
                    "sponsor_logo" => $sponsorLogo,
                    "all_sponsor" => $allsponsor,
                    "start_like" => $result->start_like,
                    "comment_count" => $result->count_comment,
                    "rating" => ($avgrating['averageRating'] != '') ? ($avgrating['averageRating'] + $result->start_like) : $result->start_like,
                    "myrating" => ($avgrating['myrating'] != '') ? true : false,
                    "vault" => ($vault['vault'] != '') ? $vault['vault'] : 0,
                    "deeplink" => ($env != 1) ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
                    "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,
                    "disclaimer" => disclaimer('knowledge'),
                    "survey" => $vxPoll,
                );

                //echo $sql;
                //exit;
                return $vx;
            }
            break;
        case 'channel':
            $key_locked = get_user_package($user_master_id, 'channel');
            if ($key_locked == '') {
                return null;
            }
            // $sqlInt = "select
            //     count(channel_master_id) as total
            //     from
            //     channel_to_user
            //     where
            //     status = 3
            //     and
            //     channel_master_id = " . $type_id . "";
            // $queryInt = $CI->db->query($sqlInt);
            // $resultInt = $queryInt->row();

            $sql = "SELECT
                ch.channel_master_id as type_id,
                ch.title as title,
                ch.description as description,
                ch.short_description as short_description,
                ch.membershipformurl,
                cTus.status as followed_status,
                ch.cover_image,
                ch.privacy_status,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                ch.logo,
                ch.address,
                ch.added_on,
                ch.deeplink,
                ch.gl_deeplink,
                ch.featured_video,
                bnd.logo,
                cln.client_name,
                cln.client_logo,
                GROUP_CONCAT(DISTINCT chTsdoc.session_doctor_id  ORDER BY  chTsdoc.session_doctor_id  ASC ) as session_doctor_id,
                GROUP_CONCAT(DISTINCT chTsdoc.description  ORDER BY  chTsdoc.session_doctor_id  ASC SEPARATOR '----') as ch_doc_description
                FROM channel_master as ch
                LEFT JOIN channel_to_specialities  as chTs ON chTs.channel_master_id = ch.channel_master_id
                LEFT JOIN master_specialities_V1 as ms ON ms.master_specialities_id = chTs.specialities_id
                LEFT JOIN client_master as cln ON cln.client_master_id = ch.client_id
                LEFT JOIN clirbanner_master_brand as bnd ON bnd.id = ch.brand_id
                LEFT JOIN content_to_env as cTenv ON cTenv.type_id = ch.channel_master_id and  cTenv.type = 11
                LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = ch.channel_master_id and  uTpyCont.type = 11 and 	uTpyCont.user_master_id = " . $user_master_id . "
                LEFT JOIN channel_to_session_doctor as chTsdoc ON chTsdoc.channel_master_id = ch.channel_master_id
                LEFT JOIN channel_to_user  as cTus ON (cTus.channel_master_id = ch.channel_master_id and user_master_id = " . $user_master_id . "   )
                WHERE
                ch.status=3
                " . $envStatus . "
                and
                ch.channel_master_id = " . $type_id . "";
            //echo $sql; exit;
            //exit;
            //add child checking in this sql
            //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
            //exit;
            //echo  $sql; exit;
            $query = $CI->db->query($sql);
            //$CI->db->cache_off();
            $val = $query->row();
            //print_r($result); exit;
            //echo $val->gr_preview_image;
            $ses_doc_det_array = array();
            if ($val->session_doctor_id) {
                $session_doc_array = explode(",", $val->session_doctor_id);
                $session_gr_doc_description_array = explode("----", $val->ch_doc_description);
                $inc_pp = 0;
                foreach ($session_doc_array as $single_doctor) {
                    $var = session_doc_detail($single_doctor);
                    if ($session_gr_doc_description_array[$inc_pp]) {
                        $gr_doc_descriptionLa = $session_gr_doc_description_array[$inc_pp];
                    } else {
                        $gr_doc_descriptionLa = '';
                    }
                    //print_r($var);
                    if (stripos($var[0]['profile_image'], "https://storage.googleapis.com") > -1) {
                        $logic_image = $var[0]['profile_image'];
                    } else {
                        $logic_image = docimg;
                        //$logic_image = $var[0]['profile_image'];
                    }
                    $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                    $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                    $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
                    $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                    $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                    $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                    // $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $var[0]['profile'];//$var[0]['description']; //$session_gr_doc_description_array[$inc_pp];
                    $ses_doc_det_array[$inc_pp]['gr_doc_description'] = $gr_doc_descriptionLa; //$session_gr_doc_description_array[$inc_pp];
                    $inc_pp++;
                }
            }
            $vx = array();
            $vx[] = array(
                "content_id" => $val->type_id,
                "type_id" => $val->type_id,
                "type" => 'channel',
                "added_on" => date(' jS F y', strtotime($val->added_on)),
                "title" => html_entity_decode(strip_tags($val->title)),
                "cover_image" => $val->cover_image,
                "logo" => $val->client_logo,
                "followed_status" => ($val->followed_status != '') ? $val->followed_status : 0,
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => get_a_content_price($type_id, 11, $env),
                "user_content_payment" => get_user_content_status($val->type_id, 11, $user_master_id),
                //============ integrated for subscription ============//
                "follower_count" => $resultInt->total,
                "address" => $val->address,
                "privacy_status" => $val->privacy_status,
                //"user_subs_status" => $val->address,
                "featured_video" => archiveVideoDetial($val->featured_video, $user_master_id),
                "featured_video_id" => $val->featured_video,
                "description" => $val->description,
                "short_description" => html_entity_decode(strip_tags(substr($val->short_description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "channel_doctor_entities" => $ses_doc_det_array,
                "client_name" => $val->client_name,
                "client_logo" => base_url('uploads/logo/') . $val->client_logo,
                "deeplink" => ($env != 1) ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //($val->deeplink != '') ? $val->deeplink : 0,
                "disclaimer" => disclaimer('knowledge'),
                "membershipformurl" => $val->membershipformurl,
            );
            return $vx;
            break;
        case "survey":

            $CI->db->select('id');
            $CI->db->from('survey_user_answer');
            $CI->db->where(array('user_master_id' => $user_master_id, 'survey_id' => $type_id));
            $ansquery = $CI->db->get();
            $key_locked = get_user_package($user_master_id, 'survey');
            // echo "<pre>";print_r($key_locked);die;
            if ($key_locked == '') {
                return null;
            }
            if (($ansquery) && ($ansquery->num_rows() > 0)) {
                // echo $user_master_id; exit;
                $cachename = "survey_detail_" . $type_id;
                // print_r($CI->myredis->get($cachename));
                // exit;
                if ($CI->myredis->get($cachename)) {
                    $val = $CI->myredis->get($cachename);
                    //print_r($val); exit;
                } else {
                    $sql = "SELECT
                    sv.* ,
                    cm.channel_master_id, cm.title, cm.logo, cm.description, cm.privacy_status, ctu.status as followed_status,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                    cln.client_name,
                    cln.client_logo,
                    sd.is_shuffle,
                    sd.data,
                    cTenv.price,
                    uTpyCont.status as user_contnet_payment_status,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                    FROM
                    survey sv
                    left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
                    left JOIN channel_to_survey as cts ON cts.survey_id = sv.survey_id
                    left JOIN channel_master as cm ON cm.channel_master_id = cts.channel_master_id
                    left JOIN channel_to_user as ctu ON ctu.channel_master_id = cm.channel_master_id and ctu.user_master_id =  $user_master_id
                    JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                    JOIN client_master as cln ON cln.client_master_id = sv.client_id
                    LEFT JOIN content_to_env as cTenv ON cTenv.type_id = sv.survey_id  and  cTenv.type = 6
                    LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = sv.survey_id  and  uTpyCont.type = 6 and 	uTpyCont.user_master_id = " . $user_master_id . "
                    LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                    JOIN survey_detail as sd ON   sd.survey_id = sv.survey_id
                    WHERE
                    sv.status = 3  and sd.is_draft = 0 and date(sv.publishing_date)<=CURDATE()
                    " . $envStatus . "
                    AND
                    sv.survey_id = " . $type_id . " ";
                    //echo $sql; exit;
                    //exit;
                    //add child checking in $CI sql
                    //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
                    //exit;
                    #echo  $sql; exit;
                    $query = $CI->db->query($sql);
                    //$CI->db->cache_off();
                    $val = $query->row();
                    // echo "<pre>";
                    // print_r($val);
                    // die;
                    $CI->myredis->set($cachename, $val);
                }
                $Shufflestatus = $val->is_shuffle;
                $dataArry = unserialize($val->data);
                //remove correct answer from the json
                foreach ($dataArry as $key => $value) {
                    foreach ($value as $key2 => $value2) {
                        unset($dataArry[$key][$key2]['correctoption']);
                        //print_r($value2);
                    }
                }
                //remove correct answer from the json
                foreach ($dataArry['surveys'] as $key => $surveyArry) {
                    // if (  $id != 1035 && $id != 1036  && $id != 1158 ) {
                    if ($type_id != 1158) {
                        if ($Shufflestatus == 1) {
                            //  echo "a";
                        } else {
                            // echo "b";
                            shuffle($surveyArry['options']);
                        }
                    }
                    //print_r($surveyArry['options']);
                    $dataArry['surveys'][$key]['options'] = $surveyArry['options'];
                }
                // print_r($dataArry);
                //exit;
                //$optionArray = $dataArry['surveys']['question1']['options'];
                //print_r($optionArray);
                //shuffle($optionArray);
                //$dataArry['surveys']['question1']['options'] = $optionArray;
                //print_r($dataArry);
                //exit;
                $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                $str = preg_replace('/\\\"/', "\"", $json);
                #echo stripslashes($str);
                $user_survey_status = detail_user_status($type_id, $user_master_id);
                $allsponsor = array();
                $sponsorname = explode(",", $val->sponsor);
                $sp = 0;
                $sponsorLogoArry = explode(",", $val->sponsor_logo);
                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = $valueSponor;
                            $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                            $sp++;
                        }
                    }
                } else {
                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = $val->sponsor_logo;
                        $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                $CI->db->select('cm.status as followed_status');
                $CI->db->from('channel_master as cm');
                $CI->db->join('channel_to_survey as cts', 'cts.channel_master_id = cm.channel_master_id');
                $CI->db->join('channel_to_user as ctu', "(ctu.channel_master_id = cm.channel_master_id and ctu.user_master_id =  {$user_master_id})");
                $CI->db->where('cts.survey_id', $val->survey_id);
                $query_fstatus = $CI->db->get();
                // echo '<pre>';print_r($query_fstatus);die;
                if (($query_fstatus) && ($query_fstatus->num_rows() > 0)) {
                    $statusfollowed = $query->result();
                    $fstatus = $statusfollowed->followed_status;
                } else {
                    $fstatus = 0;
                }
                if (($val->channel_master_id != null) || ($val->channel_master_id != '')) {
                    $channel_details = array(
                        "channel_id" => $val->channel_master_id,
                        "title" => $val->title,
                        "logo" => $val->logo,
                        "description" => $val->description,
                        "privacy_status" => $val->privacy_status,
                        "followed_status" => $fstatus
                    );
                }
                $vx[] = array(
                    "content_id" => $val->survey_id,
                    "survey_id" => $val->survey_id,
                    "category" => $val->category,
                    "survey_title" => $val->survey_title,
                    "deeplink" => ($env != 1) ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                    "survey_description" => html_entity_decode($val->survey_description),
                    "survey_points" => $val->survey_points,
                    "point" => $val->survey_points,
                    "points_on_approval" => $val->points_on_approval,
                    "survey_time" => $val->survey_time,
                    "question_count" => $val->question_count,
                    "image" => $val->image,
                    "specialities_name" => $val->specialities_name,
                    "specialities_ids_and_names" => explode_speciality_string($val->specialities_ids_and_names),
                    // "client_name" => $val->client_name,
                    // "client_logo" => $val->client_logo,
                    "is_locked" => $key_locked,
                    "price" => $val->price,
                    "user_content_payment" => get_user_content_status($val->survey_id, 6, $user_master_id),
                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => $sponsorLogo,
                    "all_sponsor" => $allsponsor,
                    "verified" => $val->verified,
                    "publishing_date" => $val->publishing_date,
                    "data_json" => $str,
                    "user_survey_status" => $user_survey_status,
                    'disclaimer' => 'The Sponsor of this Survey is solely responsible for its content. The purpose of this survey is for research & information only and is meant for participation by registered medical practitioners only. Your participation in this survey is voluntary. The information given and results expressed in this activity are those of the participants and not that of CLIRNET or the Sponsor. The information given and results do not represent and cannot be construed as an  endorsement by CLIRNET or the Sponsor. CLIRNET at the request of the Survey Sponsor, may share your personal details such as name, location and survey results with the Sponsor for information purposes only. If you wish to not share your personal information with the Sponsor, then please do not respond to the survey. You may refer to our Privacy Policy for further details. CLIRNET reserves the right to terminate or withdraw a survey, or your opportunity to participate in the survey, at any time for any reason.',
                    "channel" => $channel_details
                );
                return $vx;
                //add child checking in this sql
                //echo $sql;
                //exit;
            } else {
                $sql = "SELECT
                sv.* ,
                GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
                cln.client_name,
                cln.client_logo,
                sd.data,
                cTenv.price,
                uTpyCont.status as user_contnet_payment_status,
                GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM
                survey sv
                left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
                JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                JOIN client_master as cln ON cln.client_master_id = sv.client_id
                LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
                LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                LEFT JOIN content_to_env as cTenv ON cTenv.type_id = sv.survey_id  and  cTenv.type = 6
                LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = sv.survey_id  and  uTpyCont.type = 6 and 	uTpyCont.user_master_id = " . $user_master_id . "
                JOIN survey_detail as sd ON   sd.survey_id = sv.survey_id
                WHERE
                sv.status = 3  and sd.is_draft = 0 and date(sv.publishing_date)<=CURDATE()
                " . $envStatus . "
                AND
                sv.survey_id = " . $type_id . " ";
                #echo  $sql; exit;
                $query = $CI->db->query($sql);
                //$CI->db->cache_off();
                $val = $query->row();
                // echo "<pre>";print_r($val);die;
                $dataArry = unserialize($val->data);
                //remove correct answer from the json
                foreach ($dataArry as $key => $value) {
                    foreach ($value as $key2 => $value2) {
                        unset($dataArry[$key][$key2]['correctoption']);
                        //print_r($value2);
                    }
                }
                //remove correct answer from the json
                foreach ($dataArry['surveys'] as $key => $surveyArry) {
                    // if (  $id != 1035 && $id != 1036  && $id != 1158 ) {
                    if ($type_id != 1158) {
                        shuffle($surveyArry['options']);
                    }
                    //print_r($surveyArry['options']);
                    $dataArry['surveys'][$key]['options'] = $surveyArry['options'];
                }

                $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                $str = preg_replace('/\\\"/', "\"", $json);
                //echo stripslashes($json);
                $user_survey_status = detail_user_status($type_id, $user_master_id);
                $allsponsor = array();
                $sponsorname = explode(",", $val->sponsor);
                $sp = 0;
                $sponsorLogoArry = explode(",", $val->sponsor_logo);
                if (count($sponsorLogoArry) > 0) {
                    foreach ($sponsorLogoArry as $valueSponor) {
                        if ($valueSponor) {
                            $sponsorLogomix[] = $valueSponor;
                            $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                            $sp++;
                        }
                    }
                } else {
                    if ($val->sponsor_logo) {
                        $sponsorLogomix[] = $val->sponsor_logo;
                        $allsponsor[] = array('name' => $val->sponsor, "logo" => $valueSponor);
                    }
                }
                $sponsorLogo = implode(",", (array)$sponsorLogomix);
                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                $vx[] = array(
                    "survey_id" => $val->survey_id,
                    "category" => $val->category,
                    "survey_title" => $val->survey_title,
                    "deeplink" => ($env != 1) ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                    "survey_description" => html_entity_decode($val->survey_description),
                    "survey_points" => $val->survey_points,
                    "point" => $val->survey_points,
                    "points_on_approval" => $val->points_on_approval,
                    "survey_time" => $val->survey_time,
                    "question_count" => $val->question_count,
                    "image" => $val->image,
                    "specialities_name" => $val->specialities_name,
                    "specialities_ids_and_names" => explode_speciality_string($val->specialities_ids_and_names),
                    // "client_name" => $val->client_name,
                    // "client_logo" => $val->client_logo,
                    "is_locked" => $key_locked,
                    "price" => get_a_content_price($type_id, 6, $env),
                    "user_content_payment" => get_user_content_status($val->survey_id, 6, $user_master_id),
                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => $sponsorLogo,
                    "all_sponsor" => $allsponsor,
                    "verified" => $val->verified,
                    "publishing_date" => $val->publishing_date,
                    "data_json" => $str,
                    "user_survey_status" => $user_survey_status,
                    'disclaimer' => 'The Sponsor of this Survey is solely responsible for its content. The purpose of this survey is for research & information only and is meant for participation by registered medical practitioners only. Your participation in this survey is voluntary. The information given and results expressed in this activity are those of the participants and not that of CLIRNET or the Sponsor. The information given and results do not represent and cannot be construed as an  endorsement by CLIRNET or the Sponsor. CLIRNET at the request of the Survey Sponsor, may share your personal details such as name, location and survey results with the Sponsor for information purposes only. If you wish to not share your personal information with the Sponsor, then please do not respond to the survey. You may refer to our Privacy Policy for further details. CLIRNET reserves the right to terminate or withdraw a survey, or your opportunity to participate in the survey, at any time for any reason.'
                );
                return $vx;
            }
            /** check survey answer submited or not */
            break;
        case "archivevideo":
            $cachename = "archieve_detail_" . $type_id;
            $key_locked = get_user_package($user_master_id, 'video_archived');
            if ($key_locked == '') {
                return null;
            }
            if ($CI->myredis->get($cachename)) {
                $result = $CI->myredis->get($cachename);
                //print_r($result); exit;
            } else {


                $sql = "WITH 
                    -- CTE for ratings data
                    ratings AS (
                        SELECT 
                            post_id,
                            COUNT(rating) as averageRating
                        FROM knwlg_rating 
                        WHERE post_type = 'video_archive' AND rating != 0
                        GROUP BY post_id
                    ),
                    -- CTE for user ratings
                    user_ratings AS (
                        SELECT 
                            post_id,
                            rating
                        FROM knwlg_rating
                        WHERE post_type = 'video_archive' AND rating != 0 
                        AND user_master_id = {$user_master_id}
                    ),
                    -- CTE for comment counts
                    comments AS (
                        SELECT 
                            type_id,
                            COUNT(knwlg_comment_id) as count_comment
                        FROM knwlg_comment
                        WHERE type = 'video_archive'
                        GROUP BY type_id
                    ),
                    -- CTE for main content with proper joins
                    main_content AS (
                        SELECT
                            cm.video_archive_id as type_id,
                            cm.video_archive_question,
                            cm.video_archive_answer,
                            cm.video_archive_question_raw,
                            cm.video_archive_answer_raw,
                            cm.video_archive_file_img,
                            cm.video_archive_file_img_thumbnail,
                            cm.video_archive_citation,
                            cm.start_like,
                            cm.added_on,
                            cm.publication_date,
                            cln.client_name,
                            cln.client_logo,
                            cTenv.price,
                            uTpyCont.status as user_contnet_payment_status,
                            cm.type,
                            cm.vendor,
                            cm.is_share,
                            cm.src,
                            cm.deeplink,
                            cm.gl_deeplink,
                            cm.video_archive_tags,
                            ks.session_doctor_id,
                            kvtd.play_time,
                            cm.video_archive_speciality_id,
                            kv.status as vault
                        FROM knwlg_video_archive as cm
                        LEFT JOIN knwlg_video_tracking_data as kvtd 
                            ON kvtd.content_id = cm.video_archive_id 
                            AND kvtd.content_type = 'video_archive' 
                            AND kvtd.user_master_id = {$user_master_id}
                        LEFT JOIN knwlg_sessions_V1 as ks 
                            ON ks.session_id = cm.video_archive_session_id
                        LEFT JOIN client_master as cln 
                            ON cln.client_master_id = cm.client_id
                        LEFT JOIN content_to_env as cTenv 
                            ON cTenv.type_id = cm.video_archive_id 
                            AND cTenv.type = 3
                        LEFT JOIN payment_user_to_content as uTpyCont 
                            ON uTpyCont.type_id = cm.video_archive_id 
                            AND uTpyCont.type = 3 
                            AND uTpyCont.user_master_id = {$user_master_id}
                        LEFT JOIN knwlg_vault as kv 
                            ON kv.post_id = cm.video_archive_id 
                            AND kv.type_text = 'video_archive' 
                            AND kv.user_id = {$user_master_id}
                        WHERE cm.status = 3 
                            AND date(cm.publication_date) <= CURDATE()
                            {$envStatus}
                            AND cm.video_archive_id = {$type_id}
                    ),
                    -- CTE for specialties data
                    specialties AS (
                        SELECT 
                            cmTs.video_archive_id,
                            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                            GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) as specialities_ids_and_names
                        FROM video_archive_to_specialities as cmTs
                        JOIN master_specialities_V1 as ms 
                            ON ms.master_specialities_id = cmTs.specialities_id
                        GROUP BY cmTs.video_archive_id
                    ),
                    -- CTE for sponsor data
                    sponsors AS (
                        SELECT 
                            cmTspon.video_archive_id,
                            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                        FROM video_archive_to_sponsor as cmTspon
                        JOIN client_master as clintspon 
                            ON clintspon.client_master_id = cmTspon.sponsor_id
                        GROUP BY cmTspon.video_archive_id
                    )

                    -- Main query joining all CTEs
                    SELECT 
                        m.*,
                        r.averageRating,
                        ur.rating as myrating,
                        c.count_comment,
                        s.specialities_name,
                        s.specialities_ids_and_names,
                        sp.sponsor,
                        sp.sponsor_logo
                    FROM main_content m
                    LEFT JOIN ratings r ON r.post_id = m.type_id
                    LEFT JOIN user_ratings ur ON ur.post_id = m.type_id
                    LEFT JOIN comments c ON c.type_id = m.type_id
                    LEFT JOIN specialties s ON s.video_archive_id = m.type_id
                    LEFT JOIN sponsors sp ON sp.video_archive_id = m.type_id;";
                // echo $sql;
                // exit;
                $query = $CI->db->query($sql);
                $result = $query->row();
                $CI->myredis->set($cachename, $result);
            }

            if ($result->video_archive_file_img) {
                $img = $result->video_archive_file_img; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;
            } else {
                $img = '';
            }
            $allsponsor = array();
            $sponsorname = explode(",", $result->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $result->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                        $sp++;
                    }
                }
            } else {
                if ($result->sponsor_logo) {
                    $sponsorLogomix[] = '' . $result->sponsor_logo;
                    $allsponsor[] = array('name' => $result->sponsor, "logo" => $valueSponor);
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            //---------------------------------------------------------------------------
            $session_doc_array = explode(",", $result->session_doctor_id);
            $ses_doc_det_array = array();
            $inc_pp = 0;
            foreach ($session_doc_array as $single_doctor) {
                $var = session_doc_detail($single_doctor);
                $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);

                if ($image) {
                    if (stripos($image, "https://storage.googleapis.com") > -1) {
                        $logic_image = $image;
                    } else {
                        $logic_image_path = docimg; //"uploads/docimg/" . $image;
                        $imgPr = image_thumb_url($logic_image_path, $image, 75, 75, '');
                        $logic_image = $imgPr;
                    }

                } else {
                    $logic_image = docimg;
                }
                $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
                $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
                $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
                $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
                $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
                $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
                $inc_pp++;
            }
            // rating starts
            // $ratingsql = "select
            // count(rt.rating) as averageRating
            // from knwlg_rating rt
            // where rt.post_id = " . $type_id . "
            // and  rt.post_type='video_archive'
            // and rt.rating!=0";

            // $queryratingsql = $CI->db->query($ratingsql);
            // if (($queryratingsql) && ($queryratingsql->num_rows() > 0)) {
            //     $rate = $queryratingsql->result();
            //     $avgrating['averageRating'] = $rate[0]->averageRating;
            // }

            // $myratingsql = "select
            // rating  as myrating
            // from knwlg_rating
            // where post_id = " . $type_id . "
            // and  post_type='video_archive'
            // and rating!=0
            // and user_master_id =" . $user_master_id;

            // $querymyratingsql = $CI->db->query($myratingsql);
            // //print_r($CI->db->last_query()); exit;
            // if (($querymyratingsql) && ($querymyratingsql->num_rows() > 0)) {
            //     $myrate = $querymyratingsql->result();
            //     $avgrating['myrating'] = $myrate[0]->myrating;
            // } else {
            //     $avgrating['myrating'] = "";
            // }

            // $vaultstatus = "select
            // kv.status
            // from knwlg_vault as kv
            // where kv.post_id =" . $type_id . "
            // and  kv.type_text='video_archive'
            // and kv.user_id=" . $user_master_id;

            // //echo $vaultstatus; exit;
            // $queryvault = $CI->db->query($vaultstatus);
            // if (($queryvault) && ($queryvault->num_rows() > 0)) {
            //     $vaultres = $queryvault->result();
            //     $vault['vault'] = $vaultres[0]->vaultres;
            // } else {
            //     $vault['vault'] = 0;
            // }
            // // echo $vault['vault'] ; exit;
            // $commentcountquery = "select
            // count(kcm.knwlg_comment_id) as count_comment
            // from knwlg_comment kcm
            // where kcm.type_id = " . $type_id . "
            // and kcm.type = 'video_archive'";

            // $commentcount = $CI->db->query($commentcountquery);
            // if (($commentcount) && ($commentcount->num_rows() > 0)) {
            //     $commentcount = $commentcount->result();
            //     //$comment_count = $commentcount[0]->vaultres;
            // } else {
            //     $commentcount = 0;
            // }
            //$string = htmlentities($result->comp_qa_question_raw, null, 'utf-8');
            $question = $result->video_archive_question_raw;
            $answer = $result->video_archive_answer_raw;
            $citationFlag = (strpos($result->video_archive_citation, 'id="references"') !== false);
            $vxPoll = array();


            $vx = array(
                "content_id" => $result->type_id,
                "type_id" => $result->type_id,
                "con_type" => $result->type,
                "type" => 'video_archive',
                "vendor" => $result->vendor,
                "src" => $result->src,
                "date" => date(' jS F y', strtotime($result->publication_date)),
                "title" => html_entity_decode($question),
                "question" => html_entity_decode($question),
                "answer" => html_entity_decode(substr($answer, 0, 100)),
                "video_archive_question" => $result->video_archive_question,
                "video_archive_answer" => $result->video_archive_answer,
                "image" => $img,

                "video_archive_citation" => $result->video_archive_citation,
                "isCitationExists" => $citationFlag,

                "specialities" => $result->specialities_name,
                "specialities_id" => $result->comp_qa_speciality_id,
                "specialities_ids_and_names" => explode_speciality_string($result->specialities_ids_and_names),

                "channel" => getchannel($result->type_id, $user_master_id, 'session'),
                "sponsor_name" => $result->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "all_sponsor" => $allsponsor,
                "play_time" => $result->play_time,
                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $result->price,
                //get_a_content_price($type_id, 3, $env),
                "user_content_payment" => $result->user_contnet_payment_status,
                //get_user_content_status($result->type_id, 3, $user_master_id),
                //============ integrated for subscription ============//
                "comment_count" => $result->count_comment, //$commentcount['count_comment'],
                "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
                "myrating" => ($result->myrating != '') ? true : false,
                "vault" => ($result->vault != '') ? $result->vault : 0, //$vault['vault']
                "is_share" => $result->is_share,
                "deeplink" => ($env != 1) ? (($result->gl_deeplink != '') ? $result->gl_deeplink : 0) : (($result->deeplink != '') ? $result->deeplink : 0), //($result->deeplink != '') ? $result->deeplink : 0,
                "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,
                "disclaimer" => disclaimer('knowledge'),
                "survey" => $vxPoll,
                "session_doctor_id" => $result->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
            );
            return $vx;
            break;
        default:
            return '';
            break;
    }
}



/**
 * Get content average rating (not user-dependent)
 * This can be cached as it's the same for all users
 *
 * @param string $type Content type (e.g., 'comp', 'epub', 'video', etc.)
 * @param int $type_id Content ID
 * @return int Returns the average rating count plus start_like
 */
if (!function_exists('get_content_average_rating')) {
    function get_content_average_rating($type, $type_id)
    {
        $CI = & get_instance();

        if (empty($type) || empty($type_id)) {
            return 0;
        }

        // Create cache key
        $cache_key = "avg_rating_{$type}_{$type_id}";

        // Check if data exists in cache
        $CI->load->library('Myredis');
        if ($CI->myredis->exists($cache_key)) {
            return $CI->myredis->get($cache_key);
        }

        // Get the content's start_like value
        $start_like = 0;
        switch ($type) {
            case 'comp':
                $table = 'knwlg_compendium_V1';
                $id_field = 'comp_qa_id';
                break;
            case 'epub':
                $table = 'epub_master';
                $id_field = 'epub_id';
                break;
            case 'gr':
                $table = 'knwlg_gr_register';
                $id_field = 'gr_id';
                break;
            case 'video':
            case 'video_archive':
                $table = 'knwlg_video_archive';
                $id_field = 'video_archive_id';
                $type = 'video_archive'; // Normalize type for rating query
                break;
            case 'session':
                $table = 'knwlg_sessions_V1';
                $id_field = 'session_id';
                break;
            case 'training':
            case 'course':
                $table = 'training_master';
                $id_field = 'id';
                $type = 'training'; // Normalize type for rating query
                break;
            default:
                $table = '';
                $id_field = '';
        }

        // Get start_like if table is defined
        if (!empty($table) && !empty($id_field)) {
            $start_like_query = "SELECT 
            start_like FROM {$table} 
            WHERE {$id_field} = {$type_id} 
            LIMIT 1";
            $start_like_result = $CI->db->query($start_like_query);

            if ($start_like_result->num_rows() > 0) {
                $row = $start_like_result->row();
                $start_like = isset($row->start_like) ? intval($row->start_like) : 0;
            }
        }

        // Get average rating (count of non-zero ratings)
        $avg_rating_sql = "SELECT 
            COUNT(rating) as averageRating 
            FROM knwlg_rating 
            WHERE post_id = {$type_id} 
            AND post_type = '{$type}' 
            AND rating != 0";

        $avg_rating_result = $CI->db->query($avg_rating_sql);
        $rating_count = 0;

        if ($avg_rating_result->num_rows() > 0) {
            $rating_count = intval($avg_rating_result->row()->averageRating);
        }

        $total_rating = $rating_count + $start_like;

        // Cache the result for 1 hour (adjust as needed)
        //$CI->myredis->set($cache_key, $total_rating, 3600);

        return $total_rating;
    }
}

/**
 * Get user-specific content interaction data (rating and vault status)
 * This cannot be cached globally as it's different for each user
 *
 * @param string $type Content type (e.g., 'comp', 'epub', 'video', etc.)
 * @param int $type_id Content ID
 * @param int $user_master_id User ID
 * @return array Returns user's rating and vault status
 */
if (!function_exists('get_user_content_interaction')) {
    function get_user_content_interaction($type, $type_id, $user_master_id)
    {
        $CI = & get_instance();

        // Initialize return array with default values
        $result = [
            'myrating' => false,
            'vault' => 0
        ];

        if (empty($type) || empty($type_id) || empty($user_master_id)) {
            return $result;
        }

        // Normalize content type if needed
        if ($type == 'video') {
            $type = 'video_archive';
        } elseif ($type == 'course') {
            $type = 'training';
        }

        // Create cache key specific to this user and content
        $cache_key = "user_interaction_{$user_master_id}_{$type}_{$type_id}";

        // Check if data exists in cache
        $CI->load->library('Myredis');
        if ($CI->myredis->exists($cache_key)) {
            return $CI->myredis->get($cache_key);
        }

        // Get user's rating
        $my_rating_sql = "SELECT 
            rating 
            FROM knwlg_rating 
            WHERE post_id = {$type_id} 
            AND post_type = '{$type}' 
            AND rating != 0 
            AND user_master_id = {$user_master_id}
            LIMIT 1";

        $my_rating_result = $CI->db->query($my_rating_sql);

        if ($my_rating_result->num_rows() > 0) {
            $result['myrating'] = true;
        }

        // Get vault status
        $vault_sql = "SELECT 
            status 
            FROM knwlg_vault 
            WHERE post_id = {$type_id} 
            AND type_text = '{$type}' 
            AND user_id = {$user_master_id}
            LIMIT 1";

        $vault_result = $CI->db->query($vault_sql);

        if ($vault_result->num_rows() > 0) {
            $result['vault'] = intval($vault_result->row()->status);
        }

        // Cache the result for this user for 15 minutes (adjust as needed)
        //$CI->myredis->set($cache_key, $result, 900);

        return $result;
    }
}

/**
 * Get complete content rating data (combines both functions above)
 * For backward compatibility and convenience
 *
 * @param string $type Content type (e.g., 'comp', 'epub', 'video', etc.)
 * @param int $type_id Content ID
 * @param int $user_master_id User ID
 * @return array Returns average rating, user's rating, and vault status
 */
if (!function_exists('get_content_rating_data')) {
    function get_content_rating_data($type, $type_id, $user_master_id)
    {
        // Get average rating (cacheable)
        $avg_rating = get_content_average_rating($type, $type_id);

        // Get user-specific data
        $user_data = get_user_content_interaction($type, $type_id, $user_master_id);

        // Combine the results
        return [
            'averageRating' => $avg_rating,
            'myrating' => $user_data['myrating'],
            'vault' => $user_data['vault']
        ];
    }
}


/**
 * Get sponsor data for any content type
 *
 * Returns formatted sponsor information including names, logos, and combined data
 *
 * @param string $sponsor_names Comma-separated list of sponsor names
 * @param string $sponsor_logos Comma-separated list of sponsor logos
 * @param bool $transform_urls Whether to transform image URLs using change_img_src()
 * @return array Returns sponsor data in a structured format
 */
if (!function_exists('get_sponsor_data')) {
    function get_sponsor_data($sponsor_names, $sponsor_logos, $transform_urls = true)
    {
        // Initialize return arrays
        $allsponsor = array();
        $sponsorLogomix = array();

        // If both sponsor names and logos are empty, return empty arrays
        if (empty($sponsor_names) && empty($sponsor_logos)) {
            return array(
                'all_sponsors' => array(),
                'sponsor_logo' => '',
                'sponsor_name' => ''
            );
        }

        // Split sponsor names and logos into arrays
        $sponsorname = !empty($sponsor_names) ? explode(",", $sponsor_names) : array();
        $sponsorLogoArry = !empty($sponsor_logos) ? explode(",", $sponsor_logos) : array();

        // Process sponsor data
        $sp = 0;
        if (!empty($sponsorLogoArry) && count($sponsorLogoArry) > 0) {
            foreach ($sponsorLogoArry as $valueSponor) {
                if (!empty($valueSponor)) {
                    // Add to logo array, with URL transformation if requested
                    $logo = $transform_urls ? change_img_src($valueSponor) : $valueSponor;
                    $sponsorLogomix[] = $logo;

                    // Add to combined array with name and logo
                    $allsponsor[] = array(
                        'name' => (isset($sponsorname[$sp]) && !empty($sponsorname[$sp])) ? $sponsorname[$sp] : '',
                        'logo' => $logo
                    );
                    $sp++;
                }
            }
        } elseif (!empty($sponsor_logos)) {
            // Single sponsor logo case
            $logo = $transform_urls ? change_img_src($sponsor_logos) : $sponsor_logos;
            $sponsorLogomix[] = $logo;
            $allsponsor[] = array(
                'name' => !empty($sponsor_names) ? $sponsor_names : '',
                'logo' => $logo
            );
        }

        // Create a comma-separated string of sponsor logos
        $sponsorLogoString = !empty($sponsorLogomix) ? implode(",", $sponsorLogomix) : '';

        // Return all sponsor data in a structured format
        return array(
            'all_sponsors' => $allsponsor,
            'sponsor_logo' => $sponsorLogoString,
            'sponsor_name' => $sponsor_names
        );
    }
}

/**
 * Get sponsor data for a specific content type and ID
 *
 * Fetches sponsor data from the database for a given content type and ID
 *
 * @param string $type Content type (e.g., 'comp', 'epub', 'video', etc.)
 * @param int $type_id Content ID
 * @param bool $transform_urls Whether to transform image URLs using change_img_src()
 * @return array Returns sponsor data in a structured format
 */
if (!function_exists('get_content_sponsors')) {
    function get_content_sponsors($type, $type_id, $transform_urls = true)
    {
        $CI = & get_instance();

        if (empty($type) || empty($type_id)) {
            return array(
                'all_sponsors' => array(),
                'sponsor_logo' => '',
                'sponsor_name' => ''
            );
        }

        // Create cache key
        $cache_key = "sponsors_{$type}_{$type_id}";

        // Check if data exists in cache
        $CI->load->library('Myredis');
        if ($CI->myredis->exists($cache_key)) {
            $cached_data = $CI->myredis->get($cache_key);

            // If transform_urls is true, we need to transform the URLs in all_sponsors
            if ($transform_urls && isset($cached_data['all_sponsors'])) {
                foreach ($cached_data['all_sponsors'] as &$sponsor) {
                    $sponsor['logo'] = change_img_src($sponsor['logo']);
                }

                // Also transform the sponsor_logo string
                $logos = explode(',', $cached_data['sponsor_logo']);
                $transformed_logos = array();

                foreach ($logos as $logo) {
                    if (!empty($logo)) {
                        $transformed_logos[] = change_img_src($logo);
                    }
                }

                $cached_data['sponsor_logo'] = implode(',', $transformed_logos);
            }

            return $cached_data;
        }

        // Determine the appropriate table and field names based on content type
        switch ($type) {
            case 'comp':
                $table = 'knwlg_compendium_to_sponsor';
                $id_field = 'comp_qa_id';
                break;
            case 'epub':
                $table = 'epub_to_sponsor';
                $id_field = 'epub_id';
                break;
            case 'gr':
                $table = 'knwlg_gr_to_sponsor';
                $id_field = 'gr_id';
                break;
            case 'video':
            case 'video_archive':
                $table = 'knwlg_video_to_sponsor';
                $id_field = 'video_id';
                break;
            case 'session':
                $table = 'knwlg_sessions_to_sponsor';
                $id_field = 'session_id';
                break;
            case 'survey':
                $table = 'survey_to_sponsor';
                $id_field = 'survey_id';
                break;
            case 'training':
            case 'course':
                $table = 'training_to_sponsor';
                $id_field = 'training_id';
                break;
            default:
                return array(
                    'all_sponsors' => array(),
                    'sponsor_logo' => '',
                    'sponsor_name' => ''
                );
        }

        // Query to get sponsor IDs for this content
        $sql = "SELECT 
                    s.sponsor_id,
                    c.client_name,
                    c.client_logo
                FROM 
                    {$table} s
                JOIN 
                    client_master c ON s.sponsor_id = c.client_master_id
                WHERE 
                    s.{$id_field} = {$type_id}";

        $query = $CI->db->query($sql);

        if ($query->num_rows() == 0) {
            $empty_result = array(
                'all_sponsors' => array(),
                'sponsor_logo' => '',
                'sponsor_name' => ''
            );

            // Cache empty result for 1 hour
            $CI->myredis->set($cache_key, $empty_result, 3600);

            return $empty_result;
        }

        // Process results
        $sponsor_names = array();
        $sponsor_logos = array();
        $all_sponsors = array();

        foreach ($query->result() as $row) {
            $sponsor_names[] = $row->client_name;
            $sponsor_logos[] = $row->client_logo;

            $all_sponsors[] = array(
                'name' => $row->client_name,
                'logo' => $transform_urls ? change_img_src($row->client_logo) : $row->client_logo
            );
        }

        // Create comma-separated strings
        $sponsor_names_str = implode(",", $sponsor_names);
        $sponsor_logos_str = implode(",", $sponsor_logos);

        // Transform logos if needed
        $sponsor_logos_transformed = $sponsor_logos;
        if ($transform_urls) {
            $sponsor_logos_transformed = array();
            foreach ($sponsor_logos as $logo) {
                $sponsor_logos_transformed[] = change_img_src($logo);
            }
        }

        $sponsor_logos_transformed_str = implode(",", $sponsor_logos_transformed);

        // Prepare result
        $result = array(
            'all_sponsors' => $all_sponsors,
            'sponsor_logo' => $sponsor_logos_transformed_str,
            'sponsor_name' => $sponsor_names_str
        );

        // Cache the result without transformed URLs
        $cache_data = array(
            'all_sponsors' => array_map(function ($sponsor) {
                return array(
                    'name' => $sponsor['name'],
                    'logo' => preg_replace('/^' . preg_quote(change_img_src(''), '/') . '/', '', $sponsor['logo'])
                );
            }, $all_sponsors),
            'sponsor_logo' => $sponsor_logos_str,
            'sponsor_name' => $sponsor_names_str
        );

        $CI->myredis->set($cache_key, $cache_data, 3600);

        return $result;
    }
}


if (!function_exists('get_channel_total_followers')) {
    function get_channel_total_followers($channel_id)
    {
        if (empty($channel_id)) {
            return 0;
        }
        $CI = &get_instance();
        $sql = "SELECT 
        COUNT(*) as total_followers 
        FROM channel_to_user 
        WHERE channel_master_id = " . $channel_id . "";
        // echo $sql;
        // exit;
        $query = $CI->db->query($sql);
        $result = $query->row();
        return $result->total_followers;
    }
}

if (!function_exists('increment_user_content_view')) {
    function increment_user_content_view($user_master_id, $type)
    {
        if (!empty($user_master_id)) {


            $CI = &get_instance();


            if ($type === 'comp') {
                $redis_key = "user_read_views_" . $user_master_id;
                $CI->load->library('Myredis');
                $CI->myredis->incr($redis_key);
                try {
                    // Increment the counter
                    $count = $CI->myredis->incr($redis_key);
                    // echo 'count---'.$count;
                    // exit;

                    // If incr failed, try to get the current value and increment manually
                    if ($count === false || $count === null) {
                        $current = $CI->myredis->get($redis_key);
                        $current = is_numeric($current) ? (int)$current : 0;
                        $count = $current + 1;
                        $CI->myredis->set($redis_key, $count);
                    }

                    // Set expiration if it's a new key
                    if ($count == 1) {
                        $CI->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
                    }

                    return $count;
                } catch (Exception $e) {
                    log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
                    return 0;
                }
            }
            if ($type === 'video_archive') {
                $redis_key = "user_video_archive_views_" . $user_master_id;
                $CI->load->library('Myredis');
                $CI->myredis->incr($redis_key);
                try {
                    // Increment the counter
                    $count = $CI->myredis->incr($redis_key);
                    // echo 'count---'.$count;
                    // exit;

                    // If incr failed, try to get the current value and increment manually
                    if ($count === false || $count === null) {
                        $current = $CI->myredis->get($redis_key);
                        $current = is_numeric($current) ? (int)$current : 0;
                        $count = $current + 1;
                        $CI->myredis->set($redis_key, $count);
                    }

                    // Set expiration if it's a new key
                    if ($count == 1) {
                        $CI->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
                    }

                    return $count;
                } catch (Exception $e) {
                    log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
                    return 0;
                }
            }
            if ($type === 'session') {
                $redis_key = "user_session_views_" . $user_master_id;
                $CI->load->library('Myredis');
                $CI->myredis->incr($redis_key);
                try {
                    // Increment the counter
                    $count = $CI->myredis->incr($redis_key);
                    // echo 'count---'.$count;
                    // exit;

                    // If incr failed, try to get the current value and increment manually
                    if ($count === false || $count === null) {
                        $current = $CI->myredis->get($redis_key);
                        $current = is_numeric($current) ? (int)$current : 0;
                        $count = $current + 1;
                        $CI->myredis->set($redis_key, $count);
                    }

                    // Set expiration if it's a new key
                    if ($count == 1) {
                        $CI->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
                    }

                    return $count;
                } catch (Exception $e) {
                    log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
                    return 0;
                }
            }
            if ($type === 'survey') {
                $redis_key = "user_survey_views_" . $user_master_id;
                $CI->load->library('Myredis');
                $CI->myredis->incr($redis_key);
                try {
                    // Increment the counter
                    $count = $CI->myredis->incr($redis_key);
                    // echo 'count---'.$count;
                    // exit;

                    // If incr failed, try to get the current value and increment manually
                    if ($count === false || $count === null) {
                        $current = $CI->myredis->get($redis_key);
                        $current = is_numeric($current) ? (int)$current : 0;
                        $count = $current + 1;
                        $CI->myredis->set($redis_key, $count);
                    }

                    // Set expiration if it's a new key
                    if ($count == 1) {
                        $CI->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
                    }

                    return $count;
                } catch (Exception $e) {
                    log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
                    return 0;
                }
            }
            if ($type === 'epub') {
                $redis_key = "user_read_views_" . $user_master_id;
                $CI->load->library('Myredis');
                $CI->myredis->incr($redis_key);
                try {
                    // Increment the counter
                    $count = $CI->myredis->incr($redis_key);
                    // echo 'count---'.$count;
                    // exit;

                    // If incr failed, try to get the current value and increment manually
                    if ($count === false || $count === null) {
                        $current = $CI->myredis->get($redis_key);
                        $current = is_numeric($current) ? (int)$current : 0;
                        $count = $current + 1;
                        $CI->myredis->set($redis_key, $count);
                    }

                    // Set expiration if it's a new key
                    if ($count == 1) {
                        $CI->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
                    }

                    return $count;
                } catch (Exception $e) {
                    log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
                    return 0;
                }
            }
            if ($type === 'training') {
                $redis_key = "user_training_views_" . $user_master_id;
                $CI->load->library('Myredis');
                $CI->myredis->incr($redis_key);
                try {
                    // Increment the counter
                    $count = $CI->myredis->incr($redis_key);
                    // echo 'count---'.$count;
                    // exit;

                    // If incr failed, try to get the current value and increment manually
                    if ($count === false || $count === null) {
                        $current = $CI->myredis->get($redis_key);
                        $current = is_numeric($current) ? (int)$current : 0;
                        $count = $current + 1;
                        $CI->myredis->set($redis_key, $count);
                    }

                    // Set expiration if it's a new key
                    if ($count == 1) {
                        $CI->myredis->expire($redis_key, 60 * 60 * 24 * 30); // 30 days
                    }

                    return $count;
                } catch (Exception $e) {
                    log_message('error', 'Failed to increment Redis counter: ' . $e->getMessage());
                    return 0;
                }
            }

            // $redis_key = "user_compendium_views_" . $user_master_id;
            // $CI->load->library('Myredis');


        }
        return 0;
    }
}


function live_sessions($user_master_id = null, $client_ids = null, $type_id = null)
{
    $CI = &get_instance();
    if ($client_ids == '') {
        $client_list = '';
    } else {
        $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
            return "FIND_IN_SET('$x', ks.client_id)";
        }, explode(',', $client_ids))) . ')';
    }
    if ($user_master_id != '') {
        $participant_id = "AND ksp.participant_id=" . $user_master_id . " ";
    } else {
        $participant_id = "";
    }
    $getchildsession = get_all_childsession();
    if (!empty($getchildsession)) {
        $cids = implode(",", (array)$getchildsession['sessions']);
        $childsessionids = " and ks.session_id NOT IN (" . $cids . ")";
    } else {
        $childsessionids = "";
    }
    $sql = "SELECT
            ksp.participant_id,
            ks.session_id,
            ks.session_doctor_id,
            ks.session_topic,
            ks.session_description,
            ks.sessions_question,
            ks.master_tag_ids,
            ks.client_id,
            ks.sponsor_id,
            ks.user_group_id,
            ks.category_id,
            ks.start_datetime,
            ks.end_datetime,
            ks.speciality_id,
            ks.total_seats,
            ks.total_buffer,
            ks.add_question_buffer_days,
            ks.session_link,
            ks.master_conf_provider_id,
            ks.session_access_code,
            ks.deeplink,
            ks.in_deeplink,
            ks.gl_deeplink,
            ks.template_id,
            ks.cert_template_id,
            ks.display_in_dashboard,
            ks.conf_phone_no,
            ks.privacy_status,
            ks.color,
            ks.added_on,
            ks.added_by,
            ks.session_status,
            ks.cover_image,
            ks.modified_on,
            ks.modified_by,
            ks.is_recommended,
            ks.is_multiday_session,
            ks.break_json,
            ks.status,
            ks.is_featured,
            ks.rating_flag,
            ks.remarks,
            ks.crm_id,
            ks.img_credits,
            ks.session_json,
            ks.certified,
            ks.env,
            ks.notification_template,
            ks.shortlink,
            ks.invitefile,
            ks.exitroute,
            ks.is_share,
            ks.is_like,
            ks.is_comment,
            sd.knwlg_sessions_docs_id,
            sd.knwlg_sessions_id,
            sd.added_on,
            sd.added_by,
            sd.modified_on,
            sd.modified_by,
            sd.updated_at,
            sd.updated_by,
            sd.status,
            cln.client_name,
            cln.client_logo,
            GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
            msct.category_name,
            msct.category_logo,
            sd.document_path,
            sd.comment,
            ksd.knwlg_sessions_docs_id,
            ksd.document_path,
            ksd.comment,
            mst.status_name,
            kv.meta_data,
            GROUP_CONCAT(DISTINCT sdoc.sessions_doctors_id SEPARATOR '----') as session_soctor_id,
            GROUP_CONCAT(DISTINCT sdoc.doctor_name SEPARATOR '----') as doctor_name,
            GROUP_CONCAT(DISTINCT sdoc.speciality SEPARATOR ',') as  speciality,
            GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
            GROUP_CONCAT(DISTINCT sdoc.profile SEPARATOR '----') as  profile,
            GROUP_CONCAT(DISTINCT sdoc.profile_image SEPARATOR '----') as  profile_images,
            GROUP_CONCAT(ksp.participant_id) as  PartName,
            GROUP_CONCAT(DISTINCT ksp.participant_id) as  users,
            GROUP_CONCAT(ksp.is_attended) as IS_ATTENDED,
            ksp.room_link,
            (ks.total_buffer + ks.total_seats) as tot_seat,
            ksv.vendor_id,ksv.video_embed_src,ksv.room_id,ksv.vouchpro_url,ksv.go_to_meeting_url,ksv.landing_page_url,ksv.session_cast_type
            FROM knwlg_sessions_V1 as ks
            LEFT JOIN master_specialities_V1 as ms ON FIND_IN_SET(ms.master_specialities_id, ks.speciality_id) > 0
            LEFT JOIN client_master as cln ON cln.client_master_id = ks.client_id
            LEFT JOIN session_to_sponsor as sTspon ON sTspon.session_id = ks.session_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = sTspon.sponsor_id
            LEFT JOIN knwlg_sessions_vendor as ksv ON ksv.session_id=ks.session_id
            LEFT JOIN master_vendor as kv ON kv.vendor_id=ksv.vendor_id
            LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
            LEFT JOIN knwlg_sessions_doctors as sdoc ON FIND_IN_SET(sdoc.sessions_doctors_id, ks.session_doctor_id) > 0
            LEFT JOIN knwlg_sessions_documents as sd ON sd.knwlg_sessions_id = ks.session_id
            LEFT JOIN knwlg_sessions_participant as ksp ON ksp.knwlg_sessions_id = ks.session_id
            LEFT JOIN knwlg_sessions_documents as ksd ON ksd.knwlg_sessions_id = ks.session_id
            LEFT JOIN master_session_status as mst ON mst.master_session_status_id = ks.session_status
            WHERE ks.session_status = 2 ";
    if ($type_id) {
        $sql .= " ks.session_id=" . $type_id . "  ";
    }
    $sql .= "" . $client_list . " and ks.session_status = 2 " . $childsessionids . "";
    if ($participant_id) {
        $sql .= $participant_id;
    }
    $sql .= "GROUP BY ks.session_id,
            ksp.participant_id,
            ks.session_doctor_id,
            ks.session_topic,
            ks.session_description,
            ks.sessions_question,
            ks.master_tag_ids,
            ks.client_id,
            ks.sponsor_id,
            ks.user_group_id,
            ks.category_id,
            ks.start_datetime,
            ks.end_datetime,
            ks.speciality_id,
            ks.total_seats,
            ks.total_buffer,
            ks.add_question_buffer_days,
            ks.session_link,
            ks.master_conf_provider_id,
            ks.session_access_code,
            ks.deeplink,
            ks.in_deeplink,
            ks.gl_deeplink,
            ks.template_id,
            ks.cert_template_id,
            ks.display_in_dashboard,
            ks.conf_phone_no,
            ks.privacy_status,
            ks.color,
            ks.added_on,
            ks.added_by,
            ks.session_status,
            ks.cover_image,
            ks.modified_on,
            ks.modified_by,
            ks.is_recommended,
            ks.is_multiday_session,
            ks.break_json,
            ks.status,
            ksp.room_link,
            ks.is_featured,
            ks.rating_flag,
            ks.remarks,
            ks.crm_id,
            ks.img_credits,
            ks.session_json,
            ks.certified,
            ks.env,
            ks.notification_template,
            ks.shortlink,
            ks.invitefile,
            ks.exitroute,
            ks.is_share,
            ks.is_like,
            ks.is_comment,
            sd.knwlg_sessions_docs_id,
            sd.knwlg_sessions_id,
            sd.added_on,
            sd.added_by,
            sd.modified_on,
            sd.modified_by,
            sd.updated_at,
            sd.updated_by,
            sd.status,
            cln.client_name,
            cln.client_logo,
            msct.category_name,
            msct.category_logo,
            sd.document_path,
            sd.comment,
            ksd.knwlg_sessions_docs_id,
            ksd.document_path,
            ksd.comment,
            mst.status_name,
            kv.meta_data,
            ksv.vendor_id,
            ksv.video_embed_src,
            ksv.room_id,
            ksv.vouchpro_url,
            ksv.go_to_meeting_url,
            ksv.landing_page_url,
            ksv.session_cast_type
            ORDER BY ks.start_datetime DESC ";
    // echo $sql;
    // exit;
    $query = $CI->db->query($sql);
    $result = $query->result_array();
    $i = 0;
    $entities = array();
    $participant_id = "AND knwlg_sessions_participant.participant_id=" . $user_master_id . " ";
    foreach ($result as $row) {
        $sql_check = "SELECT
        knwlg_sessions_participant_details.question,
        knwlg_sessions_participant_details.upload_documents,
        knwlg_sessions_participant.knwlg_sessions_participant_id,
        knwlg_sessions_participant.room_link
        FROM knwlg_sessions_participant
        LEFT JOIN knwlg_sessions_participant_details ON knwlg_sessions_participant_details.sessions_participant_id=knwlg_sessions_participant.knwlg_sessions_participant_id
        WHERE
        knwlg_sessions_participant.status=3
        AND knwlg_sessions_participant.participant_type='member'
        AND knwlg_sessions_participant.knwlg_sessions_id=" . $row['session_id'] . " " . $participant_id;
        $query_check = $CI->db->query($sql_check);
        // echo $CI->db->last_query(); exit();
        $result_check = $query_check->row_array();
        // print_r($result_check); exit();
        if (!empty($result_check)) {
            // echo $result_check['knwlg_sessions_participant_id']; exit();
            //$result_check['knwlg_sessions_participant_id']
            $entities[$i]['is_booked'] = true;
            $entities[$i]['room_link'] = $result_check['room_link'];
            $entities[$i]['asked_query'] = $result_check['question'];
            $entities[$i]['upload_documents'] = $result_check['upload_documents'];
            $entities[$i]['my_participant_id'] = $result_check['knwlg_sessions_participant_id'];
        } else {
            $entities[$i]['is_booked'] = false;
            $entities[$i]['room_link'] = '';
            $entities[$i]['asked_query'] = "";
            $entities[$i]['my_participant_id'] = "";
        }
        $entities[$i]['last_join_date'] = '';
        $entities[$i]['recorded_video_id'] = '';
        $entities[$i]['channel_details'] = [];
        $entities[$i]['type_id'] = $result->session_id;
        $entities[$i]['type'] = 'session';
        $sql_check_rating = "SELECT
        *
        FROM knwlg_session_rating_reviews
        WHERE session_id=" . $row['session_id'] . "
        AND user_master_id=" . $user_master_id . "";
        $query_check_rating = $CI->db->query($sql_check_rating);
        //echo $CI->db->last_query(); exit();
        if ($query_check_rating) {
            $result_check_rating_array = $query_check_rating->row_array();
            if (!empty($result_check_rating_array)) {
                // print_r($result_check); exit();
                // echo $result_check['knwlg_sessions_participant_id']; exit();
                //$result_check['knwlg_sessions_participant_id']
                $entities[$i]['review'] = $result_check_rating_array['review'];
                $entities[$i]['rating'] = $result_check_rating_array['rating'];
                $entities[$i]['is_rating_review'] = true;
            } else {
                $entities[$i]['is_rating_review'] = false;
            }
        } else {
            $entities[$i]['is_rating_review'] = false;
        }
        $sql_check_recording = "SELECT
        *
        FROM knwlg_session_recording_request
        WHERE session_id=" . $row['session_id'] . "
        AND user_master_id=" . $user_master_id . "";
        $query_check_recording = $CI->db->query($sql_check_recording);
        if ($query_check_recording) {
            //echo $CI->db->last_query(); exit();
            $result_check_recording_array = $query_check_recording->row_array();
            // print_r($result_check); exit();
            if (!empty($result_check_recording_array)) {
                // echo $result_check['knwlg_sessions_participant_id']; exit();
                //$result_check['knwlg_sessions_participant_id']
                $entities[$i]['recording_type'] = $result_check_recording_array['recording_type'];
                $entities[$i]['is_sent_recording'] = true;
            } else {
                $entities[$i]['is_sent_recording'] = false;
            }
        } else {
            $entities[$i]['is_sent_recording'] = false;
        }
        $complID = array();
        $sqlCompl = "SELECT
        survey_id
        FROM
        survey_user_answer sv
        WHERE
        sv.user_master_id = '" . $user_master_id . "'";
        $queryCompl = $CI->db->query($sqlCompl);
        if ($queryCompl) {
            $resultCompl = $queryCompl->result();
            foreach ($resultCompl as $valCompl) {
                if (isset($valCompl->survey_id)) {
                    $complID[] = $valCompl->survey_id;
                }
            }
        }
        //print_r($complID); exit;
        $sqlInCompl = "SELECT
        survey_id
        FROM
        survey_user_incomplete_answer sv
        WHERE
        sv.status = 3
        and
        sv.user_master_id = '" . $user_master_id . "'";
        $queryInCompl = $CI->db->query($sqlInCompl);
        $incomplID = array();
        if ($queryInCompl) {
            $resultInCompl = $queryInCompl->result();
            foreach ($resultInCompl as $valInCompl) {
                if (isset($valInCompl->survey_id)) {
                    $incomplID[] = $valInCompl->survey_id;
                }
            }
        }
        $arrayFinal = array_unique(array_merge($complID, $incomplID));
        //print_r($arrayFinal); exit;
        $complIDStr = implode(",", (array)$arrayFinal);
        //echo $complIDStr ; exit;
        if ($complIDStr) {
            $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
        } else {
            $qryStr = '';
        }
        $sqlPoll = "SELECT
        sv.* ,
        svd.data,
        GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
        GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
        cln.client_name,
        cln.client_logo,
        GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
        FROM
        survey sv
        left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
        left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
        JOIN client_master as cln ON cln.client_master_id = sv.client_id
        LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
        LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
        JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
        JOIN survey_to_session as stm ON stm.survey_id = sv.survey_id
        left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
        WHERE
        sv.status = 3
        and
        stm.session_id = " . $row['session_id'] . "
        " . $qryStr . "
        GROUP BY sv.survey_id,svd.data";
        $queryPoll = $CI->db->query($sqlPoll);
        $resultPoll = $queryPoll->result();
        // print_r($resultPoll);
        $vxPoll = array();
        foreach ($resultPoll as $valSurvey) {
            $dataArry = unserialize($valSurvey->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);
            $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);
            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {
                if ($valSurvey->sponsor_logo) {
                    $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            if ($valSurvey->survey_id) {
                $vxPoll[] = array(
                    "survey_id" => $valSurvey->survey_id,
                    "category" => $valSurvey->category,
                    "point" => $valSurvey->survey_points,
                    "json_data" => $str,
                    "survey_title" => $valSurvey->survey_title,
                    "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0), //$valSurvey->deeplink,
                    "survey_description" => substr($valSurvey->survey_description, 0, 150),
                    "image" => change_img_src($valSurvey->image),
                    "specialities_name" => $valSurvey->specialities_name,
                    "specialities_ids_and_names" => explode_speciality_string($valSurvey->specialities_ids_and_names),
                    "client_name" => $valSurvey->client_name,
                    "client_logo" => change_img_src('' . $valSurvey->client_logo),
                    "sponsor_name" => $valSurvey->sponsor,
                    "sponsor_logo" => change_img_src($sponsorLogo),
                    "publishing_date" => $valSurvey->publishing_date,
                );
            }
        }
        // $entities[$i]['my_participant_id'] = $row['participant_id'];
        $entities[$i]['is_available'] = (strtotime($row['start_datetime']) < time()) ? false : true;
        $entities[$i]['session_id'] = $row['session_id'];
        $entities[$i]['type_id'] = $row['session_id'];
        $entities[$i]['type'] = 'session';
        $entities[$i]['trending_type'] = 'session';
        $coverImg = base_url() . "uploads/sessionBgDefault.jpeg";
        $cov_img = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
        $tempcover = ($row['cover_image'] != '') ? $row['cover_image'] : $coverImg;
        $coverimageArry = explode(",", $tempcover);
        $entities[$i]['cover_image'] = change_img_src($coverimageArry);
        //$entities[$i]['cover_image'] = change_img_src($row['cover_image']);
        $entities[$i]['session_topic'] = $row['session_topic'];
        $entities[$i]['specialities_name'] = $row['specialities_name'];
        $entities[$i]['specialities_ids_and_names'] = explode_speciality_string($row['specialities_ids_and_names']);
        $entities[$i]['speciality_id'] = $row['speciality_id'];
        $entities[$i]['session_description'] = strip_tags($row['session_description']);
        $entities[$i]['master_tag_ids'] = $row['master_tag_ids'];
        $entities[$i]['client_id'] = $row['client_id'];
        $entities[$i]['vendor_meta_data'] = $row['meta_data'];
        $entities[$i]['client_name'] = $row['client_name'];
        $entities[$i]['status_name'] = $row['status_name'];
        $entities[$i]['vendor_id'] = $row['vendor_id'];
        $entities[$i]['room_id'] = $row['room_id'];
        $entities[$i]['vouchpro_url'] = $row['vouchpro_url'];
        $entities[$i]['go_to_meeting_url'] = $row['go_to_meeting_url'];
        $entities[$i]['landing_page_url'] = $row['landing_page_url'];
        $entities[$i]['video_embed_src'] = $row['video_embed_src'];
        $entities[$i]['session_cast_type'] = $row['session_cast_type'];
        $sponserentity = array();
        $sponsorLogoArry = explode(",", $row['sponsor']);
        $sponsorNameArry = explode(",", $row['sponsor_logo']);
        $ii = 0;
        foreach ($sponsorLogoArry as $singlelogo) {
            if ($singlelogo != "") {
                $sponserentity[$ii]['sponsor_name'] = $singlelogo;
                $sponserentity[$ii]['sponsor_logo'] = $sponsorNameArry[$ii];
            }
            $ii++;
        }
        $entities[$i]['sponsor_entity'] = $sponserentity;
        /**
         * new sponsor logic
         */
        $sponsorLogoArry = explode(",", $row['sponsor_logo']);
        if (count($sponsorLogoArry) > 0) {
            foreach ($sponsorLogoArry as $valueSponor) {
                if ($valueSponor) {
                    $sponsorLogomix[] = $valueSponor;
                }
            }
        } else {
            if ($row['sponsor_logo']) {
                // if full path exist
                if (stripos($row['sponsor_logo'], "https://storage.googleapis.com") > -1) {
                    $sponsorLogomix[] = $row['sponsor_logo'];
                } else {
                    $sponsorLogomix[] = base_url('uploads/logo/') . $row['sponsor_logo'];
                }
            }
            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            /**
             * new sponsor logic
             */
            $entities[$i]['sponsor_name'] = $row['sponsor'];
            $entities[$i]['sponsor_logo'] = change_img_src($sponsorLogo);
        }
        unset($sponsorLogomix);
        unset($sponsorLogoArry);
        if ($row['document_path'] != "" || $row['document_path'] != null) {
            $entities[$i]['file_size'] = round((filesize('./uploads/mastersession_docs/' . $row['document_path'] . '') / 1024)) . "Kb";
            $entities[$i]['document_path_exact_file_name'] = $row['document_path'];
            $entities[$i]['document_path'] = base_url() . "uploads/mastersession_docs/" . $row['document_path'];
            $entities[$i]['extension_logo_path'] = base_url() . "themes/front/images/" . get_logo_by_file_extension($row['document_path']);
        } else {
            $entities[$i]['document_path_exact_file_name'] = "";
            $entities[$i]['document_path'] = "";
            $entities[$i]['file_size'] = "";
            $entities[$i]['extension_logo_path'] = "";
        }
        if ($row['comment'] != "" || $row['comment'] != null) {
            $entities[$i]['comment'] = $row['comment'];
        } else {
            $entities[$i]['comment'] = "";
        }
        $entities[$i]['category_id'] = $row['category_id'];
        $entities[$i]['category_name'] = $row['category_name'];
        $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
        $entities[$i]['start_datetime'] = $row['start_datetime'];
        $entities[$i]['start_datetimex'] = strtotime($row['start_datetime']);
        $entities[$i]['now_datetimex'] = time();
        $start_time = $row['start_datetime'];
        $date = new DateTime($start_time);
        //$start_time = date("g:i A", strtotime($start_time));
        $now = new DateTime();
        $diff = date_diff($date, $now);
        $entities[$i]['days_remaining'] = abs($diff->format("%R%a")) + 1;
        $end_time = $row['end_datetime'];
        $end_time = date("g:i A", strtotime($end_time));
        $entities[$i]['display_time_format'] = $start_time . "-" . $end_time;
        $post_time = $row['start_datetime'];
        $phpdate = strtotime($post_time);
        $mysqldate = date('D, j M `y  ', $phpdate);
        $entities[$i]['display_date_format'] = $mysqldate;
        $question_users = user_questions_by_mastersession_id($row['session_id'], $user_master_id);
        $post_date = $row['added_on'];
        $start_date = $row['start_datetime'];
        $buffer_day = $row['add_question_buffer_days'];
        $last_display_date = date('Y-m-d h:i:sa', strtotime('-' . $buffer_day . ' day', strtotime($start_date)));
        $buffer_str = strtotime($last_display_date);
        $t = time();
        $date = new DateTime($last_display_date);
        $now = new DateTime();
        $now_str = strtotime("now");
        $diff = date_diff($date, $now);
        //print_r($diff);
        if ($t <= $buffer_str) {
            $dat_diff = abs($diff->format("%R%a"));
        } else {
            $dat_diff = 0;
        }
        $entities[$i]['view_edit_button_text'] = "";
        //echo $dat_diff; exit();
        if ($question_users->question == "" && $dat_diff > 0 && $row['session_status'] != 3) {
            $entities[$i]['view_edit_button_text'] = "Submit Case/Query Within " . $dat_diff . " Days";
        }
        if ($question_users->question != "" && $row['session_status'] != 3) {
            $entities[$i]['view_edit_button_text'] = "View Case/Query";
        }
        $is_attended_array = array();
        $is_attended_array = explode(",", $row['IS_ATTENDED']);
        $part_array = array();
        $part_array = explode(",", $row['PartName']);
        //$user_id = $CI->session->userdata['user_master_id'];
        $inc = 0;
        foreach ($part_array as $single) {
            if ($single == $user_master_id) {
                $key_val = $inc;
            }
            $inc++;
        }
        $is_att = $is_attended_array[$key_val];
        $entities[$i]['missed_session_text'] = "";
        if ($is_att == 2) {
            $entities[$i]['missed_session_text'] = "You Missed The Session";
        }
        $entities[$i]['i_cant_attend_button'] = 0;
        $end_time = $row['end_datetime'];
        $end_time = strtotime($end_time);
        $now_time = date('Y-m-d H:i:s');
        $now_time = strtotime($now_time);
        if ($now_time < $end_time) {
            $entities[$i]['i_cant_attend_button'] = 1;
        }
        $cpt_flag = 0;
        $on_of_booking_button = 0;
        $total_after_buffer = ($row['total_seats']) + ($row['total_buffer']);
        if ($row['users'] == null) {
            $total_original_booking = 0;
        } else {
            $users_array = array();
            $users_array = explode(",", $row['users']);
            $total_original_booking = count($users_array);
        }
        if ($total_original_booking < $row['total_seats']) {
            $total_booking = $total_original_booking;
        }
        if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
            $minus_flag = $total_after_buffer - $total_original_booking;
            $total_booking = ($row['total_seats']) - 1;
        }
        if ($total_original_booking >= ($total_after_buffer)) {
            $total_booking = $row['total_seats'];
            $on_of_booking_button = 1;
            $cpt_flag = 1;
        }
        if ($total_booking > 0) {
            $available_percent = ($total_booking / $row['total_seats']) * 100;
        } else {
            $available_percent = 0;
        }
        $available_percent = round($available_percent);
        if (($total_original_booking < $total_after_buffer) && ($total_original_booking >= $row['total_seats'])) {
            $available_percent = (($row['total_seats'] - $total_booking) / $row['total_seats']) * 100;
            $entities[$i]['total_seat'] = $row['total_seats'];
            $entities[$i]['total_booking_left'] = $total_booking;
        } else {
            $entities[$i]['total_seat'] = $row['total_seats'];
            $entities[$i]['total_booking_left'] = $row['total_seats'] - $total_booking;
        }
        $perc = $available_percent;
        if ($cpt_flag == 0) {
            $entities[$i]['percentage'] = ceil($perc);
        } else {
            $entities[$i]['percentage'] = ceil($perc);
        }
        $color = get_progress_color($perc);
        $entities[$i]['color_profress_bar'] = $color;
        $entities[$i]['session_status'] = $row['session_status'];
        $entities[$i]['start_datetime'] = $row['start_datetime'];
        $end_time = $row['end_datetime'];
        $end_time = date("g:i A", strtotime($end_time));
        $start_time = $row['start_datetime'];
        $start_time = date("g:i A", strtotime($start_time));
        $entities[$i]['display_date'] = $start_time . "-" . $end_time;
        $entities[$i]['deeplink'] = ($env == 'GL') ? (($row['gl_deeplink'] != '') ? $row['gl_deeplink'] : 0) : (($row['deeplink'] != '') ? $row['deeplink'] : 0); //$row['deeplink'];
        $entities[$i]['end_datetime'] = $row['end_datetime'];
        $entities[$i]['specialities_name'] = $row['specialities_name'];
        $entities[$i]['specialities_ids_and_names'] = explode_speciality_string($row['specialities_ids_and_names']);
        $entities[$i]['ms_cat_name'] = $row['category_name'];
        $entities[$i]['category_image'] = change_img_src(base_url() . "/themes/front/images/session/" . $row['category_logo']);
        $entities[$i]['client_logo'] = change_img_src(base_url() . "uploads/logo/" . $row['client_logo']);
        $entities[$i]['url'] = base_url() . 'session/popup_with_detail/' . $row['session_id'];
        $entities[$i]['ms_cat_logo'] = change_img_src($row['category_logo']);
        $entities[$i]['doctor_name'] = $row['doctor_name'];
        $entities[$i]['speciality'] = $row['speciality'];
        $entities[$i]['session_doctor_id'] = $row['session_doctor_id'];
        $session_doc_array = explode(",", $row['session_doctor_id']);
        $ses_doc_det_array = array();
        $inc_pp = 0;
        foreach ($session_doc_array as $single_doctor) {
            $var = session_doc_detail($single_doctor);
            //print_r($var);
            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
            if ($image) {
                if (stripos($image, "https://storage.googleapis.com") > -1) {
                    $logic_image = $image;
                } else {
                    $logic_image = docimg;
                }
            } else {
                $logic_image = docimg;
            }
            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = change_img_src($logic_image);
            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
            $ses_doc_det_array[$inc_pp]['description'] = $var[0]['description'];
            $ses_doc_det_array[$inc_pp]['subtitle'] = $var[0]['subtitle'];
            $inc_pp++;
        }
        $entities[$i]['sponsor_id'] = $row['sponsor_id'];
        $sponsor_array = explode(",", $row['sponsor_id']);
        $sponsor_det_array = array();
        if (count($sponsor_array) > 1) {
            $inc_spp = 0;
            foreach ($sponsor_array as $single_sponsor) {
                $var = sponsor_detail($single_sponsor);
                $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);
                if (@getimagesize(base_url() . "uploads/logo/" . $image)) {
                    $logic_image = base_url() . "uploads/logo/" . $image;
                } else {
                    $logic_image = base_url() . "uploads/docimg/MConsult.png";
                }
                $sponsor_det_array[$inc_spp]['sponsor_name'] = $var[0]['client_name'];
                $sponsor_det_array[$inc_spp]['sponsor_logo'] = change_img_src($logic_image);
                $sponsor_det_array[$inc_spp]['sponsor_id'] = $var[0]['client_master_id'];
                $inc_spp++;
            }
        } else {
            if ($row['sponsor_id']) {
                $var = sponsor_detail($row['sponsor_id']);
                $image = preg_replace('/\s+/', '%20', $var[0]['client_logo']);
                if (@getimagesize(base_url() . "uploads/logo/" . $image)) {
                    $logic_image = base_url() . "uploads/logo/" . $image;
                } else {
                    $logic_image = base_url() . "uploads/docimg/MConsult.png";
                }
                $sponsor_det_array['sponsor_name'] = $var[0]['client_name'];
                $sponsor_det_array['sponsor_logo'] = change_img_src($logic_image);
                $sponsor_det_array['sponsor_id'] = $var[0]['client_master_id'];
            }
        }
        //$row['sessions_question'];
        if ($row['sessions_question'] != '') {
            $qu_val = explode("#", $row['sessions_question']);
            $queries = $qu_val;
        } else {
            $queries = array();
        }
        $entities[$i]['session_doctor_entities'] = $ses_doc_det_array;
        $entities[$i]['sponsor_entities'] = $sponserentity;
        $entities[$i]['session_queries'] = $queries;
        $entities[$i]['cpddetail'] = getcpddetails($row['session_id']);
        $entities[$i]['survey'] = $vxPoll;
        $entities[$i]['disclaimer'] = 'The information in this educational activity is provided for general medical education purposes meant for registered medical practitioners only. The activity is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. The viewpoints expressed in this CME activity are those of the authors/faculty. They do not represent an endorsement by CLIRNET or the Sponsor. In no event will CLIRNET, the Sponsor or, the authors/faculty be liable for any decision made or action taken in reliance upon the information provided through this CME activity. All CMEs are recorded to be used for research & information purposes only. Clirnet at the request of Sponsor, may share your details such as name, location, recording of the session and session feedback for information purposes only.';
        $i++;
    }
    return $entities;
}
function archiveVideoDetial($type_id = '', $user_master_id = '')
{
    if ($type_id) {
        $sql = "SELECT
                    cm.video_archive_id as type_id,
                    cm.video_archive_question,
                    cm.video_archive_answer,
                    cm.video_archive_question_raw,
                    cm.video_archive_answer_raw,
                    cm.video_archive_file_img,
                    cm.video_archive_file_img_thumbnail,
                    cm.deeplink,
                    cm.gl_deeplink,
                    cm.start_like,
                    cm.added_on,
                    cm.publication_date,
                    cln.client_name,
                    cln.client_logo,
                    cm.type,
                    cm.vendor,
                    cm.src,
                    ks.session_doctor_id,
                    msct.category_name,
                    msct.category_logo,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    cm.video_archive_speciality_id,
                    kv.status as vault,
                    (select count(rt.rating) as averageRating
                    from knwlg_rating rt
                    where rt.post_id = cm.video_archive_id and rt.post_type='video_archive' and rt.rating!=0) as averageRating,
                    rtmy.rating as myrating
            FROM knwlg_video_archive as cm
            JOIN video_archive_to_specialities as cmTs ON cmTs.video_archive_id = cm.video_archive_id
            JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
            LEFT JOIN video_archive_to_sponsor as cmTspon ON cmTspon.video_archive_id = cm.video_archive_id
            LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
            LEFT JOIN knwlg_sessions_V1 as ks ON ks.session_id = cm.video_archive_session_id
            LEFT JOIN master_session_category as msct ON msct.mastersession_category_id = ks.category_id
            LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.video_archive_id and kv.type_text='video_archive' and kv.user_id = " . $user_master_id . "
            LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.video_archive_id and rtmy.post_type='video_archive' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
            LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.video_archive_id and rt.post_type='video_archive'
            JOIN client_master as cln ON cln.client_master_id = cm.client_id
            WHERE
                    cm.status = 3
                    and cm.video_archive_id = " . $type_id . "
            GROUP BY cm.video_archive_id,
                    cm.video_archive_question,
                    cm.video_archive_answer,
                    cm.video_archive_question_raw,
                    cm.video_archive_answer_raw,
                    cm.video_archive_file_img,
                    cm.video_archive_file_img_thumbnail,
                    cm.deeplink,
                    cm.gl_deeplink,
                    cm.start_like,
                    cm.added_on,
                    cm.publication_date,
                    cln.client_name,
                    cln.client_logo,
                    cm.type,
                    cm.vendor,
                    cm.src,
                    ks.session_doctor_id,
                    msct.category_name,
                    msct.category_logo,
                    cm.video_archive_speciality_id,
                    kv.status,
                    rtmy.rating
            ORDER BY cm.publication_date DESC";
        //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
        //echo $sql; exit;
        //and
        //cm.publication_date <= CURDATE()
        $query = $CI->db->query($sql);
        $val = $query->row();
        //print_r($result); exit;
        $i = 1;
        $vx = array();
        if ($val->video_archive_file_img) {
            $img = $val->video_archive_file_img;
        } else {
            $img = '';
        }
        $sponsorLogoArry = explode(",", $val->sponsor_logo);
        if (count($sponsorLogoArry) > 0) {
            foreach ($sponsorLogoArry as $valueSponor) {
                if ($valueSponor) {
                    $sponsorLogomix[] = '' . $valueSponor;
                }
            }
        } else {
            if ($val->sponsor_logo) {
                $sponsorLogomix[] = '' . $val->sponsor_logo;
            }
        }
        $sponsorLogo = implode(",", (array)$sponsorLogomix);
        unset($sponsorLogomix);
        unset($sponsorLogoArry);
        $session_doc_array = explode(",", $val->session_doctor_id);
        $ses_doc_det_array = array();
        $inc_pp = 0;
        foreach ($session_doc_array as $single_doctor) {
            $var = session_doc_detail($single_doctor);
            $image = preg_replace('/\s+/', '%20', $var[0]['profile_image']);
            if ($image) {
                $logic_image = docimg;
            } else {
                $logic_image = docimg;
            }
            $ses_doc_det_array[$inc_pp]['session_doctor_id'] = $single_doctor;
            $ses_doc_det_array[$inc_pp]['session_doctor_name'] = $var[0]['doctor_name'];
            $ses_doc_det_array[$inc_pp]['session_doctor_image'] = $logic_image;
            $ses_doc_det_array[$inc_pp]['DepartmentName'] = $var[0]['DepartmentName'];
            $ses_doc_det_array[$inc_pp]['profile'] = $var[0]['profile'];
            $inc_pp++;
        }
        $string = htmlentities($val->video_archive_question_raw, null, 'utf-8');
        $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
        $main_description = "";
        $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $val->video_archive_answer_raw);
        $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
        $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
        $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);
        //"https://doctor.clirnet.com/knowledge/uploads/kcap/image/thumb/203_304_4093C337c5a788FA9A1038cC5a.jpg";
        if ($val->type_id) {
            $vx[] = array(
                "slno" => $i,
                "con_type" => $val->type,
                "type_id" => $val->type_id,
                "vendor" => $val->vendor,
                "src" => $val->src,
                "type_id" => $val->type_id,
                "date" => date(' jS F y', strtotime($val->publication_date)),
                "question" => html_entity_decode(strip_tags($string)),
                "image" => $img,
                //"answer" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                "answer" => $val->video_archive_question_raw, //html_entity_decode(strip_tags(substr($main_description, 0, 300))),
                "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                "speciality_id" => ($val->video_archive_speciality_id != '') ? $val->video_archive_speciality_id : '',
                "client_name" => $val->client_name,
                "client_logo" => '' . $val->client_logo,
                "category_name" => $val->category_name,
                "sponsor_name" => $val->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "comment_count" => $val->count_comment,
                "deeplink" => ($env == 'GL') ? (($val->gl_deeplink != '') ? $val->gl_deeplink : 0) : (($val->deeplink != '') ? $val->deeplink : 0), //$val->deeplink,
                "rating" => ($val->averageRating != '') ? ($val->averageRating + $val->start_like) : '',
                "myrating" => ($val->myrating != '') ? true : false,
                "vault" => ($val->vault != '') ? $val->vault : 0,
                "session_doctor_id" => $val->session_doctor_id,
                "session_doctor_entities" => $ses_doc_det_array,
            );
        }
    }
    return $vx;
}
function grFile($id)
{
    $CI = &get_instance();
    $sqlInt = "select
            gr_content_url,
            gr_content_type,
            gr_content_title,
            gr_content_description
            from
            gr_to_content
            where
            gr_id = " . $id . "";
    $queryInt = $CI->db->query($sqlInt);
    $resultInt = $queryInt->result_array();
    return $resultInt;
}

function detail_user_status($id = '', $user_master_id = '')
{
    $CI = &get_instance();
    $sql = "SELECT
        id
        FROM
        survey_user_answer
        WHERE
        survey_id in (" . $id . ")
        AND
        user_master_id = " . $user_master_id . " ";
    //echo $sql; exit;
    //exit;
    //add child checking in this sql
    //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
    //exit;
    //echo  $sql; exit;
    $query = $CI->db->query($sql);
    //$CI->db->cache_off();
    if ($query) {
        $val = $query->row();
        if ($val->id > 0) {
            $status = "completed";
        } else {
            $sqlIncomp = "SELECT id
            FROM survey_user_incomplete_answer
            WHERE survey_id = ?
            AND user_master_id = ?";
            $queryIncom = $CI->db->query($sqlIncomp, array($id, $user_master_id));
            //$CI->db->cache_off();
            $valIncom = $queryIncom->row();
            if ($valIncom->id > 0) {
                $status = "pending";
            } else {
                $status = "incomplete";
            }
        }
    }
    return $status;
    //add child checking in this sql
    //echo $sql;
    //exit;
}
function getchannelid($type, $type_id)
{
    $CI = &get_instance();
    $chid = '';
    switch ($type) {
        case 'comp':
            $CI->db->select('ctc.channel_master_id,cm.title,cm.logo');
            $CI->db->from('channel_to_compendium as ctc');
            $CI->db->join('channel_master as cm', 'cm.channel_master_id = ctc.channel_master_id');
            $CI->db->where('ctc.comp_qa_id', $type_id);
            $CI->db->limit(1);
            $query = $CI->db->get();
            if ($query->num_rows() > 0) {
                $result = $query->result();
                $chid = array('channel_id' => $result[0]->channel_master_id, 'title' => $result[0]->title, 'logo' => $result[0]->logo);
            } else {
                $chid = null; ///array();
            }
            return $chid;
            break;
        case 'session':
            $CI->db->select('cts.channel_master_id,cm.title,cm.logo');
            $CI->db->from('channel_to_session as cts');
            $CI->db->join('channel_master as cm', 'cm.channel_master_id = cts.channel_master_id');
            $CI->db->where('cts.session_id', $type_id);
            $CI->db->limit(1);
            $query = $CI->db->get();
            if ($query->num_rows() > 0) {
                $result = $query->result();
                $chid = array('channel_id' => $result[0]->channel_master_id, 'title' => $result[0]->title, 'logo' => $result[0]->logo);
            } else {
                $chid = null; //array();
            }
            return $chid;
            break;
        case 'survey':
            $CI->db->select('cts.channel_master_id,cm.title,cm.logo');
            $CI->db->from('channel_to_survey as cts');
            $CI->db->join('channel_master as cm', 'cm.channel_master_id = cts.channel_master_id');
            $CI->db->where('cts.survey_id', $type_id);
            $CI->db->limit(1);
            $query = $CI->db->get();
            if ($query->num_rows() > 0) {
                $result = $query->result();
                $chid = array('channel_id' => $result[0]->channel_master_id, 'title' => $result[0]->title, 'logo' => $result[0]->logo);
            } else {
                $chid = null; //array();
            }
            return $chid;
            break;
        case 'archived_video':
            $CI->db->select('ctva.channel_master_id,cm.title,cm.logo');
            $CI->db->from('channel_to_video_archive as ctva');
            $CI->db->join('channel_master as cm', 'cm.channel_master_id = ctva.channel_master_id');
            $CI->db->where('ctva.video_archive_id', $type_id);
            $CI->db->limit(1);
            $query = $CI->db->get();
            if ($query->num_rows() > 0) {
                $result = $query->result();
                $chid = array('channel_id' => $result[0]->channel_master_id, 'title' => $result[0]->title, 'logo' => $result[0]->logo);
            } else {
                $chid = null; //array();
            }
            return $chid;
            break;
        case 'gr':
            break;
        case 'epub':
            $CI->db->select('channel_master_id,title,logo');
            $CI->db->from('channel_master');
            $CI->db->where('client_id', $type_id);
            $CI->db->limit(1);
            $query = $CI->db->get();
            if ($query->num_rows() > 0) {
                $result = $query->result();
                $chid = array('channel_id' => $result[0]->channel_master_id, 'title' => $result[0]->title, 'logo' => $result[0]->logo);
            } else {
                $chid = null; //array();
            }
            return $chid;
            break;
    }
}

function getcpddetails($session_id)
{
    $CI = &get_instance();
    $CI->db->select('id');
    $CI->db->from('Master_service');
    $CI->db->where('name', 'session');
    $query = $CI->db->get();
    if (($query) && ($query->num_rows() > 0)) {
        $result = $query->result();
        $CI->db->select('ctc.id,ctc.points,c.name,c.short_name');
        $CI->db->from('content_to_cpd as ctc');
        $CI->db->join('council as c', 'c.id=ctc.mc_id');
        $CI->db->where(array('ctc.type_id' => $session_id, 'ctc.status' => 3, 'ctc.type' => $result[0]->id));
        $query = $CI->db->get();
        #print_r($CI->db->last_query()); exit;
        if (($query) && ($query->num_rows())) {
            return $query->result();
        } else {
            return array();
        }
    } else {
        return array();
    }
}
function zoom_meeting(
    $email,
    $first_name,
    $last_name,
    $room_id
) {
    $data = '{
    "email": "' . $email . '",
    "first_name": "' . $first_name . '",
    "last_name": "' . $last_name . '"
    }';
    $curl = curl_init();
    curl_setopt_array(
        $curl,
        array(
            CURLOPT_URL => "https://api.zoom.us/v2/meetings/" . $room_id . "/registrants",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_HTTPHEADER => array(
                "authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOm51bGwsImlzcyI6Im96Wm1lbGl2Umt5QXZuLXBPVUduUFEiLCJleHAiOjE2ODU1MzM1NjAsImlhdCI6MTY1Mzk5MjIyMX0.BlxC2m9ijauvHAYExTYUeRKRLaihD57WMVhihsGty0c",
                "content-type: application/json"
            ),
        )
    );
    $response = curl_exec($curl);
    $err = curl_error($curl);
    //print_r($data);
    //print_r($response);
    //print_r($err); exit;
    curl_close($curl);
    if ($err) {
        return $err;
    } else {
        return $response;
    }
    exit;
}
function get_all_childsession()
{
    $ids = array();
    $CI = &get_instance();
    $CI->db->select(
        "kstc.multidaysession_id,
    GROUP_CONCAT(DISTINCT kstc.childsession_id SEPARATOR ',') as childsession_id,
    GROUP_CONCAT(DISTINCT sts.sessions_doctors_id SEPARATOR ',') as session_soctor_id"
    );
    $CI->db->from('knwlg_session_to_child as kstc');
    $CI->db->join('session_to_sessiondoctor as sts', 'sts.session_id = kstc.childsession_id', 'left');
    $CI->db->group_by('kstc.multidaysession_id');
    $query = $CI->db->get();
    //print_r($CI->db->last_query()); exit;
    $ids = array();
    if (($query) && ($query->num_rows() > 0)) {
        $session_ids = $query->result();
        foreach ($session_ids as $value) {
            // print_R($value);
            $totalids = explode(',', $value->childsession_id);
            $getids[$value->multidaysession_id] = count(explode(',', $value->childsession_id)); //$value->childsession_id;
            $getdoctorcount[$value->multidaysession_id] = count(explode(',', $value->session_soctor_id));
            $ids = array_merge($ids, $totalids); //$value->childsession_id;
            // $countmultidayminisession[$value->multidaysession_id] = sizeof($getids[$value->multidaysession_id]);
        }
    }
    $response['doctorcount'] = $getdoctorcount;
    $response['sessioncount'] = $getids; //$countmultidayminisession;
    $response['sessions'] = $ids;
    //print_R($response); exit;
    return $response;
}
function getPoll(
    $survey_id,
    $user_master_id
) {
    $CI = &get_instance();
    if (!empty($survey_id)) {
        /*$sql = "SELECT
        sd.*
        FROM
        survey_detail sd
        WHERE
        sd.survey_id = " . $survey_id . "
        ";
        $query = $CI->db->query($sql);
        $result = $query->row_array();
        // $result['data'];
        $dataArry = unserialize($result['data']);
        //print_r($dataArry['surveys']);*/
        $sql = "SELECT
        sd.*
        FROM
        survey_user_answer sd
        WHERE
        sd.survey_id = " . $survey_id . "
        and
        sd.user_master_id = " . $user_master_id . "
        ";
        $query = $CI->db->query($sql);
        $result = $query->row_array();
        // $result['data'];
        //$dataArry = unserialize($result['data']);
        $dataArry = json_decode($result['data'], true);
        // print_r($dataArry);
        // exit;
        //Total vote count
        $sqlAllC = "SELECT
        count(*) as totalall
        FROM
        survey_poll_result
        WHERE
        survey_id = " . $survey_id . " ";
        $queryAllc = $CI->db->query($sqlAllC);
        $resultAllc = $queryAllc->row_array();
        $totalvote = $resultAllc['totalall'];
        //Total vote count
        foreach ($dataArry as $key => $v) {
            //print_r($v['options']);
            foreach ($v['options'] as $ky => $vm) {
                if (is_int($vm['value'])) {
                    $option = " and
                    value = " . $vm['value'] . " ";
                } else {
                    $option = " and
                    value = '" . $vm['value'] . "'";
                }
                // echo "=======================";
                // print_r($vm);
                $sqlC = "SELECT
                count(*) as total
                FROM
                survey_poll_result
                WHERE
                survey_id = " . $survey_id . "" .
                    $option;
                $queryc = $CI->db->query($sqlC);
                //print_r($sqlC);
                $resultc = $queryc->row_array();
                //echo "==========================";
                $v['options'][$ky]['vote'] = round(($resultc['total'] / $totalvote) * 100);
                ////$v['options'] = $vm;
            }
            $dataArry[$key]['options'] = $v['options'];
            //print_r($v['options']);
            //$dataArry['surveys'] = $v['options'];
        }
        //print_r($dataArry);
        //exit;
        return json_encode($dataArry);
        //echo $dataArry['surveys']['question'];
    }
}
function rate(
    $type_id,
    $user_master_id,
    $type
) {
    $CI = &get_instance();
    $response = 0;
    $CI->db->select('rating');
    $CI->db->from('review_comment_rating');
    $CI->db->where(
        array(
            'type' => $type_id,
            'type_name' => $type,
            'user_master_id' => $user_master_id)
    );
    $CI->db->order_by('id', 'desc');
    $CI->db->limit(1);
    $query = $CI->db->get();
    if (($query) && ($query->num_rows() > 0)) {
        $res = $query->result();
        $response = $res[0]->rating;
    }
    return $response;
}
function explode_speciality_string($string)
{
    $final = array();
    if (!empty($string)) {
        $temp_sp_array = explode(",", $string);
        foreach ($temp_sp_array as $ky => $sp_id_name) {
            $sp_id_name_array = explode("#", $sp_id_name);
            $final[$ky] = array();
            $final[$ky]['id'] = $sp_id_name_array[0];
            $final[$ky]['name'] = $sp_id_name_array[1];
        }
    }
    return $final;
}
function getchannel(
    $type_id,
    $user_master_id,
    $type
) {
    $CI = &get_instance();
    $chid = '';
    switch ($type) {
        case 'session':
            $CI->db->select('ctva.channel_master_id,cm.title,cm.logo,cm.privacy_status,cm.short_description,cTus.status as followed_status');
            $CI->db->from('channel_to_video_archive as ctva');
            $CI->db->join('channel_master as cm', 'cm.channel_master_id = ctva.channel_master_id');
            $CI->db->join('channel_to_user  as cTus', '(cTus.channel_master_id = cm.channel_master_id and user_master_id = "' . $user_master_id . '"   )');
            $CI->db->where('ctva.video_archive_id', $type_id);
            $CI->db->where('cm.status', 3);
            $CI->db->limit(1);
            $query = $CI->db->get();
            if ($query->num_rows() > 0) {
                $result = $query->result();
                $chid = array('channel_id' => $result[0]->channel_master_id, 'title' => $result[0]->title, 'logo' => $result[0]->logo, 'description' => $result[0]->short_description, 'privacy_status' => $result[0]->privacy_status, 'followed_status' => $result[0]->followed_status);
            } else {
                $chid = null; //array();
            }
            return $chid;
            break;
        case 'comp':
            $CI->db->select('ctc.channel_master_id,cm.title,cm.logo,cm.privacy_status,cm.short_description,cTus.status as followed_status');
            $CI->db->from('channel_to_compendium as ctc');
            $CI->db->join('channel_master as cm', 'cm.channel_master_id = ctc.channel_master_id');
            $CI->db->join('channel_to_user  as cTus', '(cTus.channel_master_id = cm.channel_master_id and user_master_id = "' . $user_master_id . '"   )');
            $CI->db->where('ctc.comp_qa_id', $type_id);
            $CI->db->limit(1);
            $query = $CI->db->get();
            if ($query->num_rows() > 0) {
                $result = $query->result();
                $chid = array('channel_id' => $result[0]->channel_master_id, 'title' => $result[0]->title, 'logo' => $result[0]->logo, 'description' => $result[0]->short_description, 'privacy_status' => $result[0]->privacy_status, 'followed_status' => $result[0]->followed_status);
            } else {
                $chid = null; ///array();
            }
            return $chid;
            break;
    }
}
function complete_status(
    $training_id,
    $user_master_id
) {
    $CI = &get_instance();
    $response = 0;
    $i = 0;
    $total_tmc = array();
    $total = 0;
    if (($training_id != '') && ($user_master_id != '')) {
        $CI->db->select('id');
        $CI->db->from('training_module');
        $CI->db->where(array('training_id' => $training_id, 'status' => 3));
        $query_tm = $CI->db->get();
        if (($query_tm) && ($query_tm->num_rows() > 0)) {
            #print_r($query_tm->result());
            foreach ($query_tm->result() as $keytm => $valuetm) {
                # print_r($valuetm);
                $module[] = $valuetm->id;
            }
        }
        $CI->db->select('count(id) as total');
        $CI->db->from('training_module_content');
        $CI->db->where_in('module_id', $module);
        $CI->db->where(array('training_id' => $training_id, 'status' => 3));
        $query_tmc = $CI->db->get();
        if (($query_tmc) && ($query_tmc->num_rows() > 0)) {
            $total_tmc = $query_tmc->result();
            $total = $total_tmc[0]->total;
        }
        $CI->db->select('id,training_details');
        $CI->db->from('training_user_tracking');
        $CI->db->where(array('training_id' => $training_id, 'user_master_id' => $user_master_id));
        $query = $CI->db->get();
        #print_r($CI->db->last_query()); die;
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            foreach ($result as $key => $value) {
                foreach (json_decode($value->training_details) as $key => $value) {
                    $i = $i + 1;
                }
            }
            # print_r($i);
            # $response = $result[0]->view_percentage;
            #print_r($response);
            #return $response;
        }
        if (($total != 0) && ($i != 0)) {
            $response = ($i / $total) * 100;
        }
    }
    #die;
    return $response;
}
function livestatus($id)
{
    $CI = &get_instance();
    $currentdatetime = date('Y-m-d H:i:s');
    $status = 0;
    if ($id != '') {
        $CI->db->select('tmc.id,tmc.type_id');
        $CI->db->from('training_module_content as tmc');
        $CI->db->join('knwlg_sessions_V1 as ks', 'ks.session_id = tmc.type_id');
        $CI->db->where(
            array(
                'tmc.type' => 'session',
                'tmc.training_id' => $id,
                'ks.session_status' => 2,
                'tmc.status' => 3)
        );
        $CI->db->where("'" . $currentdatetime . "' BETWEEN ks.start_datetime and ks.end_datetime");
        $query = $CI->db->get();
        if (($query) && ($query->num_rows() > 0)) {
            $status = 1;
        } else {
            $CI->db->select('id');
            $CI->db->from('training_module_content');
            $CI->db->where(array('type' => 'live_video', 'training_id' => $id));
            $CI->db->where("'" . $currentdatetime . "' BETWEEN start_datetime and end_datetime");
            $querylivevideo = $CI->db->get();
            if (($querylivevideo) && ($querylivevideo->num_rows() > 0)) {
                $status = 1;
            }
        }
    }
    // print_r($status);
    // die;
    return $status;
}
function mask_email($email)
{
    /*
    Author: Fed
    Simple way of masking emails
    */
    $char_shown = 3;
    $mail_parts = explode("@", $email);
    $username = $mail_parts[0];
    $len = strlen($username);
    if ($len <= $char_shown) {
        return implode("@", $mail_parts);
    }
    //Logic: show asterisk in middle, but also show the last character before @
    $mail_parts[0] = substr($username, 0, $char_shown)
        . str_repeat("*", $len - $char_shown - 1)
        . substr($username, $len - $char_shown + 2, 1);
    return implode("@", $mail_parts);
}
function mask_mobile($phoneNumber)
{
    $showFirstDigits = 2; //how many digits to show in the beggining of the phone number
    $showLastDigits = 2; // how many digits to show in the end of the phone number
    return
        substr_replace(
            $phoneNumber,
            str_repeat('*', strlen($phoneNumber) - $showFirstDigits - $showLastDigits),
            $showFirstDigits,
            strlen($phoneNumber) - $showFirstDigits - $showLastDigits
        );
}
function get_bucketName_from_constant($bucketName)
{
    switch ($bucketName) {
        case 'siam_test':
            $bucketName = siam_test;
            break;
        default:
            $bucketName = default_reactnative_image_folder;
            break;
    }
    return $bucketName;
}
if (!function_exists('menu_pop')) {
    function menu_pop($menudata)
    {
        // Decode the JSON data
        $menu = json_decode($menudata, true);
        // Remove "Community" from side_menu
        foreach ($menu['side_menu'] as &$menuItem) {
            if (isset($menuItem['data']) && is_array($menuItem['data'])) {
                $menuItem['data'] = array_filter($menuItem['data'], function ($subMenuItem) {
                    return $subMenuItem['name'] !== 'Community';
                });
                // Re-index the array after filtering
                $menuItem['data'] = array_values($menuItem['data']);
            }
        }
        // Remove "Community" from bottom_menu
        $menu['bottom_menu'] = array_filter($menu['bottom_menu'], function ($menuItem) {
            return $menuItem['name'] !== 'Community';
        });
        $menu['bottom_menu'] = array_values($menu['bottom_menu']);  // Re-index the array
        // Remove "Community" from categories_menu
        $menu['categories_menu'] = array_filter($menu['categories_menu'], function ($menuItem) {
            return $menuItem['name'] !== 'Community';
        });
        $menu['categories_menu'] = array_values($menu['categories_menu']);  // Re-index the array
        // Return the updated menu data as JSON
        return json_encode($menu);
    }
}
if (!function_exists('popKeys')) {
    function popKeys(&$array, $valueToRemove)
    {
        if (empty($array) || !is_array($array)) {
            return $array;
        }
        if (defined('COMMUNITY_ENABLED') && COMMUNITY_ENABLED && $valueToRemove === "Community") {
            return $array;
        }
        foreach ($array as $key => &$item) {
            if (is_array($item)) {
                popKeys($item, $valueToRemove);
            }
            if (is_array($item) && in_array($valueToRemove, $item, true)) {
                unset($array[$key]);
            }
        }
        if (isset($array['sub_menu']) && is_array($array['sub_menu'])) {
            $array['sub_menu'] = array_values($array['sub_menu']);
        }
        if (isset($array['all_menu']) && is_array($array['all_menu'])) {
            $array['all_menu'] = array_values($array['all_menu']);
        }
        if (isset($array['data']) && is_array($array['data'])) {
            $array['data'] = array_values($array['data']);
        }
        if (isset($array['categories_menu']) && is_array($array['categories_menu'])) {
            $array['categories_menu'] = array_values($array['categories_menu']);
        }
        return $array;
    }
}

if (!function_exists('menu_pop_internal')) {
    function menu_pop_internal($menudata)
    {
        $data = json_decode($menudata, true);
        $data_v = [];
        // Loop through each element of the decoded data and pass by reference
        foreach ($data as &$k) {
            // Call removeCommunity function directly on the item
            $k = removeCommunity($k);
            // Append the processed item to the new array
            array_push($data_v, $k);
        }
        // Return the new cleaned array without "Community" entries
        return $data_v;
    }
}
function removeCommunity($array)
{
    foreach ($array as $k => $v) {
        // If it's an array, recurse
        if (is_array($v)) {
            $array[$k] = removeCommunity($v);
        }
        // If the "name" key is set and equals "Community", unset the element
        if (isset($v['name']) && $v['name'] === "Community") {
            unset($array[$k]);
        }
    }
    // Re-index the array after removal to reset the keys
    // return array_values($array);
    if (is_array($v)) {
        return array_values($array);
    } else {
        return $array;
    }
    // return array_values($array);
}
if (!function_exists('header_checker')) {
    function header_checker(
        $request,
        $checklist,
        $output1,
        $output2
    ) {
        if ($request != '') {
            $containsAny = array_filter($checklist, fn ($checklist) => str_contains($request, $checklist));
            //print_r($containsAny); exit;
            if ((empty($containsAny))) {
                return $output1;
            } else {
                return $output2;
            }
        } else {
            return $output1;
        }
    }
}
if (!function_exists('timeconversion')) {
    function timeconversion($minutes)
    {
        if ($minutes) {
            if ($minutes >= 60) { // If 1 hour or more
                $hours = floor($minutes / 60);
                $remainingMinutes = $minutes % 60;
                return "$hours hours and $remainingMinutes minutes";
            } else { // Less than 1 hour
                return "$minutes minutes";
            }
        } else {
            return 0;
        }
    }
}
