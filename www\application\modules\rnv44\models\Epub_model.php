<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Epub_model extends CI_Model
{
    public function __construct()
    {
        $ci = get_instance();
        $ci->load->helper('image');
        $ci->load->helper('utility');
        // $ci->load->library('Myredis');
    }

    public function all_epub(
        $user_master_id = '',
        $client_ids = '',
        $limitFrom = '',
        $limitTo = '',
        $val = '',
        $spIds = '',
        $type = '',
        $from_date = '',
        $to_date = '',
        $type_id,
        $convert,
        $featured
    ) {

        // if ($limitFrom != '' and $limitTo != '') {

        //     $limit = "limit " . $limitFrom . " , " . $limitTo;
        // } else {

        //     $limit = "";
        // }
        // if ($client_ids) {

        //     $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
        //         return "FIND_IN_SET('$x', cm.client_id)";
        //     }, explode(',', $client_ids))) . ')';
        // }
        // if ($convert == 1) {
        //     $mobileview = " and cm.is_converted = 1";
        // } else {
        //     $mobileview = "";
        // }

        // if ($featured == 1) {
        //     $featured = " and cm.display_in_dashboard = 1";
        // } else {
        //     $featured = " ";
        // }
        // if ($val != '') {
        //     $valX = preg_replace('/[-?]/', '', $val);
        //     $stopWords = [
        //         '/is /',
        //         '/the /',
        //         '/what /',
        //         '/where /',
        //         '/when /',
        //         '/we /',
        //         '/how /',
        //         '/to /',
        //         '/who /',
        //         '/are /',
        //     ];

        //     $valFilter = preg_replace($stopWords, '', $valX);

        //     $laString = explode(" ", $valFilter);

        //     if (count($laString) > 0) {

        //         foreach ($laString as $value) {

        //             if ($value != '') {

        //                 $sqlTagLikeArray[] = " tag_name like '%" . $value . "%'";
        //                 $sqlSepLikeArray[] = " specialities_name like '" . $value . "'";
        //             }
        //         }

        //         $sqlTagLike = implode(" or ", $sqlTagLikeArray);
        //         $sqlSepLike = implode(" or ", $sqlSepLikeArray);
        //     } else {
        //         $sqlTagLike = " tag_name like '%" . $val . "%'";
        //         $sqlSepLike = " specialities_name like '%" . $val . "%'";
        //     }

        //     /*$sqlTag ="select
        //     *
        //     from master_tags
        //     WHERE " . $sqlTagLike . "";
        //     $query = $this->db->query($sqlTag);
        //     $result = $query->result();*/

        //     $sqlSpec = "select
        //                 master_specialities_id
        //                 from master_specialities_V1
        //                 WHERE " . $sqlSepLike . "";

        //     //echo $sqlSpec; exit;
        //     $query = $this->db->query($sqlSpec);
        //     $resultSpeTag = $query->result_array();
        //     foreach ($resultSpeTag as $valSpec) {

        //         $specIdArray[] = $valSpec['master_specialities_id'];
        //     }
        //     if (count($specIdArray) > 0) {

        //         $specIdStr = implode(",", (array)$pecIdArray);
        //     } else {

        //         $specIdStr = $valSpec['master_specialities_id'];
        //     }
        //     //echo $val; exit;
        //     if ($specIdStr) {
        //         $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $specIdStr . ")  and (cm.publication_date <= CURDATE() " . $client_list . ") ) )";
        //     }

        //     if ($valFilter) {
        //         $searchQuery[] = " ( (MATCH(cm.comp_qa_question_raw, cm.comp_qa_answer_raw) AGAINST ('" . $valFilter . "' IN NATURAL LANGUAGE MODE) ) and  (cm.publication_date <= CURDATE()  " . $client_list . ") )";
        //     }
        // } else {
        //     //$searchQuery[] = "";
        // }
        // if (($spIds != '') && ($spIds != 0)) {

        //     $searchQuery[] = "  ( (cmTs.specialities_id IN (" . $spIds . ")) and cm.publication_date <= CURDATE())";
        // }

        // // $searchQuery[] = "  ( (cm.publication_date <= CURDATE() " . $client_list . ") ) ";

        // if ($val == '' and $spIds == '') {

        //     $searchQuery[] = " (cm.publication_date <= CURDATE() " . $client_list . ")";
        // }

        // if ($from_date != '' and $to_date != '') {

        //     $searchBYdate = " AND (cm.publication_date BETWEEN '" . $from_date . "' AND '" . $to_date . "' )";
        // } else {

        //     $searchBYdate = " ";
        // }
        // if ((isset($searchQuery)) && (!empty($searchQuery))) {
        //     $searchQueryStr = " AND " . implode("or", $searchQuery);
        // } else {

        //     $searchQueryStr = "";
        // }
        // //print_r($searchQuery); exit;
        // if ((isset($searchQuery)) && (!empty($searchQuery))) {
        //     $searchQueryStr = " AND " . implode("or", $searchQuery);
        // } else {

        //     $searchQueryStr = "";
        // }
        // //print_r($searchQuery); exit;

        // if (!empty($user_master_id)) {



        //     $env = get_user_env_id($user_master_id);
        //     if ($env) {
        //         if ($env != 2) {
        //             $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
        //         } else {
        //             $envStatus = "AND cTenv.env =" . $env . "";
        //         }
        //     } else {
        //         $envStatus = "";
        //     }
        //     $key_locked = get_user_package($user_master_id, 'epub');

        //     if ($key_locked == '') {
        //         return null;
        //     }

        //     $sql = "SELECT
        //     cm.epub_id as type_id,

        //     cm.epub_description as description,
        //     cm.epub_title as title,

        //     cm.epub_img,
        //     cm.epub_img_thumbnail,
        //     cm.epub_file,
        //     cm.author,
        //     cm.start_like,
        //     cm.added_on,
        //     cm.is_share,
        //     cm.publication_date as publish_date,
        //     cln.client_name,
        //     cln.client_logo,

        //     cTenv.price,
        //     uTpyCont.status as user_contnet_payment_status,

        //     cm.deeplink,
        //     cm.color,

        //     GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,

        //     GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
        //     GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,


        //     (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.epub_id and  rt.post_type='epub' and rt.rating !=0)as averageRating,
        //     rtmy.rating  as myrating,

        //     (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.epub_id and kcm.type = 'epub')as count_comment,
        //     kv.status as vault

        //     FROM epub_master as cm

        //     JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
        //     JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
        //     JOIN client_master as cln ON cln.client_master_id = cm.client_id

        //     LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id
        //     LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id

        //     LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
        //     LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type_text='epub' and  kv.user_id = " . $user_master_id . "
        //     LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'

        //     LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.epub_id and  cTenv.type = 9
        //     LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.epub_id and  uTpyCont.type = 9 and uTpyCont.user_master_id = " . $user_master_id . "
        //     WHERE
        //     cm.status=3

        //    and cm.privacy_status = 0
        //     " . $searchBYdate . "
        //     " . $envStatus . "
        //     " . $searchQueryStr . "
        //     " . $mobileview . "" . $featured . "
        //     group by cm.epub_id
        //     order by  cm.publication_date desc " . $limit . "";

        //     //echo $sql; exit;
        //     //exit;
        //     //add child checking in this sql
        //     //echo $sql;where fd.type = 'kcap' where fd.type = 'kcap'
        //     //exit;
        //     //echo  $sql; exit;
        //     $query = $this->db->query($sql);
        //     //$this->db->cache_off();
        //     $result = $query->result();
        //     //print_r($result); exit;
        //     $i = 1;
        //     $vx = array();
        //     foreach ($result as $val) {

        //         if ($val->epub_img_thumbnail) {
        //             // $logic_image_path = "uploads/compendium/" . $val->comp_qa_file_img;
        //             // $imgPr = image_thumb_url($logic_image_path, $val->comp_qa_file_img, 450, 250, '');
        //             $logic_image = $val->epub_img_thumbnail;
        //         } else {

        //             $logic_image = '';
        //         }

        //         // $allsponsor = array();
        //         // $sponsorname = explode(",", $val->sponsor);
        //         // $sp = 0;
        //         // $sponsorLogoArry = explode(",", $val->sponsor_logo);

        //         // if (count($sponsorLogoArry) > 0) {

        //         //     foreach ($sponsorLogoArry as $valueSponor) {

        //         //         if ($valueSponor) {
        //         //             $sponsorLogomix[] = '' . $valueSponor;
        //         //             $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
        //         //             $sp++;
        //         //         }
        //         //     }
        //         // } else {

        //         //     if ($val->sponsor_logo) {
        //         //         $sponsorLogomix[] = '' . $val->sponsor_logo;
        //         //         $allsponsor[] = array('name' => $val->sponsor, "logo" => $val->sponsor_logo);
        //         //     }
        //         // }

        //         // $sponsorLogo = implode(",", (array)$ponsorLogomix);

        //         // unset($sponsorLogomix);
        //         // unset($sponsorLogoArry);
        //         $sponsor_data = get_sponsor_data($val->sponsor, $val->sponsor_logo, true);
        //         $allsponsor = $sponsor_data['all_sponsors'];
        //         $sponsorLogo = $sponsor_data['sponsor_logo'];

        //         $vx[] = array(

        //             "slno" => $i,
        //             "type_id" => $val->type_id,
        //             "type" => 'epub',
        //             "date" => date(' jS F y', strtotime($val->publish_date)),
        //             "title" => html_entity_decode(strip_tags($val->title)),
        //             "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
        //             "description_short" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
        //             "author" => $val->author,
        //             "epub_file" => $val->epub_file,

        //             "is_share" => $val->is_share,
        //             "image" => change_img_src($logic_image),
        //             "color" => ($val->color != '') ? $val->color : '#918c91',

        //             //============ integrated for subscription ============//
        //             "is_locked" => $key_locked,
        //             "price" => $val->price,
        //             "user_content_payment" => get_user_content_status($val->type_id, 9, $user_master_id),
        //             //============ integrated for subscription ============//

        //             "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
        //             // "client_name" => $val->client_name,
        //             // "client_logo" => change_img_src('' . $val->client_logo),

        //             "sponsor_name" => $val->sponsor,
        //             "sponsor_logo" => change_img_src($val->sponsor_logo),
        //             "all_sponsor" => $allsponsor,

        //             "comment_count" => $val->count_comment,
        //             "rating" => ($val->averageRating != '') ? $val->averageRating + $val->start_like : $val->start_like,
        //             "myrating" => ($val->myrating != '') ? (($val->myrating == 0) ? false : true) : false,
        //             "vault" => ($val->vault != '') ? $val->vault : 0,
        //             "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,

        //         );
        //         $i++;
        //     }
        //     //print_r($vx);
        //     //exit;
        //     return $vx;
        // }




        if ($limitFrom != '' and $limitTo != '') {
            $limit = "limit " . $limitFrom . " , " . $limitTo;
        } else {
            $limit = "limit 0, 5";
        }

        if ($convert == 1) {
            $mobileview = " and cm.is_converted = 1";
        } else {
            $mobileview = "";
        }

        if ($featured == 1) {
            $featured = " and cm.display_in_dashboard = 1";
        } else {
            $featured = " ";
        }

        // Search query processing
        $searchQuery = [];

        if (($spIds != '') && ($spIds != 0)) {
            $searchQuery[] = "cmTs.specialities_id IN (" . $spIds . ") and cm.publication_date <= CURDATE()";
        } else {
            $searchQuery[] = "cm.publication_date <= CURDATE()";
        }

        if ($from_date != '' && $to_date != '') {
            $searchBYdate = " AND (cm.publication_date BETWEEN '" . $from_date . "' AND '" . $to_date . "' )";
        } else {
            $searchBYdate = " ";
        }

        $searchQueryStr = " AND (" . implode(" OR ", $searchQuery) . ")";

        if (!empty($user_master_id)) {
            $env = get_user_env_id($user_master_id);
            if ($env) {
                if ($env != 2) {
                    $envStatus = "AND (cTenv.env = 2 OR cTenv.env = " . $env . ")";
                } else {
                    $envStatus = "AND cTenv.env = " . $env . "";
                }
            } else {
                $envStatus = "";
            }

            $key_locked = get_user_package($user_master_id, 'epub');
            if ($key_locked == '') {
                return null;
            }

            // Using CTEs for better performance and readability
            $sql = "WITH FilteredEpubs AS (
                SELECT 
                    cm.epub_id
                FROM 
                    epub_master AS cm
                JOIN 
                    epub_to_specialities AS cmTs ON cmTs.epub_id = cm.epub_id
                LEFT JOIN 
                    content_to_env AS cTenv ON cTenv.type_id = cm.epub_id AND cTenv.type = 9
                WHERE 
                    cm.status = 3
                    AND cm.privacy_status = 0
                    {$searchBYdate}
                    {$envStatus}
                    {$searchQueryStr}
                    {$mobileview}
                    {$featured}
                GROUP BY 
                    cm.epub_id
                ORDER BY  
                    cm.publication_date DESC
                {$limit}
            ),
            SpecialitiesData AS (
                SELECT 
                    cmTs.epub_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name
                FROM 
                    FilteredEpubs fe
                JOIN 
                    epub_to_specialities AS cmTs ON cmTs.epub_id = fe.epub_id
                JOIN 
                    master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                GROUP BY 
                    cmTs.epub_id
            ),
            SponsorsData AS (
                SELECT 
                    cmTspon.epub_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                FROM 
                    FilteredEpubs fe
                LEFT JOIN 
                    epub_to_sponsor AS cmTspon ON cmTspon.epub_id = fe.epub_id
                LEFT JOIN 
                    client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                GROUP BY 
                    cmTspon.epub_id
            ),
            RatingData AS (
                SELECT 
                    rt.post_id AS epub_id,
                    COUNT(rt.rating) AS averageRating
                FROM 
                    FilteredEpubs fe
                LEFT JOIN 
                    knwlg_rating AS rt ON rt.post_id = fe.epub_id AND rt.post_type = 'epub' AND rt.rating != 0
                GROUP BY 
                    rt.post_id
            ),
            CommentData AS (
                SELECT 
                    kcm.type_id AS epub_id,
                    COUNT(kcm.knwlg_comment_id) AS count_comment
                FROM 
                    FilteredEpubs fe
                LEFT JOIN 
                    knwlg_comment AS kcm ON kcm.type_id = fe.epub_id AND kcm.type = 'epub'
                GROUP BY 
                    kcm.type_id
            )
            SELECT
                cm.epub_id AS type_id,
                cm.epub_description AS description,
                cm.epub_title AS title,
                cm.epub_img,
                cm.epub_img_thumbnail,
                cm.epub_file,
                cm.author,
                cm.start_like,
                cm.added_on,
                cm.is_share,
                cm.publication_date AS publish_date,
                cln.client_name,
                cln.client_logo,
                cTenv.price,
                uTpyCont.status AS user_contnet_payment_status,
                cm.deeplink,
                cm.color,
                sd.specialities_name,
                spd.sponsor,
                spd.sponsor_logo,
                COALESCE(rd.averageRating, 0) AS averageRating,
                rtmy.rating AS myrating,
                COALESCE(cd.count_comment, 0) AS count_comment,
                kv.status AS vault
            FROM 
                FilteredEpubs fe
            JOIN 
                epub_master AS cm ON cm.epub_id = fe.epub_id
            JOIN 
                client_master AS cln ON cln.client_master_id = cm.client_id
            LEFT JOIN 
                SpecialitiesData sd ON sd.epub_id = cm.epub_id
            LEFT JOIN 
                SponsorsData spd ON spd.epub_id = cm.epub_id
            LEFT JOIN 
                RatingData rd ON rd.epub_id = cm.epub_id
            LEFT JOIN 
                CommentData cd ON cd.epub_id = cm.epub_id
            LEFT JOIN 
                knwlg_rating AS rtmy ON rtmy.post_id = cm.epub_id AND rtmy.post_type = 'epub' AND rtmy.rating != 0 AND rtmy.user_master_id = {$user_master_id}
            LEFT JOIN 
                knwlg_vault AS kv ON kv.post_id = cm.epub_id AND kv.type_text = 'epub' AND kv.user_id = {$user_master_id}
            LEFT JOIN 
                content_to_env AS cTenv ON cTenv.type_id = cm.epub_id AND cTenv.type = 9
            LEFT JOIN 
                payment_user_to_content AS uTpyCont ON uTpyCont.type_id = cm.epub_id AND uTpyCont.type = 9 AND uTpyCont.user_master_id = {$user_master_id}
            ORDER BY  
                cm.publication_date DESC";

            $query = $this->db->query($sql);
            $result = $query->result();
            $i = 1;
            $vx = array();
            foreach ($result as $val) {
                if ($val->epub_img_thumbnail) {
                    $logic_image = $val->epub_img_thumbnail;
                } else {
                    $logic_image = '';
                }

                $sponsor_data = get_sponsor_data($val->sponsor, $val->sponsor_logo, true);
                $allsponsor = $sponsor_data['all_sponsors'];
                $sponsorLogo = $sponsor_data['sponsor_logo'];

                $vx[] = array(
                    "slno" => $i,
                    "type_id" => $val->type_id,
                    "type" => 'epub',
                    "date" => date(' jS F y', strtotime($val->publish_date)),
                    "title" => html_entity_decode(strip_tags($val->title)),
                    "description" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "description_short" => html_entity_decode(strip_tags(substr($val->description, 0, 300))),
                    "author" => $val->author,
                    "epub_file" => $val->epub_file,
                    "is_share" => $val->is_share,
                    "image" => change_img_src($logic_image),
                    "color" => ($val->color != '') ? $val->color : '#918c91',
                    "is_locked" => $key_locked,
                    "price" => $val->price,
                    "user_content_payment" => $val->user_contnet_payment_status,
                    //get_user_content_status($val->type_id, 9, $user_master_id),
                    "specialities" => ($val->specialities_name != '') ? $val->specialities_name : '',
                    "sponsor_name" => $val->sponsor,
                    "sponsor_logo" => change_img_src($val->sponsor_logo),
                    "all_sponsor" => $allsponsor,
                    "comment_count" => $val->count_comment,
                    "rating" => ($val->averageRating != '') ? $val->averageRating + $val->start_like : $val->start_like,
                    "myrating" => ($val->myrating != '') ? (($val->myrating == 0) ? false : true) : false,
                    "vault" => ($val->vault != '') ? $val->vault : 0,
                    "deeplink" => ($val->deeplink != '') ? $val->deeplink : 0,
                );
                $i++;
            }
            return $vx;
        }

    }

    private function getRelatedSurveys(
        $type_id,
        $user_master_id,
        $env
    ) {
        // Use CTEs for better performance
        $sql = "WITH CompletedSurveys AS (
                SELECT 
                    survey_id
                FROM 
                    survey_user_answer
                WHERE 
                    user_master_id = '{$user_master_id}'
                UNION
                SELECT 
                    survey_id
                FROM 
                    survey_user_incomplete_answer
                WHERE 
                    status = 3
                    AND user_master_id = '{$user_master_id}'
            ),
            FilteredSurveys AS (
                SELECT
                    sv.survey_id
                FROM 
                    survey sv
                JOIN 
                    survey_to_medwiki as stm ON stm.survey_id = sv.survey_id
                WHERE 
                    sv.status = 3
                    AND DATE(sv.publishing_date) <= CURDATE()
                    AND stm.medwiki_id = {$type_id}
                    AND sv.survey_id NOT IN (SELECT survey_id FROM CompletedSurveys)
            ),
            SpecialitiesAgg AS (
                SELECT 
                    svts.survey_id,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name
                FROM 
                    survey_to_speciality as svts
                JOIN 
                    master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                WHERE 
                    svts.survey_id IN (SELECT survey_id FROM FilteredSurveys)
                GROUP BY 
                    svts.survey_id
            ),
            SponsorsAgg AS (
                SELECT 
                    suvTspon.survey_id,
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                FROM 
                    survey_to_sponsor as suvTspon
                JOIN 
                    client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                WHERE 
                    suvTspon.survey_id IN (SELECT survey_id FROM FilteredSurveys)
                GROUP BY 
                    suvTspon.survey_id
            )
            SELECT
                sv.survey_id,
                sv.category,
                sv.survey_points,
                sv.survey_title,
                sv.deeplink,
                sv.gl_deeplink,
                sv.survey_description,
                sv.image,
                sv.publishing_date,
                svd.data,
                cln.client_name,
                cln.client_logo,
                spec.specialities_name,
                spon.sponsor,
                spon.sponsor_logo
            FROM 
                FilteredSurveys fs
            JOIN 
                survey sv ON sv.survey_id = fs.survey_id
            JOIN 
                survey_detail as svd ON svd.survey_id = sv.survey_id
            JOIN 
                client_master as cln ON cln.client_master_id = sv.client_id
            LEFT JOIN 
                SpecialitiesAgg spec ON spec.survey_id = sv.survey_id
            LEFT JOIN 
                SponsorsAgg spon ON spon.survey_id = sv.survey_id";

        $query = $this->db->query($sql);
        $resultPoll = $query->result();
        $vxPoll = array();

        foreach ($resultPoll as $valSurvey) {
            $dataArry = unserialize($valSurvey->data);
            $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            $str = preg_replace('/\\\"/', "\"", $json);

            // Process sponsor data
            $allsponsor = array();
            $sponsorname = explode(",", $valSurvey->sponsor);
            $sp = 0;
            $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);
            $sponsorLogomix = [];

            if (count($sponsorLogoArry) > 0) {
                foreach ($sponsorLogoArry as $valueSponor) {
                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                        $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => change_img_src($valueSponor));
                        $sp++;
                    }
                }
            } elseif ($valSurvey->sponsor_logo != '') {
                $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                $allsponsor[] = array('name' => $valSurvey->sponsor, "logo" => change_img_src($valSurvey->sponsor_logo));
            }

            $sponsorLogo = implode(",", (array)$sponsorLogomix);
            $vxPoll[] = array(
                "survey_id" => $valSurvey->survey_id,
                "category" => $valSurvey->category,
                "point" => $valSurvey->survey_points,
                "json_data" => $str,
                "survey_title" => $valSurvey->survey_title,
                "deeplink" => ($env == 'GL') ? (($valSurvey->gl_deeplink != '') ? $valSurvey->gl_deeplink : 0) : (($valSurvey->deeplink != '') ? $valSurvey->deeplink : 0),
                "survey_description" => substr($valSurvey->survey_description, 0, 150),
                "image" => change_img_src($valSurvey->image),
                "specialities_name" => $valSurvey->specialities_name,
                "sponsor_name" => $valSurvey->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "all_sponsor" => $allsponsor,
                "publishing_date" => $valSurvey->publishing_date,
            );

        }

        return $vxPoll;
    }
    public function detail(
        $type_id,
        $user_master_id,
        $from_type
    ) {
        //echo $user_master_id; exit;

        //    echo 'value-'.$this->myredis->delall();
        // exit;

        $cachename = "epub_detail_" . $type_id;

        if (!empty($type_id)) {
            // =================== user env id =======================//
            $env = get_user_env_id($user_master_id);
            $envold = get_user_env($user_master_id);
            //exit;
            if ($env) {

                if ($env != 2) {
                    $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                } else {
                    $envStatus = "AND cTenv.env =" . $env . "";
                }
            } else {
                $envStatus = "";
            }

            $key_locked = get_user_package($user_master_id, 'epub'); //exit;

            $master_user_type_id = get_master_user_type_id($user_master_id); //exit;
            // echo "<pre>";print_r($master_user_type_id);die;
            // if ($master_user_type_id == false) {
            //     return;
            // } elseif ($master_user_type_id == 5) {
            //     //internal then fetch data what is active(==3) and draft(==5), no need of cache
            //     // echo "<pre>";print_r($master_user_type_id);die;

            //     $sql = "SELECT
            //     cm.epub_id as type_id,
            //     cm.privacy_status,
            //     cm.env,
            //     cm.epub_description as description,
            //     cm.epub_title as title,
            //     cm.is_converted,
            //     cm.epub_img,
            //     cm.epub_img_thumbnail,
            //     cm.epub_file,
            //     cm.start_like,
            //     cm.added_on,
            //     cm.publication_date as publish_date,
            //     cln.client_name,
            //     cln.client_logo,

            //     cTenv.price,
            //     uTpyCont.status as user_contnet_payment_status,

            //     cm.deeplink,
            //     cm.color,
            //     cm.author,

            //     GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            //     GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
            //     GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            //     GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,


            //     (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.epub_id and  rt.post_type='epub' and rating != 0)as averageRating,
            //     rtmy.rating  as myrating,

            //     (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.epub_id and kcm.type = 'epub')as count_comment,
            //     kv.status as vault

            //     FROM epub_master as cm

            //     JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
            //     JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
            //     JOIN client_master as cln ON cln.client_master_id = cm.client_id

            //     LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id
            //     LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id

            //     LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
            //     LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type_text='epub' and  kv.user_id = " . $user_master_id . "
            //     LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'

            //     LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.epub_id and  cTenv.type = 9
            //     LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.epub_id and  uTpyCont.type = 9 and uTpyCont.user_master_id = " . $user_master_id . "
            //     WHERE
            //     cm.status in(3,5)
            //     AND
            //     cm.privacy_status in(0,1,2)
            //     AND
            //     cm.epub_id = " . $type_id . " ";

            //     //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
            //     // echo $sql;
            //     // exit;

            //     $query = $this->db->query($sql);
            //     $result = $query->row();
            //     if ($result->type_id == '') {
            //         return;
            //     }
            // } else { //not internal user
            //     // checking for cache
            //     if ($this->myredis->exists($cachename)) { //cache present
            //         $result = $this->myredis->get($cachename);
            //         // print_r($res); exit;
            //     } else { //not internal user and cache is not present, fetch from db and set the cache!!
            //         $sql = "SELECT
            //         cm.epub_id as type_id,
            //         cm.privacy_status,
            //         cm.env,
            //         cm.epub_description as description,
            //         cm.epub_title as title,
            //         cm.is_converted,
            //         cm.epub_img,
            //         cm.epub_img_thumbnail,
            //         cm.epub_file,
            //         cm.start_like,
            //         cm.added_on,
            //         cm.publication_date as publish_date,
            //         cln.client_name,
            //         cln.client_logo,

            //         cTenv.price,
            //         uTpyCont.status as user_contnet_payment_status,

            //         cm.deeplink,
            //         cm.color,
            //         cm.author,

            //         GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            //         GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,

            //         GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            //         GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,


            //         (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.epub_id and  rt.post_type='epub' and rating != 0)as averageRating,
            //         rtmy.rating  as myrating,

            //         (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.epub_id and kcm.type = 'epub')as count_comment,
            //         kv.status as vault

            //         FROM epub_master as cm

            //         JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
            //         JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
            //         JOIN client_master as cln ON cln.client_master_id = cm.client_id

            //         LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id
            //         LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id

            //         LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
            //         LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type_text='epub' and  kv.user_id = " . $user_master_id . "
            //         LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'

            //         LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.epub_id and  cTenv.type = 6
            //         LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.epub_id and  uTpyCont.type = 6 and uTpyCont.user_master_id = " . $user_master_id . "
            //         WHERE
            //         cm.status = 3

            //         AND
            //         cm.privacy_status in(0,1,2)  and cm.publication_date <= CURDATE()
            //         AND
            //         cm.epub_id = " . $type_id . "";

            //         //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
            //         // echo $sql; exit;

            //         $query = $this->db->query($sql);
            //         $result = $query->row();
            //         if ($result->type_id == '') {
            //             return;
            //         } else {
            //             $this->myredis->set($cachename, $result);
            //         }
            //     }
            // }




            // Refactored code to avoid query duplication
            if ($master_user_type_id == false) {
                return;
            }

            // Check if we can use cache
            $use_cache = ($master_user_type_id != 5 && $this->myredis->exists($cachename));

            if ($use_cache) {
                // Cache present and user is not internal
                $result = $this->myredis->get($cachename);
            } else {
                // Determine status condition based on user type
                $status_condition = ($master_user_type_id == 5)
                    ? "cm.status IN (3, 5)"
                    : "cm.status = 3 AND cm.publication_date <= CURDATE()";

                // Determine content type ID for payment_user_to_content
                $content_type_id = 9;

                // Build the query using CTEs
                $sql = "WITH EpubData AS (
                    SELECT
                        cm.epub_id,
                        cm.privacy_status,
                        cm.env,
                        cm.epub_description,
                        cm.epub_title,
                        cm.is_converted,            
                        cm.epub_img,
                        cm.epub_img_thumbnail,
                        cm.epub_file,
                        cm.start_like,
                        cm.added_on,
                        cm.publication_date,
                        cm.deeplink,
                        cm.color,
                        cm.author,
                        cm.client_id
                    FROM 
                        epub_master AS cm
                    WHERE 
                        {$status_condition}
                        AND cm.privacy_status IN (0, 1, 2)
                        AND cm.epub_id = {$type_id}
                ),
                SpecialitiesData AS (
                    SELECT
                        cmTs.epub_id,
                        GROUP_CONCAT(DISTINCT ms.specialities_name) AS specialities_name,
                        GROUP_CONCAT(DISTINCT CONCAT(ms.master_specialities_id, '#', ms.specialities_name)) AS specialities_ids_and_names
                    FROM 
                        EpubData ed
                    JOIN 
                        epub_to_specialities AS cmTs ON cmTs.epub_id = ed.epub_id
                    JOIN 
                        master_specialities_V1 AS ms ON ms.master_specialities_id = cmTs.specialities_id
                    GROUP BY 
                        cmTs.epub_id
                ),
                SponsorsData AS (
                    SELECT
                        cmTspon.epub_id,
                        GROUP_CONCAT(DISTINCT clintspon.client_name) AS sponsor,
                        GROUP_CONCAT(DISTINCT clintspon.client_logo) AS sponsor_logo
                    FROM 
                        EpubData ed
                    LEFT JOIN 
                        epub_to_sponsor AS cmTspon ON cmTspon.epub_id = ed.epub_id
                    LEFT JOIN 
                        client_master AS clintspon ON clintspon.client_master_id = cmTspon.sponsor_id
                    GROUP BY 
                        cmTspon.epub_id
                ),
                MetricsData AS (
                    SELECT
                        ed.epub_id,
                        -- (SELECT COUNT(rt.rating) FROM knwlg_rating rt 
                        --  WHERE rt.post_id = ed.epub_id AND rt.post_type = 'epub' AND rt.rating != 0) AS averageRating,
                        (SELECT COUNT(kcm.knwlg_comment_id) FROM knwlg_comment kcm 
                        WHERE kcm.type_id = ed.epub_id AND kcm.type = 'epub') AS count_comment
                    FROM 
                        EpubData ed
                ),
                UserData AS (
                    SELECT
                        ed.epub_id,
                        -- rtmy.rating AS myrating,
                        -- kv.status AS vault,
                        uTpyCont.status AS user_contnet_payment_status
                    FROM 
                        EpubData ed
                    -- LEFT JOIN 
                    --     knwlg_rating AS rtmy ON rtmy.post_id = ed.epub_id 
                    --         AND rtmy.post_type = 'epub' 
                    --         AND rtmy.rating != 0 
                    --         AND rtmy.user_master_id = {$user_master_id}
                    -- LEFT JOIN 
                    --     knwlg_vault AS kv ON kv.post_id = ed.epub_id 
                    --         AND kv.type_text = 'epub' 
                    --         AND kv.user_id = {$user_master_id}
                    LEFT JOIN 
                        payment_user_to_content AS uTpyCont ON uTpyCont.type_id = ed.epub_id 
                            AND uTpyCont.type = {$content_type_id} 
                            AND uTpyCont.user_master_id = {$user_master_id}
                ),
                EnvData AS (
                    SELECT
                        ed.epub_id,
                        cTenv.price
                    FROM 
                        EpubData ed
                    LEFT JOIN 
                        content_to_env AS cTenv ON cTenv.type_id = ed.epub_id AND cTenv.type = {$content_type_id}
                        {$envStatus}
                )
                SELECT
                    ed.epub_id AS type_id,
                    ed.privacy_status,
                    ed.env,
                    ed.epub_description AS description,
                    ed.epub_title AS title,
                    ed.is_converted,            
                    ed.epub_img,
                    ed.epub_img_thumbnail,
                    ed.epub_file,
                    ed.start_like,
                    ed.added_on,
                    ed.publication_date AS publish_date,
                    ed.deeplink,
                    ed.color,
                    ed.author,
                    cln.client_name,
                    cln.client_logo,
                    envd.price,
                    ud.user_contnet_payment_status,
                    sd.specialities_name,
                    sd.specialities_ids_and_names,
                    spd.sponsor,
                    spd.sponsor_logo,
                    -- md.averageRating,
                    -- ud.myrating,
                    md.count_comment
                    -- ud.vault
                FROM 
                    EpubData ed
                JOIN 
                    client_master AS cln ON cln.client_master_id = ed.client_id
                LEFT JOIN 
                    SpecialitiesData sd ON sd.epub_id = ed.epub_id
                LEFT JOIN 
                    SponsorsData spd ON spd.epub_id = ed.epub_id
                LEFT JOIN 
                    MetricsData md ON md.epub_id = ed.epub_id
                LEFT JOIN 
                    UserData ud ON ud.epub_id = ed.epub_id
                LEFT JOIN 
                EnvData envd ON envd.epub_id = ed.epub_id";

                $query = $this->db->query($sql);
                $result = $query->row();

                if (empty($result) || $result->type_id == '') {
                    return;
                } elseif ($master_user_type_id != 5) {
                    // Only cache for non-internal users
                    $this->myredis->set($cachename, $result);
                }
            }


            //----------------------------------------------------------------
            $sqlAuther = "SELECT * FROM epub_to_author WHERE epub_id = $type_id ";
            $queryAuther = $this->db->query($sqlAuther);
            $resultAuther = $queryAuther->result();
            $autherArray = array();
            $autherIndex = 0;
            foreach ($resultAuther as $rw) {
                $autherArray[$autherIndex] = array();
                $autherArray[$autherIndex]['author_name'] = $rw->author_name;
                $autherArray[$autherIndex]['author_image'] = change_img_src($rw->author_image);
                $autherArray[$autherIndex]['author_description'] = $rw->author_description;
                $autherArray[$autherIndex]["author_specialities"] = $result->specialities_name;
                $autherArray[$autherIndex]["author_specialities_id"] = $result->comp_qa_speciality_id;
                $autherArray[$autherIndex]["author_specialities_ids_and_names"] = $this->explode_speciality_string($result->specialities_ids_and_names);

                $autherIndex++;
            }
            // //poll start

            // $sqlCompl = "SELECT
            // sv.*
            // FROM
            // survey_user_answer sv
            // WHERE
            // sv.user_master_id = '" . $user_master_id . "'";
            // $queryCompl = $this->db->query($sqlCompl);
            // $resultCompl = $queryCompl->result();
            // $complID = array();
            // foreach ($resultCompl as $valCompl) {
            //     if ($valCompl->survey_id) {
            //         $complID[] = $valCompl->survey_id;
            //     } else {
            //         $complID[] = 0;
            //     }
            // }
            // $sqlInCompl = "SELECT
            // sv.*
            // FROM
            // survey_user_incomplete_answer sv
            // WHERE
            // sv.status = 3
            // and
            // sv.user_master_id = '" . $user_master_id . "'";
            // $queryInCompl = $this->db->query($sqlInCompl);
            // $resultInCompl = $queryInCompl->result();
            // $incomplID = array();
            // foreach ($resultInCompl as $valInCompl) {
            //     //$incomplID[] = $valInCompl->survey_id;

            //     if ($valCompl->survey_id) {
            //         $incomplID[] = $valInCompl->survey_id;
            //     } else {
            //         $incomplID[] = 0;
            //     }
            // }

            // $arrayFinal = array_filter(array_unique(array_merge($complID, $incomplID)));
            // $complIDStr = implode(",", (array)$arrayFinal);
            // if ($complIDStr) {
            //     $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
            // } else {
            //     $qryStr = '';
            // }
            // $sqlPoll = "SELECT
            // sv.* ,
            // svd.data,
            // GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
            // cln.client_name,
            // cln.client_logo,
            // GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
            // GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
            // FROM
            // survey sv
            // left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id
            // left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
            // JOIN client_master as cln ON cln.client_master_id = sv.client_id
            // LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
            // LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
            // JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
            // JOIN survey_to_medwiki as stm ON stm.survey_id = sv.survey_id
            // left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
            // WHERE
            // sv.status = 3
            // and
            // stm.medwiki_id = " . $type_id . "
            // " . $qryStr . " ";

            // // echo $sqlPoll;
            // // exit;

            // $queryPoll = $this->db->query($sqlPoll);
            // $resultPoll = $queryPoll->result();
            // $vxPoll = array();
            // foreach ($resultPoll as $valSurvey) {
            //     $dataArry = unserialize($valSurvey->data);
            //     $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
            //     $str = preg_replace('/\\\"/', "\"", $json);

            //     $allsponsor = array();
            //     $sponsorname = explode(",", $valSurvey->sponsor);
            //     $sp = 0;
            //     $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);

            //     if (count($sponsorLogoArry) > 0) {
            //         foreach ($sponsorLogoArry as $valueSponor) {

            //             if ($valueSponor) {
            //                 $sponsorLogomix[] = '' . $valueSponor;
            //                 $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
            //                 $sp++;
            //             }
            //         }
            //     } else {

            //         if ($valSurvey->sponsor_logo) {
            //             $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
            //             $allsponsor[] = array('name' => $valSurvey->sponsor, "logo" => $valSurvey->sponsor_logo);
            //         }
            //     }

            //     $sponsorLogo = implode(",", (array)$sponsorLogomix);

            //     unset($sponsorLogomix);
            //     unset($sponsorLogoArry);
            //     if ($valSurvey->survey_id) {

            //         $vxPoll[] = array(

            //             "survey_id" => $valSurvey->survey_id,
            //             "category" => $valSurvey->category,
            //             "point" => $valSurvey->survey_points,
            //             "json_data" => $str,
            //             "survey_title" => $valSurvey->survey_title,
            //             "deeplink" => $valSurvey->deeplink,
            //             "survey_description" => substr($valSurvey->survey_description, 0, 150),
            //             "image" => change_img_src($valSurvey->image),
            //             "specialities_name" => $valSurvey->specialities_name, //                GROUP_CONCAT(DISTINCT concat(ms.master_specialities_id, '#', ms.specialities_name) ) as specialities_ids_and_names,
            //             // "client_name" => $valSurvey->client_name,
            //             // "client_logo" => change_img_src('' . $valSurvey->client_logo),

            //             "sponsor_name" => $valSurvey->sponsor,
            //             "sponsor_logo" => change_img_src($sponsorLogo),
            //             "publishing_date" => $valSurvey->publishing_date,

            //         );
            //     }
            // }

            //poll end

            $vxPoll = $this->getRelatedSurveys($type_id, $user_master_id, $env);
            if ($result->epub_img_thumbnail) {

                $img = $result->epub_img_thumbnail; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;

            } else {

                $img = '';
            }

            $sponsor_data = get_sponsor_data($result->sponsor, $result->sponsor_logo, true);
            $allsponsor = $sponsor_data['all_sponsors'];
            $sponsorLogo = $sponsor_data['sponsor_logo'];

            $string = $result->comp_qa_question_raw;
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\n\t", "\n", $result->comp_qa_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);

            // print_r($result->averageRating);
            // //exit;
            // print_r($result->start_like);
            // //print_r(($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like);
            // exit;
            //echo $user_master_id." =>";

            if ($envold) {

                if ($env != 'GL') {

                    $envStatus = "AND (cm.env ='GL' or cm.env ='" . $envold . "')";
                } else {

                    $envStatus = "AND cm.env ='" . $envold . "'";
                }
            } else {

                $envStatus = "";
            }
            // $avg_rating_sql = "
            // select count(rt.rating) as averageRating
            // from knwlg_rating rt where rt.post_id = " . $type_id . " and  rt.post_type='epub' and rating != 0
            // ";
            // $comment_count_sql = "select
            // count(kcm.knwlg_comment_id) as count_comment
            // from
            // knwlg_comment kcm
            // where kcm.type_id = " . $type_id . "
            // and kcm.type = 'epub' ";

            // $vault_sql = "select
            // kv.status
            // from knwlg_vault as kv
            // where kv.post_id = " . $type_id . "
            // and  kv.type_text='epub'
            // and  kv.user_id = " . $user_master_id . "
            // ";

            // $my_rt_sql = "
            // SELECT
            // rtmy.rating
            // from knwlg_rating as rtmy
            // where rtmy.post_id = " . $type_id . "
            // and  rtmy.post_type='epub'
            // and rtmy.rating!=0
            // and rtmy.user_master_id = " . $user_master_id . "
            // ";

            // $star_like_sql = "
            // select cm.start_like
            // FROM epub_master as cm
            // WHERE
            // cm.epub_id = " . $type_id . " ";
            // $avg_rating_result = $this->db->query($avg_rating_sql)->row();
            // $comment_count = $this->db->query($comment_count_sql)->row();
            // $vault = $this->db->query($vault_sql)->row();
            // $my_rating = $this->db->query($my_rt_sql)->row();
            // $star_like_res = $this->db->query($star_like_sql)->row();

            // if (!empty($avg_rating_result)) {
            //     $avg_rating_final = $avg_rating_result->averageRating;
            // } else {
            //     $avg_rating_final = 0;
            // }
            // // my rating
            // if (!empty($my_rating)) {
            //     $my_rating_final = $my_rating->rating;
            // } else {
            //     $my_rating_final = 0;
            // }
            // if (!empty($star_like_res)) {
            //     $star_like_final = $star_like_res->start_like;
            // } else {
            //     $star_like_final = 0;
            // }

            // // vault
            // if (!empty($vault)) {
            //     $vault_final = $vault->status;
            // } else {
            //     $vault_final = 0;
            // }

            // // comment
            // if (!empty($comment_count)) {
            //     $comment_count_final = $comment_count->count_comment;
            // } else {
            //     $comment_count_final = 0;
            // }

            $rating_data = get_content_rating_data('epub', $type_id, $user_master_id);
            // Now you can access the data
            $averageRating = $rating_data['averageRating'];
            $hasUserRated = $rating_data['myrating']; // boolean
            $vaultStatus = $rating_data['vault']; // 0 or 1
            $startLike = $rating_data['start_like'];
            $vx = array(

                "type_id" => (string) $result->type_id,
                "privacy_status" => $result->privacy_status,
                "env"            => $result->env,
                "type" => 'epub',
                "date" => date(' jS F y', strtotime($result->publish_date)),
                "title" => html_entity_decode($result->title),
                "description" => html_entity_decode($result->description), //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),

                "epub_file" => $result->epub_file,
                "author" => $result->author,
                "image" => change_img_src($img),
                "author_entities" =>  $autherArray,

                "specialities" => $result->specialities_name,
                "specialities_id" => $result->comp_qa_speciality_id,
                "specialities_ids_and_names" => $this->explode_speciality_string($result->specialities_ids_and_names),

                // "client_name" => $result->client_name,
                // "client_logo" => change_img_src('' . $result->client_logo),

                //============ integrated for subscription ============//
                "is_locked" => $key_locked,
                "price" => $result->price,
                "user_content_payment" => $result->user_contnet_payment_status,
                //get_user_content_status($type_id, 9, $user_master_id),
                //============ integrated for subscription ============//

                "is_share" => get_a_content_is_share_status($result->type_id, '9'),

                "sponsor_name" => $result->sponsor,
                "sponsor_logo" => change_img_src($result->sponsor_logo),
                "all_sponsor" => $allsponsor,
                "start_like" => $result->start_like,

                "comment_count" => $result->count_comment,
                //"comment_count" => $comment_count_final,
                "rating" => ($averageRating != '') ? ($averageRating + $startLike) : $startLike,
                "myrating" => $hasUserRated,
                "vault" => $vaultStatus,

                "deeplink" => ($result->deeplink != '') ? $result->deeplink : 0,
                "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,
                "channel" => $this->get_user_channels($type_id, $envold, $user_master_id),

                "disclaimer" => disclaimer('knowledge'),
                "survey" => $vxPoll,
                "is_converted" => $result->is_converted
            );

            $campaign  = getContentCampiagn($user_master_id, '', $type_id, 'epub');
            $vx['display_banner'] = $campaign['banner_dispaly'];
            $vx['campaign_data'] = $campaign['creative_data'];
            //echo "<pre>";print_r($vx['env']);die;
            if ($master_user_type_id != 5) {
                if ($env == "GL" && $vx['env'] == "IN") {
                    $vx = [];
                }
            }
            return $vx;
        }
    }

    private function get_user_channels($type_id, $env, $user_master_id = '')
    {

        if (!empty($user_master_id  && $user_master_id != '')) {
            $clause = "AND ctus.user_master_id = $user_master_id";
        } else {
            $clause = "";
        }
        if ($env) {

            if ($env != 'GL') {

                $envStatus = "AND (chm.env ='GL' or chm.env ='" . $env . "')";
            } else {

                $envStatus = "AND chm.env ='" . $env . "'";
            }
        } else {

            $envStatus = "";
        }
        $sql = "
            SELECT
                chm.channel_master_id as channel_id,
                chm.title as channel_title,
                chm.logo as channel_logo,
                chm.logo_type,
                chm.description as channel_desc,
                chm.privacy_status as channel_privacy_stts,
                chm.follower_count as channel_follower_count,
                chm.deeplink as channel_deep_link,
                ctus.status as followed_status,
                cm.client_id as epub_client,
                cm.added_on
            
            FROM 
                epub_master as cm
        
            LEFT JOIN 
                channel_master as chm ON chm.client_id = cm.client_id 
                
            LEFT JOIN 
                channel_to_user as ctus ON ctus.channel_master_id = chm.channel_master_id {$clause}
                
            
            WHERE
                cm.status = 3
                AND 
                    cm.epub_id = " . $type_id . " " . $envStatus . "
            GROUP BY 
                chm.channel_master_id 
            ORDER BY 
                cm.added_on DESC  
            LIMIT 1
        ";
        // echo $sql; exit;
        $res = $this->db->query($sql)->result();

        if (!empty($res) && $res[0]->channel_id != '') {
            $total_follower = $this->totalFollowersCountByChannelId($res[0]->channel_id);
            $total_activity = $this->totalActivityCountByChannelIdEpub($res[0]->channel_id);
            $data = array(
                "channel_id"  => $res[0]->channel_id,
                "title"  => $res[0]->channel_title,
                "logo" => change_img_src($res[0]->channel_logo),
                "logo_type" => $res[0]->logo_type,
                "description" =>  $res[0]->channel_desc,
                "privacy_status" =>  $res[0]->channel_privacy_stts,
                "deeplink" =>  $res[0]->channel_deep_link,
                "followed_status" =>  $res[0]->followed_status,
                "epub_client" =>  $res[0]->epub_client,
                "added_on" =>  $res[0]->added_on,
                "follower_count" => $total_follower + $res[0]->channel_follower_count,
                "total_activity" => $total_activity
            );
            // $res[0]->follower_count = $total_follower;
            // $res[0]->total_activity = $total_activity;
        }

        return $data;
    }

    /**
     * Summary of totalFollowersCountByChannelId
     * @param mixed $channelId
     * @return mixed
     */
    private function totalFollowersCountByChannelId($channelId)
    {
        $sql =  "select 
            count(DISTINCT(user_master_id)) as follower_count
            from
            channel_to_user
            where 
            status = 3
            and
            channel_master_id = " . $channelId . "";
        // $sql = "SELECT count(cu.user_master_id) as follower_count, cu.channel_master_id FROM channel_to_user  as cu

        // // WHERE cu.channel_master_id = {$channelId} AND status = 3

        // // GROUP BY cu.channel_master_id"; echo $sql; exit;

        return ($this->db->query($sql)->row_array()['follower_count']) ? $this->db->query($sql)->row_array()['follower_count'] : 0;
    }
    private function totalActivityCountByChannelIdEpub($channelId)
    {

        $table_names = array(

            "channel_to_compendium",
            "channel_to_course",
            "channel_to_document",
            "channel_to_post",
            "channel_to_session",
            "channel_to_survey",
            "channel_to_video_archive"
        );

        $total_activity = 0;

        foreach ($table_names as $table) {
            $sql = "
                SELECT 
                    count(id)  as count
                FROM 
                    {$table}

                WHERE
                    channel_master_id = {$channelId}
            ";

            $total_activity +=  $this->db->query($sql)->result()[0]->count;
        }
        //print_r($total_activity); exit;
        return $total_activity;
    }

    /**
     * Code written by rkaesh epub details function for related api
     */
    public function detail_for_related(
        $type_id,
        $user_master_id,
        $from_type
    ) {
        //echo 'value-'.$this->myredis->delall();
        //exit;
        $env = get_user_env($user_master_id);
        $cachename = "epub_detail_" . $type_id . $from_type . $env;
        if (!empty($type_id)) {
            if ($this->myredis->exists($cachename)) {
                $result = $this->myredis->get($cachename);
                // print_r($res); exit;
            } else {
                $env = get_user_env_id($user_master_id);
                if ($env) {

                    if ($env != 2) {

                        $envStatus = "AND (cTenv.env = 2 or cTenv.env =" . $env . ")";
                    } else {

                        $envStatus = "AND cTenv.env =" . $env . "";
                    }
                } else {

                    $envStatus = "";
                }
                $key_locked = get_user_package($user_master_id, 'epub');
                $sql = "SELECT
                    cm.epub_id as type_id,
                    
                    cm.epub_description as description,
                    cm.epub_title as title,
                    
                    cm.epub_img,
                    cm.epub_img_thumbnail,
                    cm.epub_file,
                    cm.start_like,
                    cm.added_on,
                    cm.publication_date as publish_date,
                    cln.client_name,
                    cln.client_logo,
                    
                    
                    cm.deeplink,
                    cm.color,
                    cm.author,
                    
                    cTenv.price,
                    uTpyCont.status as user_contnet_payment_status,
                    
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                    
                    
                    (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.epub_id and  rt.post_type='epub')as averageRating,
                    rtmy.rating  as myrating,
                    
                    (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.epub_id and kcm.type = 'epub')as count_comment,
                    kv.status as vault
                    
                    FROM epub_master as cm
                    
                    JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
                    JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                    JOIN client_master as cln ON cln.client_master_id = cm.client_id
                    
                    LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id 
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id 
                    
                    
                    
                    LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                    LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type_text='epub' and  kv.user_id = " . $user_master_id . "
                    LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'
                    
                    LEFT JOIN content_to_env as cTenv ON cTenv.type_id = cm.epub_id and  cTenv.type = 9
                    LEFT JOIN payment_user_to_content as uTpyCont ON uTpyCont.type_id = cm.epub_id and  uTpyCont.type = 9 and uTpyCont.user_master_id = " . $user_master_id . "
                
        
                    
                    
                    WHERE 
                    cm.status=3
                    " . $envStatus . "
                    AND 
                    cm.epub_id = " . $type_id . "";

                //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
                //echo $sql; exit;

                $query = $this->db->query($sql);
                $result = $query->row();
                $this->myredis->set($cachename, $result);
            }
            $cachename_v1 = "epub_detail_author" . $type_id;
            if ($this->myredis->exists($cachename_v1)) {
                $resultAuther = $this->myredis->get($cachename_v1);
                // print_r($res); exit;
            } else {
                $sqlAuther = "SELECT * FROM epub_to_author WHERE epub_id = $type_id ";
                $queryAuther = $this->db->query($sqlAuther);
                $resultAuther = $queryAuther->result();
                $this->myredis->set($cachename_v1, $result);
            }
            $autherArray = array();
            $autherIndex = 0;
            foreach ($resultAuther as $rw) {
                $autherArray[$autherIndex] = array();
                $autherArray[$autherIndex]['author_name'] = $rw->author_name;
                $autherArray[$autherIndex]['author_image'] = change_img_src($rw->author_image);
                $autherArray[$autherIndex]['author_description'] = $rw->author_description;
                $autherIndex++;
            }

            //print_r($complID); exit;
            if ($result->epub_img_thumbnail) {

                $img = $result->epub_img_thumbnail; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;

            } else {

                $img = '';
            }

            $sponsorLogoArry = explode(",", $result->sponsor_logo);
            $allsponsor = array();
            $sponsorname = explode(",", $result->sponsor);
            $sp = 0;
            if (count($sponsorLogoArry) > 0) {

                foreach ($sponsorLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                    $allsponsor[] = array('name' => $sponsorname[$sp], "logo" => $valueSponor);
                    $sp++;
                }
            } else {

                if ($result->sponsor_logo != '') {
                    $sponsorLogomix[] = '' . $result->sponsor_logo;
                    $allsponsor[] = array('name' => $result->sponsor, "logo" => $result->sponsor_logo);
                }
            }

            $sponsorLogo = implode(",", (array)$ponsorLogomix);

            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $string = $result->comp_qa_question_raw;
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\n\t", "\n", $result->comp_qa_answer_raw);

            $vxPoll = array();
            // print_r($result->publish_date);
            // exit;
            $vx = array(

                "type_id" => $result->type_id,
                "trending_type" => 'epub',
                "type" => 'epub',
                "is_locked" => $key_locked,
                "price" => $result->price,
                "user_content_payment" => $result->user_contnet_payment_status,
                "date" => date(' jS F y', strtotime($result->publish_date)),
                "title" => html_entity_decode($result->title),
                "description" => html_entity_decode($result->description),
                "epub_file" => $result->epub_file,
                "author" => $result->author,
                "image" => change_img_src($img),
                "author_entities" =>  $autherArray,
                "specialities" => $result->specialities_name,
                "specialities_id" => $result->comp_qa_speciality_id,
                "client_name" => $result->client_name,
                "client_logo" => change_img_src('' . $result->client_logo),
                "sponsor_name" => $result->sponsor,
                "sponsor_logo" => change_img_src($sponsorLogo),
                "start_like" => $result->start_like,
                "comment_count" => $result->count_comment,
                "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
                "myrating" => ($result->myrating != '') ? true : false,
                "vault" => ($result->vault != '') ? $result->vault : 0,
                "deeplink" => ($result->deeplink != '') ? $result->deeplink : 0,
                "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,
                "disclaimer" => disclaimer('knowledge'),
                "survey" => $vxPoll,

            );
            // if ($from_type) {}
            //add child checking in this sql
            //echo $sql;
            //exit;

            return $vx;
        }
    }
    public function detail_for_related_bk($type_id = '', $user_master_id = '', $from_type = '')
    {
        //    echo 'value-'.$this->myredis->delall();
        // exit;
        $cachename = "epub_detail_" . $type_id;
        if (!empty($type_id)) {
            if ($this->myredis->exists($cachename)) {
                $result = $this->myredis->get($cachename);
                // print_r($res); exit;
            } else {

                $sql = "SELECT
                    cm.epub_id as type_id,
                    
                    cm.epub_description as description,
                    cm.epub_title as title,
                    
                    cm.epub_img,
                    cm.epub_img_thumbnail,
                    cm.epub_file,
                    cm.start_like,
                    cm.added_on,
                    cm.publication_date as publish_date,
                    cln.client_name,
                    cln.client_logo,
                    
                    
                    cm.deeplink,
                    cm.color,
                    cm.author,
                    
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo,
                    
                    
                    (select count(rt.rating) as averageRating from knwlg_rating rt where rt.post_id = cm.epub_id and  rt.post_type='epub')as averageRating,
                    rtmy.rating  as myrating,
                    
                    (select count(kcm.knwlg_comment_id) as count_comment from knwlg_comment kcm where kcm.type_id = cm.epub_id and kcm.type = 'epub')as count_comment,
                    kv.status as vault
                    
                    FROM epub_master as cm
                    
                    JOIN epub_to_specialities as cmTs ON cmTs.epub_id = cm.epub_id
                    JOIN master_specialities_V1 as ms ON ms.master_specialities_id = cmTs.specialities_id
                    JOIN client_master as cln ON cln.client_master_id = cm.client_id
                    
                    LEFT JOIN epub_to_sponsor as cmTspon ON cmTspon.epub_id = cm.epub_id 
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = cmTspon.sponsor_id 
                    
                    
                    
                    LEFT JOIN knwlg_rating as rtmy ON rtmy.post_id = cm.epub_id and  rtmy.post_type='epub' and rtmy.rating!=0 and rtmy.user_master_id = " . $user_master_id . "
                    LEFT JOIN knwlg_vault as kv ON kv.post_id = cm.epub_id and  kv.type_text='epub' and  kv.user_id = " . $user_master_id . "
                    LEFT JOIN knwlg_rating as rt ON rt.post_id = cm.epub_id and  rt.post_type='epub'
                    
                    WHERE 
                    cm.status=3
                    AND 
                    cm.epub_id = " . $type_id . "";

                //JOIN master_specialities as ms ON ms.master_specialities_id = cm.comp_qa_speciality_id
                //echo $sql; exit;

                $query = $this->db->query($sql);
                $result = $query->row();
                $this->myredis->set($cachename, $result);
            }
            //poll start

            $sqlCompl = "SELECT 
                    sv.* 
                    
                    FROM 
                    survey_user_answer sv
                    WHERE 
                    sv.user_master_id = '" . $user_master_id . "'";
            $queryCompl = $this->db->query($sqlCompl);
            $resultCompl = $queryCompl->result();

            $complID = array();
            foreach ($resultCompl as $valCompl) {

                $complID[] = $valCompl->survey_id;
            }
            //print_r($complID); exit;

            //----------------------------------------------------------------

            $cachename_v1 = "epub_detail_author" . $type_id;
            if ($this->myredis->exists($cachename_v1)) {
                $resultAuther = $this->myredis->get($cachename_v1);
                // print_r($res); exit;
            } else {
                $sqlAuther = "SELECT * FROM epub_to_author WHERE epub_id = $type_id ";
                $queryAuther = $this->db->query($sqlAuther);
                $resultAuther = $queryAuther->result();
                $this->myredis->set($cachename_v1, $result);
            }
            $autherArray = array();
            $autherIndex = 0;
            foreach ($resultAuther as $rw) {
                $autherArray[$autherIndex] = array();
                $autherArray[$autherIndex]['author_name'] = $rw->author_name;
                $autherArray[$autherIndex]['author_image'] = $rw->author_image;
                $autherArray[$autherIndex]['author_description'] = $rw->author_description;
                $autherIndex++;
            }

            //print_r($complID); exit;
            //----------------------------------------------------------------

            $sqlInCompl = "SELECT 
                    sv.* 
                    
                    FROM 
                    survey_user_incomplete_answer sv
                    WHERE 
                    sv.status = 3
                    and 
                    sv.user_master_id = '" . $user_master_id . "'";
            $queryInCompl = $this->db->query($sqlInCompl);
            $resultInCompl = $queryInCompl->result();

            $incomplID = array();
            foreach ($resultInCompl as $valInCompl) {

                $incomplID[] = $valInCompl->survey_id;
            }
            $arrayFinal = array_unique(array_merge($complID, $incomplID));
            //print_r($arrayFinal); exit;
            $complIDStr = implode(",", (array)$arrayFinal);
            //echo $complIDStr ; exit;

            if ($complIDStr) {
                $qryStr = 'and sv.survey_id not in (' . $complIDStr . ')';
            } else {
                $qryStr = '';
            }
            $sqlPoll = "SELECT 
                    sv.* ,
                    svd.data,
                    GROUP_CONCAT(DISTINCT ms.specialities_name) as specialities_name,
                    cln.client_name,
                    cln.client_logo,
                    
                    GROUP_CONCAT(DISTINCT clintspon.client_name) as sponsor,
                    GROUP_CONCAT(DISTINCT clintspon.client_logo) as sponsor_logo
                    
                    FROM 
                    survey sv
                    left JOIN survey_to_speciality as svts ON svts.survey_id = sv.survey_id          
                    left JOIN master_specialities_V1 as ms ON ms.master_specialities_id = svts.speciality_id
                    JOIN client_master as cln ON cln.client_master_id = sv.client_id
                    
                    
                    
                    LEFT JOIN survey_to_sponsor as suvTspon ON suvTspon.survey_id = sv.survey_id
                    LEFT JOIN client_master as clintspon ON clintspon.client_master_id = suvTspon.sponsor_id
                    
                    
                    
                    JOIN survey_detail as svd ON svd.survey_id = sv.survey_id
                    JOIN survey_to_medwiki as stm ON stm.survey_id = sv.survey_id
                    
                    left JOIN survey_user_answer as sua ON sua.survey_id = sv.survey_id
                    WHERE 
                    sv.status = 3 
                    and 
                    stm.medwiki_id = " . $type_id . "
                    " . $qryStr . " ";

            $queryPoll = $this->db->query($sqlPoll);
            $resultPoll = $queryPoll->result();

            //echo $sqlPoll;
            // print_r($resultPoll);
            $vxPoll = array();
            foreach ($resultPoll as $valSurvey) {
                $dataArry = unserialize($valSurvey->data);
                $json = stripslashes(json_encode($dataArry, JSON_UNESCAPED_SLASHES));
                $str = preg_replace('/\\\"/', "\"", $json);
                $sponsorLogoArry = explode(",", $valSurvey->sponsor_logo);

                if (count($sponsorLogoArry) > 0) {

                    foreach ($sponsorLogoArry as $valueSponor) {

                        if ($valueSponor) {
                            $sponsorLogomix[] = '' . $valueSponor;
                        }
                    }
                } else {

                    if ($valSurvey->sponsor_logo) {
                        $sponsorLogomix[] = '' . $valSurvey->sponsor_logo;
                    }
                }

                $sponsorLogo = implode(",", (array)$ponsorLogomix);

                unset($sponsorLogomix);
                unset($sponsorLogoArry);
                if ($valSurvey->survey_id) {

                    $vxPoll[] = array(
                        "trending_type" => "survey",
                        "type" => "survey",
                        "survey_id" => $valSurvey->survey_id,
                        "category" => $valSurvey->category,
                        "point" => $valSurvey->survey_points,
                        "json_data" => $str,
                        "survey_title" => $valSurvey->survey_title,
                        "deeplink" => $valSurvey->deeplink,
                        "survey_description" => substr($valSurvey->survey_description, 0, 150),
                        "image" => $valSurvey->image,
                        "specialities_name" => $valSurvey->specialities_name,
                        "client_name" => $valSurvey->client_name,
                        "client_logo" => '' . $valSurvey->client_logo,

                        "sponsor_name" => $valSurvey->sponsor,
                        "sponsor_logo" => $sponsorLogo,
                        "publishing_date" => $valSurvey->publishing_date,

                    );
                }
            }

            //print_r($vxPoll);
            //poll end
            if ($result->epub_img_thumbnail) {

                $img = $result->epub_img_thumbnail; //base_url() . "uploads/compendium/" . $result->comp_qa_file_img;

            } else {

                $img = '';
            }

            $sponsorLogoArry = explode(",", $result->sponsor_logo);

            if (count($sponsorLogoArry) > 0) {

                foreach ($sponsorLogoArry as $valueSponor) {

                    if ($valueSponor) {
                        $sponsorLogomix[] = '' . $valueSponor;
                    }
                }
            } else {

                if ($result->sponsor_logo) {
                    $sponsorLogomix[] = '' . $result->sponsor_logo;
                }
            }

            $sponsorLogo = implode(",", (array)$ponsorLogomix);

            unset($sponsorLogomix);
            unset($sponsorLogoArry);
            $string = $result->comp_qa_question_raw;
            $string = trim(html_entity_decode($string), " \t\n\r\0\x0B\xC2\xA0");
            $main_description = "";
            $main_description = str_replace("\n\t", "\n", $result->comp_qa_answer_raw);
            $main_description = str_replace("\r\n\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n\r\n", "\r\n", $main_description);
            $main_description = str_replace("\r\n\r\n", "\r\n", $main_description);

            // print_r($result->publish_date);
            // exit;
            $vx = array(

                "type_id" => $result->type_id,
                "trending_type" => 'epub',
                "type" => 'epub',
                "date" => date(' jS F y', strtotime($result->publish_date)),
                "title" => html_entity_decode($result->title),
                "description" => html_entity_decode($result->description), //strip_tags(substr($result->comp_qa_answer_raw, 0, 300)),

                "epub_file" => $result->epub_file,
                "author" => $result->author,
                "image" => $img,
                "author_entities" =>  $autherArray,

                "specialities" => $result->specialities_name,

                "specialities_id" => $result->comp_qa_speciality_id,
                "client_name" => $result->client_name,
                "client_logo" => '' . $result->client_logo,
                "sponsor_name" => $result->sponsor,
                "sponsor_logo" => $sponsorLogo,
                "start_like" => $result->start_like,

                "comment_count" => $result->count_comment,
                "rating" => ($result->averageRating != '') ? ($result->averageRating + $result->start_like) : $result->start_like,
                "myrating" => ($result->myrating != '') ? true : false,
                "vault" => ($result->vault != '') ? $result->vault : 0,
                "deeplink" => ($result->deeplink != '') ? $result->deeplink : 0,
                "tags" => ($result->comp_qa_tags != '') ? $result->comp_qa_tags : 0,

                //  "disclaimer" => 'All scientific content on the platform is provided for general medical education purposes meant for registered medical practitioners only. The content is not meant to substitute for the independent medical judgment of a physician relative to diagnostic and treatment options of a specific patient’s medical condition. In no event will CLIRNET be liable for any decision made or action taken in reliance upon the information provided through this content.',
                "disclaimer" => disclaimer('knowledge'),
                "survey" => $vxPoll,

            );
            // if ($from_type) {}
            //add child checking in this sql
            //echo $sql;
            //exit;

            return $vx;
        }
    }
    public function getmobileview($id, $from, $to)
    {
        $this->db->select('DISTINCT(page_no) as page_no, page_url');
        $this->db->from('epub_to_images');
        $this->db->where('epub_id', $id);
        if (($from == 0) && ($to != '')) {
            $this->db->limit($to, $from);
        } else {
            $this->db->limit($from, $to);
        }
        $this->db->order_by('page_no', 'asc');
        $query = $this->db->get();
        $pageResults = [];
        if (($query) && ($query->num_rows() > 0)) {
            foreach ($query->result_array() as $row) {
                $pageResults[] = [
                    'page_no' => $row['page_no'],
                    'page_url' => change_img_src($row['page_url']),
                ];
            }
        } else {
            $pageResults = [];
        }
        return $pageResults;
    }

    public function getmobileviewcount($id)
    {
        $this->db->select('count(id) as total');
        $this->db->from('epub_to_images');
        $this->db->where('epub_id', $id);

        $query = $this->db->get();
        // print_r($this->db->last_query());
        // exit;
        if (($query) && ($query->num_rows() > 0)) {
            $result = $query->result();
            return $result[0]->total;
            //return $result
        } else {
            return 0;
        }
    }

    public function explode_speciality_string($string)
    {
        $final = array();
        if (!empty($string)) {
            $temp_sp_array = explode(",", $string);
            foreach ($temp_sp_array as $ky => $sp_id_name) {
                $sp_id_name_array = explode("#", $sp_id_name);
                $final[$ky] = array();
                $final[$ky]['id'] = $sp_id_name_array[0];
                $final[$ky]['name'] = $sp_id_name_array[1];
            }
        }

        return $final;
    }
}
