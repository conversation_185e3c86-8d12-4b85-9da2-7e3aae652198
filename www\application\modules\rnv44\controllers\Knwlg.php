<?php

defined('BASEPATH') or exit('No direct script access allowed');
require dirname(__FILE__) . '/../libraries/NEW_REST_Controller.php';

class Knwlg extends MY_REST_Controller
{
    private $token_data;
    private $logdata = array();

    public function __construct()
    {
        parent::__construct();
        $token = $this->input->get_request_header('Authorization');
        //this->set_response($all_data, REST_Controller::HTTP_OK); //This is the respon if success
        //Validate user
        $this->validate_token($token);
        $this->token_data = $this->get_token_data($token);
        //print_r($token);
        $this->load->library('form_validation');
        $this->load->model('Knwlg_model');
        $this->load->helper('image');

        /**
         * For api detail table
         */
        $this->logdata['called_on'] = date('Y-m-d H:i:s', time());
        $this->logdata['process_start_time'] = date('Y-m-d H:i:s');
        $this->logdata['version'] = $this->input->get_request_header('version');
        /**
         * For api detail table
         */
    }

    public function trending_get()
    {

        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id

        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $group_ids = $this->token_data->userdetail->group_ids;

        $limitTo = 5;
        $limitFrom = 0;
        $val = $this->input->get('val'); // for search value.
        $spIds = $this->input->get('spIds'); // for search value.

        $all_data = $this->Knwlg_model->all_trending($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, $val, $spIds);
        $message = 'Success';

        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    /**
     *
     */
    public function feed_get()
    {

        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $group_ids = $this->token_data->userdetail->group_ids;

        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $val = $this->input->get('val'); // for search value.
        $spIds = $this->input->get('spIds'); // for search value.
        $type = $this->input->get('type'); // for search value.

        $from_date = $this->input->get('from_date'); // for search value.
        $to_date = $this->input->get('to_date'); // for search value.



        $all_data = $this->Knwlg_model->all_compendium(
            $user_master_id,
            $client_ids,
            $group_ids,
            $limitFrom,
            $limitTo,
            $val,
            $spIds,
            $type,
            $from_date,
            $to_date
        );

        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    /**
     *
     */
    public function feed2_get()
    {

        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $group_ids = $this->token_data->userdetail->group_ids;

        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $val = $this->input->get('val'); // for search value.
        $spIds = $this->input->get('spIds'); // for search value.
        $type = $this->input->get('type'); // for search value.

        $from_date = $this->input->get('from_date'); // for search value.
        $to_date = $this->input->get('to_date'); // for search value.

        $all_data = $this->Knwlg_model->all_compendium2(
            $user_master_id,
            $client_ids,
            $group_ids,
            $limitFrom,
            $limitTo,
            $val,
            $spIds,
            $type,
            $from_date,
            $to_date
        );
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    /**
     *
     */
    public function myvault_get()
    {

        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $group_ids = $this->token_data->userdetail->group_ids;

        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $val = $this->input->get('val'); // for search value.
        $type = $this->input->get('type');

        // if ($limitFrom == '' || $limitTo == '') {
        //     $message = 'Limit missing';
        //     $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        //     $this->logdata['response'] = REST_Controller::HTTP_OK;
        //     $this->logdata['message'] = $message;
        //     $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        //     $this->set_response($all_data, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);

        // }


        $all_data = $this->Knwlg_model->all_vault_feed($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, $val, $type);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    /**
     *
     */
    public function feedDetail_get()
    {



        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);

        $type_id = $this->input->get('type_id');
        $type = $this->input->get('type');
        $from_type = $this->input->get('from');
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $all_data = $this->Knwlg_model->detail_feed($type_id, $type, $user_master_id, $from_type, $client_ids);
        $message = 'Success';

        if ($all_data["type_id"] == "" || $all_data["type_id"] == null) {
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
        } else {
            // Increment the compendium view counter
            increment_user_content_view($user_master_id, 'comp');
            $this->logdata['response'] = REST_Controller::HTTP_OK;
        }

        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        // $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, $this->logdata['response'], $this->logdata); //This is the respon if success

    }


    /**
     *
     */
    public function comment_get()
    {

        $type_id = $this->input->get('type_id');
        $type = $this->input->get('type');
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $all_data = $this->Knwlg_model->get_comment($type_id, $type);
        $message = 'Success';

        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }


    /**
     *
     */
    public function save_comment_post()
    {

        $dataArray = json_decode(file_get_contents('php://input'), true);
        $type_id = $dataArray['type_id'];
        $type = $dataArray['type'];
        $comment = $dataArray['comment'];
        $user_master_id = $this->token_data->userdetail->user_master_id;
        //echo $dataArray['speciality_id'];
        //print_r($dataArray); exit;
        $this->load->library('form_validation');
        $this->form_validation->set_data($dataArray);

        $this->form_validation->set_rules('comment', 'comment', 'trim|required');
        $this->form_validation->set_rules('type_id', 'type_id', 'trim|required');
        $this->form_validation->set_rules('type', 'type', 'trim|required');
        if ($this->form_validation->run() == false) {
            //
        } else {
            if ($all_data = $this->Knwlg_model->save_comment($dataArray, $user_master_id)) {

                $message = 'Success';

                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

            }
        }
    }

    /**
     *
     */
    public function save_like_post()
    {

        $dataArray = json_decode(file_get_contents('php://input'), true);
        $type_id = $dataArray['type_id'];
        $type = $dataArray['type'];

        $user_master_id = $this->token_data->userdetail->user_master_id;
        //echo $dataArray['speciality_id'];
        //print_r($dataArray); exit;
        $this->load->library('form_validation');
        $this->form_validation->set_data($dataArray);


        $this->form_validation->set_rules('type_id', 'type_id', 'trim|required');
        $this->form_validation->set_rules('type', 'type', 'trim|required');
        if ($this->form_validation->run() == false) {
            //
        } else {
            if ($all_data = $this->Knwlg_model->save_like($dataArray, $user_master_id)) {

                $message = 'Success';

                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

            }
        }
    }

    /**
     *
     */
    public function vault_switching_post()
    {


        $dataArray = json_decode(file_get_contents('php://input'), true);
        $status = $dataArray['status'];
        $postid = $dataArray['postid'];
        $type = $dataArray['type'];
        $user_master_id = $this->token_data->userdetail->user_master_id;

        $this->load->library('form_validation');
        $this->form_validation->set_data($dataArray);


        //$this->form_validation->set_rules('type_id', 'type_id', 'trim|required');
        $this->form_validation->set_rules('postid', 'postid', 'trim|required');
        $this->form_validation->set_rules('type', 'type', 'trim|required');
        //$this->form_validation->set_rules('status', 'status', 'trim|required');
        if ($this->form_validation->run() == false) {

            $output['error'] = $this->form_validation->error_array(); //This is the output token
            $message = 'Data validation error';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['detail_message'] = serialize($output['error']);
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */

            $this->set_response($output, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

        } else {

            $all_data = $this->Knwlg_model->save_vault($postid, $user_master_id, $type);

            $message = 'Success';
            //$all_data = array();
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

        }
    }



    /**
     *
     */
    public function replay_comment_get()
    {

        $type_id = $this->input->get('type_id');
        // $type = $this->input->get('type');
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $all_data = $this->Knwlg_model->get_sub_comments($type_id);
        $message = 'Success';

        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    //==========================================

    /**
     *
     */
    public function nested_comment_get()
    {

        $type_id = $this->input->get('type_id');
        $type = $this->input->get('type');
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $all_data = $this->Knwlg_model->get_comment_nasted($type_id, $type);
        $message = 'Success';

        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    /**
     *
     */
    public function nested_save_comment_post()
    {

        $dataArray = json_decode(file_get_contents('php://input'), true);
        $type_id = $dataArray['type_id'];
        $type = $dataArray['type'];
        $comment = $dataArray['comment'];
        $user_master_id = $this->token_data->userdetail->user_master_id;
        //echo $dataArray['speciality_id'];
        //print_r($dataArray); exit;
        $this->load->library('form_validation');
        $this->form_validation->set_data($dataArray);

        $this->form_validation->set_rules('comment', 'comment', 'trim|required');
        $this->form_validation->set_rules('type_id', 'type_id', 'trim|required');
        $this->form_validation->set_rules('type', 'type', 'trim|required');
        $this->form_validation->set_rules('parent_id', 'parent_id', 'trim|required');
        if ($this->form_validation->run() == false) {
            //
        } else {
            if ($all_data = $this->Knwlg_model->nasted_save_comment($dataArray, $user_master_id)) {


                $this->load->library('Background');

                /**
                 * This is for background process
                 */
                /*$basic_detail['comment_id'] = $all_data['insert_id'];
                $basic_detail['Authorization'] = $this->input->get_request_header('Authorization');
                $url = base_url()."rnv5/knwlg/sendmail";
                $param = $basic_detail;
                $this->background->do_in_background($url, $param);*/
                /**
                 * This is for background process
                 */


                $message = 'Success';

                /**
                 * For api detail table
                 */
                $this->logdata['doctor_id'] = $user_master_id;
                $this->logdata['response'] = REST_Controller::HTTP_OK;
                $this->logdata['message'] = $message;
                $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
                /**
                 * For api detail table
                 */
                $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

            }
        }
    }

    /**
     *
     */
    public function sendmail_post()
    {

        $comment_id = $this->input->post('comment_id');
        performemailUsercommentsend($comment_id);
    }

    public function comp_get()
    {

        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $group_ids = $this->token_data->userdetail->group_ids;

        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $val = $this->input->get('val'); // for search value.
        $spIds = $this->input->get('spIds'); // for search value.

        $all_data = $this->Knwlg_model->all_comp();


        // exit;
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    /**
     *
     */
    public function archiveVideoList_get()
    {

        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $group_ids = $this->token_data->userdetail->group_ids;

        $type = $this->input->get('type'); // for search value.
        $spIds = $this->input->get('spIds'); // for search value.
        $type_id = $this->input->get('type_id'); // for search value.

        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');


        if ($limitFrom == "" && $limitTo == "") {

            $message = "limit From To Not Present";
            /**
             * For api detail table
             */
            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {


            $all_data = $this->Knwlg_model->all_archiveVideo($user_master_id, $client_ids, $group_ids, $spIds, $limitFrom, $limitTo, $type_id, $type);
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success

        }
    }

    /**
     *
     */
    public function archiveVideoDetail_get()
    {


        $type_id = $this->input->get('type_id');
        $from_type = $this->input->get('from');
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $btype = $this->input->get('btype'); // for search value.

        $all_data = $this->Knwlg_model->detail_archiveVideo($type_id, $user_master_id, $from_type, $btype);
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;


        if (!empty($all_data)) {

            increment_user_content_view($user_master_id, 'video_archive');

            $this->logdata['response'] = REST_Controller::HTTP_OK;
        } else {
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
        }
        $message = 'Success';


        // echo $all_data->comp_qa_tags ;
        //  echo $all_data->type_id ;
        //print_r($all_data) ; tags
        //-------------------------------------
        // get session tags ids

        /**
         * This is for background process
         */


        if ($type == 'comp') {
            $this->load->library('Background');
            $basic_detail = array();
            $basic_detail['user_master_id'] = $this->token_data->userdetail->user_master_id;
            $basic_detail['tag_ids'] = $all_data['tags'];
            $basic_detail['content_type'] = 'comp';
            $basic_detail['content_id'] = $this->input->get('type_id');
            $basic_detail['speciality_ids'] = $all_data['specialities_id'];
            $basic_detail['Authorization'] = $this->input->get_request_header('Authorization');
            $url = base_url() . "rnv6/knwlgmastersession/addUserToTagDetails";
            $param = $basic_detail;
            $this->background->do_in_background($url, $param);/**/
        }


        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, $this->logdata['response'], $this->logdata); //This is the respon if success
    }


    public function historyvideo_get()
    {

        $limitTo = $this->input->get('to', true);
        $limitFrom = $this->input->get('from', true);


        if ((!is_numeric($limitFrom)) && (!is_numeric($limitTo))) {
            $message = "limit From To must be numeric and cannot be blank";

            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        } else {
            $all_data = $this->Knwlg_model->gethistoryvideo($this->token_data->userdetail->user_master_id, $limitTo, $limitFrom);
            $message = 'Success';

            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        }
    }

    /**
     *
     */
    public function getrecordinglist_get()
    {

        $user_master_id = $this->token_data->userdetail->user_master_id;
        $limitTo = $this->input->get('to', true);
        $limitFrom = $this->input->get('from', true);

        $all_data = $this->Knwlg_model->getrecordinglist($user_master_id, $limitFrom, $limitTo);
        if ($all_data != '') {
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
        } else {
            $message = "data not available";

            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }
    }


    public function topcommentedmedwiki_get()
    {
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $group_ids = $this->token_data->userdetail->group_ids;


        $type = $this->input->get('type'); // for search value.
        $spIds = $this->input->get('spIds'); // for search value.
        $type_id = $this->input->get('type_id'); // for search value.
        $val = $this->input->get('val'); // for search value.
        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $all_data = $this->Knwlg_model->topcomment($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, $val, $type, $spIds);

        if ($all_data != "") {
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if succ
        } else {
            $message = "data not available";

            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }
        #print_r($all_data);
    }

    public function topratedmedwiki_get()
    {
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $group_ids = $this->token_data->userdetail->group_ids;


        $type = $this->input->get('type'); // for search value.
        $spIds = $this->input->get('spIds'); // for search value.
        $type_id = $this->input->get('type_id'); // for search value.
        $val = $this->input->get('val'); // for search value.
        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $all_data = $this->Knwlg_model->toprated($user_master_id, $client_ids, $group_ids, $limitFrom, $limitTo, $val, $type, $spIds);

        if ($all_data != "") {
            $message = 'Success';
            /**
             * For api detail table
             */
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_OK;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
            /**
             * For api detail table
             */
            $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if succ
        } else {
            $message = "data not available";

            $output['error'] = $message;
            $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
            $this->logdata['response'] = REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION;
            $this->logdata['message'] = $message;
            $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

            $this->set_response($output, $message, REST_Controller::HTTP_NON_AUTHORITATIVE_INFORMATION, $this->logdata);
        }
        #print_r($all_data);
    }

    public function rating_post()
    {
        $data = json_decode(file_get_contents('php://input'), true);

        $user_id = $this->token_data->userdetail->user_master_id;

        $array = array(
            "user_master_id" => $user_id,
            "type" => $data['type'],
            "type_name" => $data['type_name'],
            "rating" => $data['rating'],
            "addedon" => date('Y-m-d H:i:s'),
        );

        $this->Knwlg_model->review_insert($array);
        $all_data = array();
        $message = 'Success';

        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());

        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }

    public function notification_get()
    {

        //print_r($this->token_data);
        //echo $token->userdetail->user_master_id
        $user_master_id = $this->token_data->userdetail->user_master_id;
        $client_ids = $this->token_data->userdetail->client_ids;
        $group_ids = $this->token_data->userdetail->group_ids;

        $limitTo = $this->input->get('to');
        $limitFrom = $this->input->get('from');
        $val = $this->input->get('val'); // for search value.
        $spIds = $this->input->get('spIds'); // for search value.






        $sqluser = "select 
        SELECT 
        *  
        FROM 
        `user_notification_communication` 
        WHERE `user_master_id` =" . $user_master_id . " 
        ORDER BY `id`  DESC
        ";


        $queryUser = $this->db->query($sqluser);
        $resultUser = $queryUser->result();
        $onesignal = array();
        foreach ($resultUser as $value) {

            $onesignal[] = $value->onesignal;
        }
        $onesignalSring = implode(",", (array)$onesignal);








        $sql = "select 
        distinct(notification_logs.temp_id) as temp_id, 
        notification_temp_log.send_data as response_data, 
        notification_temp_log.updated_at as datetime, 
        read_notifications.status as status 
        from notification_logs 
        LEFT JOIN notification_temp_log ON notification_logs.temp_id = notification_temp_log.temp_id 
        LEFT JOIN read_notifications ON notification_temp_log.temp_id = read_notifications.temp_id 
        where 
        notification_logs.to in(" . $onesignalSring . ") 
        group by notification_logs.temp_id
        order by notification_logs.id desc 
        limit 10";


        $query = $this->db->query($sql);
        $result = $query->result();
        print_r($result);




























        //$all_data = $this->Knwlg_model->all_comp();


        // exit;
        $message = 'Success';
        /**
         * For api detail table
         */
        $this->logdata['doctor_id'] = $this->token_data->userdetail->user_master_id;
        $this->logdata['response'] = REST_Controller::HTTP_OK;
        $this->logdata['message'] = $message;
        $this->logdata['response_on'] = date('Y-m-d H:i:s', time());
        /**
         * For api detail table
         */
        $this->set_response($all_data, $message, REST_Controller::HTTP_OK, $this->logdata); //This is the respon if success
    }
}
