<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Cta_model extends CI_Model
{
    /**
     * @param $user_master_id
     * @param $client_ids
     * @param $limitTo
     * @param $limitFrom
     * @return array
     *
     */

    public function __construct()
    {

        parent::__construct();
        //Table Name
        $ci = get_instance();
        $ci->load->helper('image');
        $ci->load->helper('utility');
    }
    public function update_schedule($user_master_id, $dataArray, $data)
    {
        // print_r($dataArray); exit;
        $this->insertdb = $this->load->database('insert', true);
        $content_type = $dataArray['content_type'];
        $content_id = $dataArray['content_id'];
        $cta_id = $dataArray['cta_id'];

        $this->db->select("*");
        $this->db->from("cta_analytics_to_user");
        $this->db->where(['user_id' => $user_master_id, 'content_type' => $content_type, 'content_id' => $content_id, 'cta_id' => $cta_id]);
        $query = $this->db->get();
        #print_r($this->db->last_query()); exit;
        if ($query->num_rows() > 0) {
            // echo "found"; exit;

            if ($dataArray['contact_type'] == '1') {
                $array['callback_status'] = 1;
                $array['schedule_data'] = $data;
            }

            if ($dataArray['contact_type'] == '2') {
                $array['schedule_status'] = 1;
                $array['schedule_data'] = $data;
            }

            $this->insertdb->where(['user_id' => $user_master_id, 'content_type' => $content_type, 'content_id' => $content_id]);
            $this->insertdb->update('cta_analytics_to_user', $array);
            #print_r($this->db->last_query()); exit;
            #if($this->db->affected_rows()){

            // if($dataArray['contact_type'] == 1){
            // $this->clirnet_notification->sms($user_master_id,'ctacallback',"","");
            // $current_time = date('Y-m-d H:i:s');
            // $temp_param = array('user_id'=>$user_master_id,'current_time'=>$current_time);
            // $this->clirnet_notification->email($temp_param,$cta_id,'CtaEmail');
            // }
            // return true;
            // }else{
            //     return false;
            // }
        } else {
            // echo 'not found'; exit;
            // echo $dataArray['contact_type']; exit;
            if ($dataArray['contact_type'] == 1) {
                $array = array(
                    'user_id' => $user_master_id,
                    "cta_id" => $dataArray['cta_id'],
                    'added_on' => date('Y-m-d H:i:s'),
                    "content_type" => $dataArray['content_type'],
                    "content_id" => $dataArray['content_id'],
                    "status" => 4,
                    "callback_status" => 1,
                    "schedule_status" => 0,
                    "schedule_data" => $data
                );
            }

            if ($dataArray['contact_type'] == 2) {
                $array = array(
                    'user_id' => $user_master_id,
                    "cta_id" => $dataArray['cta_id'],
                    'added_on' => date('Y-m-d H:i:s'),
                    "content_type" => $dataArray['content_type'],
                    "content_id" => $dataArray['content_id'],
                    "status" => 4,
                    "callback_status" => 0,
                    "schedule_status" => 1,
                    "schedule_data" => $data
                );
            }

            // print_r($array); exit;

            $this->insertdb->insert('cta_analytics_to_user', $array);
            #print_r($this->db->last_query()); exit;
            // if($this->db->affected_rows()){
            // if($dataArray['contact_type'] == 1){
            // $this->clirnet_notification->sms($user_master_id,'ctacallback',"","");
            // $current_time = date('Y-m-d H:i:s');
            // $temp_param = array('user_id'=>$user_master_id,'current_time'=>$current_time);
            // $this->clirnet_notification->email($temp_param,$cta_id,'CtaEmail');
            // }
            // return true;
            // }else{
            //     return false;
            // }

        }

        $to = '';

        $this->db->select("email");
        $this->db->from("cta_master");
        $this->db->where(['id' => $cta_id]);
        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            $query = $query->row();
            $to = $query->email;
        }

        $userenv = get_user_env($user_master_id); //(100627);

        switch ($userenv) {
            case 'GL':
                //echo 1;
                $notify = new Clirnetnotification_V3();

                $current_time = date('Y-m-d H:i:s');
                $temp_param = array('user_id' => $user_master_id, 'current_time' => $current_time);
                $notify->email($to, $temp_param, $cta_id, 'CtaEmail');
                break;
            case 'IN':
                //echo 2;
                $notify = new Clirnetnotification_V3();
                $notify->sms($user_master_id, 'ctacallback', "", "");

                $current_time = date('Y-m-d H:i:s');
                $temp_param = array('user_id' => $user_master_id, 'current_time' => $current_time);
                $notify->email($to, $temp_param, $cta_id, 'CtaEmail');

                break;
            default:
                break;
        }
        // $this->clirnet_notification->sms($user_master_id, 'ctacallback', "", "");
        // // if(!empty($to)){
        // $current_time = date('Y-m-d H:i:s');
        // $temp_param = array('user_id' => $user_master_id, 'current_time' => $current_time);
        // $this->clirnet_notification->email($to, $temp_param, $cta_id, 'CtaEmail');
        // }

        return true;
    }

    public function ctalist($type, $type_id, $user_master_id, $client_ids, $limitTo, $limitFrom)
    {
        // print_r($type);
        //print_r($type_id);
        //exit;
        //echo 'user_master_id_ctalist: '.$user_master_id;

        $response = array();
        // $this->db->distinct();
        // $sqluserpollanswer = "select
        // survey_id
        // from survey as s
        // join survey_user_answer as sua on sua.survey_id=s.survey_id and sua.user_master_id =" . $user_master_id . "
        // join cta_to_content as ctc on ctc.cta_id=" . $type_id . " and ctc.type='survey' where s.category = 'poll'";
        //echo $sqluserpollanswer; exit;

        $this->load->library('CrmMyredis');

        $cachename = USER_INFO_CACHE_KEY.$user_master_id;

        $resultUserGroup = null;
        if ($this->crmmyredis->exists($cachename)) {
            $userDetail = $this->crmmyredis->get($cachename);
            // $user_group = $userData["grp"];

            if (!empty($userDetail) && $userDetail["grp"] != '') {
                $group_ids = explode(",", $userDetail["grp"]);
                $resultUserGroup = array();
                foreach ($group_ids as $key => $val) {
                    $resultUserGroup[] = array("group_id" => $val );
                }
            }
        } else {

            $sqlUserGroup = "select 
            *
            from
            user_to_usergroup 
            where 
            user_master_id = " . $user_master_id . "";
            // echo $sqlInt; exit;

            $queryUserGroup = $this->db->query($sqlUserGroup);
            $resultUserGroup = $queryUserGroup->result_array();

        }

        $userGroup = array();
        foreach ($resultUserGroup as $valGroup) {
            $userGroup[] = $valGroup['group_id'];
        }

        if ($userGroup != '') {
            $userGroups = implode(",", (array)$userGroup);
        } else {
            $userGroups = null;
        }

        // echo "<pre>";print_r($userGroups);die();
        //$resultUserGroup['group_id'];
        //echo 'userGroups: '.$userGroups;

        if ($userGroups) {
            /*$this->db->select('ctc.*, catu.callback_status, catu.schedule_status, catu.user_id,cToc.type,cToc.type_id');
            $this->db->from('cta_master  as ctc');
            $this->db->join('cta_analytics_to_user as catu', 'ctc.id=catu.cta_id AND catu.user_id = ' . $user_master_id . '', 'left');
            $this->db->join('cta_to_content  as cToc', 'ctc.id=cToc.cta_id ', 'left');

            $this->db->join('cta_to_group  as cTgr', 'cTgr.cta_id = ctc.id ', 'left');

            $this->db->where(array('cToc.type' => $type, 'cToc.type_id' => $type_id, 'ctc.status' => 3));

            $this->db->where_in('cTgr.group_id', $userGroups);
            //$this->db->group_start();
            $this->db->or_where('cTgr.cta_id is null');
            //$this->db->group_end();*/
            if (($limitTo != '') && ($limitFrom != '')) {
                $this->db->limit($limitFrom, $limitTo);
            }
            // $sql = "SELECT `ctc`.*, `catu`.`callback_status`, `catu`.`schedule_status`, `catu`.`user_id`, `cToc`.`type`, `cToc`.`type_id`
            // FROM `cta_master` as `ctc`
            // LEFT JOIN `cta_analytics_to_user` as `catu` ON `ctc`.`id`=`catu`.`cta_id` AND `catu`.`user_id` = ".$user_master_id."
            // LEFT JOIN `cta_to_content` as `cToc` ON `ctc`.`id`=`cToc`.`cta_id`
            // LEFT JOIN `cta_to_group` as `cTgr` ON `cTgr`.`cta_id` = `ctc`.`id`
            // WHERE `cToc`.`type` = '".$type."'
            // AND `cToc`.`type_id` = ".$type_id."
            // AND `ctc`.`status` = 3
            // AND ( `cTgr`.`group_id` IN(".$userGroups.") OR `cTgr`.`cta_id` is null )
            // ";

            // detecting the user origin
            $env = get_user_env($user_master_id);
            if (!empty($env)) {
                if ($env != 'GL') {
                    $envStatus = "AND (ctc.env ='GL' or ctc.env ='" . $env . "')";
                } else {
                    $envStatus = "AND ctc.env ='" . $env . "'";
                }
            } else {
                $envStatus = "";
            }
            $sql = "SELECT `ctc`.*, `catu`.`callback_status`, `catu`.`schedule_status`, `catu`.`user_id`, `cToc`.`type`, `cToc`.`type_id`
            FROM `cta_master` as `ctc`
            LEFT JOIN `cta_analytics_to_user` as `catu` ON `ctc`.`id`=`catu`.`cta_id` AND `catu`.`user_id` = " . $user_master_id . "
            LEFT JOIN `cta_to_content` as `cToc` ON `ctc`.`id`=`cToc`.`cta_id`
            LEFT JOIN `cta_to_group` as `cTgr` ON `cTgr`.`cta_id` = `ctc`.`id`
            LEFT JOIN knwlg_sessions_V1 as s ON s.session_id = cToc.type_id
            LEFT JOIN knwlg_compendium_V1 AS c ON c.comp_qa_id=cToc.type_id
            LEFT JOIN knwlg_video_archive as v ON v.video_archive_id = cToc.type_id
            LEFT JOIN knwlg_gr_register as g ON g.gr_id=cToc.type_id
            LEFT JOIN survey as spq ON spq.survey_id =cToc.type_id
            WHERE `cToc`.`type` = '" . $type . "'
            AND `cToc`.`type_id` = " . $type_id . "
            AND `ctc`.`status` = 3
            " . $envStatus . "
            and

            (
                
                (date(v.publication_date)<=CURDATE())
    
            or (date(c.publication_date)<=CURDATE())
    
            or (date(g.gr_date_of_publication)<=CURDATE())
    
            or (s.status = 3 and s.session_status not in (5,6) )
    
            or  ( date(spq.publishing_date) <= CURDATE() )
                
                )
            AND ( `cTgr`.`group_id` IN(" . $userGroups . ") OR `cTgr`.`cta_id` is null )
            ";

            /**/

            // echo "<pre>";print_r($sql);die();
        } else {
            /*$this->db->select('ctc.*, catu.callback_status, catu.schedule_status, catu.user_id,cToc.type,cToc.type_id');
            $this->db->from('cta_master  as ctc');
            $this->db->join('cta_analytics_to_user as catu', 'ctc.id=catu.cta_id AND catu.user_id = ' . $user_master_id . '', 'left');
            $this->db->join('cta_to_content  as cToc', 'ctc.id=cToc.cta_id ', 'left');
            $this->db->where(array('cToc.type' => $type, 'cToc.type_id' => $type_id, 'ctc.status' => 3));

            if (($limitTo != '') && ($limitFrom != '')) {
                $this->db->limit($limitFrom, $limitTo);
            }*/

            //echo 'user_master_id_in_model: '.$user_master_id;

            $env = get_user_env($user_master_id);
            if (!empty($env)) {
                if ($env != 'GL') {
                    $envStatus = "AND (ctc.env ='GL' or ctc.env ='" . $env . "')";
                } else {
                    $envStatus = "AND ctc.env ='" . $env . "'";
                }
            } else {
                $envStatus = "";
            }

            if ($type_id == 0) {
                $sql = "SELECT `ctc`.*, `catu`.`callback_status`, `catu`.`schedule_status`, `catu`.`user_id`, `cToc`.`type`, `cToc`.`type_id`
                FROM `cta_master` as `ctc`
                LEFT JOIN `cta_analytics_to_user` as `catu` ON `ctc`.`id`=`catu`.`cta_id` AND `catu`.`user_id` = " . $user_master_id . "
                LEFT JOIN `cta_to_content` as `cToc` ON `ctc`.`id`=`cToc`.`cta_id`
                LEFT JOIN `cta_to_group` as `cTgr` ON `cTgr`.`cta_id` = `ctc`.`id`
                WHERE `cToc`.`type` = '" . $type . "'
                AND `cToc`.`type_id` = " . $type_id . "
                AND `ctc`.`status` = 3
                " . $envStatus . "
                ";
            } else {
                $sql = "SELECT `ctc`.*, `catu`.`callback_status`, `catu`.`schedule_status`, `catu`.`user_id`, `cToc`.`type`, `cToc`.`type_id`
                FROM `cta_master` as `ctc`
                LEFT JOIN `cta_analytics_to_user` as `catu` ON `ctc`.`id`=`catu`.`cta_id` AND `catu`.`user_id` = " . $user_master_id . "
                LEFT JOIN `cta_to_content` as `cToc` ON `ctc`.`id`=`cToc`.`cta_id`
                LEFT JOIN `cta_to_group` as `cTgr` ON `cTgr`.`cta_id` = `ctc`.`id`
                LEFT JOIN knwlg_sessions_V1 as s ON s.session_id = cToc.type_id
                LEFT JOIN knwlg_compendium_V1 AS c ON c.comp_qa_id=cToc.type_id
                LEFT JOIN knwlg_video_archive as v ON v.video_archive_id = cToc.type_id
                LEFT JOIN knwlg_gr_register as g ON g.gr_id=cToc.type_id
                LEFT JOIN survey as spq ON spq.survey_id =cToc.type_id
                WHERE `cToc`.`type` = '" . $type . "'
                AND `cToc`.`type_id` = " . $type_id . "
                AND `ctc`.`status` = 3
                " . $envStatus . "
                and
                (
                (date(v.publication_date)<=CURDATE())
                or (date(c.publication_date)<=CURDATE())
                or (date(g.gr_date_of_publication)<=CURDATE())
                or (s.status = 3 and s.session_status not in (5,6) )
                or  ( date(spq.publishing_date) <= CURDATE() )
                )
                AND (cTgr.cta_id is NULL)";
                //== echo $sql;
                // exit;
            }
        }

        //print_r($type_id); exit;
        //$query = $this->db->get();
        // echo $sql;
        // exit;
        $query = $this->db->query($sql);

        if (($query) && ($query->num_rows() > 0)) {
            // print_r($this->db->last_query());
            // exit;
            // print_r($query->result());
            // exit;
            //$i = 0;

            foreach ($query->result() as $key => $value) {

                //print_r($type_id);exit;
                // $check_active_service = $this->servicestatus($value->cta_type,$value->cta_type_id,$client_ids);
                //print_r($check_active_service); exit;
                //if($check_active_service == 1){
                if ($value->user_id == $user_master_id and $value->callback_status == 1) {
                    $callback_status = $value->callback_status;
                } else {
                    $callback_status = 0;
                }
                if ($value->user_id == $user_master_id and $value->schedule_status == 1) {
                    $schedule_status = $value->schedule_status;
                } else {
                    $schedule_status = 0;
                }

                // $ctadetail = cta($value->cta_type, $value->cta_type_id, $user_master_id, $client_ids);
                $ctadetail = cta($value->cta_type, $value->cta_type_id, $user_master_id, $client_ids);

                //echo 'xxxx----xxxxxx--'.$i;
                //print_r($ctadetail);
                //$i++;
                //exit;
                // echo '++xxxx----xxxxxx';
                // die();
                // echo "<pre>";print_r($ctadetail); die();

                $row = [];
                $row['id'] = $value->id;
                $row['type'] = $value->type;
                $row['type_id'] = $value->type_id;
                $row['cta_type'] = $value->cta_type;
                $row['cta_type_id'] = $value->cta_type_id;
                $row['cta_action_type'] = $value->cta_action_type;
                $row['position'] = $value->position;
                if (
                    $value->cta_type === 'external' ||
                    $value->cta_type === 'schedule' ||
                    $value->cta_type === 'event' ||
                    $value->cta_type === 'featured_content' ||
                    $value->cta_type === 'sample_collection' ||
                    $value->cta_type === 'leaderboard'
                ) {
                    $row['details'] = "";
                } else {
                    //echo 'xxxxx';
                    //print_r($ctadetail);
                    //exit;

                    if (!empty($ctadetail)) {
                        $row['details'] = $ctadetail;
                    } else {
                        $row['details'] = '';
                    }
                    //$row['details'] = $ctadetail['content_detail']; //cta($value->cta_type, $value->cta_type_id, $user_master_id, $client_ids);

                }

                $row['button_action'] = $value->button_action;
                $row['sub_header'] = $value->sub_header;
                $row['logo'] = change_img_src($value->logo);
                $row['consent_statement'] = $value->consent_statement;
                $row['colour'] = $value->consent_statement;
                // $row['cta_type_data'] = $value->cta_type_data;
                $row['cta_url'] = $value->cta_url;
                $row['template_id'] = (string) $value->template_id;
                $row['title'] = $value->title;
                $row['description'] = $value->description;
                $row['button_text'] = $value->button_text;
                $row['image'] = change_img_src($value->image);
                $row['settingsJson'] = $value->settingsJson;
                $row['settingsJson_new'] = $value->settingsJsonnew;
                $row['formJson'] = $value->formJson;
                $row['status'] = $value->status;
                $row['callback_status'] = (int)$callback_status;
                $row['schedule_status'] = (int)$schedule_status;
                // $row['user_id'] = $value->user_id;
                // $row['user_master_id'] = $user_master_id;
                // print_r('content->'.''.$ctadetail['content_id']);
                // print_r('type->'.''.$value->cta_type);
                // echo 'xxxxxxxxxxxxxx<br>';
                // print_R($ctadetail);
                // echo 'xxxxxxxxxxxxxx<br>';
                //print_r($row);

                if (!empty($ctadetail)) {

                    // echo 'content_id: '.$ctadetail['content_id'];

                    if (($ctadetail['content_id'] == '') && ($value->cta_type == "external")) {
                        $response[] = $row;
                    } elseif (($ctadetail['content_id'] == '') && ($value->cta_type == "schedule")) {
                        $response[] = $row;
                    } elseif (($ctadetail['content_id'] == '') && ($value->cta_type == "event")) {
                        $response[] = $row;
                    } elseif (($ctadetail['content_id'] == '') && ($value->cta_type == "featured_content")) {
                        $response[] = $row;
                    } elseif (($ctadetail['content_id'] == '') && ($value->cta_type == "sample_collection")) {
                        $response[] = $row;
                    } elseif (($ctadetail['content_id'] == '') && ($value->cta_type == "leaderboard")) {
                        $response[] = $row;
                    } elseif (($ctadetail['content_id'] == '') && ($value->cta_type == "iframe")) {
                        $response[] = $row;
                    } elseif ($ctadetail['content_id'] != '') {
                        // echo '-zzzzzzzzzzzzzzzzzzzzz-';
                        // print_r($row);
                        // exit;
                        $response[] = $row;

                    } else {
                        $response[] = $row;
                    }
                } else {

                    $response[] = $row;
                }
                //}
            }
        }
        // echo "<pre>";
        // print_r($response);
        // exit;
        //die;
        return $response;
    }

    public function ctalist_bk($type, $type_id, $user_master_id, $client_ids, $limitTo, $limitFrom)
    {
        // print_r($type);
        // print_r($type_id);
        //exit;
        //   echo $user_master_id.'  '.$type; exit;

        $response = array();
        // $this->db->distinct();
        $sqluserpollanswer = "select survey_id from survey as s join survey_user_answer as sua on sua.survey_id=s.survey_id and sua.user_master_id =" . $user_master_id . " join cta_to_content as ctc on ctc.cta_id=" . $type_id . " and ctc.type='survey' where s.category = 'poll'";
        //echo $sqluserpollanswer; exit;
        $sqlUserGroup = "select 
            *
            from
            user_to_usergroup 
            where 
            user_master_id = " . $user_master_id . "";
        //echo $sqlInt; exit;

        $queryUserGroup = $this->db->query($sqlUserGroup);
        $resultUserGroup = $queryUserGroup->result_array();

        $userGroup = array();
        foreach ($resultUserGroup as $valGroup) {

            $userGroup[] = $valGroup['group_id'];
        }

        $userGroups = implode(",", (array)$userGroup);
        //$resultUserGroup['group_id'];
        if ($userGroups) {
            /*$this->db->select('ctc.*, catu.callback_status, catu.schedule_status, catu.user_id,cToc.type,cToc.type_id');
            $this->db->from('cta_master  as ctc');
            $this->db->join('cta_analytics_to_user as catu', 'ctc.id=catu.cta_id AND catu.user_id = ' . $user_master_id . '', 'left');
            $this->db->join('cta_to_content  as cToc', 'ctc.id=cToc.cta_id ', 'left');

            $this->db->join('cta_to_group  as cTgr', 'cTgr.cta_id = ctc.id ', 'left');

            $this->db->where(array('cToc.type' => $type, 'cToc.type_id' => $type_id, 'ctc.status' => 3));

            $this->db->where_in('cTgr.group_id', $userGroups);
            //$this->db->group_start();
            $this->db->or_where('cTgr.cta_id is null');
            //$this->db->group_end();*/
            if (($limitTo != '') && ($limitFrom != '')) {
                $this->db->limit($limitFrom, $limitTo);
            }
            // $sql = "SELECT `ctc`.*, `catu`.`callback_status`, `catu`.`schedule_status`, `catu`.`user_id`, `cToc`.`type`, `cToc`.`type_id`
            // FROM `cta_master` as `ctc`
            // LEFT JOIN `cta_analytics_to_user` as `catu` ON `ctc`.`id`=`catu`.`cta_id` AND `catu`.`user_id` = ".$user_master_id."
            // LEFT JOIN `cta_to_content` as `cToc` ON `ctc`.`id`=`cToc`.`cta_id`
            // LEFT JOIN `cta_to_group` as `cTgr` ON `cTgr`.`cta_id` = `ctc`.`id`
            // WHERE `cToc`.`type` = '".$type."'
            // AND `cToc`.`type_id` = ".$type_id."
            // AND `ctc`.`status` = 3
            // AND ( `cTgr`.`group_id` IN(".$userGroups.") OR `cTgr`.`cta_id` is null )
            // ";

            $sql = "SELECT `ctc`.*, `catu`.`callback_status`, `catu`.`schedule_status`, `catu`.`user_id`, `cToc`.`type`, `cToc`.`type_id`
            FROM `cta_master` as `ctc`
            LEFT JOIN `cta_analytics_to_user` as `catu` ON `ctc`.`id`=`catu`.`cta_id` AND `catu`.`user_id` = " . $user_master_id . "
            LEFT JOIN `cta_to_content` as `cToc` ON `ctc`.`id`=`cToc`.`cta_id`
            LEFT JOIN `cta_to_group` as `cTgr` ON `cTgr`.`cta_id` = `ctc`.`id`
            LEFT JOIN knwlg_sessions_V1 as s ON s.session_id = cToc.type_id
            LEFT JOIN knwlg_compendium_V1 AS c ON c.comp_qa_id=cToc.type_id
            LEFT JOIN knwlg_video_archive as v ON v.video_archive_id = cToc.type_id
            LEFT JOIN knwlg_gr_register as g ON g.gr_id=cToc.type_id
            LEFT JOIN survey as spq ON spq.survey_id =cToc.type_id
            WHERE `cToc`.`type` = '" . $type . "'
            AND `cToc`.`type_id` = " . $type_id . "
            AND `ctc`.`status` = 3
            and

            (
                
                (date(v.publication_date)<=CURDATE())
    
            or (date(c.publication_date)<=CURDATE())
    
            or (date(g.gr_date_of_publication)<=CURDATE())
    
            or (s.status = 3 and s.session_status <> 6 )
    
            or  ( date(spq.publishing_date) <= CURDATE() )
                
                )
            AND ( `cTgr`.`group_id` IN(" . $userGroups . ") OR `cTgr`.`cta_id` is null )
            ";

            /**/
        } else {
            /*$this->db->select('ctc.*, catu.callback_status, catu.schedule_status, catu.user_id,cToc.type,cToc.type_id');
            $this->db->from('cta_master  as ctc');
            $this->db->join('cta_analytics_to_user as catu', 'ctc.id=catu.cta_id AND catu.user_id = ' . $user_master_id . '', 'left');
            $this->db->join('cta_to_content  as cToc', 'ctc.id=cToc.cta_id ', 'left');
            $this->db->where(array('cToc.type' => $type, 'cToc.type_id' => $type_id, 'ctc.status' => 3));

            if (($limitTo != '') && ($limitFrom != '')) {
                $this->db->limit($limitFrom, $limitTo);
            }*/
            $sql = "SELECT `ctc`.*, `catu`.`callback_status`, `catu`.`schedule_status`, `catu`.`user_id`, `cToc`.`type`, `cToc`.`type_id`
            FROM `cta_master` as `ctc`
            LEFT JOIN `cta_analytics_to_user` as `catu` ON `ctc`.`id`=`catu`.`cta_id` AND `catu`.`user_id` = " . $user_master_id . "
            LEFT JOIN `cta_to_content` as `cToc` ON `ctc`.`id`=`cToc`.`cta_id`
            LEFT JOIN `cta_to_group` as `cTgr` ON `cTgr`.`cta_id` = `ctc`.`id`
            LEFT JOIN knwlg_sessions_V1 as s ON s.session_id = cToc.type_id
            LEFT JOIN knwlg_compendium_V1 AS c ON c.comp_qa_id=cToc.type_id
            LEFT JOIN knwlg_video_archive as v ON v.video_archive_id = cToc.type_id
            LEFT JOIN knwlg_gr_register as g ON g.gr_id=cToc.type_id
            LEFT JOIN survey as spq ON spq.survey_id =cToc.type_id
            WHERE `cToc`.`type` = '" . $type . "'
            AND `cToc`.`type_id` = " . $type_id . "
            AND `ctc`.`status` = 3
            and

            (
                
                (date(v.publication_date)<=CURDATE())
    
            or (date(c.publication_date)<=CURDATE())
    
            or (date(g.gr_date_of_publication)<=CURDATE())
    
            or (s.status = 3 and s.session_status <> 6 )
    
            or  ( date(spq.publishing_date) <= CURDATE() )
                
                )
            AND (cTgr.cta_id is NULL)
            
            ";
        }

        // print_r($sql); exit;
        //$query = $this->db->get();
        $query = $this->db->query($sql);

        if (($query) && ($query->num_rows() > 0)) {
            //echo "<pre>";print_r($query->result()); exit;

            foreach ($query->result() as $key => $value) {
                //print_r($value);
                // $check_active_service = $this->servicestatus($value->cta_type,$value->cta_type_id,$client_ids);
                //print_r($check_active_service); exit;
                //if($check_active_service == 1){
                if ($value->user_id == $user_master_id and $value->callback_status == 1) {
                    $callback_status = $value->callback_status;
                } else {
                    $callback_status = 0;
                }
                if ($value->user_id == $user_master_id and $value->schedule_status == 1) {
                    $schedule_status = $value->schedule_status;
                } else {
                    $schedule_status = 0;
                }
                $ctadetail = cta($value->cta_type, $value->cta_type_id, $user_master_id, $client_ids);
                if ($ctadetail['content_id'] != '') {
                    $row = [];
                    $row['id'] = $value->id;
                    $row['type'] = $value->type;
                    $row['type_id'] = $value->type_id;
                    $row['cta_type'] = $value->cta_type;
                    $row['cta_type_id'] = $value->cta_type_id;
                    $row['cta_action_type'] = $value->cta_action_type;
                    $row['position'] = $value->position;

                    if ($value->cta_type == 'external') {
                        $row['details'] = "";
                    } else {
                        $row['details'] = $ctadetail; //cta($value->cta_type, $value->cta_type_id, $user_master_id, $client_ids);

                    }

                    $row['button_action'] = $value->button_action;
                    $row['sub_header'] = $value->sub_header;
                    $row['logo'] = $value->logo;
                    $row['consent_statement'] = $value->consent_statement;
                    $row['colour'] = $value->consent_statement;
                    // $row['cta_type_data'] = $value->cta_type_data;
                    $row['cta_url'] = $value->cta_url;
                    $row['template_id'] = $value->template_id;
                    $row['title'] = $value->title;
                    $row['description'] = $value->description;
                    $row['button_text'] = $value->button_text;
                    $row['status'] = $value->status;
                    $row['callback_status'] = (int)$callback_status;
                    $row['schedule_status'] = (int)$schedule_status;
                    // $row['user_id'] = $value->user_id;
                    // $row['user_master_id'] = $user_master_id;
                    $response[] = $row;
                }

                //}
            }
        }
        // echo "<pre>";print_r($response);exit;
        //die;
        return $response;
    }

    public function servicestatus($type, $type_id, $client_ids)
    {
        // $type." ".$type_id; exit;
        $CI = &get_instance();
        switch ($type) {
            case 'clinical_video':

                $sql = "SELECT
                cm.video_archive_id as type_id
                
                FROM knwlg_video_archive  as cm
                
                WHERE cm.status=3
                AND date(cm.publication_date)<=CURDATE() and
                cm.video_archive_id = " . $type_id . "";

                $query = $this->db->query($sql);
                //echo $sql;
                if (($query) && ($query->num_rows() > 0)) {
                    return 1;
                } else {
                    return 0;
                }
                break;
            case 'comp':
                //MEDWIKI
                $sql = "SELECT
                cm.comp_qa_id as type_id

                FROM knwlg_compendium_V1 as cm

                WHERE cm.status=3
                AND date(cm.publication_date)<=CURDATE() and
                cm.comp_qa_id = " . $type_id . "";

                $query = $CI->db->query($sql);
                //echo $sql;
                if (($query) && ($query->num_rows() > 0)) {
                    return 1;
                } else {
                    return 0;
                }
                break;
            case 'gr':

                $sql = "SELECT
                gr.gr_id as type_id
                FROM knwlg_gr_register as gr
                WHERE 
                gr.status=3
                and date(gr.gr_date_of_publication)<=CURDATE() and
                gr.gr_id = " . $type_id . "";

                $query = $CI->db->query($sql);
                if (($query) && ($query->num_rows() > 0)) {
                    return 1;
                } else {
                    return 0;
                }

                break;
            case 'session':

                $client_list = ' and (' . implode(' OR ', array_map(function ($x) {
                    return "FIND_IN_SET('$x', ks.client_id)";
                }, explode(',', $client_ids))) . ')';
                $sql = "SELECT
                ks.session_id
                FROM knwlg_sessions_V1 as ks
                WHERE
                ks.session_id=" . $type_id . "  " . $client_list . "
                and ks.status = 3 and ks.session_status <> 6 ";

                $query = $CI->db->query($sql);
                //echo $sql." ";
                if (($query) && ($query->num_rows() > 0)) {
                    return 1;
                } else {
                    return 0;
                }

                break;
            case 'survey':

                $sql = "SELECT 
                sv.* 
                FROM 
                survey sv
                WHERE 
                sv.status = 3
                and date(sv.publishing_date) <= CURDATE() and
                sv.survey_id = " . $type_id . " ";

                $query = $CI->db->query($sql);
                //echo $sql."   ";
                if (($query) && ($query->num_rows() > 0)) {
                    return 1;
                } else {
                    return 0;
                }

                break;
            default:
                return 1;
                break;
        }
    }

    /**
     * @param $insertbatch
     * @return bool
     */
    public function ctareport($value)
    {
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($value)) {
            $this->insertdb->insert('cta_report', $value);

            # print_r($this->db->last_query()); die;
        }
        return true;
    }

    /**
     * @param $eid
     * @param $cid
     * @return array
     */
    public function share($eid, $cid)
    {
        $final_array = array();
        if (($eid != '') && ($cid != '')) {
            $this->db->select('cms.channel_id,cm.title as title,cms.id as election_id,cm.logo,cms.startdate,cms.enddate,epm.id,epm.position_name');
            $this->db->from('channel_election_schedule as cms');
            $this->db->join('election_position_master as epm', 'epm.channel_id=cms.channel_id');
            $this->db->join('channel_master as cm', 'cm.channel_master_id = cms.channel_id');
            $this->db->where(array('cms.id' => $eid, 'cms.channel_id' => $cid));

            $query_position = $this->db->get();
            //print_r($this->db->last_query());

            if ($query_position->num_rows() > 0) {

                foreach ($query_position->result() as $key => $value) {

                    $final_array['channeldetail']['channel_title'] = $value->title;
                    $final_array['channeldetail']['channel_logo'] = $value->logo;

                    $this->db->select('enl.session_doctor_id');
                    $this->db->from('election_nominiee_list as enl');

                    $this->db->where(array('enl.election_id' => $eid, 'enl.position_id' => $value->id));

                    $query = $this->db->get();

                    if ($query->num_rows() > 0) {

                        foreach ($query->result() as $keydoc => $valuedoc) {

                            $doctor_detail = session_doc_detail($valuedoc->session_doctor_id);
                            $doctor_id = array('session_doctor_id' => $valuedoc->session_doctor_id);

                            $doctorlist = array_merge($doctor_id, $doctor_detail[0]);
                            $positionid['id'] = $value->id;
                            $doctors[$value->id]['data'][] = $doctorlist;
                        }
                    }
                    $doctordetail['position'] = array('position_id' => $value->id, 'position_name' => $value->position_name);
                    if ($positionid['id'] == $value->id) {
                        $doctor[] = array_merge($doctordetail, $doctors[$value->id]);
                    }
                    $final_array['nominielist'] = $doctor;
                }
            }
        }

        return $final_array;
    }

    /**
     * @param $user_master_id
     * @param $election_id
     * @return int
     */
    public function electioncastvotecheck($user_master_id, $election_id)
    {
        $response = 0;
        $this->db->select('channel_id');
        $this->db->from('election_ballotbox');
        $this->db->where(array('election_id' => $election_id, 'user_master_id' => $user_master_id));

        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            $response = $query->num_rows();
        }
        return $response;
    }

    /**
     * @param $array
     * @return bool
     */
    public function insert_audit($array)
    {
        $this->insertdb = $this->load->database('insert', true);
        $this->insertdb->insert('election_audit', $array);

        return true;
    }

    public function cta_submitted_forms($value)
    {
        // print_r($value);
        // exit;
        $this->insertdb = $this->load->database('insert', true);
        if (!empty($value)) {
            $this->insertdb->insert('cta_submitted_forms', $value);

            //  print_r($this->insertdb->last_query()); die;
            $insert_id = $this->insertdb->insert_id();
        }
        //print_r($insert_id);exit;
        return $insert_id;
    }
}
